# 开发环境设置指南

## 文档概述

本文档提供了 Native Messaging 企业级独立守护进程系统的完整开发环境设置指南，包括工具安装、配置步骤、开发规范和故障排除。

**相关文档**:
- [API设计规范](./API_DESIGN_SPECIFICATION.md)
- [技术栈选型指南](./TECHNOLOGY_STACK_GUIDE.md)
- [增量开发路线图](./NATIVE_MESSAGING_INCREMENTAL_ROADMAP.md)
- [跨平台实施计划](./NATIVE_MESSAGING_CROSS_PLATFORM_IMPLEMENTATION_PLAN.md)

## 🎯 环境要求概览

### 硬件要求

| 组件 | 最低要求 | 推荐配置 | 说明 |
|------|----------|----------|------|
| **CPU** | 4核心 2.0GHz | 8核心 3.0GHz+ | 支持并行编译 |
| **内存** | 8GB RAM | 16GB+ RAM | Rust 编译内存消耗大 |
| **存储** | 20GB 可用空间 | 50GB+ SSD | 包含工具链和依赖 |
| **网络** | 稳定互联网连接 | 高速宽带 | 下载依赖和工具 |

### 操作系统支持

| 平台 | 版本要求 | 开发状态 | 说明 |
|------|----------|----------|------|
| **Windows** | Windows 10 1903+ | ✅ 完全支持 | 推荐 Windows 11 |
| **macOS** | macOS 10.15+ | ✅ 完全支持 | 支持 Intel 和 Apple Silicon |
| **Linux** | Ubuntu 18.04+ | ✅ 完全支持 | 或等效发行版 |

## 🛠️ 核心工具安装

### 1. Rust 工具链安装

#### 安装 Rustup (推荐方式)

**Windows (PowerShell)**:
```powershell
# 下载并运行 rustup 安装器
Invoke-WebRequest -Uri "https://win.rustup.rs/x86_64" -OutFile "rustup-init.exe"
.\rustup-init.exe

# 或使用 Chocolatey
choco install rustup.install
```

**macOS/Linux**:
```bash
# 官方安装脚本
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# 重新加载环境变量
source ~/.cargo/env
```

#### 配置 Rust 工具链

```bash
# 安装最新稳定版本
rustup install stable
rustup default stable

# 安装必要组件
rustup component add rustfmt clippy rust-src rust-analyzer

# 安装目标平台 (跨平台编译)
rustup target add x86_64-pc-windows-msvc    # Windows
rustup target add x86_64-apple-darwin       # macOS Intel
rustup target add aarch64-apple-darwin      # macOS Apple Silicon
rustup target add x86_64-unknown-linux-gnu  # Linux

# 验证安装
rustc --version
cargo --version
rustfmt --version
clippy-driver --version
```

### 2. Node.js 和 npm 安装

#### 使用 Node Version Manager (推荐)

**Windows (使用 nvm-windows)**:
```powershell
# 下载并安装 nvm-windows
# 从 https://github.com/coreybutler/nvm-windows/releases 下载

# 安装 Node.js LTS
nvm install lts
nvm use lts
```

**macOS/Linux (使用 nvm)**:
```bash
# 安装 nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# 重新加载 shell
source ~/.bashrc

# 安装 Node.js LTS
nvm install --lts
nvm use --lts

# 验证安装
node --version
npm --version
```

#### 配置 npm

```bash
# 设置 npm 镜像 (可选，提高下载速度)
npm config set registry https://registry.npmmirror.com

# 全局安装开发工具
npm install -g typescript@latest
npm install -g @types/node
npm install -g webpack webpack-cli
npm install -g vite
```

### 3. 平台特定工具

#### Windows 开发环境

```powershell
# 安装 Visual Studio Build Tools
# 下载 Visual Studio Installer
# 选择 "C++ build tools" 工作负载

# 或使用 Chocolatey 安装
choco install visualstudio2022buildtools
choco install visualstudio2022-workload-vctools

# 安装 Windows SDK
choco install windows-sdk-10-version-2004-all

# 验证 MSVC 工具链
where cl.exe
where link.exe
```

#### macOS 开发环境

```bash
# 安装 Xcode Command Line Tools
xcode-select --install

# 安装 Homebrew (包管理器)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装开发工具
brew install git
brew install cmake
brew install pkg-config

# 验证工具
clang --version
git --version
```

#### Linux 开发环境

**Ubuntu/Debian**:
```bash
# 更新包管理器
sudo apt update

# 安装构建工具
sudo apt install -y build-essential
sudo apt install -y cmake
sudo apt install -y pkg-config
sudo apt install -y libssl-dev
sudo apt install -y libgtk-3-dev
sudo apt install -y libwebkit2gtk-4.0-dev
sudo apt install -y libappindicator3-dev
sudo apt install -y librsvg2-dev

# 安装 Git
sudo apt install -y git

# 验证安装
gcc --version
make --version
cmake --version
```

**CentOS/RHEL/Fedora**:
```bash
# CentOS/RHEL
sudo yum groupinstall -y "Development Tools"
sudo yum install -y cmake pkg-config openssl-devel

# Fedora
sudo dnf groupinstall -y "Development Tools"
sudo dnf install -y cmake pkg-config openssl-devel
```

## 🔧 IDE 和编辑器配置

### Visual Studio Code (推荐)

#### 安装 VS Code
```bash
# Windows (Chocolatey)
choco install vscode

# macOS (Homebrew)
brew install --cask visual-studio-code

# Linux (Ubuntu)
sudo snap install code --classic
```

#### 必需扩展
```json
{
  "recommendations": [
    "rust-lang.rust-analyzer",           // Rust 语言支持
    "tauri-apps.tauri-vscode",          // Tauri 开发支持
    "ms-vscode.vscode-typescript-next",  // TypeScript 支持
    "bradlc.vscode-tailwindcss",        // CSS 框架支持
    "esbenp.prettier-vscode",           // 代码格式化
    "ms-vscode.vscode-json",            // JSON 支持
    "redhat.vscode-yaml",               // YAML 支持
    "ms-vscode.cmake-tools",            // CMake 支持
    "ms-vscode.cpptools",               // C++ 支持 (Windows)
    "vadimcn.vscode-lldb",              // 调试器支持
    "serayuzgur.crates",                // Cargo.toml 依赖管理
    "fill-labs.dependi"                 // 依赖版本检查
  ]
}
```

#### VS Code 配置文件

**.vscode/settings.json**:
```json
{
  "rust-analyzer.check.command": "clippy",
  "rust-analyzer.cargo.features": "all",
  "rust-analyzer.procMacro.enable": true,
  "rust-analyzer.imports.granularity.group": "module",
  "rust-analyzer.completion.addCallArgumentSnippets": true,
  "rust-analyzer.completion.addCallParenthesis": true,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },
  "files.associations": {
    "*.rs": "rust",
    "Cargo.toml": "toml",
    "Cargo.lock": "toml"
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.suggest.autoImports": true
}
```

**.vscode/launch.json** (调试配置):
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Daemon",
      "type": "lldb",
      "request": "launch",
      "program": "${workspaceFolder}/target/debug/secure-password-daemon",
      "args": ["--config", "daemon.toml"],
      "cwd": "${workspaceFolder}",
      "sourceLanguages": ["rust"]
    },
    {
      "name": "Debug Tauri App",
      "type": "lldb", 
      "request": "launch",
      "program": "${workspaceFolder}/src-tauri/target/debug/secure-password",
      "cwd": "${workspaceFolder}/src-tauri"
    }
  ]
}
```

**.vscode/tasks.json** (构建任务):
```json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Build Daemon",
      "type": "cargo",
      "command": "build",
      "args": ["--manifest-path", "src-tauri/daemon/Cargo.toml"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      }
    },
    {
      "label": "Test All",
      "type": "cargo", 
      "command": "test",
      "args": ["--workspace"],
      "group": "test"
    },
    {
      "label": "Clippy Check",
      "type": "cargo",
      "command": "clippy",
      "args": ["--workspace", "--all-targets", "--all-features"],
      "group": "build"
    }
  ]
}
```

### 其他 IDE 选项

#### JetBrains RustRover
- **优势**: 强大的代码分析和重构功能
- **配置**: 导入项目后自动识别 Cargo 工作空间
- **插件**: Tauri 插件，TOML 支持

#### Vim/Neovim
```lua
-- init.lua (Neovim 配置示例)
require('packer').startup(function()
  use 'neovim/nvim-lspconfig'
  use 'simrat39/rust-tools.nvim'
  use 'hrsh7th/nvim-cmp'
  use 'hrsh7th/cmp-nvim-lsp'
end)

-- Rust LSP 配置
require('rust-tools').setup({
  server = {
    on_attach = function(_, bufnr)
      vim.keymap.set('n', '<C-space>', require('rust-tools').hover_actions.hover_actions, { buffer = bufnr })
    end
  }
})
```

## 📁 项目结构初始化

### 克隆项目仓库

```bash
# 克隆主仓库
git clone https://github.com/your-org/secure-password.git
cd secure-password

# 初始化子模块 (如果有)
git submodule update --init --recursive
```

### 项目结构验证

```bash
# 验证项目结构
tree -L 3 -I 'target|node_modules|.git'
```

期望的项目结构:
```
secure-password/
├── src-tauri/
│   ├── daemon/                 # 守护进程项目
│   │   ├── Cargo.toml
│   │   └── src/
│   ├── src/                    # 主应用后端
│   │   ├── main.rs
│   │   └── lib.rs
│   ├── Cargo.toml
│   └── tauri.conf.json
├── src/                        # 前端源码
│   ├── components/
│   ├── pages/
│   └── main.ts
├── extensions/                 # 浏览器扩展
│   ├── chrome/
│   ├── firefox/
│   └── shared/
├── crates/                     # 共享库
│   ├── native-messaging/
│   ├── security/
│   └── ipc/
├── docs/                       # 文档
├── scripts/                    # 构建脚本
├── tests/                      # 集成测试
├── Cargo.toml                  # 工作空间配置
├── package.json                # 前端依赖
└── README.md
```

### 依赖安装

```bash
# 安装 Rust 依赖
cargo fetch

# 安装前端依赖
npm install

# 安装 Tauri CLI
cargo install tauri-cli --version "^2.0.0-beta"

# 验证 Tauri 安装
cargo tauri --version
```

## 🔨 开发工具配置

### Git 配置

```bash
# 配置用户信息
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# 配置编辑器
git config --global core.editor "code --wait"

# 配置换行符处理
git config --global core.autocrlf input    # macOS/Linux
git config --global core.autocrlf true     # Windows

# 配置 Git Hooks
cp scripts/pre-commit .git/hooks/
chmod +x .git/hooks/pre-commit
```

### Cargo 配置

创建 `.cargo/config.toml`:
```toml
[build]
# 并行编译作业数
jobs = 8

[target.x86_64-unknown-linux-gnu]
linker = "clang"
rustflags = ["-C", "link-arg=-fuse-ld=lld"]

[target.x86_64-pc-windows-msvc]
rustflags = ["-C", "target-feature=+crt-static"]

[registries.crates-io]
protocol = "sparse"

[net]
retry = 3
git-fetch-with-cli = true

[profile.dev]
# 开发模式优化
opt-level = 0
debug = true
split-debuginfo = "unpacked"

[profile.release]
# 发布模式优化
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
```

### 环境变量配置

创建 `.env` 文件:
```bash
# 开发环境配置
RUST_LOG=debug
RUST_BACKTRACE=1

# Tauri 配置
TAURI_DEV_WATCHER_IGNORE_FILE=.taurignore

# 数据库配置
DATABASE_URL=sqlite:./data/secure-password.db

# 加密配置
ENCRYPTION_KEY_FILE=./keys/master.key

# 开发服务器配置
DEV_SERVER_PORT=3000
DAEMON_IPC_PORT=9090
```

## 🚀 开发流程和规范

### 代码风格规范

#### Rust 代码规范

**rustfmt.toml** (项目根目录):
```toml
# Rust 代码格式化配置
edition = "2021"
max_width = 100
hard_tabs = false
tab_spaces = 4
newline_style = "Unix"
use_small_heuristics = "Default"
reorder_imports = true
reorder_modules = true
remove_nested_parens = true
merge_derives = true
use_try_shorthand = true
use_field_init_shorthand = true
force_explicit_abi = true
empty_item_single_line = true
struct_lit_single_line = true
fn_single_line = false
where_single_line = false
imports_layout = "Vertical"
group_imports = "StdExternalCrate"
```

**clippy.toml** (项目根目录):
```toml
# Clippy 静态分析配置
msrv = "1.75.0"
cognitive-complexity-threshold = 30
too-many-arguments-threshold = 7
type-complexity-threshold = 250
single-char-lifetime-names-threshold = 4
trivial-copy-size-limit = 64
```

#### TypeScript 代码规范

**prettier.config.js**:
```javascript
module.exports = {
  semi: true,
  trailingComma: 'es5',
  singleQuote: true,
  printWidth: 100,
  tabWidth: 2,
  useTabs: false,
  bracketSpacing: true,
  arrowParens: 'avoid',
  endOfLine: 'lf',
  overrides: [
    {
      files: '*.json',
      options: {
        printWidth: 80,
      },
    },
  ],
};
```

**eslint.config.js**:
```javascript
module.exports = {
  extends: [
    '@typescript-eslint/recommended',
    'prettier',
  ],
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint'],
  rules: {
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/explicit-function-return-type': 'warn',
    '@typescript-eslint/no-explicit-any': 'warn',
    'prefer-const': 'error',
    'no-var': 'error',
  },
};
```

### Git 工作流规范

#### 分支命名规范
```bash
# 功能分支
feature/module-1-daemon-core
feature/module-2-system-service

# 修复分支
fix/ipc-connection-timeout
fix/memory-leak-in-daemon

# 发布分支
release/v1.0.0
release/v1.1.0

# 热修复分支
hotfix/security-vulnerability
hotfix/critical-bug-fix
```

#### 提交消息规范
```bash
# 格式: <type>(<scope>): <description>
#
# type: feat, fix, docs, style, refactor, test, chore
# scope: daemon, tauri, extension, ipc, security, etc.
# description: 简洁描述变更内容

# 示例
feat(daemon): implement IPC server with tokio
fix(security): resolve buffer overflow in message parsing
docs(api): add comprehensive API documentation
test(integration): add cross-platform integration tests
```

#### Pre-commit Hook 脚本

**scripts/pre-commit**:
```bash
#!/bin/bash
set -e

echo "Running pre-commit checks..."

# 1. Rust 代码格式检查
echo "Checking Rust code formatting..."
cargo fmt --all -- --check

# 2. Rust 静态分析
echo "Running Clippy..."
cargo clippy --workspace --all-targets --all-features -- -D warnings

# 3. Rust 测试
echo "Running Rust tests..."
cargo test --workspace

# 4. TypeScript 代码格式检查
if [ -f "package.json" ]; then
    echo "Checking TypeScript formatting..."
    npm run lint
    npm run format:check
fi

# 5. 安全审计
echo "Running security audit..."
cargo audit

echo "All pre-commit checks passed!"
```

### 开发命令脚本

#### Makefile (跨平台兼容)
```makefile
# 开发环境 Makefile
.PHONY: help install build test clean dev daemon tauri extension

help: ## 显示帮助信息
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

install: ## 安装所有依赖
	cargo fetch
	npm install
	cargo install tauri-cli --version "^2.0.0-beta"

build: ## 构建所有组件
	cargo build --workspace
	npm run build

test: ## 运行所有测试
	cargo test --workspace
	npm test

clean: ## 清理构建产物
	cargo clean
	rm -rf node_modules
	rm -rf dist

dev: ## 启动开发环境
	cargo tauri dev

daemon: ## 单独构建守护进程
	cargo build --manifest-path src-tauri/daemon/Cargo.toml

tauri: ## 单独构建 Tauri 应用
	cargo build --manifest-path src-tauri/Cargo.toml

extension: ## 构建浏览器扩展
	npm run build:extension

format: ## 格式化代码
	cargo fmt --all
	npm run format

lint: ## 代码静态检查
	cargo clippy --workspace --all-targets --all-features
	npm run lint

audit: ## 安全审计
	cargo audit
	npm audit

release: ## 构建发布版本
	cargo build --release --workspace
	npm run build:production
```

#### package.json 脚本
```json
{
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "build:production": "vite build --mode production",
    "build:extension": "webpack --config webpack.extension.js",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "lint": "eslint src --ext .ts,.tsx",
    "lint:fix": "eslint src --ext .ts,.tsx --fix",
    "format": "prettier --write src",
    "format:check": "prettier --check src",
    "type-check": "tsc --noEmit",
    "tauri": "tauri",
    "tauri:dev": "tauri dev",
    "tauri:build": "tauri build"
  }
}
```

## 🧪 测试环境配置

### 单元测试配置

**Cargo.toml** 测试配置:
```toml
[dev-dependencies]
tokio-test = "0.4"
mockall = "0.11"
serial_test = "3.0"
tempfile = "3.8"
criterion = { version = "0.5", features = ["html_reports"] }
proptest = "1.4"

[[bench]]
name = "ipc_performance"
harness = false

[[test]]
name = "integration_tests"
path = "tests/integration_tests.rs"
```

### 集成测试环境

**tests/common/mod.rs**:
```rust
use std::sync::Once;
use tempfile::TempDir;
use tokio::runtime::Runtime;

static INIT: Once = Once::new();

pub fn setup_test_environment() -> TempDir {
    INIT.call_once(|| {
        env_logger::init();
    });

    tempfile::tempdir().expect("Failed to create temp directory")
}

pub fn create_test_runtime() -> Runtime {
    tokio::runtime::Builder::new_multi_thread()
        .worker_threads(2)
        .enable_all()
        .build()
        .expect("Failed to create test runtime")
}
```

### 性能测试配置

**benches/ipc_performance.rs**:
```rust
use criterion::{criterion_group, criterion_main, Criterion, BenchmarkId};
use secure_password_daemon::ipc::IpcServer;

fn bench_message_throughput(c: &mut Criterion) {
    let rt = tokio::runtime::Runtime::new().unwrap();

    let mut group = c.benchmark_group("ipc_throughput");

    for message_count in [100, 1000, 10000].iter() {
        group.bench_with_input(
            BenchmarkId::new("messages", message_count),
            message_count,
            |b, &count| {
                b.to_async(&rt).iter(|| async {
                    // 性能测试逻辑
                });
            },
        );
    }

    group.finish();
}

criterion_group!(benches, bench_message_throughput);
criterion_main!(benches);
```

## 🔍 调试和故障排除

### 常见问题解决

#### Rust 编译问题

**问题**: `error: linker 'cc' not found`
```bash
# Linux 解决方案
sudo apt install build-essential

# macOS 解决方案
xcode-select --install

# Windows 解决方案
# 安装 Visual Studio Build Tools
```

**问题**: `error: Microsoft Visual C++ 14.0 is required`
```bash
# Windows 解决方案
# 1. 安装 Visual Studio 2022 Community
# 2. 或安装 Build Tools for Visual Studio 2022
# 3. 确保选择 "C++ build tools" 工作负载
```

**问题**: 编译速度慢
```bash
# 解决方案：配置并行编译
export CARGO_BUILD_JOBS=8

# 或在 .cargo/config.toml 中配置
[build]
jobs = 8
```

#### Tauri 开发问题

**问题**: `tauri dev` 启动失败
```bash
# 检查 Tauri CLI 版本
cargo tauri --version

# 重新安装 Tauri CLI
cargo install tauri-cli --version "^2.0.0-beta" --force

# 检查 tauri.conf.json 配置
cargo tauri info
```

**问题**: 前端热重载不工作
```bash
# 检查开发服务器配置
npm run dev

# 检查 tauri.conf.json 中的 devPath 配置
{
  "build": {
    "devPath": "http://localhost:3000"
  }
}
```

#### 依赖问题

**问题**: 依赖版本冲突
```bash
# 查看依赖树
cargo tree

# 更新依赖
cargo update

# 强制使用特定版本
[dependencies]
problematic-crate = "=1.2.3"
```

**问题**: 网络连接问题
```bash
# 配置代理 (如果需要)
export HTTPS_PROXY=http://proxy.company.com:8080
export HTTP_PROXY=http://proxy.company.com:8080

# 或配置 Cargo 镜像
[source.crates-io]
replace-with = "ustc"

[source.ustc]
registry = "https://mirrors.ustc.edu.cn/crates.io-index"
```

### 调试技巧

#### Rust 调试
```rust
// 使用 dbg! 宏进行快速调试
let result = dbg!(some_function());

// 使用 tracing 进行结构化日志
use tracing::{info, warn, error, debug};

debug!("Processing message: {:?}", message);
info!("Server started on port {}", port);
warn!("Connection timeout: {}ms", timeout);
error!("Failed to process request: {}", error);
```

#### 性能分析
```bash
# 使用 perf 进行性能分析 (Linux)
cargo build --release
perf record --call-graph=dwarf ./target/release/secure-password-daemon
perf report

# 使用 Instruments 进行性能分析 (macOS)
cargo build --release
instruments -t "Time Profiler" ./target/release/secure-password-daemon

# 使用 cargo-flamegraph 生成火焰图
cargo install flamegraph
cargo flamegraph --bin secure-password-daemon
```

### 日志配置

**开发环境日志配置**:
```rust
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

fn init_logging() {
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "debug".into()),
        )
        .with(tracing_subscriber::fmt::layer().pretty())
        .init();
}
```

**生产环境日志配置**:
```rust
fn init_production_logging() {
    tracing_subscriber::registry()
        .with(tracing_subscriber::EnvFilter::new("info"))
        .with(
            tracing_subscriber::fmt::layer()
                .json()
                .with_current_span(false)
                .with_span_list(true),
        )
        .with(
            tracing_appender::rolling::daily("logs", "daemon.log")
        )
        .init();
}
```

## 📚 开发资源和文档

### 官方文档链接
- [Rust 官方文档](https://doc.rust-lang.org/)
- [Tauri 官方文档](https://tauri.app/v1/guides/)
- [Tokio 异步编程指南](https://tokio.rs/tokio/tutorial)
- [WebExtension API 文档](https://developer.mozilla.org/en-US/docs/Mozilla/Add-ons/WebExtensions)

### 推荐学习资源
- [Rust 程序设计语言](https://doc.rust-lang.org/book/)
- [Rust 异步编程](https://rust-lang.github.io/async-book/)
- [Tauri 示例项目](https://github.com/tauri-apps/tauri/tree/dev/examples)
- [WebExtension 示例](https://github.com/mdn/webextensions-examples)

### 社区支持
- [Rust 用户论坛](https://users.rust-lang.org/)
- [Tauri Discord 社区](https://discord.com/invite/tauri)
- [Stack Overflow Rust 标签](https://stackoverflow.com/questions/tagged/rust)
- [GitHub Discussions](https://github.com/tauri-apps/tauri/discussions)
