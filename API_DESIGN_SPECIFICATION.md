# API 设计规范文档

## 文档概述

本文档定义了 Native Messaging 企业级独立守护进程系统的完整 API 接口规范，包括守护进程、Tauri 主应用、浏览器扩展之间的所有通信接口。

**相关文档**:
- [增量开发路线图](./NATIVE_MESSAGING_INCREMENTAL_ROADMAP.md)
- [跨平台实施计划](./NATIVE_MESSAGING_CROSS_PLATFORM_IMPLEMENTATION_PLAN.md)
- [技术栈选型指南](./TECHNOLOGY_STACK_GUIDE.md)
- [开发环境设置指南](./DEVELOPMENT_ENVIRONMENT_SETUP.md)

## 🏗️ API 架构概览

```
┌─────────────────────────────────────────────────────────────────┐
│                        API 层次架构                             │
├─────────────────────────────────────────────────────────────────┤
│  🌐 浏览器扩展 API (TypeScript)                                 │
│  ├─ Native Messaging Protocol API                              │
│  └─ Browser Extension Runtime API                              │
├─────────────────────────────────────────────────────────────────┤
│  📡 守护进程 API (Rust)                                         │
│  ├─ Native Messaging Host API                                  │
│  ├─ IPC Server API                                             │
│  ├─ System Service API                                         │
│  └─ Security Proxy API                                         │
├─────────────────────────────────────────────────────────────────┤
│  🎨 Tauri 主应用 API (Rust)                                     │
│  ├─ IPC Client API                                             │
│  ├─ Business Logic API                                         │
│  ├─ Storage Management API                                     │
│  └─ Configuration API                                          │
├─────────────────────────────────────────────────────────────────┤
│  📚 共享核心库 API (Rust)                                       │
│  ├─ Protocol Definition API                                    │
│  ├─ Type System API                                            │
│  ├─ Validation API                                             │
│  └─ Utility API                                                │
└─────────────────────────────────────────────────────────────────┘
```

## 🔧 守护进程核心 API

### 1. 守护进程服务 API

#### 1.1 服务生命周期管理

```rust
/// 守护进程服务主接口
#[async_trait]
pub trait DaemonService: Send + Sync {
    /// 启动守护进程服务
    /// 
    /// 初始化并启动所有子服务组件，包括系统服务注册、IPC服务器、
    /// Native Messaging Host 和应用管理器
    /// 
    /// # 参数
    /// - `config`: 守护进程配置
    /// 
    /// # 返回
    /// - `Ok(())`: 启动成功
    /// - `Err(DaemonError)`: 启动失败，包含详细错误信息
    /// 
    /// # 错误类型
    /// - `DaemonError::ConfigInvalid`: 配置无效
    /// - `DaemonError::ServiceRegistrationFailed`: 系统服务注册失败
    /// - `DaemonError::IpcServerStartFailed`: IPC服务器启动失败
    /// 
    /// # 示例
    /// ```rust
    /// let config = DaemonConfig::load_from_file("daemon.toml").await?;
    /// let daemon = SecurePasswordDaemon::new();
    /// daemon.start(config).await?;
    /// ```
    async fn start(&mut self, config: DaemonConfig) -> Result<(), DaemonError>;

    /// 优雅关闭守护进程
    /// 
    /// 按顺序关闭所有子服务，确保数据完整性和资源清理
    /// 
    /// # 超时控制
    /// - 默认超时: 30秒
    /// - 强制终止: 超时后强制关闭所有服务
    /// 
    /// # 关闭顺序
    /// 1. 停止接受新连接
    /// 2. 等待现有请求完成
    /// 3. 关闭 IPC 服务器
    /// 4. 关闭 Native Messaging Host
    /// 5. 停止应用管理器
    /// 6. 注销系统服务
    async fn shutdown(&mut self) -> Result<(), DaemonError>;

    /// 重新加载配置
    /// 
    /// 在不重启服务的情况下重新加载配置文件
    /// 
    /// # 支持热重载的配置项
    /// - IPC 连接参数
    /// - Native Messaging 设置
    /// - 安全策略配置
    /// - 监控告警配置
    /// 
    /// # 不支持热重载的配置项
    /// - 系统服务配置
    /// - 基础网络绑定配置
    async fn reload_config(&mut self, config: DaemonConfig) -> Result<(), DaemonError>;

    /// 获取服务健康状态
    /// 
    /// 返回详细的服务运行状态和性能指标
    /// 
    /// # 返回信息包含
    /// - 服务运行状态
    /// - 各子服务状态
    /// - 性能指标统计
    /// - 错误和警告信息
    async fn get_health_status(&self) -> DaemonHealthStatus;

    /// 获取服务统计信息
    /// 
    /// 返回详细的运行时统计数据
    async fn get_statistics(&self) -> DaemonStatistics;
}
```

#### 1.2 系统服务集成 API

```rust
/// 跨平台系统服务管理接口
#[async_trait]
pub trait SystemServiceManager: Send + Sync {
    /// 安装系统服务
    /// 
    /// 在目标平台上安装守护进程作为系统服务
    /// 
    /// # 平台支持
    /// - Windows: Windows Service
    /// - macOS: LaunchDaemon
    /// - Linux: systemd service
    /// 
    /// # 权限要求
    /// - Windows: 管理员权限
    /// - macOS: sudo 权限
    /// - Linux: root 权限或 systemd 用户权限
    async fn install_service(&self, config: &ServiceInstallConfig) -> Result<(), ServiceError>;

    /// 卸载系统服务
    /// 
    /// 从系统中完全移除守护进程服务
    async fn uninstall_service(&self) -> Result<(), ServiceError>;

    /// 启动系统服务
    async fn start_service(&self) -> Result<(), ServiceError>;

    /// 停止系统服务
    async fn stop_service(&self) -> Result<(), ServiceError>;

    /// 重启系统服务
    async fn restart_service(&self) -> Result<(), ServiceError>;

    /// 获取服务状态
    /// 
    /// # 返回状态
    /// - `ServiceStatus::Running`: 服务正在运行
    /// - `ServiceStatus::Stopped`: 服务已停止
    /// - `ServiceStatus::Starting`: 服务正在启动
    /// - `ServiceStatus::Stopping`: 服务正在停止
    /// - `ServiceStatus::Error(String)`: 服务异常
    async fn get_service_status(&self) -> Result<ServiceStatus, ServiceError>;

    /// 配置自动启动
    /// 
    /// 设置服务开机自动启动
    async fn enable_auto_start(&self) -> Result<(), ServiceError>;

    /// 禁用自动启动
    async fn disable_auto_start(&self) -> Result<(), ServiceError>;
}
```

### 2. IPC 通信引擎 API

#### 2.1 IPC 服务器 API

```rust
/// IPC 服务器接口
#[async_trait]
pub trait IpcServer: Send + Sync {
    /// 启动 IPC 服务器
    /// 
    /// 根据配置启动相应的传输层服务器
    /// 
    /// # 传输层选择
    /// - Windows: Named Pipe (优先) 或 TCP
    /// - macOS/Linux: Unix Domain Socket (优先) 或 TCP
    /// - 跨平台: TCP Socket
    async fn start(&mut self, config: IpcServerConfig) -> Result<(), IpcError>;

    /// 停止 IPC 服务器
    async fn stop(&mut self) -> Result<(), IpcError>;

    /// 发送消息到指定客户端
    /// 
    /// # 参数
    /// - `client_id`: 客户端连接标识符
    /// - `message`: 要发送的 IPC 消息
    /// 
    /// # 错误处理
    /// - 客户端不存在: 返回 `IpcError::ClientNotFound`
    /// - 连接已断开: 返回 `IpcError::ConnectionClosed`
    /// - 序列化失败: 返回 `IpcError::SerializationFailed`
    async fn send_to_client(&self, client_id: &str, message: IpcMessage) -> Result<(), IpcError>;

    /// 广播消息到所有客户端
    /// 
    /// 向所有活跃的客户端连接发送消息
    async fn broadcast(&self, message: IpcMessage) -> Result<(), IpcError>;

    /// 获取活跃连接列表
    /// 
    /// 返回当前所有活跃的客户端连接信息
    async fn get_active_connections(&self) -> Vec<IpcConnectionInfo>;

    /// 断开指定客户端连接
    async fn disconnect_client(&self, client_id: &str) -> Result<(), IpcError>;

    /// 注册消息处理器
    /// 
    /// 为特定消息类型注册处理器
    /// 
    /// # 参数
    /// - `message_type`: 消息类型标识符
    /// - `handler`: 消息处理器实现
    async fn register_handler(
        &mut self, 
        message_type: IpcMessageType, 
        handler: Box<dyn IpcMessageHandler>
    ) -> Result<(), IpcError>;

    /// 获取服务器统计信息
    async fn get_server_stats(&self) -> IpcServerStats;
}
```

#### 2.2 IPC 消息协议 API

```rust
/// IPC 消息定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IpcMessage {
    /// 消息唯一标识符
    pub message_id: String,
    
    /// 消息类型
    pub message_type: IpcMessageType,
    
    /// 消息负载数据
    pub payload: serde_json::Value,
    
    /// 消息时间戳 (Unix 时间戳，毫秒)
    pub timestamp: u64,
    
    /// 是否需要响应
    pub response_required: bool,
    
    /// 消息优先级
    pub priority: MessagePriority,
    
    /// 消息来源标识
    pub source: String,
    
    /// 消息目标标识 (可选)
    pub target: Option<String>,
    
    /// 消息元数据
    pub metadata: HashMap<String, String>,
}

/// IPC 消息类型枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum IpcMessageType {
    // 应用生命周期管理
    LaunchApp,
    ShutdownApp,
    RestartApp,
    AppStatus,
    AppHealthCheck,
    
    // Native Messaging 代理
    BrowserRequest,
    BrowserResponse,
    BrowserConnectionStatus,
    
    // 安全验证
    AuthRequest,
    AuthResponse,
    SecurityAlert,
    
    // 配置管理
    ConfigUpdate,
    ConfigQuery,
    ConfigValidation,
    
    // 系统监控
    HealthCheck,
    MetricsReport,
    PerformanceAlert,
    
    // 自定义消息类型
    Custom(String),
}

/// 消息优先级
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum MessagePriority {
    Low = 1,
    Normal = 2,
    High = 3,
    Critical = 4,
}

/// IPC 响应消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IpcResponse {
    /// 对应的请求消息 ID
    pub request_id: String,
    
    /// 响应状态
    pub status: ResponseStatus,
    
    /// 响应数据
    pub data: serde_json::Value,
    
    /// 错误信息 (如果有)
    pub error: Option<String>,
    
    /// 响应时间戳
    pub timestamp: u64,
    
    /// 处理耗时 (毫秒)
    pub processing_time: u64,
}

/// 响应状态枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ResponseStatus {
    Success,
    Error,
    Timeout,
    NotFound,
    Unauthorized,
    RateLimited,
}
```

### 3. Native Messaging 代理 API

#### 3.1 Native Messaging Host API

```rust
/// Native Messaging Host 接口
#[async_trait]
pub trait NativeMessagingHost: Send + Sync {
    /// 启动 Native Messaging Host
    /// 
    /// 初始化标准输入/输出监听，注册浏览器 Host 配置
    async fn start(&mut self, config: NativeMessagingConfig) -> Result<(), NativeMessagingError>;

    /// 停止 Native Messaging Host
    async fn stop(&mut self) -> Result<(), NativeMessagingError>;

    /// 注册浏览器 Host 配置
    /// 
    /// 自动检测系统中安装的浏览器并注册相应的 Host 配置
    /// 
    /// # 支持的浏览器
    /// - Google Chrome
    /// - Mozilla Firefox
    /// - Microsoft Edge
    /// - Safari (macOS only)
    /// - Chromium
    /// - Brave Browser
    /// - Opera
    async fn register_browser_hosts(&self) -> Result<Vec<BrowserRegistration>, NativeMessagingError>;

    /// 处理浏览器扩展请求
    /// 
    /// 接收来自浏览器扩展的请求并转发给 Tauri 主应用
    /// 
    /// # 处理流程
    /// 1. 验证请求来源和格式
    /// 2. 安全检查和净化
    /// 3. 转换为 IPC 消息格式
    /// 4. 转发给主应用
    /// 5. 处理响应并返回给浏览器
    async fn handle_browser_request(&self, request: BrowserRequest) -> Result<BrowserResponse, NativeMessagingError>;

    /// 获取已注册的浏览器列表
    async fn get_registered_browsers(&self) -> Vec<BrowserInfo>;

    /// 获取活跃的浏览器连接
    async fn get_active_browser_connections(&self) -> Vec<BrowserConnectionInfo>;

    /// 获取 Native Messaging 统计信息
    async fn get_statistics(&self) -> NativeMessagingStats;
}
```

#### 3.2 浏览器请求/响应协议 API

```rust
/// 浏览器扩展请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BrowserRequest {
    /// 请求唯一标识符
    pub request_id: String,
    
    /// 扩展 ID
    pub extension_id: String,
    
    /// 消息类型
    pub message_type: String,
    
    /// 请求负载数据
    pub payload: serde_json::Value,
    
    /// 浏览器类型
    pub browser: BrowserType,
    
    /// 请求时间戳
    pub timestamp: u64,
    
    /// 请求来源 URL (可选)
    pub origin_url: Option<String>,
    
    /// 请求优先级
    pub priority: RequestPriority,
    
    /// 超时时间 (毫秒)
    pub timeout: Option<u64>,
}

/// 浏览器响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BrowserResponse {
    /// 对应的请求 ID
    pub request_id: String,
    
    /// 响应状态
    pub status: ResponseStatus,
    
    /// 响应数据
    pub payload: serde_json::Value,
    
    /// 错误信息 (如果有)
    pub error: Option<String>,
    
    /// 响应时间戳
    pub timestamp: u64,
    
    /// 处理耗时 (毫秒)
    pub processing_time: u64,
    
    /// 响应元数据
    pub metadata: HashMap<String, String>,
}

/// 浏览器类型枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum BrowserType {
    Chrome,
    Firefox,
    Edge,
    Safari,
    Chromium,
    Brave,
    Opera,
    Unknown(String),
}

/// 请求优先级
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum RequestPriority {
    Background = 1,
    Normal = 2,
    UserInitiated = 3,
    Critical = 4,
}
```

## 🎨 Tauri 主应用 API

### 1. IPC 客户端 API

#### 1.1 守护进程连接 API

```rust
/// IPC 客户端接口
#[async_trait]
pub trait IpcClient: Send + Sync {
    /// 连接到守护进程
    /// 
    /// 建立与守护进程的 IPC 连接
    /// 
    /// # 连接参数
    /// - `config`: IPC 连接配置
    /// 
    /// # 连接流程
    /// 1. 检测守护进程是否运行
    /// 2. 选择最佳传输方式
    /// 3. 建立连接
    /// 4. 执行身份验证
    /// 5. 注册消息处理器
    async fn connect(&mut self, config: IpcConnectionConfig) -> Result<(), IpcClientError>;

    /// 断开连接
    async fn disconnect(&mut self) -> Result<(), IpcClientError>;

    /// 发送消息到守护进程
    /// 
    /// # 参数
    /// - `message`: 要发送的 IPC 消息
    /// 
    /// # 返回
    /// - 如果 `response_required` 为 true，等待并返回响应
    /// - 如果 `response_required` 为 false，立即返回发送结果
    async fn send_message(&self, message: IpcMessage) -> Result<Option<IpcResponse>, IpcClientError>;

    /// 发送请求并等待响应
    /// 
    /// 便捷方法，自动设置 `response_required = true` 并等待响应
    async fn send_request(&self, message: IpcMessage) -> Result<IpcResponse, IpcClientError>;

    /// 注册消息处理器
    /// 
    /// 注册处理来自守护进程的消息的处理器
    async fn register_handler(
        &mut self, 
        message_type: IpcMessageType, 
        handler: Box<dyn IpcMessageHandler>
    ) -> Result<(), IpcClientError>;

    /// 获取连接状态
    async fn get_connection_status(&self) -> ConnectionStatus;

    /// 启用自动重连
    /// 
    /// 当连接断开时自动尝试重新连接
    async fn enable_auto_reconnect(&mut self, config: ReconnectConfig) -> Result<(), IpcClientError>;
}
```

#### 1.2 浏览器消息处理 API

```rust
/// 浏览器消息处理器接口
#[async_trait]
pub trait BrowserMessageHandler: Send + Sync {
    /// 处理浏览器扩展消息
    ///
    /// # 参数
    /// - `message`: 来自浏览器扩展的消息
    ///
    /// # 返回
    /// - `Ok(BrowserResponse)`: 处理成功，返回响应
    /// - `Err(HandlerError)`: 处理失败
    ///
    /// # 示例
    /// ```rust
    /// async fn handle(&self, message: BrowserMessage) -> Result<BrowserResponse, HandlerError> {
    ///     match message.message_type.as_str() {
    ///         "get_credentials" => self.handle_get_credentials(message).await,
    ///         "save_credential" => self.handle_save_credential(message).await,
    ///         _ => Err(HandlerError::UnsupportedMessageType(message.message_type))
    ///     }
    /// }
    /// ```
    async fn handle(&self, message: BrowserMessage) -> Result<BrowserResponse, HandlerError>;

    /// 获取支持的消息类型
    fn supported_message_types(&self) -> Vec<String>;

    /// 处理器优先级 (数值越大优先级越高)
    fn priority(&self) -> i32 { 0 }

    /// 处理器名称
    fn name(&self) -> &str;
}

/// 浏览器消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BrowserMessage {
    /// 消息 ID
    pub message_id: String,

    /// 消息类型
    pub message_type: String,

    /// 消息数据
    pub payload: serde_json::Value,

    /// 扩展信息
    pub extension_info: ExtensionInfo,

    /// 浏览器信息
    pub browser_info: BrowserInfo,

    /// 时间戳
    pub timestamp: u64,

    /// 来源 URL
    pub origin_url: Option<String>,
}

/// 扩展信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExtensionInfo {
    /// 扩展 ID
    pub id: String,

    /// 扩展名称
    pub name: Option<String>,

    /// 扩展版本
    pub version: Option<String>,

    /// 权限列表
    pub permissions: Vec<String>,
}
```

### 2. 业务逻辑 API

#### 2.1 凭证管理 API

```rust
/// 凭证管理器接口
#[async_trait]
pub trait CredentialManager: Send + Sync {
    /// 获取指定域名的凭证
    ///
    /// # 参数
    /// - `domain`: 目标域名
    /// - `options`: 查询选项
    ///
    /// # 返回
    /// - `Ok(Vec<Credential>)`: 匹配的凭证列表
    /// - `Err(CredentialError)`: 查询失败
    async fn get_credentials_for_domain(
        &self,
        domain: &str,
        options: GetCredentialsOptions
    ) -> Result<Vec<Credential>, CredentialError>;

    /// 保存新凭证
    ///
    /// # 参数
    /// - `credential`: 要保存的凭证数据
    ///
    /// # 返回
    /// - `Ok(String)`: 保存成功，返回凭证 ID
    /// - `Err(CredentialError)`: 保存失败
    async fn save_credential(&self, credential: NewCredential) -> Result<String, CredentialError>;

    /// 更新现有凭证
    async fn update_credential(&self, id: &str, updates: CredentialUpdate) -> Result<(), CredentialError>;

    /// 删除凭证
    async fn delete_credential(&self, id: &str) -> Result<(), CredentialError>;

    /// 搜索凭证
    ///
    /// # 参数
    /// - `query`: 搜索查询
    /// - `options`: 搜索选项
    async fn search_credentials(
        &self,
        query: &str,
        options: SearchOptions
    ) -> Result<Vec<Credential>, CredentialError>;

    /// 生成密码
    ///
    /// # 参数
    /// - `policy`: 密码策略
    ///
    /// # 返回
    /// - `Ok(String)`: 生成的密码
    async fn generate_password(&self, policy: PasswordPolicy) -> Result<String, CredentialError>;

    /// 检查密码强度
    async fn check_password_strength(&self, password: &str) -> PasswordStrength;

    /// 检测密码泄露
    ///
    /// 使用 HaveIBeenPwned API 检查密码是否已泄露
    async fn check_password_breach(&self, password: &str) -> Result<BreachCheckResult, CredentialError>;
}

/// 凭证数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Credential {
    /// 凭证 ID
    pub id: String,

    /// 域名
    pub domain: String,

    /// 用户名
    pub username: String,

    /// 加密的密码
    pub encrypted_password: String,

    /// 显示名称
    pub display_name: Option<String>,

    /// 备注
    pub notes: Option<String>,

    /// 标签
    pub tags: Vec<String>,

    /// 创建时间
    pub created_at: DateTime<Utc>,

    /// 更新时间
    pub updated_at: DateTime<Utc>,

    /// 最后使用时间
    pub last_used_at: Option<DateTime<Utc>>,

    /// 是否收藏
    pub is_favorite: bool,

    /// 自定义字段
    pub custom_fields: HashMap<String, String>,
}
```

#### 2.2 安全笔记管理 API

```rust
/// 安全笔记管理器接口
#[async_trait]
pub trait SecureNoteManager: Send + Sync {
    /// 创建安全笔记
    async fn create_note(&self, note: NewSecureNote) -> Result<String, SecureNoteError>;

    /// 获取安全笔记
    async fn get_note(&self, id: &str) -> Result<SecureNote, SecureNoteError>;

    /// 更新安全笔记
    async fn update_note(&self, id: &str, updates: SecureNoteUpdate) -> Result<(), SecureNoteError>;

    /// 删除安全笔记
    async fn delete_note(&self, id: &str) -> Result<(), SecureNoteError>;

    /// 列出所有安全笔记
    async fn list_notes(&self, options: ListNotesOptions) -> Result<Vec<SecureNoteSummary>, SecureNoteError>;

    /// 搜索安全笔记
    async fn search_notes(&self, query: &str, options: SearchOptions) -> Result<Vec<SecureNoteSummary>, SecureNoteError>;
}

/// 安全笔记数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecureNote {
    /// 笔记 ID
    pub id: String,

    /// 标题
    pub title: String,

    /// 加密的内容
    pub encrypted_content: String,

    /// 类型
    pub note_type: SecureNoteType,

    /// 标签
    pub tags: Vec<String>,

    /// 创建时间
    pub created_at: DateTime<Utc>,

    /// 更新时间
    pub updated_at: DateTime<Utc>,

    /// 是否收藏
    pub is_favorite: bool,
}

/// 安全笔记类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SecureNoteType {
    General,
    CreditCard,
    BankAccount,
    Identity,
    License,
    Passport,
    Custom(String),
}
```

### 3. 存储管理 API

#### 3.1 数据库管理 API

```rust
/// 数据库管理器接口
#[async_trait]
pub trait DatabaseManager: Send + Sync {
    /// 初始化数据库
    ///
    /// 创建数据库文件和表结构
    async fn initialize(&self, config: DatabaseConfig) -> Result<(), DatabaseError>;

    /// 执行数据库迁移
    ///
    /// 升级数据库结构到最新版本
    async fn migrate(&self) -> Result<(), DatabaseError>;

    /// 备份数据库
    ///
    /// # 参数
    /// - `backup_path`: 备份文件路径
    /// - `options`: 备份选项
    async fn backup(&self, backup_path: &Path, options: BackupOptions) -> Result<(), DatabaseError>;

    /// 恢复数据库
    ///
    /// 从备份文件恢复数据库
    async fn restore(&self, backup_path: &Path, options: RestoreOptions) -> Result<(), DatabaseError>;

    /// 优化数据库
    ///
    /// 执行数据库优化操作，如重建索引、清理碎片等
    async fn optimize(&self) -> Result<(), DatabaseError>;

    /// 获取数据库统计信息
    async fn get_statistics(&self) -> Result<DatabaseStatistics, DatabaseError>;

    /// 验证数据库完整性
    async fn verify_integrity(&self) -> Result<IntegrityCheckResult, DatabaseError>;
}

/// 数据库配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    /// 数据库文件路径
    pub database_path: PathBuf,

    /// 连接池大小
    pub connection_pool_size: u32,

    /// 连接超时时间 (秒)
    pub connection_timeout: u64,

    /// 是否启用 WAL 模式
    pub enable_wal_mode: bool,

    /// 是否启用外键约束
    pub enable_foreign_keys: bool,

    /// 缓存大小 (KB)
    pub cache_size: u64,

    /// 是否启用加密
    pub enable_encryption: bool,

    /// 加密密钥 (如果启用加密)
    pub encryption_key: Option<String>,
}
```

#### 3.2 加密存储 API

```rust
/// 加密存储管理器接口
#[async_trait]
pub trait EncryptionManager: Send + Sync {
    /// 初始化加密管理器
    ///
    /// # 参数
    /// - `master_key`: 主密钥
    /// - `config`: 加密配置
    async fn initialize(&mut self, master_key: &[u8], config: EncryptionConfig) -> Result<(), EncryptionError>;

    /// 加密数据
    ///
    /// # 参数
    /// - `plaintext`: 明文数据
    /// - `context`: 加密上下文 (可选)
    ///
    /// # 返回
    /// - `Ok(Vec<u8>)`: 加密后的数据
    async fn encrypt(&self, plaintext: &[u8], context: Option<&str>) -> Result<Vec<u8>, EncryptionError>;

    /// 解密数据
    ///
    /// # 参数
    /// - `ciphertext`: 密文数据
    /// - `context`: 解密上下文 (可选)
    ///
    /// # 返回
    /// - `Ok(Vec<u8>)`: 解密后的数据
    async fn decrypt(&self, ciphertext: &[u8], context: Option<&str>) -> Result<Vec<u8>, EncryptionError>;

    /// 加密字符串
    async fn encrypt_string(&self, plaintext: &str, context: Option<&str>) -> Result<String, EncryptionError>;

    /// 解密字符串
    async fn decrypt_string(&self, ciphertext: &str, context: Option<&str>) -> Result<String, EncryptionError>;

    /// 生成随机密钥
    ///
    /// # 参数
    /// - `key_length`: 密钥长度 (字节)
    async fn generate_key(&self, key_length: usize) -> Result<Vec<u8>, EncryptionError>;

    /// 派生密钥
    ///
    /// 从主密钥派生子密钥
    ///
    /// # 参数
    /// - `context`: 派生上下文
    /// - `key_length`: 目标密钥长度
    async fn derive_key(&self, context: &str, key_length: usize) -> Result<Vec<u8>, EncryptionError>;

    /// 更换主密钥
    ///
    /// 使用新的主密钥重新加密所有数据
    async fn change_master_key(&mut self, new_master_key: &[u8]) -> Result<(), EncryptionError>;

    /// 验证主密钥
    ///
    /// 验证提供的密钥是否正确
    async fn verify_master_key(&self, master_key: &[u8]) -> Result<bool, EncryptionError>;
}

/// 加密配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EncryptionConfig {
    /// 加密算法
    pub algorithm: EncryptionAlgorithm,

    /// 密钥派生函数
    pub key_derivation: KeyDerivationFunction,

    /// 密钥派生迭代次数
    pub kdf_iterations: u32,

    /// 盐值长度
    pub salt_length: usize,

    /// 随机数长度
    pub nonce_length: usize,

    /// 是否启用压缩
    pub enable_compression: bool,
}

/// 加密算法枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EncryptionAlgorithm {
    AES256GCM,
    ChaCha20Poly1305,
    XChaCha20Poly1305,
}

/// 密钥派生函数枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum KeyDerivationFunction {
    PBKDF2,
    Argon2id,
    Scrypt,
}
```

## 🌐 浏览器扩展 API

### 1. Native Messaging 客户端 API

#### 1.1 TypeScript 接口定义

```typescript
/**
 * Native Messaging 客户端接口
 *
 * 提供与守护进程通信的标准化接口
 */
export interface NativeMessagingClient {
    /**
     * 连接到 Native Messaging Host
     *
     * @returns Promise<void> 连接成功或失败
     * @throws NativeMessagingError 连接失败时抛出错误
     */
    connect(): Promise<void>;

    /**
     * 断开连接
     */
    disconnect(): void;

    /**
     * 发送消息到应用
     *
     * @param message 要发送的消息
     * @returns Promise<NativeResponse> 应用响应
     * @throws NativeMessagingError 发送失败或超时
     *
     * @example
     * ```typescript
     * const response = await client.sendMessage({
     *     type: 'get_credentials',
     *     requestId: generateId(),
     *     payload: { domain: 'example.com' },
     *     timestamp: Date.now(),
     *     source: 'browser-extension'
     * });
     * ```
     */
    sendMessage(message: NativeMessage): Promise<NativeResponse>;

    /**
     * 监听来自应用的消息
     *
     * @param callback 消息处理回调函数
     *
     * @example
     * ```typescript
     * client.onMessage((message) => {
     *     console.log('Received message:', message);
     *     // 处理消息
     * });
     * ```
     */
    onMessage(callback: (message: NativeMessage) => void): void;

    /**
     * 获取连接状态
     *
     * @returns ConnectionStatus 当前连接状态
     */
    getConnectionStatus(): ConnectionStatus;

    /**
     * 设置错误处理器
     *
     * @param handler 错误处理函数
     */
    onError(handler: (error: NativeMessagingError) => void): void;

    /**
     * 设置连接状态变化监听器
     *
     * @param listener 状态变化监听函数
     */
    onConnectionStatusChange(listener: (status: ConnectionStatus) => void): void;
}

/**
 * Native Messaging 消息接口
 */
export interface NativeMessage {
    /** 消息类型 */
    type: string;

    /** 请求 ID */
    requestId: string;

    /** 消息负载 */
    payload: any;

    /** 时间戳 */
    timestamp: number;

    /** 消息来源 */
    source: string;

    /** 优先级 (可选) */
    priority?: MessagePriority;

    /** 超时时间 (可选) */
    timeout?: number;

    /** 元数据 (可选) */
    metadata?: Record<string, string>;
}

/**
 * Native Messaging 响应接口
 */
export interface NativeResponse {
    /** 对应的请求 ID */
    requestId: string;

    /** 响应状态 */
    status: 'success' | 'error' | 'timeout';

    /** 响应数据 */
    data?: any;

    /** 错误信息 (如果有) */
    error?: string;

    /** 响应时间戳 */
    timestamp: number;

    /** 处理耗时 (毫秒) */
    processingTime: number;

    /** 元数据 (可选) */
    metadata?: Record<string, string>;
}

/**
 * 连接状态枚举
 */
export enum ConnectionStatus {
    Disconnected = 'disconnected',
    Connecting = 'connecting',
    Connected = 'connected',
    Reconnecting = 'reconnecting',
    Error = 'error'
}

/**
 * 消息优先级枚举
 */
export enum MessagePriority {
    Low = 1,
    Normal = 2,
    High = 3,
    Critical = 4
}
```
