//! 消息安全验证模块
//!
//! 提供消息签名验证、完整性检查、重放攻击防护和内容过滤功能

pub mod signature_verifier;
pub mod integrity_checker;
pub mod replay_protection;
pub mod content_filter;

// 重新导出主要类型
pub use signature_verifier::{SignatureVerifier, SignatureVerificationResult, SignatureAlgorithm};
pub use integrity_checker::{IntegrityChecker, IntegrityCheckResult, IntegrityAlgorithm};
pub use replay_protection::{ReplayProtection, ReplayProtectionResult, NonceManager};
pub use content_filter::{ContentFilter, ContentFilterResult, FilterRule};

use crate::native_messaging::error::{NativeMessagingError, Result};
use crate::native_messaging::protocol::message::NativeMessage;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{Duration, SystemTime};
use tracing::{debug, info, warn, error};

/// 消息安全管理器
#[derive(Debug)]
pub struct MessageSecurityManager {
    signature_verifier: SignatureVerifier,
    integrity_checker: IntegrityChecker,
    replay_protection: ReplayProtection,
    content_filter: ContentFilter,
    config: MessageSecurityConfig,
}

impl MessageSecurityManager {
    /// 创建新的消息安全管理器
    pub fn new(config: MessageSecurityConfig) -> Result<Self> {
        let signature_verifier = SignatureVerifier::new(&config.signature_config)?;
        let integrity_checker = IntegrityChecker::new(&config.integrity_config)?;
        let replay_protection = ReplayProtection::new(&config.replay_config)?;
        let content_filter = ContentFilter::new(&config.filter_config)?;

        Ok(Self {
            signature_verifier,
            integrity_checker,
            replay_protection,
            content_filter,
            config,
        })
    }

    /// 验证消息安全性
    pub async fn verify_message_security(&self, message: &NativeMessage) -> Result<MessageSecurityResult> {
        debug!("开始消息安全验证: {}", message.request_id);
        
        let mut result = MessageSecurityResult {
            message_id: message.request_id.clone(),
            is_secure: false,
            signature_result: None,
            integrity_result: None,
            replay_result: None,
            content_result: None,
            security_score: 0.0,
            verified_at: SystemTime::now(),
            errors: Vec::new(),
            warnings: Vec::new(),
        };

        // 1. 签名验证
        if self.config.enable_signature_verification {
            match self.signature_verifier.verify_signature(message).await {
                Ok(sig_result) => {
                    result.signature_result = Some(sig_result.clone());
                    if !sig_result.is_valid {
                        result.errors.push("消息签名验证失败".to_string());
                    }
                }
                Err(e) => {
                    result.errors.push(format!("签名验证错误: {}", e));
                }
            }
        }

        // 2. 完整性检查
        if self.config.enable_integrity_check {
            match self.integrity_checker.check_integrity(message).await {
                Ok(int_result) => {
                    result.integrity_result = Some(int_result.clone());
                    if !int_result.is_valid {
                        result.errors.push("消息完整性检查失败".to_string());
                    }
                }
                Err(e) => {
                    result.errors.push(format!("完整性检查错误: {}", e));
                }
            }
        }

        // 3. 重放攻击防护
        if self.config.enable_replay_protection {
            match self.replay_protection.check_replay(message).await {
                Ok(rep_result) => {
                    result.replay_result = Some(rep_result.clone());
                    if !rep_result.is_valid {
                        result.errors.push("检测到重放攻击".to_string());
                    }
                }
                Err(e) => {
                    result.errors.push(format!("重放检查错误: {}", e));
                }
            }
        }

        // 4. 内容过滤
        if self.config.enable_content_filtering {
            match self.content_filter.filter_content(message).await {
                Ok(cont_result) => {
                    result.content_result = Some(cont_result.clone());
                    if !cont_result.is_clean {
                        result.warnings.push("消息内容包含可疑内容".to_string());
                    }
                }
                Err(e) => {
                    result.errors.push(format!("内容过滤错误: {}", e));
                }
            }
        }

        // 计算安全分数
        result.security_score = self.calculate_security_score(&result);
        result.is_secure = result.errors.is_empty() && result.security_score >= self.config.min_security_score;

        if result.is_secure {
            info!("消息安全验证通过: {} (分数: {:.2})", message.request_id, result.security_score);
        } else {
            warn!("消息安全验证失败: {} (分数: {:.2})", message.request_id, result.security_score);
        }

        Ok(result)
    }

    /// 计算安全分数
    fn calculate_security_score(&self, result: &MessageSecurityResult) -> f64 {
        let mut score = 100.0;
        let mut total_checks = 0;
        let mut passed_checks = 0;

        // 签名验证权重
        if let Some(ref sig_result) = result.signature_result {
            total_checks += 1;
            if sig_result.is_valid {
                passed_checks += 1;
            } else {
                score -= 30.0; // 签名失败扣30分
            }
        }

        // 完整性检查权重
        if let Some(ref int_result) = result.integrity_result {
            total_checks += 1;
            if int_result.is_valid {
                passed_checks += 1;
            } else {
                score -= 25.0; // 完整性失败扣25分
            }
        }

        // 重放防护权重
        if let Some(ref rep_result) = result.replay_result {
            total_checks += 1;
            if rep_result.is_valid {
                passed_checks += 1;
            } else {
                score -= 20.0; // 重放攻击扣20分
            }
        }

        // 内容过滤权重
        if let Some(ref cont_result) = result.content_result {
            total_checks += 1;
            if cont_result.is_clean {
                passed_checks += 1;
            } else {
                score -= 15.0; // 内容问题扣15分
            }
        }

        // 错误和警告扣分
        score -= result.errors.len() as f64 * 10.0;
        score -= result.warnings.len() as f64 * 5.0;

        score.max(0.0).min(100.0)
    }
}

/// 消息安全配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageSecurityConfig {
    /// 启用签名验证
    pub enable_signature_verification: bool,
    /// 启用完整性检查
    pub enable_integrity_check: bool,
    /// 启用重放攻击防护
    pub enable_replay_protection: bool,
    /// 启用内容过滤
    pub enable_content_filtering: bool,
    /// 最小安全分数
    pub min_security_score: f64,
    /// 签名验证配置
    pub signature_config: SignatureVerifierConfig,
    /// 完整性检查配置
    pub integrity_config: IntegrityCheckerConfig,
    /// 重放防护配置
    pub replay_config: ReplayProtectionConfig,
    /// 内容过滤配置
    pub filter_config: ContentFilterConfig,
}

impl Default for MessageSecurityConfig {
    fn default() -> Self {
        Self {
            enable_signature_verification: true,
            enable_integrity_check: true,
            enable_replay_protection: true,
            enable_content_filtering: true,
            min_security_score: 80.0,
            signature_config: SignatureVerifierConfig::default(),
            integrity_config: IntegrityCheckerConfig::default(),
            replay_config: ReplayProtectionConfig::default(),
            filter_config: ContentFilterConfig::default(),
        }
    }
}

/// 消息安全验证结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageSecurityResult {
    /// 消息ID
    pub message_id: String,
    /// 是否安全
    pub is_secure: bool,
    /// 签名验证结果
    pub signature_result: Option<SignatureVerificationResult>,
    /// 完整性检查结果
    pub integrity_result: Option<IntegrityCheckResult>,
    /// 重放防护结果
    pub replay_result: Option<ReplayProtectionResult>,
    /// 内容过滤结果
    pub content_result: Option<ContentFilterResult>,
    /// 安全分数
    pub security_score: f64,
    /// 验证时间
    pub verified_at: SystemTime,
    /// 错误信息
    pub errors: Vec<String>,
    /// 警告信息
    pub warnings: Vec<String>,
}

impl MessageSecurityResult {
    /// 获取验证摘要
    pub fn summary(&self) -> String {
        format!(
            "Message {} security: {} (Score: {:.2}, Errors: {}, Warnings: {})",
            self.message_id,
            if self.is_secure { "SECURE" } else { "INSECURE" },
            self.security_score,
            self.errors.len(),
            self.warnings.len()
        )
    }
}

// 占位符配置类型，将在各自的文件中实现
pub use signature_verifier::SignatureVerifierConfig;
pub use integrity_checker::IntegrityCheckerConfig;
pub use replay_protection::ReplayProtectionConfig;
pub use content_filter::ContentFilterConfig;

/// 消息安全错误
#[derive(Debug, thiserror::Error)]
pub enum MessageSecurityError {
    #[error("签名验证失败: {0}")]
    SignatureVerificationFailed(String),
    
    #[error("完整性检查失败: {0}")]
    IntegrityCheckFailed(String),
    
    #[error("重放攻击检测失败: {0}")]
    ReplayProtectionFailed(String),
    
    #[error("内容过滤失败: {0}")]
    ContentFilterFailed(String),
    
    #[error("配置错误: {0}")]
    ConfigError(String),
    
    #[error("IO错误: {0}")]
    IoError(#[from] std::io::Error),
    
    #[error("序列化错误: {0}")]
    SerializationError(#[from] serde_json::Error),
}
