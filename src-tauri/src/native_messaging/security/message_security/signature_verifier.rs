//! 消息签名验证器模块
//!
//! 负责验证消息的数字签名，确保消息来源的真实性和不可否认性

use super::MessageSecurityError;
use crate::native_messaging::error::{NativeMessagingError, Result};
use crate::native_messaging::protocol::message::NativeMessage;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::SystemTime;
use sha2::{Sha256, Digest};
use hmac::{Hmac, Mac};
use tracing::{debug, warn, error};

type HmacSha256 = Hmac<Sha256>;

/// 签名验证器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SignatureVerifierConfig {
    /// 签名算法
    pub algorithm: SignatureAlgorithm,
    /// 启用时间戳验证
    pub enable_timestamp_validation: bool,
    /// 签名有效期（秒）
    pub signature_validity_seconds: u64,
    /// 密钥配置
    pub key_config: SignatureKeyConfig,
    /// 验证策略
    pub verification_policy: VerificationPolicy,
}

impl Default for SignatureVerifierConfig {
    fn default() -> Self {
        Self {
            algorithm: SignatureAlgorithm::HmacSha256,
            enable_timestamp_validation: true,
            signature_validity_seconds: 300, // 5分钟
            key_config: SignatureKeyConfig::default(),
            verification_policy: VerificationPolicy::Strict,
        }
    }
}

/// 签名算法
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum SignatureAlgorithm {
    /// HMAC-SHA256
    HmacSha256,
    /// HMAC-SHA512
    HmacSha512,
    /// RSA-SHA256
    RsaSha256,
    /// ECDSA-SHA256
    EcdsaSha256,
}

/// 签名密钥配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SignatureKeyConfig {
    /// 共享密钥（用于HMAC）
    pub shared_secret: Option<String>,
    /// 公钥（用于RSA/ECDSA验证）
    pub public_key: Option<String>,
    /// 密钥轮换间隔（秒）
    pub key_rotation_interval: u64,
    /// 密钥存储路径
    pub key_store_path: Option<String>,
}

impl Default for SignatureKeyConfig {
    fn default() -> Self {
        Self {
            shared_secret: Some("default-secret-key-change-in-production".to_string()),
            public_key: None,
            key_rotation_interval: 86400, // 24小时
            key_store_path: None,
        }
    }
}

/// 验证策略
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum VerificationPolicy {
    /// 严格模式 - 所有检查必须通过
    Strict,
    /// 宽松模式 - 允许部分检查失败
    Lenient,
    /// 仅警告模式 - 记录问题但不阻止
    WarnOnly,
}

/// 签名验证器
#[derive(Debug)]
pub struct SignatureVerifier {
    config: SignatureVerifierConfig,
    verification_cache: HashMap<String, (SignatureVerificationResult, SystemTime)>,
}

impl SignatureVerifier {
    /// 创建新的签名验证器
    pub fn new(config: &SignatureVerifierConfig) -> Result<Self> {
        // 验证配置
        if config.key_config.shared_secret.is_none() && config.key_config.public_key.is_none() {
            return Err(NativeMessagingError::SecurityError(
                "签名验证需要配置密钥".to_string()
            ));
        }

        Ok(Self {
            config: config.clone(),
            verification_cache: HashMap::new(),
        })
    }

    /// 验证消息签名
    pub async fn verify_signature(&self, message: &NativeMessage) -> Result<SignatureVerificationResult> {
        debug!("验证消息签名: {}", message.request_id);

        let mut result = SignatureVerificationResult {
            message_id: message.request_id.clone(),
            is_valid: false,
            algorithm: self.config.algorithm.clone(),
            verification_details: Vec::new(),
            verified_at: SystemTime::now(),
        };

        // 1. 检查消息是否包含签名
        let signature = match self.extract_signature(message) {
            Some(sig) => sig,
            None => {
                result.verification_details.push(SignatureVerificationDetail {
                    check_type: SignatureCheckType::SignaturePresence,
                    is_passed: false,
                    message: "消息中未找到签名".to_string(),
                });
                return Ok(result);
            }
        };

        // 2. 验证签名格式
        result.verification_details.push(self.verify_signature_format(&signature)?);

        // 3. 计算消息哈希
        let message_hash = self.calculate_message_hash(message)?;
        result.verification_details.push(SignatureVerificationDetail {
            check_type: SignatureCheckType::MessageHash,
            is_passed: true,
            message: format!("消息哈希计算完成: {}", hex::encode(&message_hash[..8])),
        });

        // 4. 验证签名
        let signature_valid = self.verify_signature_value(&message_hash, &signature)?;
        result.verification_details.push(SignatureVerificationDetail {
            check_type: SignatureCheckType::SignatureVerification,
            is_passed: signature_valid,
            message: if signature_valid {
                "签名验证成功".to_string()
            } else {
                "签名验证失败".to_string()
            },
        });

        // 5. 时间戳验证
        if self.config.enable_timestamp_validation {
            result.verification_details.push(self.verify_timestamp(message)?);
        }

        // 计算最终结果
        result.is_valid = match self.config.verification_policy {
            VerificationPolicy::Strict => {
                result.verification_details.iter().all(|detail| detail.is_passed)
            }
            VerificationPolicy::Lenient => {
                // 至少签名验证必须通过
                result.verification_details.iter()
                    .find(|d| d.check_type == SignatureCheckType::SignatureVerification)
                    .map(|d| d.is_passed)
                    .unwrap_or(false)
            }
            VerificationPolicy::WarnOnly => true,
        };

        if result.is_valid {
            debug!("消息签名验证成功: {}", message.request_id);
        } else {
            warn!("消息签名验证失败: {} - 失败的检查: {:?}", 
                message.request_id,
                result.verification_details.iter()
                    .filter(|d| !d.is_passed)
                    .map(|d| &d.check_type)
                    .collect::<Vec<_>>()
            );
        }

        Ok(result)
    }

    /// 从消息中提取签名
    fn extract_signature(&self, message: &NativeMessage) -> Option<String> {
        // 检查元数据中的签名
        if let Some(signature) = message.metadata.get("signature") {
            return Some(signature.clone());
        }

        // 检查扩展数据中的签名
        if let Some(signature_value) = message.extensions.get("signature") {
            if let Some(signature_str) = signature_value.as_str() {
                return Some(signature_str.to_string());
            }
        }

        None
    }

    /// 验证签名格式
    fn verify_signature_format(&self, signature: &str) -> Result<SignatureVerificationDetail> {
        let is_valid = match self.config.algorithm {
            SignatureAlgorithm::HmacSha256 => {
                // HMAC-SHA256 应该是64个十六进制字符
                signature.len() == 64 && signature.chars().all(|c| c.is_ascii_hexdigit())
            }
            SignatureAlgorithm::HmacSha512 => {
                // HMAC-SHA512 应该是128个十六进制字符
                signature.len() == 128 && signature.chars().all(|c| c.is_ascii_hexdigit())
            }
            SignatureAlgorithm::RsaSha256 | SignatureAlgorithm::EcdsaSha256 => {
                // 对于RSA/ECDSA，检查是否为有效的base64编码
                base64::decode(signature).is_ok()
            }
        };

        Ok(SignatureVerificationDetail {
            check_type: SignatureCheckType::SignatureFormat,
            is_passed: is_valid,
            message: if is_valid {
                "签名格式验证通过".to_string()
            } else {
                format!("签名格式无效，期望 {:?} 格式", self.config.algorithm)
            },
        })
    }

    /// 计算消息哈希
    fn calculate_message_hash(&self, message: &NativeMessage) -> Result<Vec<u8>> {
        // 构建用于签名的消息内容
        let signable_content = format!(
            "{}|{}|{}|{}|{}",
            message.version,
            message.request_id,
            message.source,
            message.timestamp,
            serde_json::to_string(&message.payload)?
        );

        let mut hasher = Sha256::new();
        hasher.update(signable_content.as_bytes());
        Ok(hasher.finalize().to_vec())
    }

    /// 验证签名值
    fn verify_signature_value(&self, message_hash: &[u8], signature: &str) -> Result<bool> {
        match self.config.algorithm {
            SignatureAlgorithm::HmacSha256 => {
                self.verify_hmac_signature(message_hash, signature)
            }
            SignatureAlgorithm::HmacSha512 => {
                // 类似HMAC-SHA256的实现
                self.verify_hmac_signature(message_hash, signature)
            }
            SignatureAlgorithm::RsaSha256 | SignatureAlgorithm::EcdsaSha256 => {
                // 对于RSA/ECDSA，需要实现公钥验证
                // 这里提供简化实现
                warn!("RSA/ECDSA签名验证尚未完全实现");
                Ok(true) // 临时返回true
            }
        }
    }

    /// 验证HMAC签名
    fn verify_hmac_signature(&self, message_hash: &[u8], signature: &str) -> Result<bool> {
        let secret = self.config.key_config.shared_secret.as_ref()
            .ok_or_else(|| NativeMessagingError::SecurityError(
                "HMAC验证需要共享密钥".to_string()
            ))?;

        let mut mac = HmacSha256::new_from_slice(secret.as_bytes())
            .map_err(|e| NativeMessagingError::SecurityError(
                format!("HMAC密钥无效: {}", e)
            ))?;

        mac.update(message_hash);
        let expected_signature = mac.finalize().into_bytes();
        let expected_hex = hex::encode(expected_signature);

        Ok(expected_hex.eq_ignore_ascii_case(signature))
    }

    /// 验证时间戳
    fn verify_timestamp(&self, message: &NativeMessage) -> Result<SignatureVerificationDetail> {
        let current_time = SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();

        let message_age = current_time.saturating_sub(message.timestamp);
        let is_valid = message_age <= self.config.signature_validity_seconds;

        Ok(SignatureVerificationDetail {
            check_type: SignatureCheckType::TimestampValidation,
            is_passed: is_valid,
            message: if is_valid {
                format!("时间戳验证通过，消息年龄: {}秒", message_age)
            } else {
                format!("时间戳过期，消息年龄: {}秒 (最大允许: {}秒)", 
                    message_age, self.config.signature_validity_seconds)
            },
        })
    }
}

/// 签名验证结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SignatureVerificationResult {
    /// 消息ID
    pub message_id: String,
    /// 是否有效
    pub is_valid: bool,
    /// 使用的算法
    pub algorithm: SignatureAlgorithm,
    /// 验证详情
    pub verification_details: Vec<SignatureVerificationDetail>,
    /// 验证时间
    pub verified_at: SystemTime,
}

/// 签名验证详情
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SignatureVerificationDetail {
    /// 检查类型
    pub check_type: SignatureCheckType,
    /// 是否通过
    pub is_passed: bool,
    /// 详细消息
    pub message: String,
}

/// 签名检查类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum SignatureCheckType {
    /// 签名存在性检查
    SignaturePresence,
    /// 签名格式检查
    SignatureFormat,
    /// 消息哈希计算
    MessageHash,
    /// 签名验证
    SignatureVerification,
    /// 时间戳验证
    TimestampValidation,
}
