//! 重放攻击防护模块
//!
//! 负责检测和防止重放攻击，确保消息的唯一性和时效性

use super::MessageSecurityError;
use crate::native_messaging::error::{NativeMessagingError, Result};
use crate::native_messaging::protocol::message::NativeMessage;
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use uuid::Uuid;
use tracing::{debug, warn, error};

/// 重放防护配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReplayProtectionConfig {
    /// 启用时间窗口检查
    pub enable_time_window_check: bool,
    /// 时间窗口大小（秒）
    pub time_window_seconds: u64,
    /// 启用nonce检查
    pub enable_nonce_check: bool,
    /// nonce缓存大小
    pub nonce_cache_size: usize,
    /// 启用序列号检查
    pub enable_sequence_check: bool,
    /// 序列号容忍度
    pub sequence_tolerance: u64,
    /// 缓存清理间隔（秒）
    pub cache_cleanup_interval: u64,
}

impl Default for ReplayProtectionConfig {
    fn default() -> Self {
        Self {
            enable_time_window_check: true,
            time_window_seconds: 300, // 5分钟
            enable_nonce_check: true,
            nonce_cache_size: 10000,
            enable_sequence_check: false, // 默认关闭，因为需要状态管理
            sequence_tolerance: 10,
            cache_cleanup_interval: 3600, // 1小时
        }
    }
}

/// 重放攻击防护器
#[derive(Debug)]
pub struct ReplayProtection {
    config: ReplayProtectionConfig,
    nonce_manager: NonceManager,
    sequence_tracker: SequenceTracker,
    last_cleanup: SystemTime,
}

impl ReplayProtection {
    /// 创建新的重放攻击防护器
    pub fn new(config: &ReplayProtectionConfig) -> Result<Self> {
        let nonce_manager = NonceManager::new(config.nonce_cache_size);
        let sequence_tracker = SequenceTracker::new(config.sequence_tolerance);

        Ok(Self {
            config: config.clone(),
            nonce_manager,
            sequence_tracker,
            last_cleanup: SystemTime::now(),
        })
    }

    /// 检查重放攻击
    pub async fn check_replay(&mut self, message: &NativeMessage) -> Result<ReplayProtectionResult> {
        debug!("检查重放攻击: {}", message.request_id);

        let mut result = ReplayProtectionResult {
            message_id: message.request_id.clone(),
            is_valid: false,
            protection_details: Vec::new(),
            checked_at: SystemTime::now(),
        };

        // 定期清理缓存
        self.cleanup_if_needed().await?;

        // 1. 时间窗口检查
        if self.config.enable_time_window_check {
            result.protection_details.push(self.check_time_window(message)?);
        }

        // 2. Nonce检查
        if self.config.enable_nonce_check {
            result.protection_details.push(self.check_nonce(message).await?);
        }

        // 3. 序列号检查
        if self.config.enable_sequence_check {
            result.protection_details.push(self.check_sequence(message).await?);
        }

        // 4. 请求ID唯一性检查
        result.protection_details.push(self.check_request_id_uniqueness(message).await?);

        // 计算最终结果
        result.is_valid = result.protection_details.iter().all(|detail| detail.is_passed);

        if result.is_valid {
            debug!("重放攻击检查通过: {}", message.request_id);
            // 记录消息以防止未来的重放
            self.record_message(message).await?;
        } else {
            warn!("检测到重放攻击: {} - 失败的检查: {:?}", 
                message.request_id,
                result.protection_details.iter()
                    .filter(|d| !d.is_passed)
                    .map(|d| &d.check_type)
                    .collect::<Vec<_>>()
            );
        }

        Ok(result)
    }

    /// 检查时间窗口
    fn check_time_window(&self, message: &NativeMessage) -> Result<ReplayProtectionDetail> {
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();

        let message_age = current_time.saturating_sub(message.timestamp);
        let is_valid = message_age <= self.config.time_window_seconds;

        Ok(ReplayProtectionDetail {
            check_type: ReplayCheckType::TimeWindow,
            is_passed: is_valid,
            message: if is_valid {
                format!("时间窗口检查通过，消息年龄: {}秒", message_age)
            } else {
                format!("消息超出时间窗口: {}秒 > {}秒", 
                    message_age, self.config.time_window_seconds)
            },
        })
    }

    /// 检查nonce
    async fn check_nonce(&mut self, message: &NativeMessage) -> Result<ReplayProtectionDetail> {
        let nonce = self.extract_nonce(message);
        
        if let Some(nonce_value) = nonce {
            let is_unique = self.nonce_manager.check_and_record_nonce(&nonce_value).await;
            
            Ok(ReplayProtectionDetail {
                check_type: ReplayCheckType::Nonce,
                is_passed: is_unique,
                message: if is_unique {
                    "Nonce检查通过".to_string()
                } else {
                    format!("检测到重复的nonce: {}", nonce_value)
                },
            })
        } else {
            // 如果没有nonce，生成警告但不阻止
            Ok(ReplayProtectionDetail {
                check_type: ReplayCheckType::Nonce,
                is_passed: true,
                message: "消息中未找到nonce，跳过检查".to_string(),
            })
        }
    }

    /// 检查序列号
    async fn check_sequence(&mut self, message: &NativeMessage) -> Result<ReplayProtectionDetail> {
        let sequence = self.extract_sequence_number(message);
        
        if let Some(seq_num) = sequence {
            let is_valid = self.sequence_tracker.check_sequence(&message.source, seq_num).await;
            
            Ok(ReplayProtectionDetail {
                check_type: ReplayCheckType::Sequence,
                is_passed: is_valid,
                message: if is_valid {
                    format!("序列号检查通过: {}", seq_num)
                } else {
                    format!("序列号异常: {}", seq_num)
                },
            })
        } else {
            Ok(ReplayProtectionDetail {
                check_type: ReplayCheckType::Sequence,
                is_passed: true,
                message: "消息中未找到序列号，跳过检查".to_string(),
            })
        }
    }

    /// 检查请求ID唯一性
    async fn check_request_id_uniqueness(&mut self, message: &NativeMessage) -> Result<ReplayProtectionDetail> {
        let is_unique = self.nonce_manager.check_and_record_nonce(&message.request_id).await;
        
        Ok(ReplayProtectionDetail {
            check_type: ReplayCheckType::RequestIdUniqueness,
            is_passed: is_unique,
            message: if is_unique {
                "请求ID唯一性检查通过".to_string()
            } else {
                format!("检测到重复的请求ID: {}", message.request_id)
            },
        })
    }

    /// 从消息中提取nonce
    fn extract_nonce(&self, message: &NativeMessage) -> Option<String> {
        // 检查元数据中的nonce
        if let Some(nonce) = message.metadata.get("nonce") {
            return Some(nonce.clone());
        }

        // 检查扩展数据中的nonce
        if let Some(nonce_value) = message.extensions.get("nonce") {
            if let Some(nonce_str) = nonce_value.as_str() {
                return Some(nonce_str.to_string());
            }
        }

        None
    }

    /// 从消息中提取序列号
    fn extract_sequence_number(&self, message: &NativeMessage) -> Option<u64> {
        // 检查元数据中的序列号
        if let Some(seq_str) = message.metadata.get("sequence") {
            if let Ok(seq_num) = seq_str.parse::<u64>() {
                return Some(seq_num);
            }
        }

        // 检查扩展数据中的序列号
        if let Some(seq_value) = message.extensions.get("sequence") {
            if let Some(seq_num) = seq_value.as_u64() {
                return Some(seq_num);
            }
        }

        None
    }

    /// 记录消息以防止重放
    async fn record_message(&mut self, message: &NativeMessage) -> Result<()> {
        // 记录请求ID
        self.nonce_manager.record_nonce(&message.request_id).await;

        // 如果有nonce，也记录nonce
        if let Some(nonce) = self.extract_nonce(message) {
            self.nonce_manager.record_nonce(&nonce).await;
        }

        // 如果有序列号，更新序列跟踪器
        if let Some(seq_num) = self.extract_sequence_number(message) {
            self.sequence_tracker.update_sequence(&message.source, seq_num).await;
        }

        Ok(())
    }

    /// 定期清理缓存
    async fn cleanup_if_needed(&mut self) -> Result<()> {
        let now = SystemTime::now();
        let elapsed = now.duration_since(self.last_cleanup).unwrap_or(Duration::ZERO);
        
        if elapsed.as_secs() >= self.config.cache_cleanup_interval {
            self.nonce_manager.cleanup_expired().await;
            self.sequence_tracker.cleanup_expired().await;
            self.last_cleanup = now;
            debug!("重放防护缓存清理完成");
        }

        Ok(())
    }
}

/// Nonce管理器
#[derive(Debug)]
pub struct NonceManager {
    used_nonces: HashMap<String, SystemTime>,
    max_cache_size: usize,
}

impl NonceManager {
    /// 创建新的nonce管理器
    pub fn new(max_cache_size: usize) -> Self {
        Self {
            used_nonces: HashMap::new(),
            max_cache_size,
        }
    }

    /// 检查并记录nonce
    pub async fn check_and_record_nonce(&mut self, nonce: &str) -> bool {
        if self.used_nonces.contains_key(nonce) {
            false // nonce已被使用
        } else {
            self.record_nonce(nonce).await;
            true
        }
    }

    /// 记录nonce
    pub async fn record_nonce(&mut self, nonce: &str) {
        // 如果缓存已满，清理最旧的条目
        if self.used_nonces.len() >= self.max_cache_size {
            self.cleanup_oldest().await;
        }

        self.used_nonces.insert(nonce.to_string(), SystemTime::now());
    }

    /// 清理过期的nonce
    pub async fn cleanup_expired(&mut self) {
        let cutoff_time = SystemTime::now() - Duration::from_secs(3600); // 1小时前
        self.used_nonces.retain(|_, &mut timestamp| timestamp > cutoff_time);
    }

    /// 清理最旧的条目
    async fn cleanup_oldest(&mut self) {
        if let Some((oldest_nonce, _)) = self.used_nonces.iter()
            .min_by_key(|(_, &timestamp)| timestamp)
            .map(|(k, v)| (k.clone(), *v)) {
            self.used_nonces.remove(&oldest_nonce);
        }
    }
}

/// 序列跟踪器
#[derive(Debug)]
pub struct SequenceTracker {
    source_sequences: HashMap<String, u64>,
    tolerance: u64,
}

impl SequenceTracker {
    /// 创建新的序列跟踪器
    pub fn new(tolerance: u64) -> Self {
        Self {
            source_sequences: HashMap::new(),
            tolerance,
        }
    }

    /// 检查序列号
    pub async fn check_sequence(&self, source: &str, sequence: u64) -> bool {
        if let Some(&last_sequence) = self.source_sequences.get(source) {
            // 检查序列号是否在容忍范围内
            sequence > last_sequence && sequence <= last_sequence + self.tolerance
        } else {
            // 第一次见到这个来源，接受任何序列号
            true
        }
    }

    /// 更新序列号
    pub async fn update_sequence(&mut self, source: &str, sequence: u64) {
        self.source_sequences.insert(source.to_string(), sequence);
    }

    /// 清理过期的序列跟踪
    pub async fn cleanup_expired(&mut self) {
        // 简化实现：保留最近的1000个来源
        if self.source_sequences.len() > 1000 {
            let sources_to_remove: Vec<String> = self.source_sequences.keys()
                .take(self.source_sequences.len() - 1000)
                .cloned()
                .collect();
            
            for source in sources_to_remove {
                self.source_sequences.remove(&source);
            }
        }
    }
}

/// 重放防护结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReplayProtectionResult {
    /// 消息ID
    pub message_id: String,
    /// 是否有效
    pub is_valid: bool,
    /// 防护检查详情
    pub protection_details: Vec<ReplayProtectionDetail>,
    /// 检查时间
    pub checked_at: SystemTime,
}

/// 重放防护详情
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReplayProtectionDetail {
    /// 检查类型
    pub check_type: ReplayCheckType,
    /// 是否通过
    pub is_passed: bool,
    /// 详细消息
    pub message: String,
}

/// 重放检查类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ReplayCheckType {
    /// 时间窗口检查
    TimeWindow,
    /// Nonce检查
    Nonce,
    /// 序列号检查
    Sequence,
    /// 请求ID唯一性检查
    RequestIdUniqueness,
}
