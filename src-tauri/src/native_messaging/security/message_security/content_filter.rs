//! 内容过滤器模块
//!
//! 负责过滤消息中的恶意内容、敏感信息和不当内容

use super::MessageSecurityError;
use crate::native_messaging::error::{NativeMessagingError, Result};
use crate::native_messaging::protocol::message::NativeMessage;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::SystemTime;
use regex::Regex;
use tracing::{debug, warn, error};

/// 内容过滤器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContentFilterConfig {
    /// 启用恶意内容检测
    pub enable_malicious_content_detection: bool,
    /// 启用敏感信息检测
    pub enable_sensitive_data_detection: bool,
    /// 启用不当内容检测
    pub enable_inappropriate_content_detection: bool,
    /// 启用自定义规则
    pub enable_custom_rules: bool,
    /// 过滤严格程度
    pub filter_strictness: FilterStrictness,
    /// 自定义过滤规则
    pub custom_rules: Vec<FilterRule>,
    /// 白名单模式
    pub whitelist_mode: bool,
    /// 白名单内容
    pub whitelist_patterns: Vec<String>,
}

impl Default for ContentFilterConfig {
    fn default() -> Self {
        Self {
            enable_malicious_content_detection: true,
            enable_sensitive_data_detection: true,
            enable_inappropriate_content_detection: false,
            enable_custom_rules: true,
            filter_strictness: FilterStrictness::Medium,
            custom_rules: Vec::new(),
            whitelist_mode: false,
            whitelist_patterns: Vec::new(),
        }
    }
}

/// 过滤严格程度
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum FilterStrictness {
    /// 宽松模式
    Low,
    /// 中等模式
    Medium,
    /// 严格模式
    High,
    /// 极严格模式
    Extreme,
}

/// 过滤规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FilterRule {
    /// 规则名称
    pub name: String,
    /// 规则描述
    pub description: String,
    /// 匹配模式（正则表达式）
    pub pattern: String,
    /// 规则类型
    pub rule_type: FilterRuleType,
    /// 严重程度
    pub severity: FilterSeverity,
    /// 是否启用
    pub enabled: bool,
    /// 动作
    pub action: FilterAction,
}

/// 过滤规则类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum FilterRuleType {
    /// 恶意内容
    MaliciousContent,
    /// 敏感数据
    SensitiveData,
    /// 不当内容
    InappropriateContent,
    /// 自定义规则
    Custom,
}

/// 过滤严重程度
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum FilterSeverity {
    /// 信息
    Info,
    /// 警告
    Warning,
    /// 错误
    Error,
    /// 严重
    Critical,
}

/// 过滤动作
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum FilterAction {
    /// 仅记录
    LogOnly,
    /// 警告
    Warn,
    /// 阻止
    Block,
    /// 净化
    Sanitize,
}

/// 内容过滤器
#[derive(Debug)]
pub struct ContentFilter {
    config: ContentFilterConfig,
    compiled_rules: Vec<CompiledFilterRule>,
    malicious_patterns: Vec<Regex>,
    sensitive_patterns: Vec<Regex>,
}

impl ContentFilter {
    /// 创建新的内容过滤器
    pub fn new(config: &ContentFilterConfig) -> Result<Self> {
        let mut filter = Self {
            config: config.clone(),
            compiled_rules: Vec::new(),
            malicious_patterns: Vec::new(),
            sensitive_patterns: Vec::new(),
        };

        // 编译自定义规则
        filter.compile_custom_rules()?;
        
        // 初始化内置模式
        filter.initialize_builtin_patterns()?;

        Ok(filter)
    }

    /// 过滤内容
    pub async fn filter_content(&self, message: &NativeMessage) -> Result<ContentFilterResult> {
        debug!("过滤消息内容: {}", message.request_id);

        let mut result = ContentFilterResult {
            message_id: message.request_id.clone(),
            is_clean: true,
            filter_details: Vec::new(),
            detected_issues: Vec::new(),
            filtered_at: SystemTime::now(),
        };

        // 1. 白名单检查
        if self.config.whitelist_mode {
            result.filter_details.push(self.check_whitelist(message)?);
        }

        // 2. 恶意内容检测
        if self.config.enable_malicious_content_detection {
            result.filter_details.extend(self.detect_malicious_content(message)?);
        }

        // 3. 敏感信息检测
        if self.config.enable_sensitive_data_detection {
            result.filter_details.extend(self.detect_sensitive_data(message)?);
        }

        // 4. 不当内容检测
        if self.config.enable_inappropriate_content_detection {
            result.filter_details.extend(self.detect_inappropriate_content(message)?);
        }

        // 5. 自定义规则检查
        if self.config.enable_custom_rules {
            result.filter_details.extend(self.apply_custom_rules(message)?);
        }

        // 收集检测到的问题
        result.detected_issues = result.filter_details.iter()
            .filter(|detail| !detail.is_passed)
            .map(|detail| ContentIssue {
                issue_type: detail.filter_type.clone(),
                severity: detail.severity.clone(),
                description: detail.message.clone(),
                location: detail.location.clone(),
            })
            .collect();

        // 计算最终结果
        result.is_clean = self.calculate_cleanliness(&result);

        if result.is_clean {
            debug!("内容过滤通过: {}", message.request_id);
        } else {
            warn!("内容过滤检测到问题: {} - 问题数量: {}", 
                message.request_id, result.detected_issues.len());
        }

        Ok(result)
    }

    /// 编译自定义规则
    fn compile_custom_rules(&mut self) -> Result<()> {
        for rule in &self.config.custom_rules {
            if rule.enabled {
                match Regex::new(&rule.pattern) {
                    Ok(regex) => {
                        self.compiled_rules.push(CompiledFilterRule {
                            rule: rule.clone(),
                            regex,
                        });
                    }
                    Err(e) => {
                        warn!("无法编译过滤规则 '{}': {}", rule.name, e);
                    }
                }
            }
        }
        Ok(())
    }

    /// 初始化内置模式
    fn initialize_builtin_patterns(&mut self) -> Result<()> {
        // 恶意内容模式
        let malicious_patterns = vec![
            r"(?i)<script[^>]*>.*?</script>", // XSS脚本
            r"(?i)javascript\s*:", // JavaScript协议
            r"(?i)(union\s+select|select\s+.*\s+from)", // SQL注入
            r"(?i)(exec\s*\(|eval\s*\()", // 代码执行
            r"(?i)(cmd|powershell|bash|sh)\s+", // 命令执行
        ];

        for pattern in malicious_patterns {
            if let Ok(regex) = Regex::new(pattern) {
                self.malicious_patterns.push(regex);
            }
        }

        // 敏感信息模式
        let sensitive_patterns = vec![
            r"(?i)(password|passwd|pwd)\s*[:=]\s*\S+", // 密码
            r"(?i)(api[_-]?key|secret|token)\s*[:=]\s*\S+", // API密钥
            r"\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b", // 信用卡号
            r"\b\d{3}-\d{2}-\d{4}\b", // SSN
            r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b", // 邮箱
        ];

        for pattern in sensitive_patterns {
            if let Ok(regex) = Regex::new(pattern) {
                self.sensitive_patterns.push(regex);
            }
        }

        Ok(())
    }

    /// 检查白名单
    fn check_whitelist(&self, message: &NativeMessage) -> Result<ContentFilterDetail> {
        if self.config.whitelist_patterns.is_empty() {
            return Ok(ContentFilterDetail {
                filter_type: FilterRuleType::Custom,
                is_passed: true,
                severity: FilterSeverity::Info,
                message: "白名单为空，允许所有内容".to_string(),
                location: "whitelist".to_string(),
            });
        }

        let message_content = serde_json::to_string(message)?;
        let is_whitelisted = self.config.whitelist_patterns.iter()
            .any(|pattern| message_content.contains(pattern));

        Ok(ContentFilterDetail {
            filter_type: FilterRuleType::Custom,
            is_passed: is_whitelisted,
            severity: if is_whitelisted { FilterSeverity::Info } else { FilterSeverity::Warning },
            message: if is_whitelisted {
                "内容在白名单中".to_string()
            } else {
                "内容不在白名单中".to_string()
            },
            location: "whitelist".to_string(),
        })
    }

    /// 检测恶意内容
    fn detect_malicious_content(&self, message: &NativeMessage) -> Result<Vec<ContentFilterDetail>> {
        let mut details = Vec::new();
        let message_content = serde_json::to_string(message)?;

        for (index, pattern) in self.malicious_patterns.iter().enumerate() {
            if pattern.is_match(&message_content) {
                details.push(ContentFilterDetail {
                    filter_type: FilterRuleType::MaliciousContent,
                    is_passed: false,
                    severity: FilterSeverity::Critical,
                    message: format!("检测到恶意内容模式 #{}", index + 1),
                    location: "message_content".to_string(),
                });
            }
        }

        if details.is_empty() {
            details.push(ContentFilterDetail {
                filter_type: FilterRuleType::MaliciousContent,
                is_passed: true,
                severity: FilterSeverity::Info,
                message: "未检测到恶意内容".to_string(),
                location: "message_content".to_string(),
            });
        }

        Ok(details)
    }

    /// 检测敏感数据
    fn detect_sensitive_data(&self, message: &NativeMessage) -> Result<Vec<ContentFilterDetail>> {
        let mut details = Vec::new();
        let message_content = serde_json::to_string(message)?;

        for (index, pattern) in self.sensitive_patterns.iter().enumerate() {
            if pattern.is_match(&message_content) {
                details.push(ContentFilterDetail {
                    filter_type: FilterRuleType::SensitiveData,
                    is_passed: false,
                    severity: FilterSeverity::Warning,
                    message: format!("检测到敏感数据模式 #{}", index + 1),
                    location: "message_content".to_string(),
                });
            }
        }

        if details.is_empty() {
            details.push(ContentFilterDetail {
                filter_type: FilterRuleType::SensitiveData,
                is_passed: true,
                severity: FilterSeverity::Info,
                message: "未检测到敏感数据".to_string(),
                location: "message_content".to_string(),
            });
        }

        Ok(details)
    }

    /// 检测不当内容
    fn detect_inappropriate_content(&self, _message: &NativeMessage) -> Result<Vec<ContentFilterDetail>> {
        // 简化实现 - 实际应用中可以集成更复杂的内容分析
        Ok(vec![ContentFilterDetail {
            filter_type: FilterRuleType::InappropriateContent,
            is_passed: true,
            severity: FilterSeverity::Info,
            message: "不当内容检测已跳过".to_string(),
            location: "message_content".to_string(),
        }])
    }

    /// 应用自定义规则
    fn apply_custom_rules(&self, message: &NativeMessage) -> Result<Vec<ContentFilterDetail>> {
        let mut details = Vec::new();
        let message_content = serde_json::to_string(message)?;

        for compiled_rule in &self.compiled_rules {
            if compiled_rule.regex.is_match(&message_content) {
                details.push(ContentFilterDetail {
                    filter_type: compiled_rule.rule.rule_type.clone(),
                    is_passed: false,
                    severity: compiled_rule.rule.severity.clone(),
                    message: format!("触发自定义规则: {}", compiled_rule.rule.name),
                    location: "message_content".to_string(),
                });
            }
        }

        if details.is_empty() {
            details.push(ContentFilterDetail {
                filter_type: FilterRuleType::Custom,
                is_passed: true,
                severity: FilterSeverity::Info,
                message: "未触发自定义规则".to_string(),
                location: "message_content".to_string(),
            });
        }

        Ok(details)
    }

    /// 计算内容清洁度
    fn calculate_cleanliness(&self, result: &ContentFilterResult) -> bool {
        let critical_issues = result.detected_issues.iter()
            .filter(|issue| issue.severity == FilterSeverity::Critical)
            .count();

        let error_issues = result.detected_issues.iter()
            .filter(|issue| issue.severity == FilterSeverity::Error)
            .count();

        match self.config.filter_strictness {
            FilterStrictness::Low => critical_issues == 0,
            FilterStrictness::Medium => critical_issues == 0 && error_issues == 0,
            FilterStrictness::High => result.detected_issues.is_empty(),
            FilterStrictness::Extreme => result.filter_details.iter().all(|d| d.is_passed),
        }
    }
}

/// 编译后的过滤规则
#[derive(Debug)]
struct CompiledFilterRule {
    rule: FilterRule,
    regex: Regex,
}

/// 内容过滤结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContentFilterResult {
    /// 消息ID
    pub message_id: String,
    /// 是否清洁
    pub is_clean: bool,
    /// 过滤详情
    pub filter_details: Vec<ContentFilterDetail>,
    /// 检测到的问题
    pub detected_issues: Vec<ContentIssue>,
    /// 过滤时间
    pub filtered_at: SystemTime,
}

/// 内容过滤详情
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContentFilterDetail {
    /// 过滤类型
    pub filter_type: FilterRuleType,
    /// 是否通过
    pub is_passed: bool,
    /// 严重程度
    pub severity: FilterSeverity,
    /// 详细消息
    pub message: String,
    /// 位置
    pub location: String,
}

/// 内容问题
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContentIssue {
    /// 问题类型
    pub issue_type: FilterRuleType,
    /// 严重程度
    pub severity: FilterSeverity,
    /// 描述
    pub description: String,
    /// 位置
    pub location: String,
}
