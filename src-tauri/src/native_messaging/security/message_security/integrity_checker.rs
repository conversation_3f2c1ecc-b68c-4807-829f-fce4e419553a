//! 消息完整性检查器模块
//!
//! 负责验证消息的完整性，确保消息在传输过程中未被篡改

use super::MessageSecurityError;
use crate::native_messaging::error::{NativeMessagingError, Result};
use crate::native_messaging::protocol::message::NativeMessage;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::SystemTime;
use sha2::{Sha256, Sha512, Digest};
use crc32fast::Hasher as Crc32Hasher;
use tracing::{debug, warn, error};

/// 完整性检查器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IntegrityCheckerConfig {
    /// 完整性算法
    pub algorithm: IntegrityAlgorithm,
    /// 启用多重校验
    pub enable_multiple_checksums: bool,
    /// 启用结构验证
    pub enable_structure_validation: bool,
    /// 启用大小验证
    pub enable_size_validation: bool,
    /// 最大消息大小（字节）
    pub max_message_size: usize,
    /// 校验和缓存大小
    pub checksum_cache_size: usize,
}

impl Default for IntegrityCheckerConfig {
    fn default() -> Self {
        Self {
            algorithm: IntegrityAlgorithm::Sha256,
            enable_multiple_checksums: true,
            enable_structure_validation: true,
            enable_size_validation: true,
            max_message_size: 1024 * 1024, // 1MB
            checksum_cache_size: 1000,
        }
    }
}

/// 完整性算法
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum IntegrityAlgorithm {
    /// SHA-256
    Sha256,
    /// SHA-512
    Sha512,
    /// CRC32
    Crc32,
    /// 多重校验（SHA-256 + CRC32）
    Multiple,
}

/// 完整性检查器
#[derive(Debug)]
pub struct IntegrityChecker {
    config: IntegrityCheckerConfig,
    checksum_cache: HashMap<String, (String, SystemTime)>,
}

impl IntegrityChecker {
    /// 创建新的完整性检查器
    pub fn new(config: &IntegrityCheckerConfig) -> Result<Self> {
        Ok(Self {
            config: config.clone(),
            checksum_cache: HashMap::new(),
        })
    }

    /// 检查消息完整性
    pub async fn check_integrity(&self, message: &NativeMessage) -> Result<IntegrityCheckResult> {
        debug!("检查消息完整性: {}", message.request_id);

        let mut result = IntegrityCheckResult {
            message_id: message.request_id.clone(),
            is_valid: false,
            algorithm: self.config.algorithm.clone(),
            calculated_checksum: String::new(),
            expected_checksum: None,
            integrity_details: Vec::new(),
            checked_at: SystemTime::now(),
        };

        // 1. 大小验证
        if self.config.enable_size_validation {
            result.integrity_details.push(self.validate_message_size(message)?);
        }

        // 2. 结构验证
        if self.config.enable_structure_validation {
            result.integrity_details.push(self.validate_message_structure(message)?);
        }

        // 3. 计算校验和
        let calculated_checksum = self.calculate_checksum(message)?;
        result.calculated_checksum = calculated_checksum.clone();

        result.integrity_details.push(IntegrityCheckDetail {
            check_type: IntegrityCheckType::ChecksumCalculation,
            is_passed: true,
            message: format!("校验和计算完成: {}", &calculated_checksum[..16]),
        });

        // 4. 验证校验和（如果消息中包含期望的校验和）
        if let Some(expected) = self.extract_expected_checksum(message) {
            result.expected_checksum = Some(expected.clone());
            let checksum_valid = calculated_checksum.eq_ignore_ascii_case(&expected);
            
            result.integrity_details.push(IntegrityCheckDetail {
                check_type: IntegrityCheckType::ChecksumVerification,
                is_passed: checksum_valid,
                message: if checksum_valid {
                    "校验和验证通过".to_string()
                } else {
                    format!("校验和不匹配: 期望 {}, 实际 {}", expected, calculated_checksum)
                },
            });
        } else {
            // 如果没有期望的校验和，只记录计算结果
            result.integrity_details.push(IntegrityCheckDetail {
                check_type: IntegrityCheckType::ChecksumVerification,
                is_passed: true,
                message: "未提供期望校验和，跳过验证".to_string(),
            });
        }

        // 5. 多重校验（如果启用）
        if self.config.enable_multiple_checksums {
            result.integrity_details.push(self.perform_multiple_checksums(message)?);
        }

        // 计算最终结果
        result.is_valid = result.integrity_details.iter().all(|detail| detail.is_passed);

        if result.is_valid {
            debug!("消息完整性检查通过: {}", message.request_id);
        } else {
            warn!("消息完整性检查失败: {} - 失败的检查: {:?}", 
                message.request_id,
                result.integrity_details.iter()
                    .filter(|d| !d.is_passed)
                    .map(|d| &d.check_type)
                    .collect::<Vec<_>>()
            );
        }

        Ok(result)
    }

    /// 验证消息大小
    fn validate_message_size(&self, message: &NativeMessage) -> Result<IntegrityCheckDetail> {
        let message_json = serde_json::to_string(message)?;
        let message_size = message_json.len();
        let is_valid = message_size <= self.config.max_message_size;

        Ok(IntegrityCheckDetail {
            check_type: IntegrityCheckType::SizeValidation,
            is_passed: is_valid,
            message: if is_valid {
                format!("消息大小验证通过: {} 字节", message_size)
            } else {
                format!("消息大小超过限制: {} > {} 字节", 
                    message_size, self.config.max_message_size)
            },
        })
    }

    /// 验证消息结构
    fn validate_message_structure(&self, message: &NativeMessage) -> Result<IntegrityCheckDetail> {
        let mut issues = Vec::new();

        // 检查必需字段
        if message.request_id.is_empty() {
            issues.push("缺少请求ID");
        }
        if message.source.is_empty() {
            issues.push("缺少来源标识");
        }
        if message.timestamp == 0 {
            issues.push("无效的时间戳");
        }

        // 检查字段格式
        if !message.request_id.chars().all(|c| c.is_ascii_alphanumeric() || c == '-' || c == '_') {
            issues.push("请求ID包含无效字符");
        }

        // 检查JSON payload有效性
        if let Err(_) = serde_json::to_string(&message.payload) {
            issues.push("payload JSON格式无效");
        }

        let is_valid = issues.is_empty();
        Ok(IntegrityCheckDetail {
            check_type: IntegrityCheckType::StructureValidation,
            is_passed: is_valid,
            message: if is_valid {
                "消息结构验证通过".to_string()
            } else {
                format!("消息结构问题: {}", issues.join(", "))
            },
        })
    }

    /// 计算校验和
    fn calculate_checksum(&self, message: &NativeMessage) -> Result<String> {
        // 构建用于校验的消息内容（排除可能的校验和字段）
        let mut message_copy = message.clone();
        message_copy.metadata.remove("checksum");
        message_copy.metadata.remove("integrity_hash");
        message_copy.extensions.remove("checksum");
        message_copy.extensions.remove("integrity_hash");

        let message_bytes = serde_json::to_vec(&message_copy)?;

        match self.config.algorithm {
            IntegrityAlgorithm::Sha256 => {
                let mut hasher = Sha256::new();
                hasher.update(&message_bytes);
                Ok(hex::encode(hasher.finalize()))
            }
            IntegrityAlgorithm::Sha512 => {
                let mut hasher = Sha512::new();
                hasher.update(&message_bytes);
                Ok(hex::encode(hasher.finalize()))
            }
            IntegrityAlgorithm::Crc32 => {
                let mut hasher = Crc32Hasher::new();
                hasher.update(&message_bytes);
                Ok(format!("{:08x}", hasher.finalize()))
            }
            IntegrityAlgorithm::Multiple => {
                // 计算多个校验和并组合
                let mut sha256_hasher = Sha256::new();
                sha256_hasher.update(&message_bytes);
                let sha256_hash = hex::encode(sha256_hasher.finalize());

                let mut crc32_hasher = Crc32Hasher::new();
                crc32_hasher.update(&message_bytes);
                let crc32_hash = format!("{:08x}", crc32_hasher.finalize());

                Ok(format!("sha256:{},crc32:{}", sha256_hash, crc32_hash))
            }
        }
    }

    /// 从消息中提取期望的校验和
    fn extract_expected_checksum(&self, message: &NativeMessage) -> Option<String> {
        // 检查元数据中的校验和
        if let Some(checksum) = message.metadata.get("checksum") {
            return Some(checksum.clone());
        }
        if let Some(checksum) = message.metadata.get("integrity_hash") {
            return Some(checksum.clone());
        }

        // 检查扩展数据中的校验和
        if let Some(checksum_value) = message.extensions.get("checksum") {
            if let Some(checksum_str) = checksum_value.as_str() {
                return Some(checksum_str.to_string());
            }
        }
        if let Some(checksum_value) = message.extensions.get("integrity_hash") {
            if let Some(checksum_str) = checksum_value.as_str() {
                return Some(checksum_str.to_string());
            }
        }

        None
    }

    /// 执行多重校验
    fn perform_multiple_checksums(&self, message: &NativeMessage) -> Result<IntegrityCheckDetail> {
        let message_bytes = serde_json::to_vec(message)?;
        
        // 计算SHA-256
        let mut sha256_hasher = Sha256::new();
        sha256_hasher.update(&message_bytes);
        let sha256_hash = hex::encode(sha256_hasher.finalize());

        // 计算CRC32
        let mut crc32_hasher = Crc32Hasher::new();
        crc32_hasher.update(&message_bytes);
        let crc32_hash = format!("{:08x}", crc32_hasher.finalize());

        // 验证两个校验和是否一致（通过重新计算）
        let verification_bytes = serde_json::to_vec(message)?;
        let mut verify_sha256 = Sha256::new();
        verify_sha256.update(&verification_bytes);
        let verify_sha256_hash = hex::encode(verify_sha256.finalize());

        let mut verify_crc32 = Crc32Hasher::new();
        verify_crc32.update(&verification_bytes);
        let verify_crc32_hash = format!("{:08x}", verify_crc32.finalize());

        let is_consistent = sha256_hash == verify_sha256_hash && crc32_hash == verify_crc32_hash;

        Ok(IntegrityCheckDetail {
            check_type: IntegrityCheckType::MultipleChecksums,
            is_passed: is_consistent,
            message: if is_consistent {
                "多重校验和验证通过".to_string()
            } else {
                "多重校验和不一致".to_string()
            },
        })
    }
}

/// 完整性检查结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IntegrityCheckResult {
    /// 消息ID
    pub message_id: String,
    /// 是否有效
    pub is_valid: bool,
    /// 使用的算法
    pub algorithm: IntegrityAlgorithm,
    /// 计算的校验和
    pub calculated_checksum: String,
    /// 期望的校验和
    pub expected_checksum: Option<String>,
    /// 完整性检查详情
    pub integrity_details: Vec<IntegrityCheckDetail>,
    /// 检查时间
    pub checked_at: SystemTime,
}

/// 完整性检查详情
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IntegrityCheckDetail {
    /// 检查类型
    pub check_type: IntegrityCheckType,
    /// 是否通过
    pub is_passed: bool,
    /// 详细消息
    pub message: String,
}

/// 完整性检查类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum IntegrityCheckType {
    /// 大小验证
    SizeValidation,
    /// 结构验证
    StructureValidation,
    /// 校验和计算
    ChecksumCalculation,
    /// 校验和验证
    ChecksumVerification,
    /// 多重校验和
    MultipleChecksums,
}
