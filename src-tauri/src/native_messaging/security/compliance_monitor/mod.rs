//! 合规监控模块
//!
//! 提供标准合规检查、策略执行、审计跟踪和安全报告功能

// 暂时注释掉子模块，直接在此文件中实现基础功能
// pub mod policy_engine;
// pub mod audit_logger;
// pub mod compliance_checker;
// pub mod security_reporter;

// 重新导出主要类型
// pub use policy_engine::{PolicyEngine, PolicyRule, PolicyResult, PolicyAction};
// pub use audit_logger::{AuditLogger, AuditEvent, AuditLevel};
// pub use compliance_checker::{ComplianceChecker, ComplianceResult, ComplianceStandard};
// pub use security_reporter::{SecurityReporter, SecurityReport, ReportType};

use crate::native_messaging::error::{NativeMessagingError, Result};
use crate::native_messaging::protocol::message::NativeMessage;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::SystemTime;
use tracing::{debug, info, warn, error};

// ============================================================================
// 策略引擎相关类型和实现
// ============================================================================

/// 策略引擎配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PolicyEngineConfig {
    /// 启用策略执行
    pub enable_policy_execution: bool,
    /// 默认策略动作
    pub default_action: PolicyAction,
    /// 策略规则
    pub policy_rules: Vec<PolicyRule>,
}

impl Default for PolicyEngineConfig {
    fn default() -> Self {
        Self {
            enable_policy_execution: true,
            default_action: PolicyAction::Allow,
            policy_rules: Self::default_rules(),
        }
    }
}

impl PolicyEngineConfig {
    fn default_rules() -> Vec<PolicyRule> {
        vec![
            PolicyRule {
                id: "policy_001".to_string(),
                name: "Message Size Limit".to_string(),
                description: "限制消息大小".to_string(),
                condition: PolicyCondition::MessageSizeLimit(1024 * 1024), // 1MB
                action: PolicyAction::Block,
                enabled: true,
                priority: 100,
            },
            PolicyRule {
                id: "policy_002".to_string(),
                name: "Source Whitelist".to_string(),
                description: "来源白名单检查".to_string(),
                condition: PolicyCondition::SourceWhitelist(vec![
                    "trusted-extension-1".to_string(),
                    "trusted-extension-2".to_string(),
                ]),
                action: PolicyAction::Allow,
                enabled: true,
                priority: 90,
            },
            PolicyRule {
                id: "policy_003".to_string(),
                name: "Rate Limit".to_string(),
                description: "频率限制".to_string(),
                condition: PolicyCondition::RateLimit(100), // 每分钟100次
                action: PolicyAction::RateLimit,
                enabled: true,
                priority: 80,
            },
        ]
    }
}

/// 策略引擎
#[derive(Debug)]
pub struct PolicyEngine {
    config: PolicyEngineConfig,
    rule_cache: HashMap<String, PolicyRule>,
}

impl PolicyEngine {
    /// 创建新的策略引擎
    pub fn new(config: &PolicyEngineConfig) -> Result<Self> {
        let mut rule_cache = HashMap::new();
        for rule in &config.policy_rules {
            rule_cache.insert(rule.id.clone(), rule.clone());
        }

        Ok(Self {
            config: config.clone(),
            rule_cache,
        })
    }

    /// 评估策略
    pub async fn evaluate_policies(&self, message: &NativeMessage) -> Result<Vec<PolicyResult>> {
        let mut results = Vec::new();

        for rule in &self.config.policy_rules {
            if !rule.enabled {
                continue;
            }

            let is_match = self.evaluate_condition(&rule.condition, message).await?;

            results.push(PolicyResult {
                rule_id: rule.id.clone(),
                rule_name: rule.name.clone(),
                is_allowed: match rule.action {
                    PolicyAction::Allow => true,
                    PolicyAction::Block => !is_match,
                    PolicyAction::Monitor => true,
                    PolicyAction::RateLimit => true, // 简化处理
                },
                action: rule.action.clone(),
                reason: if is_match {
                    format!("匹配规则: {}", rule.description)
                } else {
                    "未匹配规则".to_string()
                },
                evaluated_at: SystemTime::now(),
            });
        }

        Ok(results)
    }

    /// 评估条件
    async fn evaluate_condition(&self, condition: &PolicyCondition, message: &NativeMessage) -> Result<bool> {
        match condition {
            PolicyCondition::MessageSizeLimit(limit) => {
                let message_size = serde_json::to_string(&message.payload)?.len();
                Ok(message_size > *limit)
            }
            PolicyCondition::SourceWhitelist(whitelist) => {
                Ok(whitelist.contains(&message.source))
            }
            PolicyCondition::RateLimit(_limit) => {
                // 简化实现，实际应该检查频率
                Ok(false)
            }
            PolicyCondition::Custom(expression) => {
                // 简化实现，实际应该解析表达式
                Ok(expression.contains(&message.source))
            }
        }
    }
}

/// 策略规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PolicyRule {
    /// 规则ID
    pub id: String,
    /// 规则名称
    pub name: String,
    /// 规则描述
    pub description: String,
    /// 条件
    pub condition: PolicyCondition,
    /// 动作
    pub action: PolicyAction,
    /// 是否启用
    pub enabled: bool,
    /// 优先级
    pub priority: u32,
}

/// 策略条件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PolicyCondition {
    /// 消息大小限制
    MessageSizeLimit(usize),
    /// 来源白名单
    SourceWhitelist(Vec<String>),
    /// 频率限制
    RateLimit(u32),
    /// 自定义表达式
    Custom(String),
}

/// 策略动作
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum PolicyAction {
    /// 允许
    Allow,
    /// 阻止
    Block,
    /// 监控
    Monitor,
    /// 限流
    RateLimit,
}

/// 策略结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PolicyResult {
    /// 规则ID
    pub rule_id: String,
    /// 规则名称
    pub rule_name: String,
    /// 是否允许
    pub is_allowed: bool,
    /// 动作
    pub action: PolicyAction,
    /// 原因
    pub reason: String,
    /// 评估时间
    pub evaluated_at: SystemTime,
}

// ============================================================================
// 审计记录器相关类型和实现
// ============================================================================

/// 审计记录器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuditLoggerConfig {
    /// 启用审计记录
    pub enable_audit_logging: bool,
    /// 日志文件路径
    pub log_file_path: Option<String>,
    /// 最大日志文件大小（字节）
    pub max_log_file_size: usize,
    /// 日志轮转数量
    pub log_rotation_count: u32,
    /// 记录级别
    pub log_level: AuditLevel,
}

impl Default for AuditLoggerConfig {
    fn default() -> Self {
        Self {
            enable_audit_logging: true,
            log_file_path: Some("logs/audit.log".to_string()),
            max_log_file_size: 10 * 1024 * 1024, // 10MB
            log_rotation_count: 5,
            log_level: AuditLevel::Info,
        }
    }
}

/// 审计记录器
#[derive(Debug)]
pub struct AuditLogger {
    config: AuditLoggerConfig,
    event_count: u64,
}

impl AuditLogger {
    /// 创建新的审计记录器
    pub fn new(config: &AuditLoggerConfig) -> Result<Self> {
        Ok(Self {
            config: config.clone(),
            event_count: 0,
        })
    }

    /// 记录审计事件
    pub async fn log_event(&mut self, event: &AuditEvent) -> Result<()> {
        if !self.config.enable_audit_logging {
            return Ok(());
        }

        // 检查日志级别
        if event.level < self.config.log_level {
            return Ok(());
        }

        // 记录到文件（简化实现）
        if let Some(ref log_path) = self.config.log_file_path {
            let log_entry = self.format_log_entry(event)?;

            // 实际实现应该处理文件轮转等
            tokio::fs::write(log_path, log_entry).await?;
        }

        // 记录到系统日志
        match event.level {
            AuditLevel::Error => error!("AUDIT: {}", event.description),
            AuditLevel::Warning => warn!("AUDIT: {}", event.description),
            AuditLevel::Info => info!("AUDIT: {}", event.description),
            AuditLevel::Debug => debug!("AUDIT: {}", event.description),
        }

        self.event_count += 1;
        Ok(())
    }

    /// 格式化日志条目
    fn format_log_entry(&self, event: &AuditEvent) -> Result<String> {
        let timestamp = event.timestamp
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        let metadata_str = serde_json::to_string(&event.metadata)?;

        Ok(format!(
            "[{}] {} {} {} {} {} {}\n",
            timestamp,
            event.event_id,
            format!("{:?}", event.level),
            event.event_type,
            event.message_id,
            event.description,
            metadata_str
        ))
    }

    /// 获取事件计数
    pub fn get_event_count(&self) -> u64 {
        self.event_count
    }
}

/// 审计事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuditEvent {
    /// 事件ID
    pub event_id: String,
    /// 事件类型
    pub event_type: String,
    /// 消息ID
    pub message_id: String,
    /// 来源
    pub source: String,
    /// 级别
    pub level: AuditLevel,
    /// 描述
    pub description: String,
    /// 元数据
    pub metadata: HashMap<String, String>,
    /// 时间戳
    pub timestamp: SystemTime,
}

/// 审计级别
#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum AuditLevel {
    /// 调试
    Debug,
    /// 信息
    Info,
    /// 警告
    Warning,
    /// 错误
    Error,
}

// ============================================================================
// 合规检查器相关类型和实现
// ============================================================================

/// 合规检查器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceCheckerConfig {
    /// 启用合规检查
    pub enable_compliance_checking: bool,
    /// 支持的合规标准
    pub supported_standards: Vec<ComplianceStandard>,
    /// 严格模式
    pub strict_mode: bool,
}

impl Default for ComplianceCheckerConfig {
    fn default() -> Self {
        Self {
            enable_compliance_checking: true,
            supported_standards: vec![
                ComplianceStandard::GDPR,
                ComplianceStandard::SOX,
                ComplianceStandard::HIPAA,
                ComplianceStandard::PCI_DSS,
            ],
            strict_mode: false,
        }
    }
}

/// 合规检查器
#[derive(Debug)]
pub struct ComplianceChecker {
    config: ComplianceCheckerConfig,
    check_count: u64,
}

impl ComplianceChecker {
    /// 创建新的合规检查器
    pub fn new(config: &ComplianceCheckerConfig) -> Result<Self> {
        Ok(Self {
            config: config.clone(),
            check_count: 0,
        })
    }

    /// 检查合规性
    pub async fn check_compliance(&mut self, message: &NativeMessage) -> Result<Vec<ComplianceResult>> {
        if !self.config.enable_compliance_checking {
            return Ok(vec![]);
        }

        let mut results = Vec::new();

        for standard in &self.config.supported_standards {
            let result = self.check_standard_compliance(standard, message).await?;
            results.push(result);
        }

        self.check_count += 1;
        Ok(results)
    }

    /// 检查特定标准的合规性
    async fn check_standard_compliance(&self, standard: &ComplianceStandard, message: &NativeMessage) -> Result<ComplianceResult> {
        let mut violations = Vec::new();
        let mut is_compliant = true;

        match standard {
            ComplianceStandard::GDPR => {
                // GDPR 合规检查
                if self.check_personal_data_handling(message) {
                    violations.push("可能包含个人数据但未加密".to_string());
                    is_compliant = false;
                }

                if !self.check_consent_tracking(message) {
                    violations.push("缺少同意跟踪信息".to_string());
                    is_compliant = false;
                }
            }
            ComplianceStandard::SOX => {
                // SOX 合规检查
                if !self.check_audit_trail(message) {
                    violations.push("缺少审计跟踪信息".to_string());
                    is_compliant = false;
                }

                if !self.check_data_integrity(message) {
                    violations.push("数据完整性验证失败".to_string());
                    is_compliant = false;
                }
            }
            ComplianceStandard::HIPAA => {
                // HIPAA 合规检查
                if self.check_health_data(message) && !self.check_encryption(message) {
                    violations.push("健康数据未加密".to_string());
                    is_compliant = false;
                }

                if !self.check_access_controls(message) {
                    violations.push("访问控制不足".to_string());
                    is_compliant = false;
                }
            }
            ComplianceStandard::PCI_DSS => {
                // PCI DSS 合规检查
                if self.check_payment_data(message) && !self.check_tokenization(message) {
                    violations.push("支付数据未标记化".to_string());
                    is_compliant = false;
                }

                if !self.check_secure_transmission(message) {
                    violations.push("传输安全性不足".to_string());
                    is_compliant = false;
                }
            }
            ComplianceStandard::Custom(name) => {
                // 自定义标准检查
                violations.push(format!("自定义标准 {} 检查未实现", name));
                is_compliant = false;
            }
        }

        Ok(ComplianceResult {
            standard: standard.clone(),
            is_compliant,
            violation_details: violations,
            checked_at: SystemTime::now(),
            recommendations: self.generate_recommendations(standard, &violations),
        })
    }

    /// 检查个人数据处理
    fn check_personal_data_handling(&self, message: &NativeMessage) -> bool {
        // 简化实现：检查是否包含可能的个人数据字段
        let payload_str = serde_json::to_string(&message.payload).unwrap_or_default();
        let personal_data_indicators = ["email", "phone", "ssn", "name", "address"];

        personal_data_indicators.iter().any(|indicator| payload_str.contains(indicator))
    }

    /// 检查同意跟踪
    fn check_consent_tracking(&self, message: &NativeMessage) -> bool {
        // 简化实现：检查是否有同意标记
        message.payload.get("consent_id").is_some() ||
        message.payload.get("user_consent").is_some()
    }

    /// 检查审计跟踪
    fn check_audit_trail(&self, _message: &NativeMessage) -> bool {
        // 简化实现：假设所有消息都有审计跟踪
        true
    }

    /// 检查数据完整性
    fn check_data_integrity(&self, message: &NativeMessage) -> bool {
        // 简化实现：检查是否有完整性校验
        message.payload.get("checksum").is_some() ||
        message.payload.get("hash").is_some()
    }

    /// 检查健康数据
    fn check_health_data(&self, message: &NativeMessage) -> bool {
        let payload_str = serde_json::to_string(&message.payload).unwrap_or_default();
        let health_indicators = ["medical", "health", "diagnosis", "treatment"];

        health_indicators.iter().any(|indicator| payload_str.contains(indicator))
    }

    /// 检查加密
    fn check_encryption(&self, message: &NativeMessage) -> bool {
        message.payload.get("encrypted").is_some() ||
        message.payload.get("cipher").is_some()
    }

    /// 检查访问控制
    fn check_access_controls(&self, message: &NativeMessage) -> bool {
        message.payload.get("access_token").is_some() ||
        message.payload.get("authorization").is_some()
    }

    /// 检查支付数据
    fn check_payment_data(&self, message: &NativeMessage) -> bool {
        let payload_str = serde_json::to_string(&message.payload).unwrap_or_default();
        let payment_indicators = ["card", "payment", "transaction", "amount"];

        payment_indicators.iter().any(|indicator| payload_str.contains(indicator))
    }

    /// 检查标记化
    fn check_tokenization(&self, message: &NativeMessage) -> bool {
        message.payload.get("token").is_some() ||
        message.payload.get("tokenized").is_some()
    }

    /// 检查安全传输
    fn check_secure_transmission(&self, _message: &NativeMessage) -> bool {
        // 简化实现：假设传输是安全的
        true
    }

    /// 生成建议
    fn generate_recommendations(&self, standard: &ComplianceStandard, violations: &[String]) -> Vec<String> {
        if violations.is_empty() {
            return vec!["合规检查通过".to_string()];
        }

        let mut recommendations = Vec::new();

        match standard {
            ComplianceStandard::GDPR => {
                recommendations.push("实施数据加密".to_string());
                recommendations.push("添加同意跟踪机制".to_string());
                recommendations.push("实施数据最小化原则".to_string());
            }
            ComplianceStandard::SOX => {
                recommendations.push("增强审计跟踪".to_string());
                recommendations.push("实施数据完整性检查".to_string());
                recommendations.push("加强访问控制".to_string());
            }
            ComplianceStandard::HIPAA => {
                recommendations.push("加密健康数据".to_string());
                recommendations.push("实施强访问控制".to_string());
                recommendations.push("添加审计日志".to_string());
            }
            ComplianceStandard::PCI_DSS => {
                recommendations.push("实施支付数据标记化".to_string());
                recommendations.push("加强传输安全".to_string());
                recommendations.push("定期安全评估".to_string());
            }
            ComplianceStandard::Custom(_) => {
                recommendations.push("联系管理员获取自定义合规要求".to_string());
            }
        }

        recommendations
    }

    /// 获取检查计数
    pub fn get_check_count(&self) -> u64 {
        self.check_count
    }
}

/// 合规标准
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ComplianceStandard {
    /// 通用数据保护条例
    GDPR,
    /// 萨班斯-奥克斯利法案
    SOX,
    /// 健康保险流通与责任法案
    HIPAA,
    /// 支付卡行业数据安全标准
    PCI_DSS,
    /// 自定义标准
    Custom(String),
}

/// 合规检查结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceResult {
    /// 合规标准
    pub standard: ComplianceStandard,
    /// 是否合规
    pub is_compliant: bool,
    /// 违规详情
    pub violation_details: Vec<String>,
    /// 检查时间
    pub checked_at: SystemTime,
    /// 建议
    pub recommendations: Vec<String>,
}

// ============================================================================
// 安全报告器相关类型和实现
// ============================================================================

/// 安全报告器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityReporterConfig {
    /// 启用报告生成
    pub enable_reporting: bool,
    /// 报告输出目录
    pub report_output_dir: String,
    /// 报告格式
    pub report_formats: Vec<ReportFormat>,
    /// 自动报告间隔（秒）
    pub auto_report_interval: Option<u64>,
}

impl Default for SecurityReporterConfig {
    fn default() -> Self {
        Self {
            enable_reporting: true,
            report_output_dir: "reports".to_string(),
            report_formats: vec![ReportFormat::Json, ReportFormat::Html],
            auto_report_interval: Some(3600), // 每小时
        }
    }
}

/// 安全报告器
#[derive(Debug)]
pub struct SecurityReporter {
    config: SecurityReporterConfig,
    report_count: u64,
}

impl SecurityReporter {
    /// 创建新的安全报告器
    pub fn new(config: &SecurityReporterConfig) -> Result<Self> {
        Ok(Self {
            config: config.clone(),
            report_count: 0,
        })
    }

    /// 生成违规报告
    pub async fn generate_violation_report(&mut self, result: &ComplianceMonitorResult) -> Result<SecurityReport> {
        let report = SecurityReport {
            report_id: uuid::Uuid::new_v4().to_string(),
            report_type: ReportType::ViolationReport,
            title: "合规违规报告".to_string(),
            summary: format!("检测到 {} 个违规", result.violations.len()),
            generated_at: SystemTime::now(),
            data: serde_json::to_value(result)?,
            recommendations: result.recommended_actions.iter()
                .map(|action| format!("{:?}", action))
                .collect(),
        };

        self.save_report(&report).await?;
        self.report_count += 1;
        Ok(report)
    }

    /// 生成报告
    pub async fn generate_report(&mut self, report_type: ReportType) -> Result<SecurityReport> {
        let report = match report_type {
            ReportType::ComplianceReport => self.generate_compliance_report().await?,
            ReportType::SecuritySummary => self.generate_security_summary().await?,
            ReportType::AuditReport => self.generate_audit_report().await?,
            ReportType::ViolationReport => {
                return Err(NativeMessagingError::ValidationError(
                    "违规报告需要提供具体的违规数据".to_string()
                ));
            }
        };

        self.save_report(&report).await?;
        self.report_count += 1;
        Ok(report)
    }

    /// 生成合规报告
    async fn generate_compliance_report(&self) -> Result<SecurityReport> {
        Ok(SecurityReport {
            report_id: uuid::Uuid::new_v4().to_string(),
            report_type: ReportType::ComplianceReport,
            title: "合规状态报告".to_string(),
            summary: "系统合规状态概览".to_string(),
            generated_at: SystemTime::now(),
            data: serde_json::json!({
                "compliance_standards": ["GDPR", "SOX", "HIPAA", "PCI_DSS"],
                "overall_compliance": "良好",
                "last_check": SystemTime::now()
            }),
            recommendations: vec![
                "定期更新合规策略".to_string(),
                "加强员工培训".to_string(),
                "实施持续监控".to_string(),
            ],
        })
    }

    /// 生成安全摘要
    async fn generate_security_summary(&self) -> Result<SecurityReport> {
        Ok(SecurityReport {
            report_id: uuid::Uuid::new_v4().to_string(),
            report_type: ReportType::SecuritySummary,
            title: "安全状态摘要".to_string(),
            summary: "系统安全状态概览".to_string(),
            generated_at: SystemTime::now(),
            data: serde_json::json!({
                "security_level": "高",
                "threats_detected": 0,
                "vulnerabilities": 0,
                "last_scan": SystemTime::now()
            }),
            recommendations: vec![
                "保持安全更新".to_string(),
                "定期安全评估".to_string(),
                "监控异常活动".to_string(),
            ],
        })
    }

    /// 生成审计报告
    async fn generate_audit_report(&self) -> Result<SecurityReport> {
        Ok(SecurityReport {
            report_id: uuid::Uuid::new_v4().to_string(),
            report_type: ReportType::AuditReport,
            title: "审计跟踪报告".to_string(),
            summary: "系统审计活动概览".to_string(),
            generated_at: SystemTime::now(),
            data: serde_json::json!({
                "audit_events": 0,
                "period": "last_24_hours",
                "critical_events": 0,
                "warnings": 0
            }),
            recommendations: vec![
                "审查关键事件".to_string(),
                "优化审计策略".to_string(),
                "加强日志分析".to_string(),
            ],
        })
    }

    /// 保存报告
    async fn save_report(&self, report: &SecurityReport) -> Result<()> {
        if !self.config.enable_reporting {
            return Ok(());
        }

        // 确保输出目录存在
        tokio::fs::create_dir_all(&self.config.report_output_dir).await?;

        for format in &self.config.report_formats {
            let filename = format!("{}/{}_{}.{}",
                self.config.report_output_dir,
                report.report_id,
                format!("{:?}", report.report_type).to_lowercase(),
                format.extension()
            );

            let content = match format {
                ReportFormat::Json => serde_json::to_string_pretty(report)?,
                ReportFormat::Html => self.generate_html_report(report)?,
                ReportFormat::Csv => self.generate_csv_report(report)?,
            };

            tokio::fs::write(&filename, content).await?;
            info!("报告已保存: {}", filename);
        }

        Ok(())
    }

    /// 生成HTML报告
    fn generate_html_report(&self, report: &SecurityReport) -> Result<String> {
        let html = format!(
            r#"<!DOCTYPE html>
<html>
<head>
    <title>{}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 10px; }}
        .content {{ margin: 20px 0; }}
        .recommendations {{ background-color: #e8f4fd; padding: 10px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>{}</h1>
        <p>报告ID: {}</p>
        <p>生成时间: {:?}</p>
    </div>
    <div class="content">
        <h2>摘要</h2>
        <p>{}</p>
        <h2>详细数据</h2>
        <pre>{}</pre>
    </div>
    <div class="recommendations">
        <h2>建议</h2>
        <ul>
            {}
        </ul>
    </div>
</body>
</html>"#,
            report.title,
            report.title,
            report.report_id,
            report.generated_at,
            report.summary,
            serde_json::to_string_pretty(&report.data)?,
            report.recommendations.iter()
                .map(|r| format!("<li>{}</li>", r))
                .collect::<Vec<_>>()
                .join("\n            ")
        );

        Ok(html)
    }

    /// 生成CSV报告
    fn generate_csv_report(&self, report: &SecurityReport) -> Result<String> {
        let mut csv = String::new();
        csv.push_str("字段,值\n");
        csv.push_str(&format!("报告ID,{}\n", report.report_id));
        csv.push_str(&format!("报告类型,{:?}\n", report.report_type));
        csv.push_str(&format!("标题,{}\n", report.title));
        csv.push_str(&format!("摘要,{}\n", report.summary));
        csv.push_str(&format!("生成时间,{:?}\n", report.generated_at));

        for (i, recommendation) in report.recommendations.iter().enumerate() {
            csv.push_str(&format!("建议{},{}\n", i + 1, recommendation));
        }

        Ok(csv)
    }

    /// 获取报告计数
    pub fn get_report_count(&self) -> u64 {
        self.report_count
    }
}

/// 报告格式
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ReportFormat {
    /// JSON格式
    Json,
    /// HTML格式
    Html,
    /// CSV格式
    Csv,
}

impl ReportFormat {
    fn extension(&self) -> &'static str {
        match self {
            ReportFormat::Json => "json",
            ReportFormat::Html => "html",
            ReportFormat::Csv => "csv",
        }
    }
}

/// 报告类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ReportType {
    /// 合规报告
    ComplianceReport,
    /// 安全摘要
    SecuritySummary,
    /// 审计报告
    AuditReport,
    /// 违规报告
    ViolationReport,
}

/// 安全报告
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityReport {
    /// 报告ID
    pub report_id: String,
    /// 报告类型
    pub report_type: ReportType,
    /// 标题
    pub title: String,
    /// 摘要
    pub summary: String,
    /// 生成时间
    pub generated_at: SystemTime,
    /// 数据
    pub data: serde_json::Value,
    /// 建议
    pub recommendations: Vec<String>,
}

/// 合规监控管理器
#[derive(Debug)]
pub struct ComplianceMonitor {
    policy_engine: PolicyEngine,
    audit_logger: AuditLogger,
    compliance_checker: ComplianceChecker,
    security_reporter: SecurityReporter,
    config: ComplianceConfig,
}

impl ComplianceMonitor {
    /// 创建新的合规监控管理器
    pub fn new(config: ComplianceConfig) -> Result<Self> {
        let policy_engine = PolicyEngine::new(&config.policy_config)?;
        let audit_logger = AuditLogger::new(&config.audit_config)?;
        let compliance_checker = ComplianceChecker::new(&config.compliance_config)?;
        let security_reporter = SecurityReporter::new(&config.reporting_config)?;

        Ok(Self {
            policy_engine,
            audit_logger,
            compliance_checker,
            security_reporter,
            config,
        })
    }

    /// 监控消息合规性
    pub async fn monitor_compliance(&mut self, message: &NativeMessage) -> Result<ComplianceMonitorResult> {
        debug!("开始合规监控: {}", message.request_id);
        
        let mut result = ComplianceMonitorResult {
            message_id: message.request_id.clone(),
            is_compliant: true,
            policy_results: Vec::new(),
            compliance_results: Vec::new(),
            audit_events: Vec::new(),
            violations: Vec::new(),
            monitored_at: SystemTime::now(),
            recommended_actions: Vec::new(),
        };

        // 1. 策略检查
        if self.config.enable_policy_enforcement {
            match self.policy_engine.evaluate_policies(message).await {
                Ok(policy_results) => {
                    result.policy_results = policy_results.clone();
                    
                    // 检查是否有策略违规
                    for policy_result in &policy_results {
                        if !policy_result.is_allowed {
                            result.is_compliant = false;
                            result.violations.push(ComplianceViolation {
                                violation_type: ViolationType::PolicyViolation,
                                severity: ViolationSeverity::High,
                                description: format!("策略违规: {}", policy_result.rule_name),
                                details: policy_result.reason.clone(),
                            });
                        }
                    }
                }
                Err(e) => {
                    warn!("策略检查错误: {}", e);
                    result.violations.push(ComplianceViolation {
                        violation_type: ViolationType::SystemError,
                        severity: ViolationSeverity::Medium,
                        description: "策略检查失败".to_string(),
                        details: e.to_string(),
                    });
                }
            }
        }

        // 2. 合规标准检查
        if self.config.enable_compliance_checking {
            match self.compliance_checker.check_compliance(message).await {
                Ok(compliance_results) => {
                    result.compliance_results = compliance_results.clone();
                    
                    // 检查合规性
                    for compliance_result in &compliance_results {
                        if !compliance_result.is_compliant {
                            result.is_compliant = false;
                            result.violations.push(ComplianceViolation {
                                violation_type: ViolationType::ComplianceViolation,
                                severity: ViolationSeverity::High,
                                description: format!("合规标准违规: {:?}", compliance_result.standard),
                                details: compliance_result.violation_details.join(", "),
                            });
                        }
                    }
                }
                Err(e) => {
                    warn!("合规检查错误: {}", e);
                }
            }
        }

        // 3. 审计记录
        if self.config.enable_audit_logging {
            let audit_event = AuditEvent {
                event_id: uuid::Uuid::new_v4().to_string(),
                event_type: "compliance_check".to_string(),
                message_id: message.request_id.clone(),
                source: message.source.clone(),
                level: if result.is_compliant { AuditLevel::Info } else { AuditLevel::Warning },
                description: if result.is_compliant {
                    "合规检查通过".to_string()
                } else {
                    format!("合规检查失败: {} 个违规", result.violations.len())
                },
                metadata: self.create_audit_metadata(message, &result),
                timestamp: SystemTime::now(),
            };

            match self.audit_logger.log_event(&audit_event).await {
                Ok(_) => {
                    result.audit_events.push(audit_event);
                }
                Err(e) => {
                    warn!("审计记录失败: {}", e);
                }
            }
        }

        // 4. 生成推荐动作
        result.recommended_actions = self.generate_recommended_actions(&result);

        // 5. 安全报告（如果有违规）
        if !result.is_compliant && self.config.enable_security_reporting {
            match self.security_reporter.generate_violation_report(&result).await {
                Ok(_) => {
                    info!("安全违规报告已生成: {}", message.request_id);
                }
                Err(e) => {
                    warn!("生成安全报告失败: {}", e);
                }
            }
        }

        if result.is_compliant {
            debug!("合规监控通过: {}", message.request_id);
        } else {
            warn!("合规监控发现违规: {} - 违规数量: {}", 
                message.request_id, result.violations.len());
        }

        Ok(result)
    }

    /// 创建审计元数据
    fn create_audit_metadata(&self, message: &NativeMessage, result: &ComplianceMonitorResult) -> HashMap<String, String> {
        let mut metadata = HashMap::new();
        
        metadata.insert("message_type".to_string(), format!("{:?}", message.message_type));
        metadata.insert("source".to_string(), message.source.clone());
        metadata.insert("timestamp".to_string(), message.timestamp.to_string());
        metadata.insert("is_compliant".to_string(), result.is_compliant.to_string());
        metadata.insert("violation_count".to_string(), result.violations.len().to_string());
        
        if !result.violations.is_empty() {
            let violation_types: Vec<String> = result.violations.iter()
                .map(|v| format!("{:?}", v.violation_type))
                .collect();
            metadata.insert("violation_types".to_string(), violation_types.join(","));
        }

        metadata
    }

    /// 生成推荐动作
    fn generate_recommended_actions(&self, result: &ComplianceMonitorResult) -> Vec<ComplianceAction> {
        let mut actions = Vec::new();

        if result.is_compliant {
            actions.push(ComplianceAction::Allow);
            return actions;
        }

        // 根据违规类型和严重程度生成动作
        let has_critical = result.violations.iter().any(|v| v.severity == ViolationSeverity::Critical);
        let has_high = result.violations.iter().any(|v| v.severity == ViolationSeverity::High);

        if has_critical {
            actions.push(ComplianceAction::Block);
            actions.push(ComplianceAction::Alert);
            actions.push(ComplianceAction::Quarantine);
        } else if has_high {
            actions.push(ComplianceAction::Block);
            actions.push(ComplianceAction::Alert);
        } else {
            actions.push(ComplianceAction::Monitor);
            actions.push(ComplianceAction::Log);
        }

        // 总是记录违规
        actions.push(ComplianceAction::Log);

        actions
    }

    /// 获取合规统计信息
    pub async fn get_compliance_stats(&self) -> ComplianceStats {
        ComplianceStats {
            total_messages_monitored: 0, // 需要实现统计跟踪
            compliant_messages: 0,
            violation_count: 0,
            policy_violations: 0,
            compliance_violations: 0,
            audit_events_logged: 0,
            reports_generated: 0,
        }
    }

    /// 生成合规报告
    pub async fn generate_compliance_report(&mut self, report_type: ReportType) -> Result<SecurityReport> {
        self.security_reporter.generate_report(report_type).await
    }
}

/// 合规监控配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceConfig {
    /// 启用策略执行
    pub enable_policy_enforcement: bool,
    /// 启用合规检查
    pub enable_compliance_checking: bool,
    /// 启用审计记录
    pub enable_audit_logging: bool,
    /// 启用安全报告
    pub enable_security_reporting: bool,
    /// 策略引擎配置
    pub policy_config: PolicyEngineConfig,
    /// 审计配置
    pub audit_config: AuditLoggerConfig,
    /// 合规检查配置
    pub compliance_config: ComplianceCheckerConfig,
    /// 报告配置
    pub reporting_config: SecurityReporterConfig,
}

impl Default for ComplianceConfig {
    fn default() -> Self {
        Self {
            enable_policy_enforcement: true,
            enable_compliance_checking: true,
            enable_audit_logging: true,
            enable_security_reporting: true,
            policy_config: PolicyEngineConfig::default(),
            audit_config: AuditLoggerConfig::default(),
            compliance_config: ComplianceCheckerConfig::default(),
            reporting_config: SecurityReporterConfig::default(),
        }
    }
}

/// 合规监控结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceMonitorResult {
    /// 消息ID
    pub message_id: String,
    /// 是否合规
    pub is_compliant: bool,
    /// 策略检查结果
    pub policy_results: Vec<PolicyResult>,
    /// 合规检查结果
    pub compliance_results: Vec<ComplianceResult>,
    /// 审计事件
    pub audit_events: Vec<AuditEvent>,
    /// 违规记录
    pub violations: Vec<ComplianceViolation>,
    /// 监控时间
    pub monitored_at: SystemTime,
    /// 推荐动作
    pub recommended_actions: Vec<ComplianceAction>,
}

/// 合规违规记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceViolation {
    /// 违规类型
    pub violation_type: ViolationType,
    /// 严重程度
    pub severity: ViolationSeverity,
    /// 描述
    pub description: String,
    /// 详细信息
    pub details: String,
}

/// 违规类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ViolationType {
    /// 策略违规
    PolicyViolation,
    /// 合规标准违规
    ComplianceViolation,
    /// 系统错误
    SystemError,
    /// 配置错误
    ConfigurationError,
}

/// 违规严重程度
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ViolationSeverity {
    /// 低
    Low,
    /// 中
    Medium,
    /// 高
    High,
    /// 严重
    Critical,
}

/// 合规动作
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ComplianceAction {
    /// 允许
    Allow,
    /// 阻止
    Block,
    /// 监控
    Monitor,
    /// 记录日志
    Log,
    /// 告警
    Alert,
    /// 隔离
    Quarantine,
    /// 审查
    Review,
}

/// 合规统计信息
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct ComplianceStats {
    /// 监控的消息总数
    pub total_messages_monitored: u64,
    /// 合规消息数
    pub compliant_messages: u64,
    /// 违规总数
    pub violation_count: u64,
    /// 策略违规数
    pub policy_violations: u64,
    /// 合规标准违规数
    pub compliance_violations: u64,
    /// 记录的审计事件数
    pub audit_events_logged: u64,
    /// 生成的报告数
    pub reports_generated: u64,
}

// 配置类型已在本文件中实现
// pub use policy_engine::PolicyEngineConfig;
// pub use audit_logger::AuditLoggerConfig;
// pub use compliance_checker::ComplianceCheckerConfig;
// pub use security_reporter::SecurityReporterConfig;

/// 合规监控错误
#[derive(Debug, thiserror::Error)]
pub enum ComplianceMonitorError {
    #[error("策略执行失败: {0}")]
    PolicyExecutionFailed(String),
    
    #[error("合规检查失败: {0}")]
    ComplianceCheckFailed(String),
    
    #[error("审计记录失败: {0}")]
    AuditLoggingFailed(String),
    
    #[error("报告生成失败: {0}")]
    ReportGenerationFailed(String),
    
    #[error("配置错误: {0}")]
    ConfigError(String),
    
    #[error("IO错误: {0}")]
    IoError(#[from] std::io::Error),
    
    #[error("序列化错误: {0}")]
    SerializationError(#[from] serde_json::Error),
}
