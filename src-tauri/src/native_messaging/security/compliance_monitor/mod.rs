//! 合规监控模块
//!
//! 提供标准合规检查、策略执行、审计跟踪和安全报告功能

pub mod policy_engine;
pub mod audit_logger;
pub mod compliance_checker;
pub mod security_reporter;

// 重新导出主要类型
pub use policy_engine::{PolicyEngine, PolicyRule, PolicyResult, PolicyAction};
pub use audit_logger::{AuditLogger, AuditEvent, AuditLevel};
pub use compliance_checker::{ComplianceChecker, ComplianceResult, ComplianceStandard};
pub use security_reporter::{SecurityReporter, SecurityReport, ReportType};

use crate::native_messaging::error::{NativeMessagingError, Result};
use crate::native_messaging::protocol::message::NativeMessage;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::SystemTime;
use tracing::{debug, info, warn, error};

/// 合规监控管理器
#[derive(Debug)]
pub struct ComplianceMonitor {
    policy_engine: PolicyEngine,
    audit_logger: AuditLogger,
    compliance_checker: ComplianceChe<PERSON>,
    security_reporter: SecurityReporter,
    config: ComplianceConfig,
}

impl ComplianceMonitor {
    /// 创建新的合规监控管理器
    pub fn new(config: ComplianceConfig) -> Result<Self> {
        let policy_engine = PolicyEngine::new(&config.policy_config)?;
        let audit_logger = AuditLogger::new(&config.audit_config)?;
        let compliance_checker = ComplianceChecker::new(&config.compliance_config)?;
        let security_reporter = SecurityReporter::new(&config.reporting_config)?;

        Ok(Self {
            policy_engine,
            audit_logger,
            compliance_checker,
            security_reporter,
            config,
        })
    }

    /// 监控消息合规性
    pub async fn monitor_compliance(&mut self, message: &NativeMessage) -> Result<ComplianceMonitorResult> {
        debug!("开始合规监控: {}", message.request_id);
        
        let mut result = ComplianceMonitorResult {
            message_id: message.request_id.clone(),
            is_compliant: true,
            policy_results: Vec::new(),
            compliance_results: Vec::new(),
            audit_events: Vec::new(),
            violations: Vec::new(),
            monitored_at: SystemTime::now(),
            recommended_actions: Vec::new(),
        };

        // 1. 策略检查
        if self.config.enable_policy_enforcement {
            match self.policy_engine.evaluate_policies(message).await {
                Ok(policy_results) => {
                    result.policy_results = policy_results.clone();
                    
                    // 检查是否有策略违规
                    for policy_result in &policy_results {
                        if !policy_result.is_allowed {
                            result.is_compliant = false;
                            result.violations.push(ComplianceViolation {
                                violation_type: ViolationType::PolicyViolation,
                                severity: ViolationSeverity::High,
                                description: format!("策略违规: {}", policy_result.rule_name),
                                details: policy_result.reason.clone(),
                            });
                        }
                    }
                }
                Err(e) => {
                    warn!("策略检查错误: {}", e);
                    result.violations.push(ComplianceViolation {
                        violation_type: ViolationType::SystemError,
                        severity: ViolationSeverity::Medium,
                        description: "策略检查失败".to_string(),
                        details: e.to_string(),
                    });
                }
            }
        }

        // 2. 合规标准检查
        if self.config.enable_compliance_checking {
            match self.compliance_checker.check_compliance(message).await {
                Ok(compliance_results) => {
                    result.compliance_results = compliance_results.clone();
                    
                    // 检查合规性
                    for compliance_result in &compliance_results {
                        if !compliance_result.is_compliant {
                            result.is_compliant = false;
                            result.violations.push(ComplianceViolation {
                                violation_type: ViolationType::ComplianceViolation,
                                severity: ViolationSeverity::High,
                                description: format!("合规标准违规: {:?}", compliance_result.standard),
                                details: compliance_result.violation_details.join(", "),
                            });
                        }
                    }
                }
                Err(e) => {
                    warn!("合规检查错误: {}", e);
                }
            }
        }

        // 3. 审计记录
        if self.config.enable_audit_logging {
            let audit_event = AuditEvent {
                event_id: uuid::Uuid::new_v4().to_string(),
                event_type: "compliance_check".to_string(),
                message_id: message.request_id.clone(),
                source: message.source.clone(),
                level: if result.is_compliant { AuditLevel::Info } else { AuditLevel::Warning },
                description: if result.is_compliant {
                    "合规检查通过".to_string()
                } else {
                    format!("合规检查失败: {} 个违规", result.violations.len())
                },
                metadata: self.create_audit_metadata(message, &result),
                timestamp: SystemTime::now(),
            };

            match self.audit_logger.log_event(&audit_event).await {
                Ok(_) => {
                    result.audit_events.push(audit_event);
                }
                Err(e) => {
                    warn!("审计记录失败: {}", e);
                }
            }
        }

        // 4. 生成推荐动作
        result.recommended_actions = self.generate_recommended_actions(&result);

        // 5. 安全报告（如果有违规）
        if !result.is_compliant && self.config.enable_security_reporting {
            match self.security_reporter.generate_violation_report(&result).await {
                Ok(_) => {
                    info!("安全违规报告已生成: {}", message.request_id);
                }
                Err(e) => {
                    warn!("生成安全报告失败: {}", e);
                }
            }
        }

        if result.is_compliant {
            debug!("合规监控通过: {}", message.request_id);
        } else {
            warn!("合规监控发现违规: {} - 违规数量: {}", 
                message.request_id, result.violations.len());
        }

        Ok(result)
    }

    /// 创建审计元数据
    fn create_audit_metadata(&self, message: &NativeMessage, result: &ComplianceMonitorResult) -> HashMap<String, String> {
        let mut metadata = HashMap::new();
        
        metadata.insert("message_type".to_string(), format!("{:?}", message.message_type));
        metadata.insert("source".to_string(), message.source.clone());
        metadata.insert("timestamp".to_string(), message.timestamp.to_string());
        metadata.insert("is_compliant".to_string(), result.is_compliant.to_string());
        metadata.insert("violation_count".to_string(), result.violations.len().to_string());
        
        if !result.violations.is_empty() {
            let violation_types: Vec<String> = result.violations.iter()
                .map(|v| format!("{:?}", v.violation_type))
                .collect();
            metadata.insert("violation_types".to_string(), violation_types.join(","));
        }

        metadata
    }

    /// 生成推荐动作
    fn generate_recommended_actions(&self, result: &ComplianceMonitorResult) -> Vec<ComplianceAction> {
        let mut actions = Vec::new();

        if result.is_compliant {
            actions.push(ComplianceAction::Allow);
            return actions;
        }

        // 根据违规类型和严重程度生成动作
        let has_critical = result.violations.iter().any(|v| v.severity == ViolationSeverity::Critical);
        let has_high = result.violations.iter().any(|v| v.severity == ViolationSeverity::High);

        if has_critical {
            actions.push(ComplianceAction::Block);
            actions.push(ComplianceAction::Alert);
            actions.push(ComplianceAction::Quarantine);
        } else if has_high {
            actions.push(ComplianceAction::Block);
            actions.push(ComplianceAction::Alert);
        } else {
            actions.push(ComplianceAction::Monitor);
            actions.push(ComplianceAction::Log);
        }

        // 总是记录违规
        actions.push(ComplianceAction::Log);

        actions
    }

    /// 获取合规统计信息
    pub async fn get_compliance_stats(&self) -> ComplianceStats {
        ComplianceStats {
            total_messages_monitored: 0, // 需要实现统计跟踪
            compliant_messages: 0,
            violation_count: 0,
            policy_violations: 0,
            compliance_violations: 0,
            audit_events_logged: 0,
            reports_generated: 0,
        }
    }

    /// 生成合规报告
    pub async fn generate_compliance_report(&mut self, report_type: ReportType) -> Result<SecurityReport> {
        self.security_reporter.generate_report(report_type).await
    }
}

/// 合规监控配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceConfig {
    /// 启用策略执行
    pub enable_policy_enforcement: bool,
    /// 启用合规检查
    pub enable_compliance_checking: bool,
    /// 启用审计记录
    pub enable_audit_logging: bool,
    /// 启用安全报告
    pub enable_security_reporting: bool,
    /// 策略引擎配置
    pub policy_config: PolicyEngineConfig,
    /// 审计配置
    pub audit_config: AuditLoggerConfig,
    /// 合规检查配置
    pub compliance_config: ComplianceCheckerConfig,
    /// 报告配置
    pub reporting_config: SecurityReporterConfig,
}

impl Default for ComplianceConfig {
    fn default() -> Self {
        Self {
            enable_policy_enforcement: true,
            enable_compliance_checking: true,
            enable_audit_logging: true,
            enable_security_reporting: true,
            policy_config: PolicyEngineConfig::default(),
            audit_config: AuditLoggerConfig::default(),
            compliance_config: ComplianceCheckerConfig::default(),
            reporting_config: SecurityReporterConfig::default(),
        }
    }
}

/// 合规监控结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceMonitorResult {
    /// 消息ID
    pub message_id: String,
    /// 是否合规
    pub is_compliant: bool,
    /// 策略检查结果
    pub policy_results: Vec<PolicyResult>,
    /// 合规检查结果
    pub compliance_results: Vec<ComplianceResult>,
    /// 审计事件
    pub audit_events: Vec<AuditEvent>,
    /// 违规记录
    pub violations: Vec<ComplianceViolation>,
    /// 监控时间
    pub monitored_at: SystemTime,
    /// 推荐动作
    pub recommended_actions: Vec<ComplianceAction>,
}

/// 合规违规记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceViolation {
    /// 违规类型
    pub violation_type: ViolationType,
    /// 严重程度
    pub severity: ViolationSeverity,
    /// 描述
    pub description: String,
    /// 详细信息
    pub details: String,
}

/// 违规类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ViolationType {
    /// 策略违规
    PolicyViolation,
    /// 合规标准违规
    ComplianceViolation,
    /// 系统错误
    SystemError,
    /// 配置错误
    ConfigurationError,
}

/// 违规严重程度
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ViolationSeverity {
    /// 低
    Low,
    /// 中
    Medium,
    /// 高
    High,
    /// 严重
    Critical,
}

/// 合规动作
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ComplianceAction {
    /// 允许
    Allow,
    /// 阻止
    Block,
    /// 监控
    Monitor,
    /// 记录日志
    Log,
    /// 告警
    Alert,
    /// 隔离
    Quarantine,
    /// 审查
    Review,
}

/// 合规统计信息
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct ComplianceStats {
    /// 监控的消息总数
    pub total_messages_monitored: u64,
    /// 合规消息数
    pub compliant_messages: u64,
    /// 违规总数
    pub violation_count: u64,
    /// 策略违规数
    pub policy_violations: u64,
    /// 合规标准违规数
    pub compliance_violations: u64,
    /// 记录的审计事件数
    pub audit_events_logged: u64,
    /// 生成的报告数
    pub reports_generated: u64,
}

// 占位符配置类型，将在各自的文件中实现
pub use policy_engine::PolicyEngineConfig;
pub use audit_logger::AuditLoggerConfig;
pub use compliance_checker::ComplianceCheckerConfig;
pub use security_reporter::SecurityReporterConfig;

/// 合规监控错误
#[derive(Debug, thiserror::Error)]
pub enum ComplianceMonitorError {
    #[error("策略执行失败: {0}")]
    PolicyExecutionFailed(String),
    
    #[error("合规检查失败: {0}")]
    ComplianceCheckFailed(String),
    
    #[error("审计记录失败: {0}")]
    AuditLoggingFailed(String),
    
    #[error("报告生成失败: {0}")]
    ReportGenerationFailed(String),
    
    #[error("配置错误: {0}")]
    ConfigError(String),
    
    #[error("IO错误: {0}")]
    IoError(#[from] std::io::Error),
    
    #[error("序列化错误: {0}")]
    SerializationError(#[from] serde_json::Error),
}
