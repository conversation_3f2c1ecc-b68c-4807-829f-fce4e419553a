//! Native Messaging 安全模块
//!
//! 提供认证授权、加密传输和审计日志功能

pub mod auth;
pub mod crypto;
pub mod audit;
pub mod browser_auth;
pub mod protocol_guard;
pub mod message_security;
pub mod threat_detection;
pub mod compliance_monitor;

// 重新导出主要类型和接口
pub use auth::{
    AuthContext, AuthManager, AuthStatistics, Permission, SessionManager,
};
pub use crypto::{
    CryptoConfig, CryptoKey, CryptoManager, EncryptedMessage, KeyStatisticsInfo,
};
pub use audit::{
    AuditConfig, AuditEvent, AuditEventFilter, AuditEventType, AuditLogger, 
    AuditResult, AuditSeverity, AuditStatisticsInfo,
};

// 导出协议防护器
pub use protocol_guard::{
    ProtocolGuard, ProtocolGuardConfig, ProtocolSecurityLevel, 
    SecurityRule, SecurityAction, ThreatLevel
};

use tracing::info;
use crate::native_messaging::config::SecurityConfig;

/// 统一的安全管理器
/// 
/// 整合认证、加密和审计功能，提供统一的安全服务接口
#[derive(Debug)]
pub struct SecurityManager {
    /// 认证管理器
    auth_manager: AuthManager,
    /// 加密管理器
    crypto_manager: Option<CryptoManager>,
    /// 审计日志管理器
    audit_logger: AuditLogger,
    /// 是否启用加密
    encryption_enabled: bool,
}

impl SecurityManager {
    /// 创建新的安全管理器
    pub fn new(security_config: &SecurityConfig) -> Self {
        // 创建认证管理器
        let auth_manager = AuthManager::new(security_config);
        
        // 创建加密管理器（如果启用）
        let crypto_manager = if security_config.enable_encryption {
            let crypto_config = CryptoConfig {
                enable_encryption: true,
                enable_integrity_check: true,
                key_rotation_interval: 3600, // 1小时
                max_message_size: 1024 * 1024, // 1MB
            };
            Some(CryptoManager::new(crypto_config))
        } else {
            None
        };
        
        // 创建审计日志管理器
        let audit_config = AuditConfig {
            enabled: security_config.enable_audit_log,
            log_file_path: Some("audit.log".to_string()),
            detailed_logging: true,
            retention_days: 90,
            max_file_size: 100 * 1024 * 1024, // 100MB
            enable_real_time_analysis: true,
            high_risk_threshold: 70,
        };
        let audit_logger = AuditLogger::new(audit_config);
        
        Self {
            auth_manager,
            crypto_manager,
            audit_logger,
            encryption_enabled: security_config.enable_encryption,
        }
    }
    
    /// 获取认证管理器引用
    pub fn auth_manager(&self) -> &AuthManager {
        &self.auth_manager
    }
    
    /// 获取加密管理器引用
    pub fn crypto_manager(&self) -> Option<&CryptoManager> {
        self.crypto_manager.as_ref()
    }
    
    /// 获取审计日志管理器引用
    pub fn audit_logger(&self) -> &AuditLogger {
        &self.audit_logger
    }
    
    /// 检查是否启用加密
    pub fn is_encryption_enabled(&self) -> bool {
        self.encryption_enabled
    }
    
    /// 清理过期数据
    pub async fn cleanup(&self) -> SecurityCleanupResult {
        let mut result = SecurityCleanupResult::default();
        
        // 清理过期会话
        result.expired_sessions = self.auth_manager.cleanup_expired_sessions().await;
        
        // 清理过期密钥
        if let Some(ref crypto_manager) = self.crypto_manager {
            result.expired_keys = crypto_manager.cleanup_expired_keys().await;
        }
        
        // 清理过期审计事件
        result.expired_audit_events = self.audit_logger.cleanup_old_events().await;
        
        if result.has_cleanup() {
            info!("安全数据清理完成: {:?}", result);
        }
        
        result
    }
    
    /// 获取安全统计信息
    pub async fn get_security_statistics(&self) -> SecurityStatistics {
        let auth_stats = self.auth_manager.get_auth_statistics().await;
        let crypto_stats = if let Some(ref crypto_manager) = self.crypto_manager {
            Some(crypto_manager.get_statistics().await)
        } else {
            None
        };
        let audit_stats = self.audit_logger.get_statistics().await;
        
        SecurityStatistics {
            auth_statistics: auth_stats,
            crypto_statistics: crypto_stats,
            audit_statistics: audit_stats,
        }
    }
}

impl Default for SecurityManager {
    fn default() -> Self {
        let config = SecurityConfig::default();
        Self::new(&config)
    }
}

/// 安全清理结果
#[derive(Debug, Default)]
pub struct SecurityCleanupResult {
    /// 清理的过期会话数
    pub expired_sessions: usize,
    /// 清理的过期密钥数
    pub expired_keys: usize,
    /// 清理的过期审计事件数
    pub expired_audit_events: usize,
}

impl SecurityCleanupResult {
    /// 检查是否有清理操作
    pub fn has_cleanup(&self) -> bool {
        self.expired_sessions > 0 || self.expired_keys > 0 || self.expired_audit_events > 0
    }
    
    /// 获取总清理数量
    pub fn total_cleaned(&self) -> usize {
        self.expired_sessions + self.expired_keys + self.expired_audit_events
    }
}

/// 安全统计信息
#[derive(Debug)]
pub struct SecurityStatistics {
    /// 认证统计
    pub auth_statistics: AuthStatistics,
    /// 加密统计（如果启用）
    pub crypto_statistics: Option<KeyStatisticsInfo>,
    /// 审计统计
    pub audit_statistics: AuditStatisticsInfo,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::native_messaging::config::SecurityConfig;

    #[tokio::test]
    async fn test_security_manager_creation() {
        let config = SecurityConfig::default();
        let manager = SecurityManager::new(&config);
        
        // 验证组件存在并且可以获取统计信息
        let auth_stats = manager.auth_manager().get_auth_statistics().await;
        let audit_stats = manager.audit_logger().get_statistics().await;
        
        assert_eq!(auth_stats.total_attempts, 0);
        assert_eq!(audit_stats.total_events, 0);
    }

    #[test]
    fn test_security_manager_encryption_disabled() {
        let mut config = SecurityConfig::default();
        config.enable_encryption = false;
        
        let manager = SecurityManager::new(&config);
        assert!(!manager.is_encryption_enabled());
        assert!(manager.crypto_manager().is_none());
    }

    #[test]
    fn test_security_manager_encryption_enabled() {
        let mut config = SecurityConfig::default();
        config.enable_encryption = true;
        
        let manager = SecurityManager::new(&config);
        assert!(manager.is_encryption_enabled());
        assert!(manager.crypto_manager().is_some());
    }

    #[tokio::test]
    async fn test_security_cleanup() {
        let config = SecurityConfig::default();
        let manager = SecurityManager::new(&config);
        
        let result = manager.cleanup().await;
        // 新创建的管理器应该没有过期数据要清理
        assert!(!result.has_cleanup());
        assert_eq!(result.total_cleaned(), 0);
    }

    #[tokio::test]
    async fn test_security_statistics() {
        let config = SecurityConfig::default();
        let manager = SecurityManager::new(&config);
        
        let stats = manager.get_security_statistics().await;
        assert_eq!(stats.auth_statistics.total_attempts, 0);
        assert_eq!(stats.audit_statistics.total_events, 0);
    }

    #[test]
    fn test_cleanup_result() {
        let mut result = SecurityCleanupResult::default();
        assert!(!result.has_cleanup());
        assert_eq!(result.total_cleaned(), 0);
        
        result.expired_sessions = 5;
        result.expired_keys = 3;
        result.expired_audit_events = 10;
        
        assert!(result.has_cleanup());
        assert_eq!(result.total_cleaned(), 18);
    }
}
