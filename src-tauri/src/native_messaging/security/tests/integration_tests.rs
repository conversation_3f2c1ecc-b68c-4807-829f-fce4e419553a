//! 安全验证组件集成测试
//!
//! 提供全面的集成测试，确保各个安全组件协同工作正常

use crate::native_messaging::protocol::message::{NativeMessage, MessageType};
use crate::native_messaging::security::{
    browser_auth::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BrowserAuthConfig},
    protocol_guard::{ProtocolGuard, ProtocolGuardConfig},
    message_security::{MessageSecurityValidator, MessageSecurityConfig},
    threat_detection::{ThreatDetector, ThreatDetectionConfig},
    compliance_monitor::{ComplianceMonitor, ComplianceConfig},
};
use serde_json::json;
use std::time::SystemTime;
use tokio;

/// 集成测试套件
#[cfg(test)]
mod integration_tests {
    use super::*;

    /// 测试完整的安全验证流程
    #[tokio::test]
    async fn test_complete_security_validation_flow() {
        // 1. 初始化所有安全组件
        let browser_auth = create_test_browser_authenticator().await;
        let protocol_guard = create_test_protocol_guard().await;
        let message_security = create_test_message_security_validator().await;
        let threat_detector = create_test_threat_detector().await;
        let mut compliance_monitor = create_test_compliance_monitor().await;

        // 2. 创建测试消息
        let test_message = create_test_message();

        // 3. 执行完整的安全验证流程
        
        // 浏览器身份验证
        let auth_result = browser_auth.authenticate(&test_message).await;
        assert!(auth_result.is_ok(), "浏览器身份验证应该成功");
        let auth_result = auth_result.unwrap();
        assert!(auth_result.is_authenticated, "消息应该通过身份验证");

        // 协议防护检查
        let protocol_result = protocol_guard.validate_message(&test_message).await;
        assert!(protocol_result.is_ok(), "协议防护检查应该成功");
        let protocol_result = protocol_result.unwrap();
        assert!(protocol_result.is_valid, "消息应该通过协议验证");

        // 消息安全验证
        let security_result = message_security.validate_message(&test_message).await;
        assert!(security_result.is_ok(), "消息安全验证应该成功");
        let security_result = security_result.unwrap();
        assert!(security_result.is_secure, "消息应该通过安全验证");

        // 威胁检测
        let threat_result = threat_detector.analyze_message(&test_message).await;
        assert!(threat_result.is_ok(), "威胁检测应该成功");
        let threat_result = threat_result.unwrap();
        assert!(!threat_result.is_threat, "正常消息不应该被识别为威胁");

        // 合规监控
        let compliance_result = compliance_monitor.monitor_compliance(&test_message).await;
        assert!(compliance_result.is_ok(), "合规监控应该成功");
        let compliance_result = compliance_result.unwrap();
        assert!(compliance_result.is_compliant, "消息应该符合合规要求");

        println!("✅ 完整安全验证流程测试通过");
    }

    /// 测试恶意消息的检测
    #[tokio::test]
    async fn test_malicious_message_detection() {
        let threat_detector = create_test_threat_detector().await;
        let mut compliance_monitor = create_test_compliance_monitor().await;

        // 创建包含恶意内容的测试消息
        let malicious_message = create_malicious_test_message();

        // 威胁检测应该识别出威胁
        let threat_result = threat_detector.analyze_message(&malicious_message).await;
        assert!(threat_result.is_ok(), "威胁检测应该成功执行");
        let threat_result = threat_result.unwrap();
        assert!(threat_result.is_threat, "恶意消息应该被识别为威胁");
        assert!(!threat_result.detected_threats.is_empty(), "应该检测到具体的威胁类型");

        // 合规监控应该发现违规
        let compliance_result = compliance_monitor.monitor_compliance(&malicious_message).await;
        assert!(compliance_result.is_ok(), "合规监控应该成功执行");
        let compliance_result = compliance_result.unwrap();
        assert!(!compliance_result.is_compliant, "恶意消息应该不符合合规要求");
        assert!(!compliance_result.violations.is_empty(), "应该记录具体的违规信息");

        println!("✅ 恶意消息检测测试通过");
    }

    /// 测试性能基准
    #[tokio::test]
    async fn test_performance_benchmark() {
        let browser_auth = create_test_browser_authenticator().await;
        let protocol_guard = create_test_protocol_guard().await;
        let message_security = create_test_message_security_validator().await;
        let threat_detector = create_test_threat_detector().await;
        let mut compliance_monitor = create_test_compliance_monitor().await;

        let test_message = create_test_message();
        let test_count = 100;

        // 测试浏览器身份验证性能
        let start_time = std::time::Instant::now();
        for _ in 0..test_count {
            let _ = browser_auth.authenticate(&test_message).await;
        }
        let auth_duration = start_time.elapsed();
        let auth_avg_ms = auth_duration.as_millis() as f64 / test_count as f64;
        assert!(auth_avg_ms < 5.0, "浏览器身份验证平均耗时应小于5ms，实际: {}ms", auth_avg_ms);

        // 测试协议防护性能
        let start_time = std::time::Instant::now();
        for _ in 0..test_count {
            let _ = protocol_guard.validate_message(&test_message).await;
        }
        let protocol_duration = start_time.elapsed();
        let protocol_avg_ms = protocol_duration.as_millis() as f64 / test_count as f64;
        assert!(protocol_avg_ms < 5.0, "协议防护平均耗时应小于5ms，实际: {}ms", protocol_avg_ms);

        // 测试消息安全验证性能
        let start_time = std::time::Instant::now();
        for _ in 0..test_count {
            let _ = message_security.validate_message(&test_message).await;
        }
        let security_duration = start_time.elapsed();
        let security_avg_ms = security_duration.as_millis() as f64 / test_count as f64;
        assert!(security_avg_ms < 5.0, "消息安全验证平均耗时应小于5ms，实际: {}ms", security_avg_ms);

        // 测试威胁检测性能
        let start_time = std::time::Instant::now();
        for _ in 0..test_count {
            let _ = threat_detector.analyze_message(&test_message).await;
        }
        let threat_duration = start_time.elapsed();
        let threat_avg_ms = threat_duration.as_millis() as f64 / test_count as f64;
        assert!(threat_avg_ms < 5.0, "威胁检测平均耗时应小于5ms，实际: {}ms", threat_avg_ms);

        // 测试合规监控性能
        let start_time = std::time::Instant::now();
        for _ in 0..test_count {
            let _ = compliance_monitor.monitor_compliance(&test_message).await;
        }
        let compliance_duration = start_time.elapsed();
        let compliance_avg_ms = compliance_duration.as_millis() as f64 / test_count as f64;
        assert!(compliance_avg_ms < 5.0, "合规监控平均耗时应小于5ms，实际: {}ms", compliance_avg_ms);

        println!("✅ 性能基准测试通过");
        println!("   - 浏览器身份验证: {:.2}ms", auth_avg_ms);
        println!("   - 协议防护: {:.2}ms", protocol_avg_ms);
        println!("   - 消息安全验证: {:.2}ms", security_avg_ms);
        println!("   - 威胁检测: {:.2}ms", threat_avg_ms);
        println!("   - 合规监控: {:.2}ms", compliance_avg_ms);
    }

    /// 测试边界条件
    #[tokio::test]
    async fn test_boundary_conditions() {
        let protocol_guard = create_test_protocol_guard().await;
        let message_security = create_test_message_security_validator().await;

        // 测试空消息
        let empty_message = create_empty_test_message();
        let protocol_result = protocol_guard.validate_message(&empty_message).await;
        assert!(protocol_result.is_ok(), "空消息处理应该成功");

        // 测试超大消息
        let large_message = create_large_test_message();
        let security_result = message_security.validate_message(&large_message).await;
        assert!(security_result.is_ok(), "超大消息处理应该成功");

        // 测试格式错误的消息
        let invalid_message = create_invalid_test_message();
        let protocol_result = protocol_guard.validate_message(&invalid_message).await;
        // 格式错误的消息应该被拒绝或处理为错误
        assert!(protocol_result.is_err() || !protocol_result.unwrap().is_valid, 
               "格式错误的消息应该被拒绝");

        println!("✅ 边界条件测试通过");
    }

    /// 测试错误处理
    #[tokio::test]
    async fn test_error_handling() {
        // 测试配置错误的组件
        let invalid_config = BrowserAuthConfig {
            enable_extension_verification: true,
            enable_certificate_validation: true,
            enable_origin_validation: true,
            enable_whitelist_management: true,
            trusted_extensions: vec![], // 空的信任列表
            certificate_store_path: "invalid/path".to_string(), // 无效路径
            max_certificate_chain_length: 0, // 无效配置
            certificate_validation_timeout: std::time::Duration::from_secs(0), // 无效超时
        };

        let auth_result = BrowserAuthenticator::new(invalid_config).await;
        // 应该能够处理配置错误
        assert!(auth_result.is_ok() || auth_result.is_err(), "应该能够处理配置错误");

        println!("✅ 错误处理测试通过");
    }

    // ========================================================================
    // 辅助函数
    // ========================================================================

    /// 创建测试用的浏览器认证器
    async fn create_test_browser_authenticator() -> BrowserAuthenticator {
        let config = BrowserAuthConfig::default();
        BrowserAuthenticator::new(config).await.expect("创建浏览器认证器失败")
    }

    /// 创建测试用的协议防护器
    async fn create_test_protocol_guard() -> ProtocolGuard {
        let config = ProtocolGuardConfig::default();
        ProtocolGuard::new(config).await.expect("创建协议防护器失败")
    }

    /// 创建测试用的消息安全验证器
    async fn create_test_message_security_validator() -> MessageSecurityValidator {
        let config = MessageSecurityConfig::default();
        MessageSecurityValidator::new(config).await.expect("创建消息安全验证器失败")
    }

    /// 创建测试用的威胁检测器
    async fn create_test_threat_detector() -> ThreatDetector {
        let config = ThreatDetectionConfig::default();
        ThreatDetector::new(config).await.expect("创建威胁检测器失败")
    }

    /// 创建测试用的合规监控器
    async fn create_test_compliance_monitor() -> ComplianceMonitor {
        let config = ComplianceConfig::default();
        ComplianceMonitor::new(config).expect("创建合规监控器失败")
    }

    /// 创建测试消息
    fn create_test_message() -> NativeMessage {
        NativeMessage {
            request_id: "test-request-001".to_string(),
            message_type: MessageType::PasswordGenerate,
            source: "test-extension".to_string(),
            timestamp: SystemTime::now(),
            payload: json!({
                "action": "generate_password",
                "length": 16,
                "include_symbols": true
            }),
            signature: Some("test-signature".to_string()),
        }
    }

    /// 创建恶意测试消息
    fn create_malicious_test_message() -> NativeMessage {
        NativeMessage {
            request_id: "malicious-request-001".to_string(),
            message_type: MessageType::PasswordGenerate,
            source: "unknown-extension".to_string(),
            timestamp: SystemTime::now(),
            payload: json!({
                "action": "generate_password",
                "length": 999999, // 异常大的长度
                "malicious_script": "<script>alert('xss')</script>", // XSS尝试
                "sql_injection": "'; DROP TABLE users; --" // SQL注入尝试
            }),
            signature: None, // 缺少签名
        }
    }

    /// 创建空测试消息
    fn create_empty_test_message() -> NativeMessage {
        NativeMessage {
            request_id: "".to_string(),
            message_type: MessageType::PasswordGenerate,
            source: "".to_string(),
            timestamp: SystemTime::now(),
            payload: json!({}),
            signature: None,
        }
    }

    /// 创建超大测试消息
    fn create_large_test_message() -> NativeMessage {
        let large_data = "x".repeat(1024 * 1024); // 1MB数据
        NativeMessage {
            request_id: "large-request-001".to_string(),
            message_type: MessageType::PasswordGenerate,
            source: "test-extension".to_string(),
            timestamp: SystemTime::now(),
            payload: json!({
                "action": "generate_password",
                "large_data": large_data
            }),
            signature: Some("test-signature".to_string()),
        }
    }

    /// 创建格式错误的测试消息
    fn create_invalid_test_message() -> NativeMessage {
        NativeMessage {
            request_id: "invalid-request-001".to_string(),
            message_type: MessageType::PasswordGenerate,
            source: "test-extension".to_string(),
            timestamp: SystemTime::UNIX_EPOCH, // 无效时间戳
            payload: json!({
                "invalid_field": null,
                "nested": {
                    "deeply": {
                        "nested": {
                            "data": "with circular reference"
                        }
                    }
                }
            }),
            signature: Some("invalid-signature-format".to_string()),
        }
    }
}
