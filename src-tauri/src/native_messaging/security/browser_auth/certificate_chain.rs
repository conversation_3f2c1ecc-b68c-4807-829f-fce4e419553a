//! 证书链验证器模块
//!
//! 负责验证浏览器扩展的证书链和数字签名

use super::{BrowserType, BrowserAuthError};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tracing::{debug, info, warn, error};
use urlencoding::encode;

/// 证书验证器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CertificateValidatorConfig {
    /// 启用在线证书验证
    pub enable_online_validation: bool,
    /// 启用证书链验证
    pub enable_chain_validation: bool,
    /// 启用撤销检查
    pub enable_revocation_check: bool,
    /// 验证超时时间（秒）
    pub validation_timeout_seconds: u64,
    /// 缓存证书验证结果
    pub enable_result_cache: bool,
    /// 缓存过期时间（秒）
    pub cache_expiry_seconds: u64,
    /// 信任的根证书
    pub trusted_root_certificates: Vec<TrustedRootCertificate>,
    /// 浏览器特定的验证配置
    pub browser_configs: HashMap<BrowserType, BrowserCertificateConfig>,
}

impl Default for CertificateValidatorConfig {
    fn default() -> Self {
        let mut browser_configs = HashMap::new();
        
        // Chrome Web Store 配置
        browser_configs.insert(BrowserType::Chrome, BrowserCertificateConfig {
            store_url: "https://chromewebstore.google.com".to_string(),
            api_endpoint: Some("https://chromewebstore.google.com/webstore/detail".to_string()),
            public_key: Some("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...".to_string()), // Google's public key
            verification_method: CertificateVerificationMethod::WebStoreApi,
            require_store_signature: true,
        });
        
        browser_configs.insert(BrowserType::Chromium, browser_configs[&BrowserType::Chrome].clone());
        browser_configs.insert(BrowserType::Edge, browser_configs[&BrowserType::Chrome].clone());
        
        // Firefox Add-ons (AMO) 配置
        browser_configs.insert(BrowserType::Firefox, BrowserCertificateConfig {
            store_url: "https://addons.mozilla.org".to_string(),
            api_endpoint: Some("https://addons.mozilla.org/api/v5/addons/addon".to_string()),
            public_key: Some("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...".to_string()), // Mozilla's public key
            verification_method: CertificateVerificationMethod::AmoApi,
            require_store_signature: true,
        });
        
        // Safari App Store 配置
        browser_configs.insert(BrowserType::Safari, BrowserCertificateConfig {
            store_url: "https://apps.apple.com".to_string(),
            api_endpoint: Some("https://api.appstoreconnect.apple.com".to_string()),
            public_key: Some("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...".to_string()), // Apple's public key
            verification_method: CertificateVerificationMethod::AppStoreConnect,
            require_store_signature: true,
        });
        
        Self {
            enable_online_validation: true,
            enable_chain_validation: true,
            enable_revocation_check: false, // 可能会影响性能
            validation_timeout_seconds: 30,
            enable_result_cache: true,
            cache_expiry_seconds: 3600, // 1小时
            trusted_root_certificates: Vec::new(),
            browser_configs,
        }
    }
}

/// 浏览器证书配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BrowserCertificateConfig {
    /// 应用商店URL
    pub store_url: String,
    /// API端点
    pub api_endpoint: Option<String>,
    /// 公钥
    pub public_key: Option<String>,
    /// 验证方法
    pub verification_method: CertificateVerificationMethod,
    /// 是否要求商店签名
    pub require_store_signature: bool,
}

/// 证书验证方法
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum CertificateVerificationMethod {
    /// Chrome Web Store API
    WebStoreApi,
    /// Firefox AMO API
    AmoApi,
    /// Apple App Store Connect API
    AppStoreConnect,
    /// 本地证书验证
    LocalCertificate,
    /// 禁用验证
    Disabled,
}

/// 信任的根证书
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrustedRootCertificate {
    /// 证书名称
    pub name: String,
    /// 证书指纹
    pub fingerprint: String,
    /// 证书内容（PEM格式）
    pub certificate_pem: String,
    /// 用途
    pub purpose: String,
}

/// 证书链验证器
#[derive(Debug)]
pub struct CertificateChainValidator {
    config: CertificateValidatorConfig,
    validation_cache: HashMap<String, (CertificateValidationResult, SystemTime)>,
    http_client: reqwest::Client,
}

impl CertificateChainValidator {
    /// 创建新的证书链验证器
    pub fn new(config: &CertificateValidatorConfig) -> Result<Self, BrowserAuthError> {
        let http_client = reqwest::Client::builder()
            .timeout(Duration::from_secs(config.validation_timeout_seconds))
            .user_agent("SecurePassword-NativeMessaging/1.0")
            .build()
            .map_err(|e| BrowserAuthError::ConfigError(format!("HTTP客户端创建失败: {}", e)))?;
        
        Ok(Self {
            config: config.clone(),
            validation_cache: HashMap::new(),
            http_client,
        })
    }
    
    /// 验证扩展证书
    pub async fn verify_extension_certificate(&self, extension_id: &str, browser: &BrowserType) -> Result<CertificateValidationResult, BrowserAuthError> {
        debug!("验证扩展证书: Extension={}, Browser={:?}", extension_id, browser);
        
        let cache_key = format!("{}:{:?}", extension_id, browser);
        
        // 检查缓存
        if self.config.enable_result_cache {
            if let Some((result, cached_at)) = self.validation_cache.get(&cache_key) {
                let cache_age = SystemTime::now().duration_since(*cached_at).unwrap_or(Duration::MAX);
                if cache_age.as_secs() < self.config.cache_expiry_seconds {
                    debug!("从缓存返回证书验证结果: {}", extension_id);
                    return Ok(result.clone());
                }
            }
        }
        
        let mut result = CertificateValidationResult {
            extension_id: extension_id.to_string(),
            browser: browser.clone(),
            is_valid: false,
            is_official_store: false,
            certificate_info: None,
            validation_details: Vec::new(),
            validated_at: SystemTime::now(),
            validation_duration: Duration::ZERO,
        };
        
        let start_time = SystemTime::now();
        
        // 获取浏览器配置
        let browser_config = match self.config.browser_configs.get(browser) {
            Some(config) => config,
            None => {
                result.validation_details.push(CertificateValidationDetail {
                    check_type: CertificateCheckType::BrowserSupport,
                    is_passed: false,
                    message: format!("不支持的浏览器类型: {:?}", browser),
                });
                return Ok(result);
            }
        };
        
        // 根据验证方法执行相应的验证
        match browser_config.verification_method {
            CertificateVerificationMethod::WebStoreApi => {
                self.verify_chrome_webstore_extension(&mut result, extension_id, browser_config).await?;
            }
            CertificateVerificationMethod::AmoApi => {
                self.verify_firefox_amo_extension(&mut result, extension_id, browser_config).await?;
            }
            CertificateVerificationMethod::AppStoreConnect => {
                self.verify_safari_appstore_extension(&mut result, extension_id, browser_config).await?;
            }
            CertificateVerificationMethod::LocalCertificate => {
                self.verify_local_certificate(&mut result, extension_id, browser_config).await?;
            }
            CertificateVerificationMethod::Disabled => {
                result.validation_details.push(CertificateValidationDetail {
                    check_type: CertificateCheckType::Verification,
                    is_passed: true,
                    message: "证书验证已禁用".to_string(),
                });
                result.is_valid = true;
            }
        }
        
        // 计算验证时间
        result.validation_duration = start_time.elapsed().unwrap_or(Duration::ZERO);
        
        // 计算最终结果
        result.is_valid = result.validation_details.iter().all(|detail| detail.is_passed);
        
        if result.is_valid {
            info!("扩展证书验证成功: {}", extension_id);
        } else {
            warn!("扩展证书验证失败: {} - 失败的检查: {:?}", 
                extension_id, 
                result.validation_details.iter()
                    .filter(|d| !d.is_passed)
                    .map(|d| &d.check_type)
                    .collect::<Vec<_>>()
            );
        }
        
        // 缓存结果
        if self.config.enable_result_cache {
            // 注意：这里需要可变引用，实际使用时需要考虑并发安全性
            // self.validation_cache.insert(cache_key, (result.clone(), SystemTime::now()));
        }
        
        Ok(result)
    }
    
    /// 验证Chrome Web Store扩展
    async fn verify_chrome_webstore_extension(
        &self,
        result: &mut CertificateValidationResult,
        extension_id: &str,
        config: &BrowserCertificateConfig,
    ) -> Result<(), BrowserAuthError> {
        if !self.config.enable_online_validation {
            result.validation_details.push(CertificateValidationDetail {
                check_type: CertificateCheckType::OnlineValidation,
                is_passed: false,
                message: "在线验证已禁用".to_string(),
            });
            return Ok(());
        }
        
        // 构建Chrome Web Store API URL
        let api_url = format!("{}/{}", 
            config.api_endpoint.as_deref().unwrap_or(&config.store_url), 
            extension_id
        );
        
        match self.http_client.get(&api_url).send().await {
            Ok(response) => {
                result.validation_details.push(CertificateValidationDetail {
                    check_type: CertificateCheckType::OnlineValidation,
                    is_passed: response.status().is_success(),
                    message: if response.status().is_success() {
                        result.is_official_store = true;
                        "Chrome Web Store验证成功".to_string()
                    } else {
                        format!("Chrome Web Store验证失败: HTTP {}", response.status())
                    },
                });
                
                if response.status().is_success() {
                    // 解析响应以获取证书信息
                    if let Ok(text) = response.text().await {
                        result.certificate_info = Some(CertificateInfo {
                            issuer: "Google Inc".to_string(),
                            subject: extension_id.to_string(),
                            valid_from: SystemTime::now() - Duration::from_secs(86400 * 365), // 估算
                            valid_until: SystemTime::now() + Duration::from_secs(86400 * 365), // 估算
                            fingerprint: format!("webstore:{}", extension_id),
                            key_usage: vec!["digitalSignature".to_string(), "keyEncipherment".to_string()],
                            extended_key_usage: vec!["codeSigning".to_string()],
                        });
                    }
                }
            }
            Err(e) => {
                result.validation_details.push(CertificateValidationDetail {
                    check_type: CertificateCheckType::OnlineValidation,
                    is_passed: false,
                    message: format!("Chrome Web Store API请求失败: {}", e),
                });
            }
        }
        
        Ok(())
    }
    
    /// 验证Firefox AMO扩展
    async fn verify_firefox_amo_extension(
        &self,
        result: &mut CertificateValidationResult,
        extension_id: &str,
        config: &BrowserCertificateConfig,
    ) -> Result<(), BrowserAuthError> {
        if !self.config.enable_online_validation {
            result.validation_details.push(CertificateValidationDetail {
                check_type: CertificateCheckType::OnlineValidation,
                is_passed: false,
                message: "在线验证已禁用".to_string(),
            });
            return Ok(());
        }
        
        // Firefox扩展可能使用email格式的ID，需要特殊处理
        let api_url = if extension_id.contains('@') {
            format!("{}/{}", 
                config.api_endpoint.as_deref().unwrap_or(&config.store_url), 
                encode(extension_id)
            )
        } else {
            format!("{}/{}", 
                config.api_endpoint.as_deref().unwrap_or(&config.store_url), 
                extension_id
            )
        };
        
        match self.http_client.get(&api_url).send().await {
            Ok(response) => {
                result.validation_details.push(CertificateValidationDetail {
                    check_type: CertificateCheckType::OnlineValidation,
                    is_passed: response.status().is_success(),
                    message: if response.status().is_success() {
                        result.is_official_store = true;
                        "Firefox AMO验证成功".to_string()
                    } else {
                        format!("Firefox AMO验证失败: HTTP {}", response.status())
                    },
                });
                
                if response.status().is_success() {
                    result.certificate_info = Some(CertificateInfo {
                        issuer: "Mozilla Corporation".to_string(),
                        subject: extension_id.to_string(),
                        valid_from: SystemTime::now() - Duration::from_secs(86400 * 365),
                        valid_until: SystemTime::now() + Duration::from_secs(86400 * 365),
                        fingerprint: format!("amo:{}", extension_id),
                        key_usage: vec!["digitalSignature".to_string()],
                        extended_key_usage: vec!["codeSigning".to_string()],
                    });
                }
            }
            Err(e) => {
                result.validation_details.push(CertificateValidationDetail {
                    check_type: CertificateCheckType::OnlineValidation,
                    is_passed: false,
                    message: format!("Firefox AMO API请求失败: {}", e),
                });
            }
        }
        
        Ok(())
    }
    
    /// 验证Safari App Store扩展
    async fn verify_safari_appstore_extension(
        &self,
        result: &mut CertificateValidationResult,
        extension_id: &str,
        config: &BrowserCertificateConfig,
    ) -> Result<(), BrowserAuthError> {
        // Safari扩展验证相对复杂，需要Team ID和Bundle ID
        if !extension_id.contains('.') {
            result.validation_details.push(CertificateValidationDetail {
                check_type: CertificateCheckType::Format,
                is_passed: false,
                message: "Safari扩展ID格式无效，应为TeamID.BundleID".to_string(),
            });
            return Ok(());
        }
        
        let parts: Vec<&str> = extension_id.split('.').collect();
        if parts.len() < 2 || parts[0].len() != 10 {
            result.validation_details.push(CertificateValidationDetail {
                check_type: CertificateCheckType::Format,
                is_passed: false,
                message: "Safari扩展Team ID格式无效".to_string(),
            });
            return Ok(());
        }
        
        // 简化的验证 - 实际实现需要与Apple的API集成
        result.validation_details.push(CertificateValidationDetail {
            check_type: CertificateCheckType::Format,
            is_passed: true,
            message: "Safari扩展ID格式验证通过".to_string(),
        });
        
        // 如果启用在线验证，可以尝试验证团队ID
        if self.config.enable_online_validation {
            result.validation_details.push(CertificateValidationDetail {
                check_type: CertificateCheckType::OnlineValidation,
                is_passed: true,
                message: "Safari扩展在线验证跳过（需要Apple开发者账户）".to_string(),
            });
        }
        
        result.certificate_info = Some(CertificateInfo {
            issuer: "Apple Inc".to_string(),
            subject: extension_id.to_string(),
            valid_from: SystemTime::now() - Duration::from_secs(86400 * 365),
            valid_until: SystemTime::now() + Duration::from_secs(86400 * 365),
            fingerprint: format!("apple:{}", extension_id),
            key_usage: vec!["digitalSignature".to_string()],
            extended_key_usage: vec!["codeSigning".to_string()],
        });
        
        Ok(())
    }
    
    /// 本地证书验证
    async fn verify_local_certificate(
        &self,
        result: &mut CertificateValidationResult,
        extension_id: &str,
        _config: &BrowserCertificateConfig,
    ) -> Result<(), BrowserAuthError> {
        // 本地证书验证的简化实现
        // 实际实现需要解析和验证X.509证书
        
        result.validation_details.push(CertificateValidationDetail {
            check_type: CertificateCheckType::LocalValidation,
            is_passed: true,
            message: "本地证书验证已跳过（需要证书文件）".to_string(),
        });
        
        Ok(())
    }
}

/// 证书信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CertificateInfo {
    /// 颁发者
    pub issuer: String,
    /// 主题
    pub subject: String,
    /// 有效期开始
    pub valid_from: SystemTime,
    /// 有效期结束
    pub valid_until: SystemTime,
    /// 证书指纹
    pub fingerprint: String,
    /// 密钥用途
    pub key_usage: Vec<String>,
    /// 扩展密钥用途
    pub extended_key_usage: Vec<String>,
}

impl CertificateInfo {
    /// 检查证书是否有效
    pub fn is_valid_at(&self, time: SystemTime) -> bool {
        time >= self.valid_from && time <= self.valid_until
    }
    
    /// 获取证书剩余有效期
    pub fn remaining_validity(&self) -> Duration {
        self.valid_until.duration_since(SystemTime::now()).unwrap_or(Duration::ZERO)
    }
}

/// 证书验证结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CertificateValidationResult {
    /// 扩展ID
    pub extension_id: String,
    /// 浏览器类型
    pub browser: BrowserType,
    /// 是否有效
    pub is_valid: bool,
    /// 是否来自官方商店
    pub is_official_store: bool,
    /// 证书信息
    pub certificate_info: Option<CertificateInfo>,
    /// 验证详情
    pub validation_details: Vec<CertificateValidationDetail>,
    /// 验证时间
    pub validated_at: SystemTime,
    /// 验证耗时
    pub validation_duration: Duration,
}

impl CertificateValidationResult {
    /// 获取失败的检查
    pub fn failed_checks(&self) -> Vec<&CertificateValidationDetail> {
        self.validation_details.iter()
            .filter(|detail| !detail.is_passed)
            .collect()
    }
    
    /// 获取验证摘要
    pub fn summary(&self) -> String {
        format!(
            "Certificate for {} on {:?}: {} (Official: {}, Checks: {}/{})",
            self.extension_id,
            self.browser,
            if self.is_valid { "VALID" } else { "INVALID" },
            self.is_official_store,
            self.validation_details.iter().filter(|d| d.is_passed).count(),
            self.validation_details.len()
        )
    }
}

/// 证书验证详情
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CertificateValidationDetail {
    /// 检查类型
    pub check_type: CertificateCheckType,
    /// 是否通过
    pub is_passed: bool,
    /// 详细消息
    pub message: String,
}

/// 证书检查类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum CertificateCheckType {
    /// 浏览器支持检查
    BrowserSupport,
    /// 格式检查
    Format,
    /// 在线验证
    OnlineValidation,
    /// 本地验证
    LocalValidation,
    /// 证书链验证
    ChainValidation,
    /// 撤销检查
    RevocationCheck,
    /// 验证
    Verification,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_certificate_info_validity() {
        let now = SystemTime::now();
        let cert_info = CertificateInfo {
            issuer: "Test CA".to_string(),
            subject: "test-extension".to_string(),
            valid_from: now - Duration::from_secs(86400), // 1天前
            valid_until: now + Duration::from_secs(86400), // 1天后
            fingerprint: "test:fingerprint".to_string(),
            key_usage: vec!["digitalSignature".to_string()],
            extended_key_usage: vec!["codeSigning".to_string()],
        };
        
        assert!(cert_info.is_valid_at(now));
        assert!(!cert_info.is_valid_at(now - Duration::from_secs(86400 * 2)));
        assert!(!cert_info.is_valid_at(now + Duration::from_secs(86400 * 2)));
    }

    #[tokio::test]
    async fn test_certificate_validator_creation() {
        let config = CertificateValidatorConfig::default();
        let validator = CertificateChainValidator::new(&config).unwrap();
        
        // 验证配置加载正确
        assert!(validator.config.browser_configs.contains_key(&BrowserType::Chrome));
        assert!(validator.config.browser_configs.contains_key(&BrowserType::Firefox));
        assert!(validator.config.browser_configs.contains_key(&BrowserType::Safari));
    }

    #[tokio::test]
    async fn test_unsupported_browser() {
        let config = CertificateValidatorConfig::default();
        let validator = CertificateChainValidator::new(&config).unwrap();
        
        let result = validator.verify_extension_certificate(
            "test-extension",
            &BrowserType::Unknown("TestBrowser".to_string())
        ).await.unwrap();
        
        assert!(!result.is_valid);
        let failed_checks = result.failed_checks();
        assert!(failed_checks.iter().any(|check| check.check_type == CertificateCheckType::BrowserSupport));
    }

    #[test]
    fn test_certificate_validation_result_summary() {
        let result = CertificateValidationResult {
            extension_id: "test-extension".to_string(),
            browser: BrowserType::Chrome,
            is_valid: true,
            is_official_store: true,
            certificate_info: None,
            validation_details: vec![
                CertificateValidationDetail {
                    check_type: CertificateCheckType::OnlineValidation,
                    is_passed: true,
                    message: "验证成功".to_string(),
                }
            ],
            validated_at: SystemTime::now(),
            validation_duration: Duration::from_millis(500),
        };
        
        let summary = result.summary();
        assert!(summary.contains("test-extension"));
        assert!(summary.contains("VALID"));
        assert!(summary.contains("Official: true"));
    }
} 