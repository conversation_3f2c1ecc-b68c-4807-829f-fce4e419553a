//! 浏览器扩展身份验证模块
//!
//! 提供扩展ID验证、来源验证、证书链验证和白名单管理功能

pub mod extension_verifier;
pub mod origin_validator;
pub mod whitelist_manager;
pub mod certificate_chain;

// 重新导出主要类型
pub use extension_verifier::{ExtensionVerifier, ExtensionVerificationResult};
pub use origin_validator::{OriginValidator, OriginValidationResult};
pub use whitelist_manager::{WhitelistManager, WhitelistEntry, WhitelistRule};
pub use certificate_chain::{CertificateChainValidator, CertificateInfo, CertificateValidationResult};

use serde::{Deserialize, Serialize};
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tracing::{debug, info, warn, error};

/// 浏览器类型枚举
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum BrowserType {
    Chrome,
    Chromium,
    Firefox,
    Edge,
    Safari,
    <PERSON>,
    Brave,
    Unknown(String),
}

impl BrowserType {
    /// 从用户代理字符串解析浏览器类型
    pub fn from_user_agent(user_agent: &str) -> Self {
        let ua = user_agent.to_lowercase();
        if ua.contains("chrome") && !ua.contains("edge") && !ua.contains("brave") {
            Self::Chrome
        } else if ua.contains("chromium") {
            Self::Chromium
        } else if ua.contains("firefox") {
            Self::Firefox
        } else if ua.contains("edge") || ua.contains("edg/") {
            Self::Edge
        } else if ua.contains("safari") && !ua.contains("chrome") {
            Self::Safari
        } else if ua.contains("opera") || ua.contains("opr/") {
            Self::Opera
        } else if ua.contains("brave") {
            Self::Brave
        } else {
            Self::Unknown(user_agent.to_string())
        }
    }
    
    /// 获取浏览器名称
    pub fn name(&self) -> &str {
        match self {
            Self::Chrome => "Chrome",
            Self::Chromium => "Chromium",
            Self::Firefox => "Firefox",
            Self::Edge => "Edge",
            Self::Safari => "Safari",
            Self::Opera => "Opera",
            Self::Brave => "Brave",
            Self::Unknown(_) => "Unknown",
        }
    }
}

/// 扩展信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExtensionInfo {
    /// 扩展ID
    pub id: String,
    /// 扩展名称
    pub name: String,
    /// 扩展来源
    pub origin: String,
    /// 浏览器类型
    pub browser: BrowserType,
    /// 验证时间
    pub verified_at: SystemTime,
    /// 信任级别
    pub trust_level: TrustLevel,
    /// 权限列表
    pub permissions: Vec<String>,
    /// 证书信息
    pub certificate_info: Option<CertificateInfo>,
}

/// 信任级别
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum TrustLevel {
    /// 高度信任 - 官方商店验证的扩展
    High,
    /// 中等信任 - 白名单中的扩展
    Medium,
    /// 低信任 - 未知但允许的扩展
    Low,
    /// 不信任 - 被禁止的扩展
    Untrusted,
}

impl TrustLevel {
    /// 转换为分数
    pub fn to_score(&self) -> u8 {
        match self {
            Self::High => 90,
            Self::Medium => 70,
            Self::Low => 50,
            Self::Untrusted => 0,
        }
    }
    
    /// 从分数创建信任级别
    pub fn from_score(score: u8) -> Self {
        match score {
            90..=100 => Self::High,
            70..=89 => Self::Medium,
            50..=69 => Self::Low,
            _ => Self::Untrusted,
        }
    }
}

/// 浏览器扩展身份验证器
#[derive(Debug)]
pub struct BrowserAuthenticator {
    extension_verifier: ExtensionVerifier,
    origin_validator: OriginValidator,
    whitelist_manager: WhitelistManager,
    certificate_validator: CertificateChainValidator,
    config: BrowserAuthConfig,
}

impl BrowserAuthenticator {
    /// 创建新的浏览器身份验证器
    pub fn new(config: BrowserAuthConfig) -> Result<Self, BrowserAuthError> {
        let extension_verifier = ExtensionVerifier::new(&config.extension_config)?;
        let origin_validator = OriginValidator::new(&config.origin_config)?;
        let whitelist_manager = WhitelistManager::new(&config.whitelist_config)?;
        let certificate_validator = CertificateChainValidator::new(&config.certificate_config)?;
        
        Ok(Self {
            extension_verifier,
            origin_validator,
            whitelist_manager,
            certificate_validator,
            config,
        })
    }
    
    /// 验证扩展身份
    pub async fn verify_extension_identity(&self, extension_id: &str, origin: &str, browser: BrowserType) -> Result<ValidationResult, BrowserAuthError> {
        debug!("验证扩展身份: ID={}, Origin={}, Browser={:?}", extension_id, origin, browser);
        
        let start_time = SystemTime::now();
        let mut result = ValidationResult::new(extension_id.to_string(), origin.to_string(), browser.clone());
        
        // 1. 扩展ID格式验证    
        let extension_result = self.extension_verifier.verify_extension_id(extension_id, &browser).await?;    
        result.extension_verification = Some(extension_result.clone());
        
        if !extension_result.is_valid {
            result.is_authenticated = false;
            result.errors.push("无效的扩展ID格式".to_string());
            return Ok(result);
        }
        
        // 2. 白名单检查
        let whitelist_result = self.whitelist_manager.check_extension(extension_id, origin, &browser).await?;
        result.whitelist_status = Some(whitelist_result.clone());
        
        if !whitelist_result.is_allowed {
            result.is_authenticated = false;
            result.errors.push("扩展未在白名单中".to_string());
            return Ok(result);
        }
        
        // 3. 来源验证
        let origin_result = self.origin_validator.validate_origin(extension_id, origin, &browser).await?;
        result.origin_validation = Some(origin_result.clone());
        
        if !origin_result.is_valid {
            result.is_authenticated = false;
            result.errors.push("无效的来源域名".to_string());
            return Ok(result);
        }
        
        // 4. 证书链验证（Chrome Web Store/Firefox AMO）
        if self.config.enable_certificate_validation {
            match self.certificate_validator.verify_extension_certificate(extension_id, &browser).await {
                Ok(cert_result) => {
                    result.certificate_validation = Some(cert_result.clone());
                    if !cert_result.is_valid {
                        result.trust_level = TrustLevel::Low;
                        result.warnings.push("证书验证失败，但允许低信任级别访问".to_string());
                    }
                }
                Err(e) => {
                    warn!("证书验证失败: {}", e);
                    result.trust_level = TrustLevel::Low;
                    result.warnings.push(format!("证书验证错误: {}", e));
                }
            }
        }
        
        // 5. 计算最终信任级别
        result.trust_level = self.calculate_trust_level(&result);
        result.is_authenticated = true;
        result.validation_duration = start_time.elapsed().unwrap_or(Duration::ZERO);
        
        info!("扩展身份验证成功: {} (信任级别: {:?})", extension_id, result.trust_level);
        Ok(result)
    }
    
    /// 计算信任级别
    fn calculate_trust_level(&self, result: &ValidationResult) -> TrustLevel {
        let mut score = 50u8; // 基础分数
        
        // 白名单加分
        if let Some(ref whitelist) = result.whitelist_status {
            score += match whitelist.rule_type {
                WhitelistRuleType::Official => 40,
                WhitelistRuleType::Verified => 30,
                WhitelistRuleType::Trusted => 20,
                WhitelistRuleType::Basic => 10,
            };
        }
        
        // 证书验证加分
        if let Some(ref cert) = result.certificate_validation {
            if cert.is_valid {
                score += 20;
                if cert.is_official_store {
                    score += 10;
                }
            } else {
                score -= 10;
            }
        }
        
        // 来源验证加分
        if let Some(ref origin) = result.origin_validation {
            if origin.is_valid && origin.is_official_domain {
                score += 10;
            }
        }
        
        TrustLevel::from_score(score)
    }
}

/// 浏览器身份验证配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BrowserAuthConfig {
    /// 启用证书验证
    pub enable_certificate_validation: bool,
    /// 启用严格模式
    pub strict_mode: bool,
    /// 缓存过期时间（秒）
    pub cache_expiry_seconds: u64,
    /// 扩展验证配置
    pub extension_config: ExtensionVerifierConfig,
    /// 来源验证配置
    pub origin_config: OriginValidatorConfig,
    /// 白名单配置
    pub whitelist_config: WhitelistManagerConfig,
    /// 证书验证配置
    pub certificate_config: CertificateValidatorConfig,
}

impl Default for BrowserAuthConfig {
    fn default() -> Self {
        Self {
            enable_certificate_validation: true,
            strict_mode: false,
            cache_expiry_seconds: 3600, // 1小时
            extension_config: ExtensionVerifierConfig::default(),
            origin_config: OriginValidatorConfig::default(),
            whitelist_config: WhitelistManagerConfig::default(),
            certificate_config: CertificateValidatorConfig::default(),
        }
    }
}

/// 验证结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationResult {
    /// 扩展ID
    pub extension_id: String,
    /// 来源
    pub origin: String,
    /// 浏览器类型
    pub browser: BrowserType,
    /// 是否认证成功
    pub is_authenticated: bool,
    /// 信任级别
    pub trust_level: TrustLevel,
    /// 扩展验证结果
    pub extension_verification: Option<ExtensionVerificationResult>,
    /// 白名单状态
    pub whitelist_status: Option<WhitelistStatus>,
    /// 来源验证结果
    pub origin_validation: Option<OriginValidationResult>,
    /// 证书验证结果
    pub certificate_validation: Option<CertificateValidationResult>,
    /// 验证时间
    pub verified_at: SystemTime,
    /// 验证耗时
    pub validation_duration: Duration,
    /// 错误信息
    pub errors: Vec<String>,
    /// 警告信息
    pub warnings: Vec<String>,
}

impl ValidationResult {
    /// 创建新的验证结果
    pub fn new(extension_id: String, origin: String, browser: BrowserType) -> Self {
        Self {
            extension_id,
            origin,
            browser,
            is_authenticated: false,
            trust_level: TrustLevel::Untrusted,
            extension_verification: None,
            whitelist_status: None,
            origin_validation: None,
            certificate_validation: None,
            verified_at: SystemTime::now(),
            validation_duration: Duration::ZERO,
            errors: Vec::new(),
            warnings: Vec::new(),
        }
    }
    
    /// 检查是否有错误
    pub fn has_errors(&self) -> bool {
        !self.errors.is_empty()
    }
    
    /// 检查是否有警告
    pub fn has_warnings(&self) -> bool {
        !self.warnings.is_empty()
    }
    
    /// 获取验证摘要
    pub fn summary(&self) -> String {
        format!(
            "Extension {} from {} on {:?}: {} (Trust: {:?})",
            self.extension_id,
            self.origin,
            self.browser,
            if self.is_authenticated { "PASS" } else { "FAIL" },
            self.trust_level
        )
    }
}

/// 白名单状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WhitelistStatus {
    /// 是否允许
    pub is_allowed: bool,
    /// 规则类型
    pub rule_type: WhitelistRuleType,
    /// 匹配的规则
    pub matched_rule: Option<WhitelistRule>,
    /// 检查时间
    pub checked_at: SystemTime,
}

/// 白名单规则类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum WhitelistRuleType {
    /// 官方规则
    Official,
    /// 已验证规则
    Verified,
    /// 信任规则
    Trusted,
    /// 基础规则
    Basic,
}

/// 浏览器身份验证错误
#[derive(Debug, thiserror::Error)]
pub enum BrowserAuthError {
    #[error("扩展验证失败: {0}")]
    ExtensionVerificationFailed(String),
    
    #[error("来源验证失败: {0}")]
    OriginValidationFailed(String),
    
    #[error("白名单检查失败: {0}")]
    WhitelistCheckFailed(String),
    
    #[error("证书验证失败: {0}")]
    CertificateValidationFailed(String),
    
    #[error("配置错误: {0}")]
    ConfigError(String),
    
    #[error("IO错误: {0}")]
    IoError(#[from] std::io::Error),
    
    #[error("序列化错误: {0}")]
    SerializationError(#[from] serde_json::Error),
}

// 占位符类型，将在各自的文件中实现
pub use extension_verifier::ExtensionVerifierConfig;
pub use origin_validator::OriginValidatorConfig;
pub use whitelist_manager::WhitelistManagerConfig;
pub use certificate_chain::CertificateValidatorConfig;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_browser_type_from_user_agent() {
        assert_eq!(BrowserType::from_user_agent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"), BrowserType::Chrome);
        assert_eq!(BrowserType::from_user_agent("Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0"), BrowserType::Firefox);
        assert_eq!(BrowserType::from_user_agent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59"), BrowserType::Edge);
    }

    #[test]
    fn test_trust_level_conversion() {
        assert_eq!(TrustLevel::High.to_score(), 90);
        assert_eq!(TrustLevel::from_score(95), TrustLevel::High);
        assert_eq!(TrustLevel::from_score(75), TrustLevel::Medium);
        assert_eq!(TrustLevel::from_score(55), TrustLevel::Low);
        assert_eq!(TrustLevel::from_score(30), TrustLevel::Untrusted);
    }

    #[test]
    fn test_validation_result_creation() {
        let result = ValidationResult::new(
            "test-extension".to_string(),
            "chrome-extension://test".to_string(),
            BrowserType::Chrome
        );
        
        assert_eq!(result.extension_id, "test-extension");
        assert_eq!(result.origin, "chrome-extension://test");
        assert_eq!(result.browser, BrowserType::Chrome);
        assert!(!result.is_authenticated);
        assert_eq!(result.trust_level, TrustLevel::Untrusted);
    }
} 