//! 来源验证器模块
//!
//! 负责验证浏览器扩展来源域名的有效性和安全性

use super::{BrowserType, BrowserAuthError};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use std::time::SystemTime;
use url::Url;
use tracing::{debug, warn};

/// 来源验证器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OriginValidatorConfig {
    /// 启用域名格式验证
    pub enable_domain_validation: bool,
    /// 启用官方域名检查
    pub enable_official_domain_check: bool,
    /// 启用危险域名检查
    pub enable_dangerous_domain_check: bool,
    /// 允许的顶级域名
    pub allowed_tlds: HashSet<String>,
    /// 危险域名黑名单
    pub dangerous_domains: HashSet<String>,
    /// 官方域名白名单
    pub official_domains: HashMap<BrowserType, HashSet<String>>,
    /// 自定义验证规则
    pub custom_rules: Vec<OriginValidationRule>,
}

impl Default for OriginValidatorConfig {
    fn default() -> Self {
        let mut official_domains = HashMap::new();
        
        // Chrome 官方域名
        let mut chrome_domains = HashSet::new();
        chrome_domains.insert("chrome.google.com".to_string());
        chrome_domains.insert("chromewebstore.google.com".to_string());
        chrome_domains.insert("chrome-extension".to_string()); // Chrome扩展协议
        official_domains.insert(BrowserType::Chrome, chrome_domains.clone());
        official_domains.insert(BrowserType::Chromium, chrome_domains.clone());
        official_domains.insert(BrowserType::Edge, chrome_domains);
        
        // Firefox 官方域名
        let mut firefox_domains = HashSet::new();
        firefox_domains.insert("addons.mozilla.org".to_string());
        firefox_domains.insert("moz-extension".to_string()); // Firefox扩展协议
        official_domains.insert(BrowserType::Firefox, firefox_domains);
        
        // Safari 官方域名
        let mut safari_domains = HashSet::new();
        safari_domains.insert("apps.apple.com".to_string());
        safari_domains.insert("safari-extension".to_string()); // Safari扩展协议
        official_domains.insert(BrowserType::Safari, safari_domains);
        
        // 允许的顶级域名
        let mut allowed_tlds = HashSet::new();
        allowed_tlds.insert("com".to_string());
        allowed_tlds.insert("org".to_string());
        allowed_tlds.insert("net".to_string());
        allowed_tlds.insert("edu".to_string());
        allowed_tlds.insert("gov".to_string());
        
        // 危险域名黑名单
        let mut dangerous_domains = HashSet::new();
        dangerous_domains.insert("localhost".to_string());
        dangerous_domains.insert("127.0.0.1".to_string());
        dangerous_domains.insert("0.0.0.0".to_string());
        dangerous_domains.insert("file".to_string());
        
        Self {
            enable_domain_validation: true,
            enable_official_domain_check: true,
            enable_dangerous_domain_check: true,
            allowed_tlds,
            dangerous_domains,
            official_domains,
            custom_rules: Vec::new(),
        }
    }
}

/// 来源验证规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OriginValidationRule {
    /// 规则名称
    pub name: String,
    /// 规则描述
    pub description: String,
    /// 匹配模式
    pub pattern: String,
    /// 是否允许
    pub allow: bool,
    /// 优先级
    pub priority: u32,
}

/// 来源验证器
#[derive(Debug)]
pub struct OriginValidator {
    config: OriginValidatorConfig,
}

impl OriginValidator {
    /// 创建新的来源验证器
    pub fn new(config: &OriginValidatorConfig) -> Result<Self, BrowserAuthError> {
        Ok(Self {
            config: config.clone(),
        })
    }
    
    /// 验证来源
    pub async fn validate_origin(&self, extension_id: &str, origin: &str, browser: &BrowserType) -> Result<OriginValidationResult, BrowserAuthError> {
        debug!("验证来源: Extension={}, Origin={}, Browser={:?}", extension_id, origin, browser);
        
        let mut result = OriginValidationResult {
            extension_id: extension_id.to_string(),
            origin: origin.to_string(),
            browser: browser.clone(),
            is_valid: false,
            is_official_domain: false,
            is_dangerous_domain: false,
            validation_details: Vec::new(),
            validated_at: SystemTime::now(),
        };
        
        // 1. 基础格式验证
        result.validation_details.push(self.check_basic_format(origin)?);
        
        // 2. URL格式验证
        if self.config.enable_domain_validation {
            result.validation_details.push(self.check_url_format(origin)?);
        }
        
        // 3. 危险域名检查
        if self.config.enable_dangerous_domain_check {
            let dangerous_check = self.check_dangerous_domain(origin)?;
            result.is_dangerous_domain = !dangerous_check.is_passed;
            result.validation_details.push(dangerous_check);
        }
        
        // 4. 官方域名检查
        if self.config.enable_official_domain_check {
            let official_check = self.check_official_domain(origin, browser)?;
            result.is_official_domain = official_check.is_passed;
            result.validation_details.push(official_check);
        }
        
        // 5. 浏览器特定验证
        result.validation_details.push(self.check_browser_specific_origin(origin, browser)?);
        
        // 6. 自定义规则验证
        if !self.config.custom_rules.is_empty() {
            result.validation_details.push(self.check_custom_rules(origin)?);
        }
        
        // 7. 顶级域名验证
        result.validation_details.push(self.check_tld(origin)?);
        
        // 计算最终结果
        result.is_valid = result.validation_details.iter().all(|detail| detail.is_passed) && !result.is_dangerous_domain;
        
        if result.is_valid {
            debug!("来源验证成功: {}", origin);
        } else {
            warn!("来源验证失败: {} - 失败的检查: {:?}", 
                origin, 
                result.validation_details.iter()
                    .filter(|d| !d.is_passed)
                    .map(|d| &d.check_type)
                    .collect::<Vec<_>>()
            );
        }
        
        Ok(result)
    }
    
    /// 基础格式检查
    fn check_basic_format(&self, origin: &str) -> Result<OriginValidationDetail, BrowserAuthError> {
        let is_passed = !origin.is_empty() && 
                       !origin.chars().any(|c| c.is_whitespace()) &&
                       origin.len() <= 2048; // 合理的最大长度限制
        
        Ok(OriginValidationDetail {
            check_type: OriginCheckType::BasicFormat,
            is_passed,
            message: if is_passed {
                "基础格式检查通过".to_string()
            } else {
                "来源格式无效：不能为空、包含空格或超过2048字符".to_string()
            },
        })
    }
    
    /// URL格式验证
    fn check_url_format(&self, origin: &str) -> Result<OriginValidationDetail, BrowserAuthError> {
        let is_passed = match Url::parse(origin) {
            Ok(url) => {
                // 验证协议
                matches!(url.scheme(), "http" | "https" | "chrome-extension" | "moz-extension" | "safari-extension" | "ms-browser-extension")
            }
            Err(_) => {
                // 对于扩展协议，可能不是标准URL格式
                origin.starts_with("chrome-extension://") ||
                origin.starts_with("moz-extension://") ||
                origin.starts_with("safari-extension://") ||
                origin.starts_with("ms-browser-extension://")
            }
        };
        
        Ok(OriginValidationDetail {
            check_type: OriginCheckType::UrlFormat,
            is_passed,
            message: if is_passed {
                "URL格式检查通过".to_string()
            } else {
                "无效的URL格式或不支持的协议".to_string()
            },
        })
    }
    
    /// 危险域名检查
    fn check_dangerous_domain(&self, origin: &str) -> Result<OriginValidationDetail, BrowserAuthError> {
        let is_safe = !self.config.dangerous_domains.iter().any(|dangerous| {
            origin.contains(dangerous)
        });
        
        Ok(OriginValidationDetail {
            check_type: OriginCheckType::DangerousDomain,
            is_passed: is_safe,
            message: if is_safe {
                "危险域名检查通过".to_string()
            } else {
                "检测到危险域名或IP地址".to_string()
            },
        })
    }
    
    /// 官方域名检查
    fn check_official_domain(&self, origin: &str, browser: &BrowserType) -> Result<OriginValidationDetail, BrowserAuthError> {
        let is_official = if let Some(official_domains) = self.config.official_domains.get(browser) {
            official_domains.iter().any(|domain| origin.contains(domain))
        } else {
            false
        };
        
        Ok(OriginValidationDetail {
            check_type: OriginCheckType::OfficialDomain,
            is_passed: true, // 这个检查总是通过，只是标记是否官方
            message: if is_official {
                "官方域名验证通过".to_string()
            } else {
                "非官方域名，但允许访问".to_string()
            },
        })
    }
    
    /// 浏览器特定来源验证
    fn check_browser_specific_origin(&self, origin: &str, browser: &BrowserType) -> Result<OriginValidationDetail, BrowserAuthError> {
        let (is_passed, message) = match browser {
            BrowserType::Chrome | BrowserType::Chromium | BrowserType::Edge => {
                if origin.starts_with("chrome-extension://") {
                    (true, "Chrome扩展协议验证通过".to_string())
                } else if origin.starts_with("https://") || origin.starts_with("http://") {
                    (true, "Web页面来源验证通过".to_string())
                } else {
                    (false, "Chrome系浏览器来源协议不正确".to_string())
                }
            }
            BrowserType::Firefox => {
                if origin.starts_with("moz-extension://") {
                    (true, "Firefox扩展协议验证通过".to_string())
                } else if origin.starts_with("https://") || origin.starts_with("http://") {
                    (true, "Web页面来源验证通过".to_string())
                } else {
                    (false, "Firefox来源协议不正确".to_string())
                }
            }
            BrowserType::Safari => {
                if origin.starts_with("safari-extension://") {
                    (true, "Safari扩展协议验证通过".to_string())
                } else if origin.starts_with("https://") || origin.starts_with("http://") {
                    (true, "Web页面来源验证通过".to_string())
                } else {
                    (false, "Safari来源协议不正确".to_string())
                }
            }
            _ => (true, "未知浏览器，跳过特定协议检查".to_string()),
        };
        
        Ok(OriginValidationDetail {
            check_type: OriginCheckType::BrowserSpecific,
            is_passed,
            message,
        })
    }
    
    /// 自定义规则检查
    fn check_custom_rules(&self, origin: &str) -> Result<OriginValidationDetail, BrowserAuthError> {
        // 按优先级排序规则
        let mut sorted_rules = self.config.custom_rules.clone();
        sorted_rules.sort_by(|a, b| b.priority.cmp(&a.priority));
        
        for rule in &sorted_rules {
            // 简单的模式匹配（可以扩展为正则表达式）
            if origin.contains(&rule.pattern) {
                return Ok(OriginValidationDetail {
                    check_type: OriginCheckType::CustomRule,
                    is_passed: rule.allow,
                    message: format!("匹配自定义规则: {} - {}", rule.name, rule.description),
                });
            }
        }
        
        Ok(OriginValidationDetail {
            check_type: OriginCheckType::CustomRule,
            is_passed: true,
            message: "未匹配任何自定义规则，默认允许".to_string(),
        })
    }
    
    /// 顶级域名检查
    fn check_tld(&self, origin: &str) -> Result<OriginValidationDetail, BrowserAuthError> {
        // 对于扩展协议，跳过TLD检查
        if origin.starts_with("chrome-extension://") || 
           origin.starts_with("moz-extension://") || 
           origin.starts_with("safari-extension://") {
            return Ok(OriginValidationDetail {
                check_type: OriginCheckType::TopLevelDomain,
                is_passed: true,
                message: "扩展协议，跳过TLD检查".to_string(),
            });
        }
        
        // 解析URL并检查TLD
        if let Ok(url) = Url::parse(origin) {
            if let Some(host) = url.host_str() {
                let parts: Vec<&str> = host.split('.').collect();
                if let Some(tld) = parts.last() {
                    let is_allowed = self.config.allowed_tlds.contains(*tld);
                    return Ok(OriginValidationDetail {
                        check_type: OriginCheckType::TopLevelDomain,
                        is_passed: is_allowed,
                        message: if is_allowed {
                            format!("顶级域名 '{}' 验证通过", tld)
                        } else {
                            format!("不允许的顶级域名: '{}'", tld)
                        },
                    });
                }
            }
        }
        
        Ok(OriginValidationDetail {
            check_type: OriginCheckType::TopLevelDomain,
            is_passed: true,
            message: "无法解析域名，跳过TLD检查".to_string(),
        })
    }
}

/// 来源验证结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OriginValidationResult {
    /// 扩展ID
    pub extension_id: String,
    /// 来源
    pub origin: String,
    /// 浏览器类型
    pub browser: BrowserType,
    /// 是否有效
    pub is_valid: bool,
    /// 是否官方域名
    pub is_official_domain: bool,
    /// 是否危险域名
    pub is_dangerous_domain: bool,
    /// 验证详情
    pub validation_details: Vec<OriginValidationDetail>,
    /// 验证时间
    pub validated_at: SystemTime,
}

impl OriginValidationResult {
    /// 获取失败的检查
    pub fn failed_checks(&self) -> Vec<&OriginValidationDetail> {
        self.validation_details.iter()
            .filter(|detail| !detail.is_passed)
            .collect()
    }
    
    /// 获取验证摘要
    pub fn summary(&self) -> String {
        format!(
            "Origin {} for extension {} on {:?}: {} (Official: {}, Dangerous: {})",
            self.origin,
            self.extension_id,
            self.browser,
            if self.is_valid { "VALID" } else { "INVALID" },
            self.is_official_domain,
            self.is_dangerous_domain
        )
    }
}

/// 来源验证详情
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OriginValidationDetail {
    /// 检查类型
    pub check_type: OriginCheckType,
    /// 是否通过
    pub is_passed: bool,
    /// 详细消息
    pub message: String,
}

/// 来源检查类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum OriginCheckType {
    /// 基础格式
    BasicFormat,
    /// URL格式
    UrlFormat,
    /// 危险域名
    DangerousDomain,
    /// 官方域名
    OfficialDomain,
    /// 浏览器特定
    BrowserSpecific,
    /// 自定义规则
    CustomRule,
    /// 顶级域名
    TopLevelDomain,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_chrome_extension_origin() {
        let config = OriginValidatorConfig::default();
        let validator = OriginValidator::new(&config).unwrap();
        
        let result = validator.validate_origin(
            "test-extension",
            "chrome-extension://abcdefghijklmnopabcdefghijklmnop/",
            &BrowserType::Chrome
        ).await.unwrap();
        
        assert!(result.is_valid);
        assert!(!result.is_dangerous_domain);
    }

    #[tokio::test]
    async fn test_firefox_extension_origin() {
        let config = OriginValidatorConfig::default();
        let validator = OriginValidator::new(&config).unwrap();
        
        let result = validator.validate_origin(
            "test-extension",
            "moz-extension://12345678-1234-1234-1234-123456789abc/",
            &BrowserType::Firefox
        ).await.unwrap();
        
        assert!(result.is_valid);
        assert!(!result.is_dangerous_domain);
    }

    #[tokio::test]
    async fn test_web_page_origin() {
        let config = OriginValidatorConfig::default();
        let validator = OriginValidator::new(&config).unwrap();
        
        let result = validator.validate_origin(
            "test-extension",
            "https://example.com",
            &BrowserType::Chrome
        ).await.unwrap();
        
        assert!(result.is_valid);
        assert!(!result.is_dangerous_domain);
    }

    #[tokio::test]
    async fn test_dangerous_origin() {
        let config = OriginValidatorConfig::default();
        let validator = OriginValidator::new(&config).unwrap();
        
        let result = validator.validate_origin(
            "test-extension",
            "http://localhost:8080",
            &BrowserType::Chrome
        ).await.unwrap();
        
        assert!(!result.is_valid);
        assert!(result.is_dangerous_domain);
    }

    #[tokio::test]
    async fn test_official_domain() {
        let config = OriginValidatorConfig::default();
        let validator = OriginValidator::new(&config).unwrap();
        
        let result = validator.validate_origin(
            "test-extension",
            "https://chromewebstore.google.com",
            &BrowserType::Chrome
        ).await.unwrap();
        
        assert!(result.is_valid);
        assert!(result.is_official_domain);
    }

    #[tokio::test]
    async fn test_invalid_url_format() {
        let config = OriginValidatorConfig::default();
        let validator = OriginValidator::new(&config).unwrap();
        
        let result = validator.validate_origin(
            "test-extension",
            "invalid://url format",
            &BrowserType::Chrome
        ).await.unwrap();
        
        assert!(!result.is_valid);
        let failed_checks = result.failed_checks();
        assert!(failed_checks.iter().any(|check| 
            check.check_type == OriginCheckType::BasicFormat || 
            check.check_type == OriginCheckType::UrlFormat
        ));
    }
} 