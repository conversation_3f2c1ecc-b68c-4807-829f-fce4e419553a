//! 白名单管理器模块
//!
//! 负责管理允许的扩展和域名白名单

use super::{BrowserType, BrowserAuthError, WhitelistRuleType};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use std::path::PathBuf;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::fs;
use tracing::{debug, info, warn, error};

/// 白名单管理器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WhitelistManagerConfig {
    /// 白名单文件路径
    pub whitelist_file_path: PathBuf,
    /// 启用自动重载
    pub enable_auto_reload: bool,
    /// 重载检查间隔（秒）
    pub reload_check_interval: u64,
    /// 启用缓存
    pub enable_cache: bool,
    /// 缓存过期时间（秒）
    pub cache_expiry_seconds: u64,
    /// 默认规则
    pub default_rules: Vec<WhitelistRule>,
}

impl Default for WhitelistManagerConfig {
    fn default() -> Self {
        let mut default_rules = Vec::new();
        
        // 添加一些常见的官方扩展
        default_rules.push(WhitelistRule {
            id: "official-chrome-extensions".to_string(),
            rule_type: WhitelistRuleType::Official,
            browser: Some(BrowserType::Chrome),
            extension_patterns: vec![
                "nkbihfbeogaeaoehlefnkodbefgpgknn".to_string(), // MetaMask
                "fhbohimaelbohpjbbldcngcnapndodjp".to_string(), // Keeper
            ],
            origin_patterns: vec![
                "chrome-extension://nkbihfbeogaeaoehlefnkodbefgpgknn".to_string(),
                "chrome-extension://fhbohimaelbohpjbbldcngcnapndodjp".to_string(),
            ],
            description: "官方认证的Chrome扩展".to_string(),
            enabled: true,
            created_at: SystemTime::now(),
            updated_at: SystemTime::now(),
        });
        
        default_rules.push(WhitelistRule {
            id: "official-firefox-extensions".to_string(),
            rule_type: WhitelistRuleType::Official,
            browser: Some(BrowserType::Firefox),
            extension_patterns: vec![
                "<EMAIL>".to_string(),
                "{ec8030f7-c20a-464f-9b0e-13a3a9e97384}".to_string(),
            ],
            origin_patterns: vec![
                "moz-extension://".to_string(),
            ],
            description: "官方认证的Firefox扩展".to_string(),
            enabled: true,
            created_at: SystemTime::now(),
            updated_at: SystemTime::now(),
        });
        
        Self {
            whitelist_file_path: PathBuf::from("config/whitelist.json"),
            enable_auto_reload: true,
            reload_check_interval: 300, // 5分钟
            enable_cache: true,
            cache_expiry_seconds: 3600, // 1小时
            default_rules,
        }
    }
}

/// 白名单规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WhitelistRule {
    /// 规则ID
    pub id: String,
    /// 规则类型
    pub rule_type: WhitelistRuleType,
    /// 目标浏览器（None表示所有浏览器）
    pub browser: Option<BrowserType>,
    /// 扩展ID模式列表
    pub extension_patterns: Vec<String>,
    /// 来源模式列表
    pub origin_patterns: Vec<String>,
    /// 规则描述
    pub description: String,
    /// 是否启用
    pub enabled: bool,
    /// 创建时间
    pub created_at: SystemTime,
    /// 更新时间
    pub updated_at: SystemTime,
}

impl WhitelistRule {
    /// 检查扩展是否匹配此规则
    pub fn matches_extension(&self, extension_id: &str, origin: &str, browser: &BrowserType) -> bool {
        if !self.enabled {
            return false;
        }
        
        // 检查浏览器匹配
        if let Some(ref rule_browser) = self.browser {
            if rule_browser != browser {
                return false;
            }
        }
        
        // 检查扩展ID匹配
        let extension_matches = self.extension_patterns.iter().any(|pattern| {
            self.pattern_matches(extension_id, pattern)
        });
        
        // 检查来源匹配
        let origin_matches = self.origin_patterns.iter().any(|pattern| {
            self.pattern_matches(origin, pattern)
        });
        
        extension_matches || origin_matches
    }
    
    /// 模式匹配
    fn pattern_matches(&self, text: &str, pattern: &str) -> bool {
        if pattern.contains('*') {
            // 简单的通配符匹配
            let parts: Vec<&str> = pattern.split('*').collect();
            if parts.len() == 2 {
                text.starts_with(parts[0]) && text.ends_with(parts[1])
            } else {
                text.contains(pattern.trim_matches('*'))
            }
        } else {
            text == pattern || text.contains(pattern)
        }
    }
}

/// 白名单条目
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WhitelistEntry {
    /// 扩展ID
    pub extension_id: String,
    /// 来源
    pub origin: String,
    /// 浏览器类型
    pub browser: BrowserType,
    /// 匹配的规则
    pub matched_rule: WhitelistRule,
    /// 检查时间
    pub checked_at: SystemTime,
}

/// 白名单管理器
#[derive(Debug)]
pub struct WhitelistManager {
    config: WhitelistManagerConfig,
    rules: Vec<WhitelistRule>,
    cache: HashMap<String, (WhitelistEntry, SystemTime)>,
    last_file_check: Option<SystemTime>,
}

impl WhitelistManager {
    /// 创建新的白名单管理器
    pub fn new(config: &WhitelistManagerConfig) -> Result<Self, BrowserAuthError> {
        let mut manager = Self {
            config: config.clone(),
            rules: config.default_rules.clone(),
            cache: HashMap::new(),
            last_file_check: None,
        };
        
        // 尝试加载白名单文件
        if let Err(e) = manager.load_whitelist_sync() {
            warn!("无法加载白名单文件，使用默认规则: {}", e);
        }
        
        Ok(manager)
    }
    
    /// 检查扩展是否在白名单中
    pub async fn check_extension(&self, extension_id: &str, origin: &str, browser: &BrowserType) -> Result<super::WhitelistStatus, BrowserAuthError> {
        debug!("检查白名单: Extension={}, Origin={}, Browser={:?}", extension_id, origin, browser);
        
        let cache_key = format!("{}:{}:{:?}", extension_id, origin, browser);
        
        // 检查缓存
        if self.config.enable_cache {
            if let Some((entry, cached_at)) = self.cache.get(&cache_key) {
                let cache_age = SystemTime::now().duration_since(*cached_at).unwrap_or(Duration::MAX);
                if cache_age.as_secs() < self.config.cache_expiry_seconds {
                    debug!("从缓存返回白名单结果: {}", extension_id);
                    return Ok(super::WhitelistStatus {
                        is_allowed: true,
                        rule_type: entry.matched_rule.rule_type.clone(),
                        matched_rule: Some(entry.matched_rule.clone()),
                        checked_at: entry.checked_at,
                    });
                }
            }
        }
        
        // 重新加载白名单（如果启用自动重载）
        if self.config.enable_auto_reload {
            self.check_and_reload().await?;
        }
        
        // 查找匹配的规则
        for rule in &self.rules {
            if rule.matches_extension(extension_id, origin, browser) {
                let status = super::WhitelistStatus {
                    is_allowed: true,
                    rule_type: rule.rule_type.clone(),
                    matched_rule: Some(rule.clone()),
                    checked_at: SystemTime::now(),
                };
                
                // 更新缓存
                if self.config.enable_cache {
                    let entry = WhitelistEntry {
                        extension_id: extension_id.to_string(),
                        origin: origin.to_string(),
                        browser: browser.clone(),
                        matched_rule: rule.clone(),
                        checked_at: status.checked_at,
                    };
                    // 注意：这里需要可变引用，实际使用时需要考虑并发安全性
                    // self.cache.insert(cache_key, (entry, SystemTime::now()));
                }
                
                info!("扩展通过白名单验证: {} (规则: {})", extension_id, rule.id);
                return Ok(status);
            }
        }
        
        // 未找到匹配规则
        warn!("扩展未在白名单中: {}", extension_id);
        Ok(super::WhitelistStatus {
            is_allowed: false,
            rule_type: WhitelistRuleType::Basic,
            matched_rule: None,
            checked_at: SystemTime::now(),
        })
    }
    
    /// 添加白名单规则
    pub async fn add_rule(&mut self, rule: WhitelistRule) -> Result<(), BrowserAuthError> {
        info!("添加白名单规则: {}", rule.id);
        
        // 检查是否已存在相同ID的规则
        if self.rules.iter().any(|r| r.id == rule.id) {
            return Err(BrowserAuthError::ConfigError(
                format!("规则ID已存在: {}", rule.id)
            ));
        }
        
        self.rules.push(rule);
        self.save_whitelist().await?;
        self.clear_cache();
        
        Ok(())
    }
    
    /// 移除白名单规则
    pub async fn remove_rule(&mut self, rule_id: &str) -> Result<bool, BrowserAuthError> {
        info!("移除白名单规则: {}", rule_id);
        
        let initial_len = self.rules.len();
        self.rules.retain(|r| r.id != rule_id);
        
        if self.rules.len() < initial_len {
            self.save_whitelist().await?;
            self.clear_cache();
            Ok(true)
        } else {
            Ok(false)
        }
    }
    
    /// 更新白名单规则
    pub async fn update_rule(&mut self, rule: WhitelistRule) -> Result<bool, BrowserAuthError> {
        info!("更新白名单规则: {}", rule.id);
        
        for existing_rule in &mut self.rules {
            if existing_rule.id == rule.id {
                *existing_rule = rule;
                self.save_whitelist().await?;
                self.clear_cache();
                return Ok(true);
            }
        }
        
        Ok(false)
    }
    
    /// 获取所有规则
    pub fn get_rules(&self) -> &[WhitelistRule] {
        &self.rules
    }
    
    /// 获取启用的规则数量
    pub fn enabled_rules_count(&self) -> usize {
        self.rules.iter().filter(|r| r.enabled).count()
    }
    
    /// 检查并重新加载白名单文件
    async fn check_and_reload(&self) -> Result<(), BrowserAuthError> {
        if !self.config.whitelist_file_path.exists() {
            return Ok(());
        }
        
        let metadata = fs::metadata(&self.config.whitelist_file_path).await?;
        let modified = metadata.modified()?;
        
        // 检查文件是否被修改
        if let Some(last_check) = self.last_file_check {
            if modified <= last_check {
                return Ok(());
            }
        }
        
        // 重新加载文件
        self.load_whitelist().await?;
        Ok(())
    }
    
    /// 加载白名单文件
    async fn load_whitelist(&self) -> Result<(), BrowserAuthError> {
        if !self.config.whitelist_file_path.exists() {
            debug!("白名单文件不存在，使用默认规则");
            return Ok(());
        }
        
        let content = fs::read_to_string(&self.config.whitelist_file_path).await?;
        let loaded_rules: Vec<WhitelistRule> = serde_json::from_str(&content)?;
        
        // 注意：这里需要可变引用，实际使用时需要考虑并发安全性
        // self.rules = loaded_rules;
        // self.last_file_check = Some(SystemTime::now());
        
        info!("成功加载白名单规则: {} 条", loaded_rules.len());
        Ok(())
    }
    
    /// 同步加载白名单文件
    fn load_whitelist_sync(&mut self) -> Result<(), BrowserAuthError> {
        if !self.config.whitelist_file_path.exists() {
            debug!("白名单文件不存在，使用默认规则");
            return Ok(());
        }
        
        let content = std::fs::read_to_string(&self.config.whitelist_file_path)?;
        let loaded_rules: Vec<WhitelistRule> = serde_json::from_str(&content)?;
        
        self.rules = loaded_rules;
        self.last_file_check = Some(SystemTime::now());
        
        info!("成功加载白名单规则: {} 条", self.rules.len());
        Ok(())
    }
    
    /// 保存白名单文件
    async fn save_whitelist(&self) -> Result<(), BrowserAuthError> {
        // 确保目录存在
        if let Some(parent) = self.config.whitelist_file_path.parent() {
            fs::create_dir_all(parent).await?;
        }
        
        let content = serde_json::to_string_pretty(&self.rules)?;
        fs::write(&self.config.whitelist_file_path, content).await?;
        
        info!("白名单已保存到文件: {:?}", self.config.whitelist_file_path);
        Ok(())
    }
    
    /// 清空缓存
    fn clear_cache(&mut self) {
        self.cache.clear();
        debug!("白名单缓存已清空");
    }
    
    /// 清理过期缓存
    fn cleanup_expired_cache(&mut self) {
        let now = SystemTime::now();
        let expiry_duration = Duration::from_secs(self.config.cache_expiry_seconds);
        
        self.cache.retain(|_, (_, cached_at)| {
            now.duration_since(*cached_at).unwrap_or(Duration::MAX) < expiry_duration
        });
    }
    
    /// 获取缓存统计信息
    pub fn get_cache_stats(&self) -> WhitelistCacheStats {
        WhitelistCacheStats {
            total_entries: self.cache.len(),
            memory_usage_bytes: self.cache.len() * std::mem::size_of::<(String, (WhitelistEntry, SystemTime))>(),
        }
    }
}

/// 白名单缓存统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WhitelistCacheStats {
    /// 总条目数
    pub total_entries: usize,
    /// 内存使用量（字节）
    pub memory_usage_bytes: usize,
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[test]
    fn test_pattern_matching() {
        let rule = WhitelistRule {
            id: "test".to_string(),
            rule_type: WhitelistRuleType::Basic,
            browser: None,
            extension_patterns: vec!["test*".to_string()],
            origin_patterns: vec!["chrome-extension://*".to_string()],
            description: "Test rule".to_string(),
            enabled: true,
            created_at: SystemTime::now(),
            updated_at: SystemTime::now(),
        };
        
        assert!(rule.pattern_matches("test123", "test*"));
        assert!(rule.pattern_matches("chrome-extension://abc123", "chrome-extension://*"));
        assert!(!rule.pattern_matches("other123", "test*"));
    }

    #[test]
    fn test_rule_matching() {
        let rule = WhitelistRule {
            id: "chrome-test".to_string(),
            rule_type: WhitelistRuleType::Basic,
            browser: Some(BrowserType::Chrome),
            extension_patterns: vec!["test-extension".to_string()],
            origin_patterns: vec!["chrome-extension://test-extension".to_string()],
            description: "Test Chrome rule".to_string(),
            enabled: true,
            created_at: SystemTime::now(),
            updated_at: SystemTime::now(),
        };
        
        // 应该匹配Chrome浏览器
        assert!(rule.matches_extension(
            "test-extension",
            "chrome-extension://test-extension",
            &BrowserType::Chrome
        ));
        
        // 不应该匹配Firefox浏览器
        assert!(!rule.matches_extension(
            "test-extension",
            "moz-extension://test-extension",
            &BrowserType::Firefox
        ));
    }

    #[tokio::test]
    async fn test_whitelist_manager_creation() {
        let config = WhitelistManagerConfig::default();
        let manager = WhitelistManager::new(&config).unwrap();
        
        assert!(!manager.rules.is_empty());
        assert_eq!(manager.enabled_rules_count(), manager.rules.len());
    }

    #[tokio::test]
    async fn test_extension_check() {
        let config = WhitelistManagerConfig::default();
        let manager = WhitelistManager::new(&config).unwrap();
        
        // 使用默认规则中的扩展ID进行测试
        let result = manager.check_extension(
            "nkbihfbeogaeaoehlefnkodbefgpgknn",
            "chrome-extension://nkbihfbeogaeaoehlefnkodbefgpgknn",
            &BrowserType::Chrome
        ).await.unwrap();
        
        assert!(result.is_allowed);
        assert_eq!(result.rule_type, WhitelistRuleType::Official);
        assert!(result.matched_rule.is_some());
    }

    #[tokio::test]
    async fn test_unknown_extension() {
        let config = WhitelistManagerConfig::default();
        let manager = WhitelistManager::new(&config).unwrap();
        
        let result = manager.check_extension(
            "unknown-extension",
            "chrome-extension://unknown-extension",
            &BrowserType::Chrome
        ).await.unwrap();
        
        assert!(!result.is_allowed);
        assert!(result.matched_rule.is_none());
    }
} 