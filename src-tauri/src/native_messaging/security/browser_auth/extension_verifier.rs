//! 扩展验证器模块
//!
//! 负责验证浏览器扩展ID的格式和有效性

use super::{BrowserType, BrowserAuthError};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::SystemTime;
use regex::Regex;
use tracing::{debug, warn};

/// 扩展验证器配置
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ExtensionVerifierConfig {
    /// 启用格式验证
    pub enable_format_validation: bool,
    /// 启用长度验证
    pub enable_length_validation: bool,
    /// 自定义验证规则
    pub custom_rules: HashMap<BrowserType, ExtensionValidationRule>,
}

impl Default for ExtensionVerifierConfig {
    fn default() -> Self {
        let mut custom_rules = HashMap::new();
        
        // Chrome/Chromium 扩展ID规则
        custom_rules.insert(BrowserType::Chrome, ExtensionValidationRule {
            pattern: r"^[a-p]{32}$".to_string(),
            min_length: 32,
            max_length: 32,
            allowed_chars: "abcdefghijklmnop".to_string(),
            description: "Chrome 扩展ID必须是32位小写字母a-p".to_string(),
        });
        
        custom_rules.insert(BrowserType::Chromium, ExtensionValidationRule {
            pattern: r"^[a-p]{32}$".to_string(),
            min_length: 32,
            max_length: 32,
            allowed_chars: "abcdefghijklmnop".to_string(),
            description: "Chromium 扩展ID必须是32位小写字母a-p".to_string(),
        });
        
        // Firefox 扩展ID规则
        custom_rules.insert(BrowserType::Firefox, ExtensionValidationRule {
            pattern: r"^[\w\-\.@]+$".to_string(),
            min_length: 1,
            max_length: 255,
            allowed_chars: "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_.@".to_string(),
            description: "Firefox 扩展ID可以是email格式或UUID格式".to_string(),
        });
        
        // Edge 扩展ID规则（与Chrome相同）
        custom_rules.insert(BrowserType::Edge, ExtensionValidationRule {
            pattern: r"^[a-p]{32}$".to_string(),
            min_length: 32,
            max_length: 32,
            allowed_chars: "abcdefghijklmnop".to_string(),
            description: "Edge 扩展ID必须是32位小写字母a-p".to_string(),
        });
        
        // Safari 扩展ID规则
        custom_rules.insert(BrowserType::Safari, ExtensionValidationRule {
            pattern: r"^[A-Z0-9]{10}\.[a-zA-Z0-9\-\.]+$".to_string(),
            min_length: 20,
            max_length: 255,
            allowed_chars: "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-.".to_string(),
            description: "Safari 扩展ID格式为Team ID + Bundle ID".to_string(),
        });
        
        Self {
            enable_format_validation: true,
            enable_length_validation: true,
            custom_rules,
        }
    }
}

/// 扩展验证规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExtensionValidationRule {
    /// 正则表达式模式
    pub pattern: String,
    /// 最小长度
    pub min_length: usize,
    /// 最大长度
    pub max_length: usize,
    /// 允许的字符
    pub allowed_chars: String,
    /// 规则描述
    pub description: String,
}

/// 扩展验证器
#[derive(Debug)]
pub struct ExtensionVerifier {
    config: ExtensionVerifierConfig,
    compiled_patterns: HashMap<BrowserType, Regex>,
}

impl ExtensionVerifier {
    /// 创建新的扩展验证器
    pub fn new(config: &ExtensionVerifierConfig) -> Result<Self, BrowserAuthError> {
        let mut compiled_patterns = HashMap::new();
        
        // 预编译所有正则表达式
        for (browser_type, rule) in &config.custom_rules {
            match Regex::new(&rule.pattern) {
                Ok(regex) => {
                    compiled_patterns.insert(browser_type.clone(), regex);
                }
                Err(e) => {
                    return Err(BrowserAuthError::ConfigError(
                        format!("无效的正则表达式模式 for {:?}: {}", browser_type, e)
                    ));
                }
            }
        }
        
        Ok(Self {
            config: config.clone(),
            compiled_patterns,
        })
    }
    
    /// 验证扩展ID
    pub async fn verify_extension_id(&self, extension_id: &str, browser: &BrowserType) -> Result<ExtensionVerificationResult, BrowserAuthError> {
        debug!("验证扩展ID: {} for {:?}", extension_id, browser);
        
        let mut result = ExtensionVerificationResult {
            extension_id: extension_id.to_string(),
            browser: browser.clone(),
            is_valid: false,
            validation_details: Vec::new(),
            verified_at: SystemTime::now(),
        };
        
        // 获取验证规则
        let rule = match self.config.custom_rules.get(browser) {
            Some(rule) => rule,
            None => {
                result.validation_details.push(ValidationDetail {
                    check_type: ValidationCheckType::BrowserSupport,
                    is_passed: false,
                    message: format!("不支持的浏览器类型: {:?}", browser),
                });
                return Ok(result);
            }
        };
        
        // 1. 基础检查
        result.validation_details.push(self.check_basic_requirements(extension_id)?);
        
        // 2. 长度验证
        if self.config.enable_length_validation {
            result.validation_details.push(self.check_length(extension_id, rule)?);
        }
        
        // 3. 格式验证
        if self.config.enable_format_validation {
            result.validation_details.push(self.check_format(extension_id, browser, rule)?);
        }
        
        // 4. 字符验证
        result.validation_details.push(self.check_allowed_characters(extension_id, rule)?);
        
        // 5. 浏览器特定验证
        result.validation_details.push(self.check_browser_specific_rules(extension_id, browser)?);
        
        // 计算最终结果
        result.is_valid = result.validation_details.iter().all(|detail| detail.is_passed);
        
        if result.is_valid {
            debug!("扩展ID验证成功: {}", extension_id);
        } else {
            warn!("扩展ID验证失败: {} - 失败的检查: {:?}", 
                extension_id, 
                result.validation_details.iter()
                    .filter(|d| !d.is_passed)
                    .map(|d| &d.check_type)
                    .collect::<Vec<_>>()
            );
        }
        
        Ok(result)
    }
    
    /// 基础要求检查
    fn check_basic_requirements(&self, extension_id: &str) -> Result<ValidationDetail, BrowserAuthError> {
        let is_passed = !extension_id.is_empty() && !extension_id.chars().any(|c| c.is_whitespace());
        
        Ok(ValidationDetail {
            check_type: ValidationCheckType::BasicRequirements,
            is_passed,
            message: if is_passed {
                "基础要求检查通过".to_string()
            } else {
                "扩展ID不能为空或包含空白字符".to_string()
            },
        })
    }
    
    /// 长度检查
    fn check_length(&self, extension_id: &str, rule: &ExtensionValidationRule) -> Result<ValidationDetail, BrowserAuthError> {
        let length = extension_id.len();
        let is_passed = length >= rule.min_length && length <= rule.max_length;
        
        Ok(ValidationDetail {
            check_type: ValidationCheckType::Length,
            is_passed,
            message: if is_passed {
                format!("长度检查通过: {} 字符", length)
            } else {
                format!("长度不符合要求: {} (要求: {}-{})", length, rule.min_length, rule.max_length)
            },
        })
    }
    
    /// 格式检查
    fn check_format(&self, extension_id: &str, browser: &BrowserType, rule: &ExtensionValidationRule) -> Result<ValidationDetail, BrowserAuthError> {
        let regex = self.compiled_patterns.get(browser).ok_or_else(|| {
            BrowserAuthError::ConfigError(format!("未找到 {:?} 的正则表达式", browser))
        })?;
        
        let is_passed = regex.is_match(extension_id);
        
        Ok(ValidationDetail {
            check_type: ValidationCheckType::Format,
            is_passed,
            message: if is_passed {
                "格式检查通过".to_string()
            } else {
                format!("格式不符合要求: {}", rule.description)
            },
        })
    }
    
    /// 允许字符检查
    fn check_allowed_characters(&self, extension_id: &str, rule: &ExtensionValidationRule) -> Result<ValidationDetail, BrowserAuthError> {
        let allowed_chars: std::collections::HashSet<char> = rule.allowed_chars.chars().collect();
        let invalid_chars: Vec<char> = extension_id.chars()
            .filter(|c| !allowed_chars.contains(c))
            .collect();
        
        let is_passed = invalid_chars.is_empty();
        
        Ok(ValidationDetail {
            check_type: ValidationCheckType::AllowedCharacters,
            is_passed,
            message: if is_passed {
                "字符检查通过".to_string()
            } else {
                format!("包含不允许的字符: {:?}", invalid_chars)
            },
        })
    }
    
    /// 浏览器特定规则检查
    fn check_browser_specific_rules(&self, extension_id: &str, browser: &BrowserType) -> Result<ValidationDetail, BrowserAuthError> {
        let (is_passed, message) = match browser {
            BrowserType::Chrome | BrowserType::Chromium | BrowserType::Edge => {
                // Chrome 系扩展ID必须是32位小写字母a-p
                if extension_id.len() == 32 && extension_id.chars().all(|c| ('a'..='p').contains(&c)) {
                    (true, "Chrome系扩展ID格式验证通过".to_string())
                } else {
                    (false, "Chrome系扩展ID必须是32位小写字母a-p".to_string())
                }
            }
            BrowserType::Firefox => {
                // Firefox 扩展ID可以是email格式或UUID格式
                if extension_id.contains('@') || extension_id.contains('-') {
                    (true, "Firefox扩展ID格式验证通过".to_string())
                } else {
                    (false, "Firefox扩展ID应该是email格式或UUID格式".to_string())
                }
            }
            BrowserType::Safari => {
                // Safari 扩展ID应该包含Team ID和Bundle ID
                if extension_id.contains('.') && extension_id.len() > 10 {
                    let parts: Vec<&str> = extension_id.split('.').collect();
                    if parts.len() >= 2 && parts[0].len() == 10 && parts[0].chars().all(|c| c.is_ascii_alphanumeric()) {
                        (true, "Safari扩展ID格式验证通过".to_string())
                    } else {
                        (false, "Safari扩展ID格式不正确，应为TeamID.BundleID".to_string())
                    }
                } else {
                    (false, "Safari扩展ID应包含TeamID和BundleID".to_string())
                }
            }
            _ => (true, "未知浏览器，跳过特定规则检查".to_string()),
        };
        
        Ok(ValidationDetail {
            check_type: ValidationCheckType::BrowserSpecific,
            is_passed,
            message,
        })
    }
}

/// 扩展验证结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExtensionVerificationResult {
    /// 扩展ID
    pub extension_id: String,
    /// 浏览器类型
    pub browser: BrowserType,
    /// 是否有效
    pub is_valid: bool,
    /// 验证详情
    pub validation_details: Vec<ValidationDetail>,
    /// 验证时间
    pub verified_at: SystemTime,
}

impl ExtensionVerificationResult {
    /// 获取失败的检查
    pub fn failed_checks(&self) -> Vec<&ValidationDetail> {
        self.validation_details.iter()
            .filter(|detail| !detail.is_passed)
            .collect()
    }
    
    /// 获取验证摘要
    pub fn summary(&self) -> String {
        format!(
            "Extension {} on {:?}: {} ({}/{} checks passed)",
            self.extension_id,
            self.browser,
            if self.is_valid { "VALID" } else { "INVALID" },
            self.validation_details.iter().filter(|d| d.is_passed).count(),
            self.validation_details.len()
        )
    }
}

/// 验证详情
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationDetail {
    /// 检查类型
    pub check_type: ValidationCheckType,
    /// 是否通过
    pub is_passed: bool,
    /// 详细消息
    pub message: String,
}

/// 验证检查类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ValidationCheckType {
    /// 基础要求
    BasicRequirements,
    /// 长度检查
    Length,
    /// 格式检查
    Format,
    /// 允许字符检查
    AllowedCharacters,
    /// 浏览器特定规则
    BrowserSpecific,
    /// 浏览器支持检查
    BrowserSupport,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_chrome_extension_id_validation() {
        let config = ExtensionVerifierConfig::default();
        let verifier = ExtensionVerifier::new(&config).unwrap();
        
        // 有效的Chrome扩展ID
        let valid_id = "abcdefghijklmnopabcdefghijklmnop";
        let result = verifier.verify_extension_id(valid_id, &BrowserType::Chrome).await.unwrap();
        assert!(result.is_valid);
        
        // 无效的Chrome扩展ID（包含q字符）
        let invalid_id = "abcdefghijklmnopqrstuvwxyzabcdef";
        let result = verifier.verify_extension_id(invalid_id, &BrowserType::Chrome).await.unwrap();
        assert!(!result.is_valid);
    }

    #[tokio::test]
    async fn test_firefox_extension_id_validation() {
        let config = ExtensionVerifierConfig::default();
        let verifier = ExtensionVerifier::new(&config).unwrap();
        
        // 有效的Firefox扩展ID（email格式）
        let valid_id = "<EMAIL>";
        let result = verifier.verify_extension_id(valid_id, &BrowserType::Firefox).await.unwrap();
        assert!(result.is_valid);
        
        // 有效的Firefox扩展ID（UUID格式）
        let valid_uuid = "550e8400-e29b-41d4-a716-************";
        let result = verifier.verify_extension_id(valid_uuid, &BrowserType::Firefox).await.unwrap();
        assert!(result.is_valid);
    }

    #[tokio::test]
    async fn test_safari_extension_id_validation() {
        let config = ExtensionVerifierConfig::default();
        let verifier = ExtensionVerifier::new(&config).unwrap();
        
        // 有效的Safari扩展ID
        let valid_id = "ABCD123456.com.example.myextension";
        let result = verifier.verify_extension_id(valid_id, &BrowserType::Safari).await.unwrap();
        assert!(result.is_valid);
        
        // 无效的Safari扩展ID（TeamID格式错误）
        let invalid_id = "ABC.com.example.myextension";
        let result = verifier.verify_extension_id(invalid_id, &BrowserType::Safari).await.unwrap();
        assert!(!result.is_valid);
    }

    #[tokio::test]
    async fn test_empty_extension_id() {
        let config = ExtensionVerifierConfig::default();
        let verifier = ExtensionVerifier::new(&config).unwrap();
        
        let result = verifier.verify_extension_id("", &BrowserType::Chrome).await.unwrap();
        assert!(!result.is_valid);
        
        let failed_checks = result.failed_checks();
        assert!(failed_checks.iter().any(|check| check.check_type == ValidationCheckType::BasicRequirements));
    }

    #[tokio::test]
    async fn test_unsupported_browser() {
        let config = ExtensionVerifierConfig::default();
        let verifier = ExtensionVerifier::new(&config).unwrap();
        
        let result = verifier.verify_extension_id("test-id", &BrowserType::Unknown("TestBrowser".to_string())).await.unwrap();
        assert!(!result.is_valid);
        
        let failed_checks = result.failed_checks();
        assert!(failed_checks.iter().any(|check| check.check_type == ValidationCheckType::BrowserSupport));
    }
} 