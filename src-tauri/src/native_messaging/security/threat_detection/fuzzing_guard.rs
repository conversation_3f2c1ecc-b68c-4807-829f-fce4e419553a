//! 模糊测试防护模块
//!
//! 负责检测和防护模糊测试攻击

use super::ThreatDetectionError;
use crate::native_messaging::error::{NativeMessagingError, Result};
use crate::native_messaging::protocol::message::NativeMessage;
use serde::{Deserialize, Serialize};
use std::time::SystemTime;
use tracing::{debug, warn};

/// 模糊测试防护配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FuzzingGuardConfig {
    /// 启用随机数据检测
    pub enable_random_data_detection: bool,
    /// 启用边界值检测
    pub enable_boundary_value_detection: bool,
    /// 启用格式异常检测
    pub enable_format_anomaly_detection: bool,
    /// 随机性阈值
    pub randomness_threshold: f64,
}

impl Default for FuzzingGuardConfig {
    fn default() -> Self {
        Self {
            enable_random_data_detection: true,
            enable_boundary_value_detection: true,
            enable_format_anomaly_detection: true,
            randomness_threshold: 0.8,
        }
    }
}

/// 模糊测试防护器
#[derive(Debug)]
pub struct FuzzingGuard {
    config: FuzzingGuardConfig,
    fuzzing_patterns: Vec<FuzzingPattern>,
}

impl FuzzingGuard {
    /// 创建新的模糊测试防护器
    pub fn new(config: &FuzzingGuardConfig) -> Result<Self> {
        Ok(Self {
            config: config.clone(),
            fuzzing_patterns: Self::initialize_patterns(),
        })
    }

    /// 检测模糊测试
    pub async fn detect_fuzzing(&self, message: &NativeMessage) -> Result<FuzzingDetectionResult> {
        debug!("检测模糊测试: {}", message.request_id);

        let mut result = FuzzingDetectionResult {
            message_id: message.request_id.clone(),
            is_safe: true,
            detected_patterns: Vec::new(),
            detection_details: Vec::new(),
            randomness_score: 0.0,
            detected_at: SystemTime::now(),
        };

        let message_content = serde_json::to_string(message)?;

        // 1. 随机数据检测
        if self.config.enable_random_data_detection {
            result.randomness_score = self.calculate_randomness(&message_content);
            if result.randomness_score > self.config.randomness_threshold {
                result.is_safe = false;
                result.detection_details.push("检测到高随机性数据".to_string());
            }
        }

        // 2. 模式匹配
        for pattern in &self.fuzzing_patterns {
            if message_content.contains(&pattern.pattern) {
                result.detected_patterns.push(pattern.clone());
                result.is_safe = false;
            }
        }

        if result.is_safe {
            debug!("模糊测试检测通过: {}", message.request_id);
        } else {
            warn!("检测到模糊测试: {}", message.request_id);
        }

        Ok(result)
    }

    /// 初始化模糊测试模式
    fn initialize_patterns() -> Vec<FuzzingPattern> {
        vec![
            FuzzingPattern {
                name: "Buffer Overflow".to_string(),
                pattern: "A".repeat(1000),
                description: "缓冲区溢出测试".to_string(),
            },
            FuzzingPattern {
                name: "Format String".to_string(),
                pattern: "%s%s%s%s".to_string(),
                description: "格式字符串测试".to_string(),
            },
            FuzzingPattern {
                name: "Null Bytes".to_string(),
                pattern: "\0\0\0\0".to_string(),
                description: "空字节测试".to_string(),
            },
        ]
    }

    /// 计算随机性分数
    fn calculate_randomness(&self, content: &str) -> f64 {
        if content.is_empty() {
            return 0.0;
        }

        let bytes = content.as_bytes();
        let mut char_counts = [0u32; 256];
        
        for &byte in bytes {
            char_counts[byte as usize] += 1;
        }

        // 计算熵
        let len = bytes.len() as f64;
        let mut entropy = 0.0;
        
        for &count in &char_counts {
            if count > 0 {
                let p = count as f64 / len;
                entropy -= p * p.log2();
            }
        }

        entropy / 8.0 // 归一化到0-1范围
    }
}

/// 模糊测试模式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FuzzingPattern {
    /// 模式名称
    pub name: String,
    /// 模式内容
    pub pattern: String,
    /// 描述
    pub description: String,
}

/// 模糊测试检测结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FuzzingDetectionResult {
    /// 消息ID
    pub message_id: String,
    /// 是否安全
    pub is_safe: bool,
    /// 检测到的模式
    pub detected_patterns: Vec<FuzzingPattern>,
    /// 检测详情
    pub detection_details: Vec<String>,
    /// 随机性分数
    pub randomness_score: f64,
    /// 检测时间
    pub detected_at: SystemTime,
}
