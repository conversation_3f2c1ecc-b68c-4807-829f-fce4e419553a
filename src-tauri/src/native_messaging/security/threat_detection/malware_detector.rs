//! 恶意软件检测器模块
//!
//! 负责检测消息中的恶意软件特征和可疑行为

use super::ThreatDetectionError;
use crate::native_messaging::error::{NativeMessagingError, Result};
use crate::native_messaging::protocol::message::NativeMessage;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::SystemTime;
use regex::Regex;
use sha2::{Sha256, Digest};
use tracing::{debug, warn, error};

/// 恶意软件检测器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MalwareDetectorConfig {
    /// 启用签名检测
    pub enable_signature_detection: bool,
    /// 启用行为检测
    pub enable_behavior_detection: bool,
    /// 启用哈希检测
    pub enable_hash_detection: bool,
    /// 签名数据库路径
    pub signature_db_path: Option<String>,
    /// 恶意哈希数据库路径
    pub hash_db_path: Option<String>,
    /// 检测敏感度
    pub detection_sensitivity: DetectionSensitivity,
    /// 自定义签名
    pub custom_signatures: Vec<MalwareSignature>,
    /// 已知恶意哈希
    pub known_malicious_hashes: Vec<String>,
}

impl Default for MalwareDetectorConfig {
    fn default() -> Self {
        Self {
            enable_signature_detection: true,
            enable_behavior_detection: true,
            enable_hash_detection: true,
            signature_db_path: None,
            hash_db_path: None,
            detection_sensitivity: DetectionSensitivity::Medium,
            custom_signatures: Self::default_signatures(),
            known_malicious_hashes: Vec::new(),
        }
    }
}

impl MalwareDetectorConfig {
    /// 默认恶意软件签名
    fn default_signatures() -> Vec<MalwareSignature> {
        vec![
            MalwareSignature {
                id: "malware_001".to_string(),
                name: "Suspicious PowerShell".to_string(),
                pattern: r"(?i)powershell.*-enc.*[A-Za-z0-9+/=]{50,}".to_string(),
                signature_type: SignatureType::Regex,
                severity: MalwareSeverity::High,
                description: "检测到可疑的PowerShell编码命令".to_string(),
                enabled: true,
            },
            MalwareSignature {
                id: "malware_002".to_string(),
                name: "Base64 Encoded Script".to_string(),
                pattern: r"(?i)(eval|exec)\s*\(\s*['\"]?[A-Za-z0-9+/=]{100,}".to_string(),
                signature_type: SignatureType::Regex,
                severity: MalwareSeverity::Medium,
                description: "检测到Base64编码的可疑脚本".to_string(),
                enabled: true,
            },
            MalwareSignature {
                id: "malware_003".to_string(),
                name: "Suspicious File Extension".to_string(),
                pattern: r"(?i)\.(scr|pif|com|bat|cmd|exe|vbs|js|jar)$".to_string(),
                signature_type: SignatureType::Regex,
                severity: MalwareSeverity::Medium,
                description: "检测到可疑的文件扩展名".to_string(),
                enabled: true,
            },
            MalwareSignature {
                id: "malware_004".to_string(),
                name: "Obfuscated JavaScript".to_string(),
                pattern: r"(?i)eval\s*\(\s*unescape\s*\(|document\.write\s*\(\s*unescape".to_string(),
                signature_type: SignatureType::Regex,
                severity: MalwareSeverity::High,
                description: "检测到混淆的JavaScript代码".to_string(),
                enabled: true,
            },
            MalwareSignature {
                id: "malware_005".to_string(),
                name: "Suspicious Registry Access".to_string(),
                pattern: r"(?i)HKEY_(LOCAL_MACHINE|CURRENT_USER)\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run".to_string(),
                signature_type: SignatureType::Regex,
                severity: MalwareSeverity::Medium,
                description: "检测到可疑的注册表访问".to_string(),
                enabled: true,
            },
        ]
    }
}

/// 检测敏感度
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum DetectionSensitivity {
    /// 低敏感度
    Low,
    /// 中等敏感度
    Medium,
    /// 高敏感度
    High,
    /// 极高敏感度
    Paranoid,
}

/// 恶意软件检测器
#[derive(Debug)]
pub struct MalwareDetector {
    config: MalwareDetectorConfig,
    compiled_signatures: Vec<CompiledSignature>,
    behavior_patterns: Vec<BehaviorPattern>,
    detection_stats: MalwareDetectionStats,
}

impl MalwareDetector {
    /// 创建新的恶意软件检测器
    pub fn new(config: &MalwareDetectorConfig) -> Result<Self> {
        let mut detector = Self {
            config: config.clone(),
            compiled_signatures: Vec::new(),
            behavior_patterns: Vec::new(),
            detection_stats: MalwareDetectionStats::default(),
        };

        // 编译签名
        detector.compile_signatures()?;
        
        // 初始化行为模式
        detector.initialize_behavior_patterns()?;

        Ok(detector)
    }

    /// 检测恶意软件
    pub async fn detect_malware(&mut self, message: &NativeMessage) -> Result<MalwareDetectionResult> {
        debug!("检测恶意软件: {}", message.request_id);

        let mut result = MalwareDetectionResult {
            message_id: message.request_id.clone(),
            is_clean: true,
            detected_signatures: Vec::new(),
            detected_behaviors: Vec::new(),
            malicious_hashes: Vec::new(),
            detection_details: Vec::new(),
            detected_at: SystemTime::now(),
        };

        self.detection_stats.total_scans += 1;

        // 1. 签名检测
        if self.config.enable_signature_detection {
            self.perform_signature_detection(message, &mut result).await?;
        }

        // 2. 行为检测
        if self.config.enable_behavior_detection {
            self.perform_behavior_detection(message, &mut result).await?;
        }

        // 3. 哈希检测
        if self.config.enable_hash_detection {
            self.perform_hash_detection(message, &mut result).await?;
        }

        // 更新统计信息
        if !result.is_clean {
            self.detection_stats.malware_detected += 1;
        }

        if result.is_clean {
            debug!("恶意软件检测通过: {}", message.request_id);
        } else {
            warn!("检测到恶意软件: {} - 签名: {}, 行为: {}, 哈希: {}", 
                message.request_id,
                result.detected_signatures.len(),
                result.detected_behaviors.len(),
                result.malicious_hashes.len()
            );
        }

        Ok(result)
    }

    /// 执行签名检测
    async fn perform_signature_detection(&self, message: &NativeMessage, result: &mut MalwareDetectionResult) -> Result<()> {
        let message_content = serde_json::to_string(message)?;
        
        for compiled_sig in &self.compiled_signatures {
            if compiled_sig.signature.enabled {
                match &compiled_sig.signature.signature_type {
                    SignatureType::Regex => {
                        if compiled_sig.regex.is_match(&message_content) {
                            result.detected_signatures.push(compiled_sig.signature.clone());
                            result.is_clean = false;
                            result.detection_details.push(format!(
                                "签名匹配: {} - {}", 
                                compiled_sig.signature.name, 
                                compiled_sig.signature.description
                            ));
                        }
                    }
                    SignatureType::String => {
                        if message_content.contains(&compiled_sig.signature.pattern) {
                            result.detected_signatures.push(compiled_sig.signature.clone());
                            result.is_clean = false;
                            result.detection_details.push(format!(
                                "字符串匹配: {} - {}", 
                                compiled_sig.signature.name, 
                                compiled_sig.signature.description
                            ));
                        }
                    }
                    SignatureType::Yara => {
                        // YARA规则检测的简化实现
                        // 实际应用中需要集成YARA引擎
                        result.detection_details.push("YARA检测已跳过".to_string());
                    }
                }
            }
        }

        Ok(())
    }

    /// 执行行为检测
    async fn perform_behavior_detection(&self, message: &NativeMessage, result: &mut MalwareDetectionResult) -> Result<()> {
        let message_content = serde_json::to_string(message)?;
        
        for pattern in &self.behavior_patterns {
            if pattern.enabled {
                let mut score = 0;
                
                for indicator in &pattern.indicators {
                    if message_content.contains(indicator) {
                        score += 1;
                    }
                }
                
                // 如果匹配的指标数量超过阈值，认为是可疑行为
                if score >= pattern.threshold {
                    result.detected_behaviors.push(pattern.clone());
                    result.is_clean = false;
                    result.detection_details.push(format!(
                        "行为检测: {} - 匹配指标: {}/{}", 
                        pattern.name, score, pattern.indicators.len()
                    ));
                }
            }
        }

        Ok(())
    }

    /// 执行哈希检测
    async fn perform_hash_detection(&self, message: &NativeMessage, result: &mut MalwareDetectionResult) -> Result<()> {
        // 计算消息内容的哈希
        let message_content = serde_json::to_string(message)?;
        let mut hasher = Sha256::new();
        hasher.update(message_content.as_bytes());
        let hash = hex::encode(hasher.finalize());

        // 检查是否在已知恶意哈希列表中
        if self.config.known_malicious_hashes.contains(&hash) {
            result.malicious_hashes.push(hash.clone());
            result.is_clean = false;
            result.detection_details.push(format!("恶意哈希匹配: {}", &hash[..16]));
        }

        // 检查消息中是否包含已知恶意哈希
        for malicious_hash in &self.config.known_malicious_hashes {
            if message_content.contains(malicious_hash) {
                result.malicious_hashes.push(malicious_hash.clone());
                result.is_clean = false;
                result.detection_details.push(format!("消息包含恶意哈希: {}", &malicious_hash[..16]));
            }
        }

        Ok(())
    }

    /// 编译签名
    fn compile_signatures(&mut self) -> Result<()> {
        for signature in &self.config.custom_signatures {
            if signature.enabled && signature.signature_type == SignatureType::Regex {
                match Regex::new(&signature.pattern) {
                    Ok(regex) => {
                        self.compiled_signatures.push(CompiledSignature {
                            signature: signature.clone(),
                            regex,
                        });
                    }
                    Err(e) => {
                        warn!("无法编译恶意软件签名 '{}': {}", signature.name, e);
                    }
                }
            } else {
                // 对于非正则表达式签名，创建一个虚拟的正则表达式
                self.compiled_signatures.push(CompiledSignature {
                    signature: signature.clone(),
                    regex: Regex::new("").unwrap(), // 不会被使用
                });
            }
        }
        Ok(())
    }

    /// 初始化行为模式
    fn initialize_behavior_patterns(&mut self) -> Result<()> {
        self.behavior_patterns = vec![
            BehaviorPattern {
                name: "Credential Harvesting".to_string(),
                description: "检测凭据收集行为".to_string(),
                indicators: vec![
                    "password".to_string(),
                    "credential".to_string(),
                    "login".to_string(),
                    "auth".to_string(),
                    "token".to_string(),
                ],
                threshold: 3,
                severity: MalwareSeverity::High,
                enabled: true,
            },
            BehaviorPattern {
                name: "Data Exfiltration".to_string(),
                description: "检测数据泄露行为".to_string(),
                indicators: vec![
                    "upload".to_string(),
                    "send".to_string(),
                    "transmit".to_string(),
                    "export".to_string(),
                    "copy".to_string(),
                ],
                threshold: 2,
                severity: MalwareSeverity::Medium,
                enabled: true,
            },
            BehaviorPattern {
                name: "System Modification".to_string(),
                description: "检测系统修改行为".to_string(),
                indicators: vec![
                    "registry".to_string(),
                    "startup".to_string(),
                    "service".to_string(),
                    "process".to_string(),
                    "file".to_string(),
                ],
                threshold: 2,
                severity: MalwareSeverity::Medium,
                enabled: true,
            },
        ];
        Ok(())
    }

    /// 获取检测统计信息
    pub fn get_detection_stats(&self) -> &MalwareDetectionStats {
        &self.detection_stats
    }

    /// 添加自定义签名
    pub fn add_custom_signature(&mut self, signature: MalwareSignature) -> Result<()> {
        if signature.signature_type == SignatureType::Regex {
            let regex = Regex::new(&signature.pattern)
                .map_err(|e| NativeMessagingError::SecurityError(
                    format!("无效的正则表达式: {}", e)
                ))?;
            
            self.compiled_signatures.push(CompiledSignature {
                signature: signature.clone(),
                regex,
            });
        }
        
        self.config.custom_signatures.push(signature);
        Ok(())
    }

    /// 更新恶意哈希数据库
    pub fn update_malicious_hashes(&mut self, hashes: Vec<String>) {
        self.config.known_malicious_hashes = hashes;
    }
}

/// 编译后的签名
#[derive(Debug)]
struct CompiledSignature {
    signature: MalwareSignature,
    regex: Regex,
}

/// 恶意软件签名
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MalwareSignature {
    /// 签名ID
    pub id: String,
    /// 签名名称
    pub name: String,
    /// 匹配模式
    pub pattern: String,
    /// 签名类型
    pub signature_type: SignatureType,
    /// 严重程度
    pub severity: MalwareSeverity,
    /// 描述
    pub description: String,
    /// 是否启用
    pub enabled: bool,
}

/// 签名类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum SignatureType {
    /// 正则表达式
    Regex,
    /// 字符串匹配
    String,
    /// YARA规则
    Yara,
}

/// 恶意软件严重程度
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum MalwareSeverity {
    /// 低
    Low,
    /// 中
    Medium,
    /// 高
    High,
    /// 严重
    Critical,
}

/// 行为模式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BehaviorPattern {
    /// 模式名称
    pub name: String,
    /// 描述
    pub description: String,
    /// 行为指标
    pub indicators: Vec<String>,
    /// 匹配阈值
    pub threshold: usize,
    /// 严重程度
    pub severity: MalwareSeverity,
    /// 是否启用
    pub enabled: bool,
}

/// 恶意软件检测结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MalwareDetectionResult {
    /// 消息ID
    pub message_id: String,
    /// 是否干净
    pub is_clean: bool,
    /// 检测到的签名
    pub detected_signatures: Vec<MalwareSignature>,
    /// 检测到的行为
    pub detected_behaviors: Vec<BehaviorPattern>,
    /// 恶意哈希
    pub malicious_hashes: Vec<String>,
    /// 检测详情
    pub detection_details: Vec<String>,
    /// 检测时间
    pub detected_at: SystemTime,
}

/// 恶意软件检测统计信息
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct MalwareDetectionStats {
    /// 总扫描次数
    pub total_scans: u64,
    /// 检测到的恶意软件数
    pub malware_detected: u64,
    /// 签名匹配次数
    pub signature_matches: u64,
    /// 行为检测次数
    pub behavior_detections: u64,
    /// 哈希匹配次数
    pub hash_matches: u64,
}
