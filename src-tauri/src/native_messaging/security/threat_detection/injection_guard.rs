//! 注入攻击防护器模块
//!
//! 负责检测和防护各种注入攻击，包括SQL注入、XSS、命令注入等

use super::ThreatDetectionError;
use crate::native_messaging::error::{NativeMessagingError, Result};
use crate::native_messaging::protocol::message::NativeMessage;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::SystemTime;
use regex::Regex;
use tracing::{debug, warn, error};

/// 注入攻击防护器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InjectionGuardConfig {
    /// 启用SQL注入检测
    pub enable_sql_injection_detection: bool,
    /// 启用XSS检测
    pub enable_xss_detection: bool,
    /// 启用命令注入检测
    pub enable_command_injection_detection: bool,
    /// 启用LDAP注入检测
    pub enable_ldap_injection_detection: bool,
    /// 启用NoSQL注入检测
    pub enable_nosql_injection_detection: bool,
    /// 检测严格程度
    pub detection_strictness: InjectionDetectionStrictness,
    /// 自定义注入模式
    pub custom_patterns: Vec<InjectionPattern>,
    /// 白名单模式
    pub whitelist_mode: bool,
    /// 白名单模式
    pub whitelist_patterns: Vec<String>,
}

impl Default for InjectionGuardConfig {
    fn default() -> Self {
        Self {
            enable_sql_injection_detection: true,
            enable_xss_detection: true,
            enable_command_injection_detection: true,
            enable_ldap_injection_detection: true,
            enable_nosql_injection_detection: true,
            detection_strictness: InjectionDetectionStrictness::Medium,
            custom_patterns: Vec::new(),
            whitelist_mode: false,
            whitelist_patterns: Vec::new(),
        }
    }
}

/// 注入检测严格程度
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum InjectionDetectionStrictness {
    /// 宽松
    Lenient,
    /// 中等
    Medium,
    /// 严格
    Strict,
    /// 极严格
    Paranoid,
}

/// 注入攻击防护器
#[derive(Debug)]
pub struct InjectionGuard {
    config: InjectionGuardConfig,
    sql_patterns: Vec<Regex>,
    xss_patterns: Vec<Regex>,
    command_patterns: Vec<Regex>,
    ldap_patterns: Vec<Regex>,
    nosql_patterns: Vec<Regex>,
    custom_patterns: Vec<CompiledInjectionPattern>,
    detection_stats: InjectionDetectionStats,
}

impl InjectionGuard {
    /// 创建新的注入攻击防护器
    pub fn new(config: &InjectionGuardConfig) -> Result<Self> {
        let mut guard = Self {
            config: config.clone(),
            sql_patterns: Vec::new(),
            xss_patterns: Vec::new(),
            command_patterns: Vec::new(),
            ldap_patterns: Vec::new(),
            nosql_patterns: Vec::new(),
            custom_patterns: Vec::new(),
            detection_stats: InjectionDetectionStats::default(),
        };

        // 初始化检测模式
        guard.initialize_sql_patterns()?;
        guard.initialize_xss_patterns()?;
        guard.initialize_command_patterns()?;
        guard.initialize_ldap_patterns()?;
        guard.initialize_nosql_patterns()?;
        guard.compile_custom_patterns()?;

        Ok(guard)
    }

    /// 检测注入攻击
    pub async fn detect_injection(&mut self, message: &NativeMessage) -> Result<InjectionDetectionResult> {
        debug!("检测注入攻击: {}", message.request_id);

        let mut result = InjectionDetectionResult {
            message_id: message.request_id.clone(),
            is_safe: true,
            injection_type: None,
            detected_patterns: Vec::new(),
            detection_details: Vec::new(),
            confidence_score: 0.0,
            detected_at: SystemTime::now(),
        };

        self.detection_stats.total_checks += 1;

        let message_content = serde_json::to_string(message)?;

        // 1. 白名单检查
        if self.config.whitelist_mode {
            if !self.is_whitelisted(&message_content) {
                result.is_safe = false;
                result.detection_details.push("内容不在白名单中".to_string());
                return Ok(result);
            }
        }

        // 2. SQL注入检测
        if self.config.enable_sql_injection_detection {
            if let Some(sql_result) = self.detect_sql_injection(&message_content)? {
                result.is_safe = false;
                result.injection_type = Some(InjectionType::SqlInjection);
                result.detected_patterns.extend(sql_result);
                result.detection_details.push("检测到SQL注入攻击".to_string());
                self.detection_stats.sql_injections_detected += 1;
            }
        }

        // 3. XSS检测
        if self.config.enable_xss_detection {
            if let Some(xss_result) = self.detect_xss(&message_content)? {
                result.is_safe = false;
                result.injection_type = Some(InjectionType::CrossSiteScripting);
                result.detected_patterns.extend(xss_result);
                result.detection_details.push("检测到XSS攻击".to_string());
                self.detection_stats.xss_attacks_detected += 1;
            }
        }

        // 4. 命令注入检测
        if self.config.enable_command_injection_detection {
            if let Some(cmd_result) = self.detect_command_injection(&message_content)? {
                result.is_safe = false;
                result.injection_type = Some(InjectionType::CommandInjection);
                result.detected_patterns.extend(cmd_result);
                result.detection_details.push("检测到命令注入攻击".to_string());
                self.detection_stats.command_injections_detected += 1;
            }
        }

        // 5. LDAP注入检测
        if self.config.enable_ldap_injection_detection {
            if let Some(ldap_result) = self.detect_ldap_injection(&message_content)? {
                result.is_safe = false;
                result.injection_type = Some(InjectionType::LdapInjection);
                result.detected_patterns.extend(ldap_result);
                result.detection_details.push("检测到LDAP注入攻击".to_string());
                self.detection_stats.ldap_injections_detected += 1;
            }
        }

        // 6. NoSQL注入检测
        if self.config.enable_nosql_injection_detection {
            if let Some(nosql_result) = self.detect_nosql_injection(&message_content)? {
                result.is_safe = false;
                result.injection_type = Some(InjectionType::NoSqlInjection);
                result.detected_patterns.extend(nosql_result);
                result.detection_details.push("检测到NoSQL注入攻击".to_string());
                self.detection_stats.nosql_injections_detected += 1;
            }
        }

        // 7. 自定义模式检测
        if let Some(custom_result) = self.detect_custom_patterns(&message_content)? {
            result.is_safe = false;
            result.injection_type = Some(InjectionType::Custom);
            result.detected_patterns.extend(custom_result);
            result.detection_details.push("检测到自定义注入模式".to_string());
            self.detection_stats.custom_patterns_detected += 1;
        }

        // 计算置信度分数
        result.confidence_score = self.calculate_confidence_score(&result);

        if !result.is_safe {
            self.detection_stats.total_injections_detected += 1;
        }

        if result.is_safe {
            debug!("注入攻击检测通过: {}", message.request_id);
        } else {
            warn!("检测到注入攻击: {} - 类型: {:?}, 置信度: {:.2}", 
                message.request_id, result.injection_type, result.confidence_score);
        }

        Ok(result)
    }

    /// 初始化SQL注入模式
    fn initialize_sql_patterns(&mut self) -> Result<()> {
        let patterns = vec![
            r"(?i)(union\s+select|union\s+all\s+select)",
            r"(?i)(select\s+.*\s+from\s+.*\s+where)",
            r"(?i)(insert\s+into|update\s+.*\s+set|delete\s+from)",
            r"(?i)(drop\s+table|create\s+table|alter\s+table)",
            r"(?i)(\'\s*or\s+\'\d+\'\s*=\s*\'\d+|\'\s*or\s+\d+\s*=\s*\d+)",
            r"(?i)(\'\s*;\s*drop\s+table|\'\s*;\s*delete\s+from)",
            r"(?i)(exec\s*\(|sp_executesql|xp_cmdshell)",
            r"(?i)(benchmark\s*\(|sleep\s*\(|waitfor\s+delay)",
        ];

        for pattern in patterns {
            if let Ok(regex) = Regex::new(pattern) {
                self.sql_patterns.push(regex);
            }
        }

        Ok(())
    }

    /// 初始化XSS模式
    fn initialize_xss_patterns(&mut self) -> Result<()> {
        let patterns = vec![
            r"(?i)<script[^>]*>.*?</script>",
            r"(?i)<iframe[^>]*>.*?</iframe>",
            r"(?i)<object[^>]*>.*?</object>",
            r"(?i)<embed[^>]*>.*?</embed>",
            r"(?i)javascript\s*:",
            r"(?i)on(load|error|click|mouseover|focus)\s*=",
            r"(?i)eval\s*\(|setTimeout\s*\(|setInterval\s*\(",
            r"(?i)document\.(cookie|write|location)",
            r"(?i)window\.(location|open)",
            r"(?i)<svg[^>]*onload\s*=",
        ];

        for pattern in patterns {
            if let Ok(regex) = Regex::new(pattern) {
                self.xss_patterns.push(regex);
            }
        }

        Ok(())
    }

    /// 初始化命令注入模式
    fn initialize_command_patterns(&mut self) -> Result<()> {
        let patterns = vec![
            r"(?i)(;|\||\&\&|\|\|)\s*(cat|ls|dir|type|more|less)",
            r"(?i)(;|\||\&\&|\|\|)\s*(rm|del|rmdir|rd)\s+",
            r"(?i)(;|\||\&\&|\|\|)\s*(wget|curl|nc|netcat)",
            r"(?i)(;|\||\&\&|\|\|)\s*(ps|tasklist|netstat)",
            r"(?i)(;|\||\&\&|\|\|)\s*(whoami|id|uname)",
            r"(?i)`[^`]*`|\$\([^)]*\)",
            r"(?i)%[a-zA-Z_][a-zA-Z0-9_]*%",
            r"(?i)\$\{[^}]*\}",
        ];

        for pattern in patterns {
            if let Ok(regex) = Regex::new(pattern) {
                self.command_patterns.push(regex);
            }
        }

        Ok(())
    }

    /// 初始化LDAP注入模式
    fn initialize_ldap_patterns(&mut self) -> Result<()> {
        let patterns = vec![
            r"(?i)\*\)\(|\)\(\*",
            r"(?i)\(\|\(\&",
            r"(?i)\)\)\)|\(\(\(",
            r"(?i)(\*\)|&\*|\|\*)",
            r"(?i)(objectClass=\*|cn=\*|uid=\*)",
        ];

        for pattern in patterns {
            if let Ok(regex) = Regex::new(pattern) {
                self.ldap_patterns.push(regex);
            }
        }

        Ok(())
    }

    /// 初始化NoSQL注入模式
    fn initialize_nosql_patterns(&mut self) -> Result<()> {
        let patterns = vec![
            r"(?i)\$where\s*:",
            r"(?i)\$ne\s*:|ne\s*:",
            r"(?i)\$gt\s*:|gt\s*:",
            r"(?i)\$regex\s*:|regex\s*:",
            r"(?i)\$or\s*:\s*\[|\$and\s*:\s*\[",
            r"(?i)this\.(.*)\s*==",
            r"(?i)function\s*\(\s*\)\s*\{",
        ];

        for pattern in patterns {
            if let Ok(regex) = Regex::new(pattern) {
                self.nosql_patterns.push(regex);
            }
        }

        Ok(())
    }

    /// 编译自定义模式
    fn compile_custom_patterns(&mut self) -> Result<()> {
        for pattern in &self.config.custom_patterns {
            if pattern.enabled {
                match Regex::new(&pattern.pattern) {
                    Ok(regex) => {
                        self.custom_patterns.push(CompiledInjectionPattern {
                            pattern: pattern.clone(),
                            regex,
                        });
                    }
                    Err(e) => {
                        warn!("无法编译自定义注入模式 '{}': {}", pattern.name, e);
                    }
                }
            }
        }
        Ok(())
    }

    /// 检测SQL注入
    fn detect_sql_injection(&self, content: &str) -> Result<Option<Vec<String>>> {
        let mut matches = Vec::new();
        
        for (index, pattern) in self.sql_patterns.iter().enumerate() {
            if pattern.is_match(content) {
                matches.push(format!("SQL注入模式 #{}", index + 1));
            }
        }

        if matches.is_empty() {
            Ok(None)
        } else {
            Ok(Some(matches))
        }
    }

    /// 检测XSS
    fn detect_xss(&self, content: &str) -> Result<Option<Vec<String>>> {
        let mut matches = Vec::new();
        
        for (index, pattern) in self.xss_patterns.iter().enumerate() {
            if pattern.is_match(content) {
                matches.push(format!("XSS模式 #{}", index + 1));
            }
        }

        if matches.is_empty() {
            Ok(None)
        } else {
            Ok(Some(matches))
        }
    }

    /// 检测命令注入
    fn detect_command_injection(&self, content: &str) -> Result<Option<Vec<String>>> {
        let mut matches = Vec::new();
        
        for (index, pattern) in self.command_patterns.iter().enumerate() {
            if pattern.is_match(content) {
                matches.push(format!("命令注入模式 #{}", index + 1));
            }
        }

        if matches.is_empty() {
            Ok(None)
        } else {
            Ok(Some(matches))
        }
    }

    /// 检测LDAP注入
    fn detect_ldap_injection(&self, content: &str) -> Result<Option<Vec<String>>> {
        let mut matches = Vec::new();
        
        for (index, pattern) in self.ldap_patterns.iter().enumerate() {
            if pattern.is_match(content) {
                matches.push(format!("LDAP注入模式 #{}", index + 1));
            }
        }

        if matches.is_empty() {
            Ok(None)
        } else {
            Ok(Some(matches))
        }
    }

    /// 检测NoSQL注入
    fn detect_nosql_injection(&self, content: &str) -> Result<Option<Vec<String>>> {
        let mut matches = Vec::new();
        
        for (index, pattern) in self.nosql_patterns.iter().enumerate() {
            if pattern.is_match(content) {
                matches.push(format!("NoSQL注入模式 #{}", index + 1));
            }
        }

        if matches.is_empty() {
            Ok(None)
        } else {
            Ok(Some(matches))
        }
    }

    /// 检测自定义模式
    fn detect_custom_patterns(&self, content: &str) -> Result<Option<Vec<String>>> {
        let mut matches = Vec::new();
        
        for compiled_pattern in &self.custom_patterns {
            if compiled_pattern.regex.is_match(content) {
                matches.push(compiled_pattern.pattern.name.clone());
            }
        }

        if matches.is_empty() {
            Ok(None)
        } else {
            Ok(Some(matches))
        }
    }

    /// 检查是否在白名单中
    fn is_whitelisted(&self, content: &str) -> bool {
        self.config.whitelist_patterns.iter()
            .any(|pattern| content.contains(pattern))
    }

    /// 计算置信度分数
    fn calculate_confidence_score(&self, result: &InjectionDetectionResult) -> f64 {
        if result.is_safe {
            return 0.0;
        }

        let pattern_count = result.detected_patterns.len() as f64;
        let base_score = match result.injection_type {
            Some(InjectionType::SqlInjection) => 0.8,
            Some(InjectionType::CrossSiteScripting) => 0.7,
            Some(InjectionType::CommandInjection) => 0.9,
            Some(InjectionType::LdapInjection) => 0.6,
            Some(InjectionType::NoSqlInjection) => 0.7,
            Some(InjectionType::Custom) => 0.5,
            None => 0.0,
        };

        // 根据检测到的模式数量调整分数
        let adjusted_score = base_score + (pattern_count * 0.1);
        adjusted_score.min(1.0)
    }

    /// 获取检测统计信息
    pub fn get_detection_stats(&self) -> &InjectionDetectionStats {
        &self.detection_stats
    }
}

/// 编译后的注入模式
#[derive(Debug)]
struct CompiledInjectionPattern {
    pattern: InjectionPattern,
    regex: Regex,
}

/// 注入模式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InjectionPattern {
    /// 模式名称
    pub name: String,
    /// 模式描述
    pub description: String,
    /// 正则表达式模式
    pub pattern: String,
    /// 注入类型
    pub injection_type: InjectionType,
    /// 是否启用
    pub enabled: bool,
}

/// 注入类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum InjectionType {
    /// SQL注入
    SqlInjection,
    /// 跨站脚本攻击
    CrossSiteScripting,
    /// 命令注入
    CommandInjection,
    /// LDAP注入
    LdapInjection,
    /// NoSQL注入
    NoSqlInjection,
    /// 自定义注入
    Custom,
}

/// 注入检测结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InjectionDetectionResult {
    /// 消息ID
    pub message_id: String,
    /// 是否安全
    pub is_safe: bool,
    /// 注入类型
    pub injection_type: Option<InjectionType>,
    /// 检测到的模式
    pub detected_patterns: Vec<String>,
    /// 检测详情
    pub detection_details: Vec<String>,
    /// 置信度分数
    pub confidence_score: f64,
    /// 检测时间
    pub detected_at: SystemTime,
}

/// 注入检测统计信息
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct InjectionDetectionStats {
    /// 总检查次数
    pub total_checks: u64,
    /// 检测到的注入攻击总数
    pub total_injections_detected: u64,
    /// SQL注入检测次数
    pub sql_injections_detected: u64,
    /// XSS攻击检测次数
    pub xss_attacks_detected: u64,
    /// 命令注入检测次数
    pub command_injections_detected: u64,
    /// LDAP注入检测次数
    pub ldap_injections_detected: u64,
    /// NoSQL注入检测次数
    pub nosql_injections_detected: u64,
    /// 自定义模式检测次数
    pub custom_patterns_detected: u64,
}
