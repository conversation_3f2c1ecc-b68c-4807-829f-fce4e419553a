//! 异常检测器模块
//!
//! 负责检测异常行为和可疑模式

use super::ThreatDetectionError;
use crate::native_messaging::error::{NativeMessagingError, Result};
use crate::native_messaging::protocol::message::NativeMessage;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::SystemTime;
use tracing::{debug, warn};

/// 异常检测器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnomalyDetectorConfig {
    /// 启用统计异常检测
    pub enable_statistical_detection: bool,
    /// 启用行为异常检测
    pub enable_behavioral_detection: bool,
    /// 异常阈值
    pub anomaly_threshold: f64,
    /// 学习窗口大小
    pub learning_window_size: usize,
}

impl Default for AnomalyDetectorConfig {
    fn default() -> Self {
        Self {
            enable_statistical_detection: true,
            enable_behavioral_detection: true,
            anomaly_threshold: 0.7,
            learning_window_size: 1000,
        }
    }
}

/// 异常检测器
#[derive(Debug)]
pub struct AnomalyDetector {
    config: AnomalyDetectorConfig,
    baseline_stats: BaselineStats,
    message_history: Vec<MessageProfile>,
}

impl AnomalyDetector {
    /// 创建新的异常检测器
    pub fn new(config: &AnomalyDetectorConfig) -> Result<Self> {
        Ok(Self {
            config: config.clone(),
            baseline_stats: BaselineStats::default(),
            message_history: Vec::new(),
        })
    }

    /// 检测异常
    pub async fn detect_anomaly(&mut self, message: &NativeMessage) -> Result<AnomalyDetectionResult> {
        debug!("检测异常行为: {}", message.request_id);

        let mut result = AnomalyDetectionResult {
            message_id: message.request_id.clone(),
            is_normal: true,
            anomaly_type: None,
            anomaly_score: 0.0,
            anomaly_description: String::new(),
            detected_at: SystemTime::now(),
        };

        let profile = MessageProfile::from_message(message);

        // 1. 统计异常检测
        if self.config.enable_statistical_detection {
            let stat_score = self.detect_statistical_anomaly(&profile);
            if stat_score > self.config.anomaly_threshold {
                result.is_normal = false;
                result.anomaly_type = Some(AnomalyType::Statistical);
                result.anomaly_score = stat_score;
                result.anomaly_description = "统计特征异常".to_string();
            }
        }

        // 2. 行为异常检测
        if self.config.enable_behavioral_detection {
            let behavior_score = self.detect_behavioral_anomaly(&profile);
            if behavior_score > self.config.anomaly_threshold {
                result.is_normal = false;
                result.anomaly_type = Some(AnomalyType::Behavioral);
                result.anomaly_score = result.anomaly_score.max(behavior_score);
                result.anomaly_description = "行为模式异常".to_string();
            }
        }

        // 更新基线统计
        self.update_baseline(&profile);

        if result.is_normal {
            debug!("异常检测通过: {}", message.request_id);
        } else {
            warn!("检测到异常行为: {} - 类型: {:?}, 分数: {:.2}", 
                message.request_id, result.anomaly_type, result.anomaly_score);
        }

        Ok(result)
    }

    /// 检测统计异常
    fn detect_statistical_anomaly(&self, profile: &MessageProfile) -> f64 {
        let mut anomaly_score = 0.0;

        // 检查消息大小异常
        if let Some(avg_size) = self.baseline_stats.average_message_size {
            let size_deviation = (profile.message_size as f64 - avg_size).abs() / avg_size;
            anomaly_score = anomaly_score.max(size_deviation);
        }

        // 检查字段数量异常
        if let Some(avg_fields) = self.baseline_stats.average_field_count {
            let field_deviation = (profile.field_count as f64 - avg_fields).abs() / avg_fields;
            anomaly_score = anomaly_score.max(field_deviation);
        }

        anomaly_score.min(1.0)
    }

    /// 检测行为异常
    fn detect_behavioral_anomaly(&self, profile: &MessageProfile) -> f64 {
        let mut anomaly_score = 0.0;

        // 检查时间模式异常
        if self.message_history.len() > 10 {
            let recent_intervals: Vec<u64> = self.message_history
                .windows(2)
                .map(|w| w[1].timestamp.saturating_sub(w[0].timestamp))
                .collect();
            
            if let Some(avg_interval) = self.calculate_average(&recent_intervals) {
                let current_interval = profile.timestamp
                    .saturating_sub(self.message_history.last().unwrap().timestamp);
                let interval_deviation = (current_interval as f64 - avg_interval).abs() / avg_interval;
                anomaly_score = anomaly_score.max(interval_deviation);
            }
        }

        anomaly_score.min(1.0)
    }

    /// 更新基线统计
    fn update_baseline(&mut self, profile: &MessageProfile) {
        // 添加到历史记录
        self.message_history.push(profile.clone());
        
        // 保持窗口大小
        if self.message_history.len() > self.config.learning_window_size {
            self.message_history.remove(0);
        }

        // 更新统计信息
        if !self.message_history.is_empty() {
            let sizes: Vec<usize> = self.message_history.iter().map(|p| p.message_size).collect();
            self.baseline_stats.average_message_size = Some(self.calculate_average_usize(&sizes));

            let fields: Vec<usize> = self.message_history.iter().map(|p| p.field_count).collect();
            self.baseline_stats.average_field_count = Some(self.calculate_average_usize(&fields));
        }
    }

    /// 计算平均值
    fn calculate_average(&self, values: &[u64]) -> Option<f64> {
        if values.is_empty() {
            None
        } else {
            Some(values.iter().sum::<u64>() as f64 / values.len() as f64)
        }
    }

    /// 计算usize平均值
    fn calculate_average_usize(&self, values: &[usize]) -> f64 {
        if values.is_empty() {
            0.0
        } else {
            values.iter().sum::<usize>() as f64 / values.len() as f64
        }
    }
}

/// 消息特征
#[derive(Debug, Clone)]
struct MessageProfile {
    message_size: usize,
    field_count: usize,
    timestamp: u64,
    source: String,
    message_type: String,
}

impl MessageProfile {
    fn from_message(message: &NativeMessage) -> Self {
        let message_json = serde_json::to_string(message).unwrap_or_default();
        let field_count = if let Some(obj) = message.payload.as_object() {
            obj.len()
        } else {
            0
        };

        Self {
            message_size: message_json.len(),
            field_count,
            timestamp: message.timestamp,
            source: message.source.clone(),
            message_type: format!("{:?}", message.message_type),
        }
    }
}

/// 基线统计信息
#[derive(Debug, Default)]
struct BaselineStats {
    average_message_size: Option<f64>,
    average_field_count: Option<f64>,
    common_sources: HashMap<String, u32>,
    common_message_types: HashMap<String, u32>,
}

/// 异常类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum AnomalyType {
    /// 统计异常
    Statistical,
    /// 行为异常
    Behavioral,
    /// 时间异常
    Temporal,
    /// 频率异常
    Frequency,
}

/// 异常检测结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnomalyDetectionResult {
    /// 消息ID
    pub message_id: String,
    /// 是否正常
    pub is_normal: bool,
    /// 异常类型
    pub anomaly_type: Option<AnomalyType>,
    /// 异常分数
    pub anomaly_score: f64,
    /// 异常描述
    pub anomaly_description: String,
    /// 检测时间
    pub detected_at: SystemTime,
}
