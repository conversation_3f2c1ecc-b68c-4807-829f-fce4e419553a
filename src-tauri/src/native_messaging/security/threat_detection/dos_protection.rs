//! DoS攻击防护模块
//!
//! 负责检测和防护拒绝服务攻击

use super::ThreatDetectionError;
use crate::native_messaging::error::{NativeMessagingError, Result};
use crate::native_messaging::protocol::message::NativeMessage;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{Duration, Instant, SystemTime};
use tracing::{debug, warn, error};

/// DoS防护配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DosProtectionConfig {
    /// 启用频率检测
    pub enable_frequency_detection: bool,
    /// 启用资源消耗检测
    pub enable_resource_detection: bool,
    /// 启用模式检测
    pub enable_pattern_detection: bool,
    /// 频率阈值（每分钟请求数）
    pub frequency_threshold: u32,
    /// 资源消耗阈值
    pub resource_threshold: ResourceThreshold,
    /// 检测窗口大小（秒）
    pub detection_window: u64,
}

impl Default for DosProtectionConfig {
    fn default() -> Self {
        Self {
            enable_frequency_detection: true,
            enable_resource_detection: true,
            enable_pattern_detection: true,
            frequency_threshold: 100,
            resource_threshold: ResourceThreshold::default(),
            detection_window: 60,
        }
    }
}

/// 资源阈值
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceThreshold {
    /// 最大消息大小（字节）
    pub max_message_size: usize,
    /// 最大处理时间（毫秒）
    pub max_processing_time: u64,
    /// 最大内存使用（字节）
    pub max_memory_usage: usize,
}

impl Default for ResourceThreshold {
    fn default() -> Self {
        Self {
            max_message_size: 1024 * 1024, // 1MB
            max_processing_time: 5000,     // 5秒
            max_memory_usage: 10 * 1024 * 1024, // 10MB
        }
    }
}

/// DoS攻击防护器
#[derive(Debug)]
pub struct DosProtection {
    config: DosProtectionConfig,
    request_tracker: HashMap<String, Vec<Instant>>,
    resource_tracker: HashMap<String, ResourceUsage>,
    detection_stats: DosDetectionStats,
}

impl DosProtection {
    /// 创建新的DoS防护器
    pub fn new(config: &DosProtectionConfig) -> Result<Self> {
        Ok(Self {
            config: config.clone(),
            request_tracker: HashMap::new(),
            resource_tracker: HashMap::new(),
            detection_stats: DosDetectionStats::default(),
        })
    }

    /// 检测DoS攻击
    pub async fn detect_dos(&mut self, message: &NativeMessage) -> Result<DosDetectionResult> {
        debug!("检测DoS攻击: {}", message.request_id);

        let mut result = DosDetectionResult {
            message_id: message.request_id.clone(),
            is_safe: true,
            attack_type: None,
            attack_details: String::new(),
            detection_details: Vec::new(),
            detected_at: SystemTime::now(),
        };

        self.detection_stats.total_checks += 1;

        // 1. 频率检测
        if self.config.enable_frequency_detection {
            if let Some(freq_attack) = self.detect_frequency_attack(&message.source).await? {
                result.is_safe = false;
                result.attack_type = Some(freq_attack);
                result.attack_details = "检测到高频请求攻击".to_string();
                result.detection_details.push("频率检测: 失败".to_string());
                self.detection_stats.frequency_attacks_detected += 1;
            } else {
                result.detection_details.push("频率检测: 通过".to_string());
            }
        }

        // 2. 资源消耗检测
        if self.config.enable_resource_detection {
            if let Some(resource_attack) = self.detect_resource_attack(message).await? {
                result.is_safe = false;
                result.attack_type = Some(resource_attack);
                result.attack_details = "检测到资源消耗攻击".to_string();
                result.detection_details.push("资源检测: 失败".to_string());
                self.detection_stats.resource_attacks_detected += 1;
            } else {
                result.detection_details.push("资源检测: 通过".to_string());
            }
        }

        // 3. 模式检测
        if self.config.enable_pattern_detection {
            if let Some(pattern_attack) = self.detect_pattern_attack(message).await? {
                result.is_safe = false;
                result.attack_type = Some(pattern_attack);
                result.attack_details = "检测到模式化攻击".to_string();
                result.detection_details.push("模式检测: 失败".to_string());
                self.detection_stats.pattern_attacks_detected += 1;
            } else {
                result.detection_details.push("模式检测: 通过".to_string());
            }
        }

        // 记录请求
        self.record_request(&message.source).await;

        if !result.is_safe {
            self.detection_stats.total_attacks_detected += 1;
        }

        if result.is_safe {
            debug!("DoS攻击检测通过: {}", message.request_id);
        } else {
            warn!("检测到DoS攻击: {} - 类型: {:?}", 
                message.request_id, result.attack_type);
        }

        Ok(result)
    }

    /// 检测频率攻击
    async fn detect_frequency_attack(&mut self, source: &str) -> Result<Option<DosAttackType>> {
        let now = Instant::now();
        let window_start = now - Duration::from_secs(self.config.detection_window);

        // 获取或创建请求跟踪记录
        let requests = self.request_tracker.entry(source.to_string()).or_insert_with(Vec::new);
        
        // 清理过期请求
        requests.retain(|&timestamp| timestamp > window_start);
        
        // 检查频率是否超过阈值
        if requests.len() as u32 >= self.config.frequency_threshold {
            return Ok(Some(DosAttackType::HighFrequency));
        }

        Ok(None)
    }

    /// 检测资源消耗攻击
    async fn detect_resource_attack(&mut self, message: &NativeMessage) -> Result<Option<DosAttackType>> {
        // 检查消息大小
        let message_size = serde_json::to_string(message)?.len();
        if message_size > self.config.resource_threshold.max_message_size {
            return Ok(Some(DosAttackType::ResourceExhaustion));
        }

        // 检查复杂度（简化实现）
        let complexity_score = self.calculate_complexity_score(message);
        if complexity_score > 1000 {
            return Ok(Some(DosAttackType::ResourceExhaustion));
        }

        Ok(None)
    }

    /// 检测模式攻击
    async fn detect_pattern_attack(&self, message: &NativeMessage) -> Result<Option<DosAttackType>> {
        let message_content = serde_json::to_string(message)?;
        
        // 检测重复模式
        if self.has_repetitive_patterns(&message_content) {
            return Ok(Some(DosAttackType::PatternBased));
        }

        // 检测异常结构
        if self.has_abnormal_structure(&message_content) {
            return Ok(Some(DosAttackType::PatternBased));
        }

        Ok(None)
    }

    /// 记录请求
    async fn record_request(&mut self, source: &str) {
        let now = Instant::now();
        let requests = self.request_tracker.entry(source.to_string()).or_insert_with(Vec::new);
        requests.push(now);
    }

    /// 计算复杂度分数
    fn calculate_complexity_score(&self, message: &NativeMessage) -> u32 {
        let mut score = 0;
        
        // 基于消息大小
        let message_size = serde_json::to_string(message).unwrap_or_default().len();
        score += (message_size / 1024) as u32;
        
        // 基于嵌套深度
        score += self.calculate_nesting_depth(&message.payload) * 10;
        
        // 基于字段数量
        if let Some(obj) = message.payload.as_object() {
            score += obj.len() as u32;
        }
        
        score
    }

    /// 计算嵌套深度
    fn calculate_nesting_depth(&self, value: &serde_json::Value) -> u32 {
        match value {
            serde_json::Value::Object(obj) => {
                1 + obj.values().map(|v| self.calculate_nesting_depth(v)).max().unwrap_or(0)
            }
            serde_json::Value::Array(arr) => {
                1 + arr.iter().map(|v| self.calculate_nesting_depth(v)).max().unwrap_or(0)
            }
            _ => 0,
        }
    }

    /// 检测重复模式
    fn has_repetitive_patterns(&self, content: &str) -> bool {
        // 简化实现：检测重复字符串
        let chars: Vec<char> = content.chars().collect();
        if chars.len() < 100 {
            return false;
        }

        // 检测连续重复字符
        let mut consecutive_count = 1;
        for i in 1..chars.len() {
            if chars[i] == chars[i-1] {
                consecutive_count += 1;
                if consecutive_count > 50 {
                    return true;
                }
            } else {
                consecutive_count = 1;
            }
        }

        false
    }

    /// 检测异常结构
    fn has_abnormal_structure(&self, content: &str) -> bool {
        // 检测异常的括号嵌套
        let mut bracket_depth = 0;
        let mut max_depth = 0;
        
        for ch in content.chars() {
            match ch {
                '{' | '[' | '(' => {
                    bracket_depth += 1;
                    max_depth = max_depth.max(bracket_depth);
                }
                '}' | ']' | ')' => {
                    bracket_depth -= 1;
                }
                _ => {}
            }
        }

        max_depth > 100 // 嵌套深度超过100认为异常
    }

    /// 获取检测统计信息
    pub fn get_detection_stats(&self) -> &DosDetectionStats {
        &self.detection_stats
    }
}

/// 资源使用情况
#[derive(Debug, Clone)]
struct ResourceUsage {
    memory_usage: usize,
    processing_time: Duration,
    last_updated: Instant,
}

/// DoS攻击类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum DosAttackType {
    /// 高频攻击
    HighFrequency,
    /// 资源耗尽攻击
    ResourceExhaustion,
    /// 基于模式的攻击
    PatternBased,
    /// 分布式攻击
    Distributed,
}

/// DoS检测结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DosDetectionResult {
    /// 消息ID
    pub message_id: String,
    /// 是否安全
    pub is_safe: bool,
    /// 攻击类型
    pub attack_type: Option<DosAttackType>,
    /// 攻击详情
    pub attack_details: String,
    /// 检测详情
    pub detection_details: Vec<String>,
    /// 检测时间
    pub detected_at: SystemTime,
}

/// DoS检测统计信息
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct DosDetectionStats {
    /// 总检查次数
    pub total_checks: u64,
    /// 检测到的攻击总数
    pub total_attacks_detected: u64,
    /// 频率攻击检测次数
    pub frequency_attacks_detected: u64,
    /// 资源攻击检测次数
    pub resource_attacks_detected: u64,
    /// 模式攻击检测次数
    pub pattern_attacks_detected: u64,
}
