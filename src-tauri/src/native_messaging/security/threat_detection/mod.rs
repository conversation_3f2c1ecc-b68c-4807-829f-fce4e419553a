//! 威胁检测模块
//!
//! 提供恶意行为检测、注入攻击防护、DoS防护和模糊测试防护功能

pub mod malware_detector;
pub mod injection_guard;
pub mod dos_protection;
pub mod fuzzing_guard;
pub mod anomaly_detector;

// 重新导出主要类型
pub use malware_detector::{MalwareDetector, MalwareDetectionResult, MalwareSignature};
pub use injection_guard::{InjectionGuard, InjectionDetectionResult, InjectionType};
pub use dos_protection::{DosProtection, DosDetectionResult, DosAttackType};
pub use fuzzing_guard::{FuzzingGuard, FuzzingDetectionResult, FuzzingPattern};
pub use anomaly_detector::{AnomalyDetector, AnomalyDetectionResult, AnomalyType};

use crate::native_messaging::error::{NativeMessagingError, Result};
use crate::native_messaging::protocol::message::NativeMessage;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{Duration, SystemTime};
use tracing::{debug, info, warn, error};

/// 威胁检测管理器
#[derive(Debug)]
pub struct ThreatDetectionManager {
    malware_detector: MalwareDetector,
    injection_guard: InjectionGuard,
    dos_protection: DosProtection,
    fuzzing_guard: FuzzingGuard,
    anomaly_detector: AnomalyDetector,
    config: ThreatDetectionConfig,
}

impl ThreatDetectionManager {
    /// 创建新的威胁检测管理器
    pub fn new(config: ThreatDetectionConfig) -> Result<Self> {
        let malware_detector = MalwareDetector::new(&config.malware_config)?;
        let injection_guard = InjectionGuard::new(&config.injection_config)?;
        let dos_protection = DosProtection::new(&config.dos_config)?;
        let fuzzing_guard = FuzzingGuard::new(&config.fuzzing_config)?;
        let anomaly_detector = AnomalyDetector::new(&config.anomaly_config)?;

        Ok(Self {
            malware_detector,
            injection_guard,
            dos_protection,
            fuzzing_guard,
            anomaly_detector,
            config,
        })
    }

    /// 检测威胁
    pub async fn detect_threats(&mut self, message: &NativeMessage) -> Result<ThreatDetectionResult> {
        debug!("开始威胁检测: {}", message.request_id);
        
        let mut result = ThreatDetectionResult {
            message_id: message.request_id.clone(),
            is_safe: true,
            threat_level: ThreatLevel::None,
            detected_threats: Vec::new(),
            detection_details: Vec::new(),
            detected_at: SystemTime::now(),
            recommended_actions: Vec::new(),
        };

        // 1. 恶意软件检测
        if self.config.enable_malware_detection {
            match self.malware_detector.detect_malware(message).await {
                Ok(malware_result) => {
                    if !malware_result.is_clean {
                        result.detected_threats.push(ThreatInfo {
                            threat_type: ThreatType::Malware,
                            severity: ThreatSeverity::High,
                            description: "检测到恶意软件特征".to_string(),
                            evidence: malware_result.detected_signatures.iter()
                                .map(|s| s.name.clone())
                                .collect(),
                        });
                        result.is_safe = false;
                    }
                    result.detection_details.push(format!("恶意软件检测: {}", 
                        if malware_result.is_clean { "通过" } else { "失败" }));
                }
                Err(e) => {
                    warn!("恶意软件检测错误: {}", e);
                    result.detection_details.push(format!("恶意软件检测错误: {}", e));
                }
            }
        }

        // 2. 注入攻击检测
        if self.config.enable_injection_detection {
            match self.injection_guard.detect_injection(message).await {
                Ok(injection_result) => {
                    if !injection_result.is_safe {
                        result.detected_threats.push(ThreatInfo {
                            threat_type: ThreatType::Injection,
                            severity: ThreatSeverity::High,
                            description: format!("检测到注入攻击: {:?}", injection_result.injection_type),
                            evidence: injection_result.detected_patterns,
                        });
                        result.is_safe = false;
                    }
                    result.detection_details.push(format!("注入攻击检测: {}", 
                        if injection_result.is_safe { "通过" } else { "失败" }));
                }
                Err(e) => {
                    warn!("注入攻击检测错误: {}", e);
                    result.detection_details.push(format!("注入攻击检测错误: {}", e));
                }
            }
        }

        // 3. DoS攻击检测
        if self.config.enable_dos_detection {
            match self.dos_protection.detect_dos(message).await {
                Ok(dos_result) => {
                    if !dos_result.is_safe {
                        result.detected_threats.push(ThreatInfo {
                            threat_type: ThreatType::DenialOfService,
                            severity: ThreatSeverity::Medium,
                            description: format!("检测到DoS攻击: {:?}", dos_result.attack_type),
                            evidence: vec![dos_result.attack_details],
                        });
                        result.is_safe = false;
                    }
                    result.detection_details.push(format!("DoS攻击检测: {}", 
                        if dos_result.is_safe { "通过" } else { "失败" }));
                }
                Err(e) => {
                    warn!("DoS攻击检测错误: {}", e);
                    result.detection_details.push(format!("DoS攻击检测错误: {}", e));
                }
            }
        }

        // 4. 模糊测试检测
        if self.config.enable_fuzzing_detection {
            match self.fuzzing_guard.detect_fuzzing(message).await {
                Ok(fuzzing_result) => {
                    if !fuzzing_result.is_safe {
                        result.detected_threats.push(ThreatInfo {
                            threat_type: ThreatType::Fuzzing,
                            severity: ThreatSeverity::Medium,
                            description: "检测到模糊测试攻击".to_string(),
                            evidence: fuzzing_result.detected_patterns.iter()
                                .map(|p| p.name.clone())
                                .collect(),
                        });
                        result.is_safe = false;
                    }
                    result.detection_details.push(format!("模糊测试检测: {}", 
                        if fuzzing_result.is_safe { "通过" } else { "失败" }));
                }
                Err(e) => {
                    warn!("模糊测试检测错误: {}", e);
                    result.detection_details.push(format!("模糊测试检测错误: {}", e));
                }
            }
        }

        // 5. 异常行为检测
        if self.config.enable_anomaly_detection {
            match self.anomaly_detector.detect_anomaly(message).await {
                Ok(anomaly_result) => {
                    if !anomaly_result.is_normal {
                        result.detected_threats.push(ThreatInfo {
                            threat_type: ThreatType::Anomaly,
                            severity: ThreatSeverity::Low,
                            description: format!("检测到异常行为: {:?}", anomaly_result.anomaly_type),
                            evidence: vec![anomaly_result.anomaly_description],
                        });
                        // 异常行为不一定意味着不安全，只是可疑
                        if result.threat_level == ThreatLevel::None {
                            result.threat_level = ThreatLevel::Low;
                        }
                    }
                    result.detection_details.push(format!("异常行为检测: {}", 
                        if anomaly_result.is_normal { "正常" } else { "异常" }));
                }
                Err(e) => {
                    warn!("异常行为检测错误: {}", e);
                    result.detection_details.push(format!("异常行为检测错误: {}", e));
                }
            }
        }

        // 计算威胁级别和推荐动作
        result.threat_level = self.calculate_threat_level(&result.detected_threats);
        result.recommended_actions = self.generate_recommended_actions(&result);

        if result.is_safe {
            debug!("威胁检测通过: {}", message.request_id);
        } else {
            warn!("威胁检测发现问题: {} - 威胁级别: {:?}, 威胁数量: {}", 
                message.request_id, result.threat_level, result.detected_threats.len());
        }

        Ok(result)
    }

    /// 计算威胁级别
    fn calculate_threat_level(&self, threats: &[ThreatInfo]) -> ThreatLevel {
        if threats.is_empty() {
            return ThreatLevel::None;
        }

        let max_severity = threats.iter()
            .map(|t| &t.severity)
            .max()
            .unwrap_or(&ThreatSeverity::Low);

        match max_severity {
            ThreatSeverity::Critical => ThreatLevel::Critical,
            ThreatSeverity::High => ThreatLevel::High,
            ThreatSeverity::Medium => ThreatLevel::Medium,
            ThreatSeverity::Low => ThreatLevel::Low,
        }
    }

    /// 生成推荐动作
    fn generate_recommended_actions(&self, result: &ThreatDetectionResult) -> Vec<SecurityAction> {
        let mut actions = Vec::new();

        match result.threat_level {
            ThreatLevel::Critical => {
                actions.push(SecurityAction::Block);
                actions.push(SecurityAction::Alert);
                actions.push(SecurityAction::Log);
                actions.push(SecurityAction::Quarantine);
            }
            ThreatLevel::High => {
                actions.push(SecurityAction::Block);
                actions.push(SecurityAction::Alert);
                actions.push(SecurityAction::Log);
            }
            ThreatLevel::Medium => {
                actions.push(SecurityAction::Monitor);
                actions.push(SecurityAction::Log);
                actions.push(SecurityAction::RateLimit);
            }
            ThreatLevel::Low => {
                actions.push(SecurityAction::Log);
                actions.push(SecurityAction::Monitor);
            }
            ThreatLevel::None => {
                actions.push(SecurityAction::Allow);
            }
        }

        actions
    }

    /// 获取威胁检测统计信息
    pub async fn get_detection_stats(&self) -> ThreatDetectionStats {
        ThreatDetectionStats {
            total_messages_scanned: 0, // 需要实现统计跟踪
            threats_detected: 0,
            malware_detected: 0,
            injections_detected: 0,
            dos_attacks_detected: 0,
            fuzzing_attempts_detected: 0,
            anomalies_detected: 0,
        }
    }
}

/// 威胁检测配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThreatDetectionConfig {
    /// 启用恶意软件检测
    pub enable_malware_detection: bool,
    /// 启用注入攻击检测
    pub enable_injection_detection: bool,
    /// 启用DoS攻击检测
    pub enable_dos_detection: bool,
    /// 启用模糊测试检测
    pub enable_fuzzing_detection: bool,
    /// 启用异常行为检测
    pub enable_anomaly_detection: bool,
    /// 恶意软件检测配置
    pub malware_config: MalwareDetectorConfig,
    /// 注入攻击检测配置
    pub injection_config: InjectionGuardConfig,
    /// DoS防护配置
    pub dos_config: DosProtectionConfig,
    /// 模糊测试防护配置
    pub fuzzing_config: FuzzingGuardConfig,
    /// 异常检测配置
    pub anomaly_config: AnomalyDetectorConfig,
}

impl Default for ThreatDetectionConfig {
    fn default() -> Self {
        Self {
            enable_malware_detection: true,
            enable_injection_detection: true,
            enable_dos_detection: true,
            enable_fuzzing_detection: true,
            enable_anomaly_detection: true,
            malware_config: MalwareDetectorConfig::default(),
            injection_config: InjectionGuardConfig::default(),
            dos_config: DosProtectionConfig::default(),
            fuzzing_config: FuzzingGuardConfig::default(),
            anomaly_config: AnomalyDetectorConfig::default(),
        }
    }
}

/// 威胁检测结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThreatDetectionResult {
    /// 消息ID
    pub message_id: String,
    /// 是否安全
    pub is_safe: bool,
    /// 威胁级别
    pub threat_level: ThreatLevel,
    /// 检测到的威胁
    pub detected_threats: Vec<ThreatInfo>,
    /// 检测详情
    pub detection_details: Vec<String>,
    /// 检测时间
    pub detected_at: SystemTime,
    /// 推荐动作
    pub recommended_actions: Vec<SecurityAction>,
}

/// 威胁信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThreatInfo {
    /// 威胁类型
    pub threat_type: ThreatType,
    /// 严重程度
    pub severity: ThreatSeverity,
    /// 描述
    pub description: String,
    /// 证据
    pub evidence: Vec<String>,
}

/// 威胁类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ThreatType {
    /// 恶意软件
    Malware,
    /// 注入攻击
    Injection,
    /// 拒绝服务攻击
    DenialOfService,
    /// 模糊测试
    Fuzzing,
    /// 异常行为
    Anomaly,
    /// 未知威胁
    Unknown,
}

/// 威胁严重程度
#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum ThreatSeverity {
    /// 低
    Low,
    /// 中
    Medium,
    /// 高
    High,
    /// 严重
    Critical,
}

/// 威胁级别
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ThreatLevel {
    /// 无威胁
    None,
    /// 低威胁
    Low,
    /// 中等威胁
    Medium,
    /// 高威胁
    High,
    /// 严重威胁
    Critical,
}

/// 安全动作
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum SecurityAction {
    /// 允许
    Allow,
    /// 阻止
    Block,
    /// 监控
    Monitor,
    /// 记录日志
    Log,
    /// 告警
    Alert,
    /// 隔离
    Quarantine,
    /// 限流
    RateLimit,
}

/// 威胁检测统计信息
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct ThreatDetectionStats {
    /// 扫描的消息总数
    pub total_messages_scanned: u64,
    /// 检测到的威胁总数
    pub threats_detected: u64,
    /// 检测到的恶意软件数
    pub malware_detected: u64,
    /// 检测到的注入攻击数
    pub injections_detected: u64,
    /// 检测到的DoS攻击数
    pub dos_attacks_detected: u64,
    /// 检测到的模糊测试尝试数
    pub fuzzing_attempts_detected: u64,
    /// 检测到的异常行为数
    pub anomalies_detected: u64,
}

// 占位符配置类型，将在各自的文件中实现
pub use malware_detector::MalwareDetectorConfig;
pub use injection_guard::InjectionGuardConfig;
pub use dos_protection::DosProtectionConfig;
pub use fuzzing_guard::FuzzingGuardConfig;
pub use anomaly_detector::AnomalyDetectorConfig;

/// 威胁检测错误
#[derive(Debug, thiserror::Error)]
pub enum ThreatDetectionError {
    #[error("恶意软件检测失败: {0}")]
    MalwareDetectionFailed(String),
    
    #[error("注入攻击检测失败: {0}")]
    InjectionDetectionFailed(String),
    
    #[error("DoS攻击检测失败: {0}")]
    DosDetectionFailed(String),
    
    #[error("模糊测试检测失败: {0}")]
    FuzzingDetectionFailed(String),
    
    #[error("异常检测失败: {0}")]
    AnomalyDetectionFailed(String),
    
    #[error("配置错误: {0}")]
    ConfigError(String),
    
    #[error("IO错误: {0}")]
    IoError(#[from] std::io::Error),
    
    #[error("序列化错误: {0}")]
    SerializationError(#[from] serde_json::Error),
}
