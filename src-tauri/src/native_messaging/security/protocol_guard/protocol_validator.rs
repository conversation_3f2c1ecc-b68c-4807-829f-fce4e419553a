use std::collections::HashMap;
use serde_json::Value;
use crate::native_messaging::security::SecurityError;

/// Native Messaging 协议验证器
pub struct ProtocolValidator {
    supported_versions: Vec<u32>,
    current_version: u32,
    max_message_size: usize,
    required_fields: Vec<String>,
    format_rules: HashMap<String, FormatRule>,
}

/// 数据格式验证规则
#[derive(Debug, Clone)]
pub struct FormatRule {
    pub field_type: FieldType,
    pub required: bool,
    pub max_length: Option<usize>,
    pub pattern: Option<String>,
    pub allowed_values: Option<Vec<String>>,
}

/// 字段类型枚举
#[derive(Debug, Clone, PartialEq)]
pub enum FieldType {
    String,
    Number,
    Boolean,
    Object,
    Array,
    Uuid,
    Timestamp,
    Base64,
}

/// 协议验证结果
#[derive(Debug, Clone)]
pub struct ProtocolValidationResult {
    pub is_valid: bool,
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
    pub compliance_score: f64,
}

impl ProtocolValidator {
    /// 创建新的协议验证器
    pub fn new() -> Self {
        let mut format_rules = HashMap::new();
        
        // 定义核心字段的验证规则
        format_rules.insert("version".to_string(), FormatRule {
            field_type: FieldType::Number,
            required: true,
            max_length: None,
            pattern: None,
            allowed_values: Some(vec!["1".to_string(), "2".to_string(), "3".to_string()]),
        });
        
        format_rules.insert("message_type".to_string(), FormatRule {
            field_type: FieldType::String,
            required: true,
            max_length: Some(64),
            pattern: Some(r"^[a-zA-Z_][a-zA-Z0-9_]*$".to_string()),
            allowed_values: None,
        });
        
        format_rules.insert("request_id".to_string(), FormatRule {
            field_type: FieldType::Uuid,
            required: true,
            max_length: Some(36),
            pattern: Some(r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$".to_string()),
            allowed_values: None,
        });
        
        format_rules.insert("timestamp".to_string(), FormatRule {
            field_type: FieldType::Timestamp,
            required: true,
            max_length: None,
            pattern: None,
            allowed_values: None,
        });
        
        Self {
            supported_versions: vec![1, 2, 3],
            current_version: 3,
            max_message_size: 1024 * 1024, // 1MB
            required_fields: vec![
                "version".to_string(),
                "message_type".to_string(),
                "request_id".to_string(),
                "timestamp".to_string(),
                "payload".to_string(),
            ],
            format_rules,
        }
    }
    
    /// 验证协议版本
    pub async fn validate_version(&self, version: u32) -> Result<(), SecurityError> {
        if !self.supported_versions.contains(&version) {
            return Err(SecurityError::UnsupportedProtocolVersion {
                version,
                supported: self.supported_versions.clone(),
            });
        }
        
        // 检查版本兼容性
        if version < self.current_version {
            log::warn!("使用旧版本协议: v{}, 当前版本: v{}", version, self.current_version);
        }
        
        Ok(())
    }
    
    /// 验证消息格式
    pub async fn validate_message_format(&self, message: &Value) -> Result<ProtocolValidationResult, SecurityError> {
        let mut result = ProtocolValidationResult {
            is_valid: true,
            errors: Vec::new(),
            warnings: Vec::new(),
            compliance_score: 100.0,
        };
        
        // 检查消息是否为对象
        if !message.is_object() {
            result.errors.push("消息必须是JSON对象".to_string());
            result.is_valid = false;
            result.compliance_score = 0.0;
            return Ok(result);
        }
        
        let obj = message.as_object().unwrap();
        
        // 验证必需字段
        self.validate_required_fields(obj, &mut result).await?;
        
        // 验证字段格式
        self.validate_field_formats(obj, &mut result).await?;
        
        // 验证消息大小
        self.validate_message_size(message, &mut result).await?;
        
        // 验证嵌套对象
        self.validate_nested_objects(obj, &mut result).await?;
        
        // 计算最终合规性分数
        result.compliance_score = self.calculate_compliance_score(&result);
        
        Ok(result)
    }
    
    /// 验证必需字段
    async fn validate_required_fields(
        &self, 
        obj: &serde_json::Map<String, Value>, 
        result: &mut ProtocolValidationResult
    ) -> Result<(), SecurityError> {
        for field in &self.required_fields {
            if !obj.contains_key(field) {
                result.errors.push(format!("缺少必需字段: {}", field));
                result.is_valid = false;
            }
        }
        Ok(())
    }
    
    /// 验证字段格式
    async fn validate_field_formats(
        &self, 
        obj: &serde_json::Map<String, Value>, 
        result: &mut ProtocolValidationResult
    ) -> Result<(), SecurityError> {
        for (field_name, value) in obj {
            if let Some(rule) = self.format_rules.get(field_name) {
                self.validate_field_value(field_name, value, rule, result).await?;
            }
        }
        Ok(())
    }
    
    /// 验证单个字段值
    async fn validate_field_value(
        &self,
        field_name: &str,
        value: &Value,
        rule: &FormatRule,
        result: &mut ProtocolValidationResult,
    ) -> Result<(), SecurityError> {
        // 验证字段类型
        if !self.validate_field_type(value, &rule.field_type) {
            result.errors.push(format!("字段 '{}' 类型不正确，期望: {:?}", field_name, rule.field_type));
            result.is_valid = false;
            return Ok(());
        }
        
        // 验证字符串长度
        if let Some(max_len) = rule.max_length {
            if let Some(str_val) = value.as_str() {
                if str_val.len() > max_len {
                    result.errors.push(format!("字段 '{}' 长度超过限制: {} > {}", field_name, str_val.len(), max_len));
                    result.is_valid = false;
                }
            }
        }
        
        // 验证正则表达式模式
        if let Some(pattern) = &rule.pattern {
            if let Some(str_val) = value.as_str() {
                if !self.validate_pattern(str_val, pattern)? {
                    result.errors.push(format!("字段 '{}' 格式不符合规范: {}", field_name, pattern));
                    result.is_valid = false;
                }
            }
        }
        
        // 验证允许的值
        if let Some(allowed_values) = &rule.allowed_values {
            let value_str = value.to_string().trim_matches('"').to_string();
            if !allowed_values.contains(&value_str) {
                result.errors.push(format!("字段 '{}' 值不在允许范围内: {:?}", field_name, allowed_values));
                result.is_valid = false;
            }
        }
        
        Ok(())
    }
    
    /// 验证字段类型
    fn validate_field_type(&self, value: &Value, expected_type: &FieldType) -> bool {
        match expected_type {
            FieldType::String => value.is_string(),
            FieldType::Number => value.is_number(),
            FieldType::Boolean => value.is_boolean(),
            FieldType::Object => value.is_object(),
            FieldType::Array => value.is_array(),
            FieldType::Uuid => {
                if let Some(str_val) = value.as_str() {
                    self.is_valid_uuid(str_val)
                } else {
                    false
                }
            },
            FieldType::Timestamp => {
                if let Some(num_val) = value.as_u64() {
                    self.is_valid_timestamp(num_val)
                } else {
                    false
                }
            },
            FieldType::Base64 => {
                if let Some(str_val) = value.as_str() {
                    self.is_valid_base64(str_val)
                } else {
                    false
                }
            },
        }
    }
    
    /// 验证正则表达式模式
    fn validate_pattern(&self, value: &str, pattern: &str) -> Result<bool, SecurityError> {
        let regex = regex::Regex::new(pattern)
            .map_err(|e| SecurityError::InvalidPattern {
                pattern: pattern.to_string(),
                error: e.to_string(),
            })?;
        
        Ok(regex.is_match(value))
    }
    
    /// 验证消息大小
    async fn validate_message_size(
        &self, 
        message: &Value, 
        result: &mut ProtocolValidationResult
    ) -> Result<(), SecurityError> {
        let message_str = message.to_string();
        let size = message_str.len();
        
        if size > self.max_message_size {
            result.errors.push(format!("消息大小超过限制: {} > {}", size, self.max_message_size));
            result.is_valid = false;
        } else if size > self.max_message_size / 2 {
            result.warnings.push(format!("消息大小接近限制: {} (限制: {})", size, self.max_message_size));
        }
        
        Ok(())
    }
    
    /// 验证嵌套对象
    async fn validate_nested_objects(
        &self, 
        obj: &serde_json::Map<String, Value>, 
        result: &mut ProtocolValidationResult
    ) -> Result<(), SecurityError> {
        // 验证 payload 对象
        if let Some(payload) = obj.get("payload") {
            if payload.is_object() {
                self.validate_payload_structure(payload, result).await?;
            }
        }
        
        Ok(())
    }
    
    /// 验证 payload 结构
    async fn validate_payload_structure(
        &self, 
        payload: &Value, 
        result: &mut ProtocolValidationResult
    ) -> Result<(), SecurityError> {
        let payload_obj = payload.as_object().unwrap();
        
        // 检查 payload 深度
        let max_depth = 5;
        let current_depth = self.calculate_object_depth(payload);
        if current_depth > max_depth {
            result.errors.push(format!("Payload 嵌套深度过深: {} > {}", current_depth, max_depth));
            result.is_valid = false;
        }
        
        // 检查 payload 字段数量
        let max_fields = 50;
        let field_count = self.count_total_fields(payload);
        if field_count > max_fields {
            result.warnings.push(format!("Payload 字段数量较多: {} (建议 < {})", field_count, max_fields));
        }
        
        Ok(())
    }
    
    /// 计算对象深度
    fn calculate_object_depth(&self, value: &Value) -> usize {
        match value {
            Value::Object(obj) => {
                1 + obj.values()
                    .map(|v| self.calculate_object_depth(v))
                    .max()
                    .unwrap_or(0)
            },
            Value::Array(arr) => {
                1 + arr.iter()
                    .map(|v| self.calculate_object_depth(v))
                    .max()
                    .unwrap_or(0)
            },
            _ => 0,
        }
    }
    
    /// 计算总字段数
    fn count_total_fields(&self, value: &Value) -> usize {
        match value {
            Value::Object(obj) => {
                obj.len() + obj.values()
                    .map(|v| self.count_total_fields(v))
                    .sum::<usize>()
            },
            Value::Array(arr) => {
                arr.iter()
                    .map(|v| self.count_total_fields(v))
                    .sum::<usize>()
            },
            _ => 0,
        }
    }
    
    /// 计算合规性分数
    fn calculate_compliance_score(&self, result: &ProtocolValidationResult) -> f64 {
        if !result.is_valid {
            return 0.0;
        }
        
        let error_penalty = result.errors.len() as f64 * 20.0;
        let warning_penalty = result.warnings.len() as f64 * 5.0;
        
        let score = 100.0 - error_penalty - warning_penalty;
        score.max(0.0).min(100.0)
    }
    
    /// 验证 UUID 格式
    fn is_valid_uuid(&self, value: &str) -> bool {
        uuid::Uuid::parse_str(value).is_ok()
    }
    
    /// 验证时间戳有效性
    fn is_valid_timestamp(&self, timestamp: u64) -> bool {
        use std::time::{SystemTime, UNIX_EPOCH};
        
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        // 时间戳应该在合理范围内 (过去24小时到未来1小时)
        let min_time = now.saturating_sub(24 * 3600); // 24小时前
        let max_time = now + 3600; // 1小时后
        
        timestamp >= min_time && timestamp <= max_time
    }
    
    /// 验证 Base64 编码
    fn is_valid_base64(&self, value: &str) -> bool {
        base64::decode(value).is_ok()
    }
}

impl Default for ProtocolValidator {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;
    use tokio;

    #[tokio::test]
    async fn test_validate_version_supported() {
        let validator = ProtocolValidator::new();
        
        assert!(validator.validate_version(3).await.is_ok());
        assert!(validator.validate_version(2).await.is_ok());
        assert!(validator.validate_version(1).await.is_ok());
    }
    
    #[tokio::test]
    async fn test_validate_version_unsupported() {
        let validator = ProtocolValidator::new();
        
        assert!(validator.validate_version(0).await.is_err());
        assert!(validator.validate_version(999).await.is_err());
    }
    
    #[tokio::test]
    async fn test_validate_message_format_valid() {
        let validator = ProtocolValidator::new();
        
        let message = json!({
            "version": 3,
            "message_type": "get_credentials",
            "request_id": "550e8400-e29b-41d4-a716-************",
            "timestamp": 1640995200,
            "payload": {
                "domain": "example.com"
            }
        });
        
        let result = validator.validate_message_format(&message).await.unwrap();
        assert!(result.is_valid);
        assert!(result.errors.is_empty());
    }
    
    #[tokio::test]
    async fn test_validate_message_format_missing_fields() {
        let validator = ProtocolValidator::new();
        
        let message = json!({
            "version": 3,
            "message_type": "get_credentials"
            // 缺少 request_id, timestamp, payload
        });
        
        let result = validator.validate_message_format(&message).await.unwrap();
        assert!(!result.is_valid);
        assert_eq!(result.errors.len(), 3); // 缺少3个必需字段
    }
    
    #[tokio::test]
    async fn test_validate_message_format_invalid_uuid() {
        let validator = ProtocolValidator::new();
        
        let message = json!({
            "version": 3,
            "message_type": "get_credentials",
            "request_id": "invalid-uuid",
            "timestamp": 1640995200,
            "payload": {}
        });
        
        let result = validator.validate_message_format(&message).await.unwrap();
        assert!(!result.is_valid);
        assert!(result.errors.iter().any(|e| e.contains("request_id")));
    }
    
    #[tokio::test]
    async fn test_validate_message_size_limit() {
        let validator = ProtocolValidator::new();
        
        // 创建超大消息
        let large_payload = "x".repeat(2 * 1024 * 1024); // 2MB
        let message = json!({
            "version": 3,
            "message_type": "test",
            "request_id": "550e8400-e29b-41d4-a716-************",
            "timestamp": 1640995200,
            "payload": {
                "data": large_payload
            }
        });
        
        let result = validator.validate_message_format(&message).await.unwrap();
        assert!(!result.is_valid);
        assert!(result.errors.iter().any(|e| e.contains("消息大小超过限制")));
    }
    
    #[tokio::test]
    async fn test_validate_nested_objects_depth() {
        let validator = ProtocolValidator::new();
        
        // 创建深度嵌套的对象
        let deep_nested = json!({
            "level1": {
                "level2": {
                    "level3": {
                        "level4": {
                            "level5": {
                                "level6": {
                                    "data": "too deep"
                                }
                            }
                        }
                    }
                }
            }
        });
        
        let message = json!({
            "version": 3,
            "message_type": "test",
            "request_id": "550e8400-e29b-41d4-a716-************",
            "timestamp": 1640995200,
            "payload": deep_nested
        });
        
        let result = validator.validate_message_format(&message).await.unwrap();
        assert!(!result.is_valid);
        assert!(result.errors.iter().any(|e| e.contains("嵌套深度过深")));
    }
    
    #[test]
    fn test_uuid_validation() {
        let validator = ProtocolValidator::new();
        
        assert!(validator.is_valid_uuid("550e8400-e29b-41d4-a716-************"));
        assert!(!validator.is_valid_uuid("invalid-uuid"));
        assert!(!validator.is_valid_uuid(""));
    }
    
    #[test]
    fn test_timestamp_validation() {
        let validator = ProtocolValidator::new();
        
        use std::time::{SystemTime, UNIX_EPOCH};
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        assert!(validator.is_valid_timestamp(now));
        assert!(validator.is_valid_timestamp(now - 3600)); // 1小时前
        assert!(!validator.is_valid_timestamp(now + 7200)); // 2小时后 (超出范围)
        assert!(!validator.is_valid_timestamp(0)); // Unix epoch (太久远)
    }
    
    #[test]
    fn test_base64_validation() {
        let validator = ProtocolValidator::new();
        
        assert!(validator.is_valid_base64("SGVsbG8gV29ybGQ=")); // "Hello World"
        assert!(!validator.is_valid_base64("invalid-base64!@#"));
        assert!(validator.is_valid_base64("")); // 空字符串是有效的base64
    }
} 