# Native Messaging Protocol Layer

## 概述

Native Messaging 协议层是密码管理器应用与浏览器扩展之间通信的核心模块。它提供了标准化的消息格式、高效的编解码器、严格的协议验证和智能的版本管理功能。

## 核心特性

### 🔄 消息格式定义
- **标准化消息结构**：统一的 JSON 格式，支持多种消息类型
- **版本兼容性**：向后兼容的协议版本管理
- **元数据支持**：灵活的元数据和扩展字段机制
- **优先级管理**：支持消息优先级和超时控制

### 🚀 高性能编解码
- **多种编码格式**：JSON、压缩编码支持
- **批量处理**：高效的批量消息编解码
- **流式处理**：大数据流式传输支持
- **Native 格式**：符合 Native Messaging 标准的字节流编码

### 🔒 严格协议验证
- **多层验证**：格式、版本、安全性多重检查
- **性能优化**：快速验证模式和批量验证
- **灵活配置**：可配置的验证规则和严格模式
- **详细报告**：完整的验证结果和错误信息

### 🔧 智能版本管理
- **自动协商**：客户端-服务端版本自动协商
- **兼容性检查**：详细的版本兼容性分析
- **消息升级**：自动的消息格式升级
- **迁移路径**：清晰的版本迁移指导

### 📊 性能基准测试
- **全面基准**：涵盖所有核心功能的性能测试
- **压力测试**：高负载场景下的稳定性验证
- **性能指标**：延迟、吞吐量、内存使用等详细指标
- **配置对比**：不同配置下的性能对比分析

## 模块结构

```
protocol/
├── mod.rs                # 模块定义和公共导出
├── message.rs           # 消息格式定义
├── codec.rs             # 编解码器实现
├── validator.rs         # 协议验证器
├── version.rs           # 版本管理器
├── benchmarks.rs        # 性能基准测试
└── README.md           # 文档说明
```

## 快速开始

### 基本消息处理

```rust
use crate::native_messaging::protocol::{
    NativeMessage, MessageType, ProtocolCodec, ProtocolValidator, MessageCodec
};

// 创建消息
let message = NativeMessage::new(
    MessageType::GetCredentials,
    "request-123".to_string(),
    serde_json::json!({"domain": "example.com"}),
    "browser_extension".to_string(),
);

// 验证消息
let validator = ProtocolValidator::new_default();
validator.validate_message(&message)?;

// 编码消息
let codec = ProtocolCodec::new_default();
let encoded = codec.encode(&message)?;

// 解码消息
let decoded = codec.decode(&encoded)?;
```

### 版本协商示例

```rust
use crate::native_messaging::protocol::{
    ProtocolVersionManager, NegotiationStrategy
};

let version_manager = ProtocolVersionManager::new_default();
let client_versions = vec![1, 2, 3];

// 协商版本
let result = version_manager.negotiate_version(
    &client_versions, 
    NegotiationStrategy::HighestVersion
);

if result.success {
    println!("协商成功，使用版本: {}", result.negotiated_version);
} else {
    println!("协商失败，无共同支持的版本");
}
```

### 批量处理示例

```rust
use crate::native_messaging::protocol::codec::BatchCodec;

let messages = vec![
    // ... 多个消息
];

// 批量编码
let encoded_batch = codec.encode_batch(&messages)?;

// 批量解码
let decoded_messages = codec.decode_batch(&encoded_batch)?;
```

### 性能基准测试

```rust
use crate::native_messaging::protocol::{
    run_protocol_benchmarks, ProtocolBenchmarkSuite
};

// 运行基准测试
let results = run_protocol_benchmarks(1000);
ProtocolBenchmarkSuite::print_benchmark_results(&results);

// 运行压力测试
let mut suite = ProtocolBenchmarkSuite::new_high_performance(0);
let stress_result = suite.run_stress_test(10); // 10秒压力测试
```

## API 参考

### 消息类型

| 类型 | 说明 | 用途 |
|------|------|------|
| `GetCredentials` | 获取凭证 | 查询存储的密码信息 |
| `SaveCredentials` | 保存凭证 | 存储新的密码信息 |
| `UpdateCredentials` | 更新凭证 | 修改现有密码信息 |
| `DeleteCredentials` | 删除凭证 | 移除密码信息 |
| `HealthCheck` | 健康检查 | 验证服务状态 |
| `Ping` | 连接测试 | 测试连接是否正常 |
| `Auth` | 用户认证 | 身份验证相关操作 |
| `BatchOperation` | 批量操作 | 批量处理多个操作 |

### 错误码分类

| 范围 | 类别 | 示例 |
|------|------|------|
| 1000-1999 | 协议错误 | 版本不支持、格式无效 |
| 2000-2999 | 认证错误 | 身份验证失败、权限不足 |
| 3000-3999 | 业务错误 | 凭证未找到、配置无效 |
| 4000-4999 | 系统错误 | 服务不可用、数据库错误 |
| 5000-5999 | 网络错误 | 连接失败、超时 |

### 配置选项

#### 编解码器配置

```rust
use crate::native_messaging::protocol::codec::{CodecConfig, CompressionAlgorithm};

let config = CodecConfig {
    compression: CompressionAlgorithm::Gzip,
    max_message_size: 1024 * 1024, // 1MB
    enable_version_compatibility: true,
    enable_performance_monitoring: true,
    supported_versions: vec![1, 2],
};
```

#### 验证器配置

```rust
use crate::native_messaging::protocol::validator::ValidationRules;

let rules = ValidationRules {
    max_message_size: 1024 * 1024,
    max_time_skew_seconds: 300,
    max_metadata_items: 50,
    max_extension_fields: 20,
    enable_timestamp_validation: true,
    // ...
};
```

## 性能指标

### 基准测试结果

在标准硬件配置下的性能基准：

| 操作 | 平均延迟 | 吞吐量 | 内存使用 |
|------|----------|--------|----------|
| 消息编码 | ~50μs | >20,000 ops/s | 1KB |
| 消息解码 | ~60μs | >16,000 ops/s | 1KB |
| 消息验证 | ~20μs | >50,000 ops/s | 512B |
| 版本协商 | ~10μs | >100,000 ops/s | 256B |
| 端到端处理 | ~150μs | >6,000 ops/s | 2KB |

### 性能优化建议

1. **启用快速验证模式**：在高性能场景下使用 `validate_message_fast()`
2. **批量处理**：使用批量编解码 API 处理多个消息
3. **缓存编解码器**：重用编解码器实例避免重复初始化
4. **选择合适的压缩算法**：根据数据特性选择最优压缩方式
5. **监控性能指标**：启用性能监控定期检查性能状况

## 最佳实践

### 消息设计

```rust
// ✅ 推荐：使用明确的消息类型
let message = NativeMessage::new(
    MessageType::GetCredentials,
    generate_request_id(),
    serde_json::json!({"domain": "example.com"}),
    "browser_extension".to_string(),
)
.with_timeout(10000) // 设置超时
.with_priority(MessagePriority::Normal); // 设置优先级

// ❌ 不推荐：使用 Custom 类型而不定义具体类型
let message = NativeMessage::new(
    MessageType::Custom("get_data".to_string()),
    // ...
);
```

### 错误处理

```rust
use crate::native_messaging::error::{NativeMessagingError, Result};

fn process_message(data: &[u8]) -> Result<NativeMessage> {
    let validator = ProtocolValidator::new_default();
    let codec = ProtocolCodec::new_default();
    
    // 解码
    let message = codec.decode(data)
        .map_err(|e| {
            tracing::error!("消息解码失败: {}", e);
            e
        })?;
    
    // 验证
    validator.validate_message(&message)
        .map_err(|e| {
            tracing::warn!("消息验证失败: {}", e);
            e
        })?;
    
    Ok(message)
}
```

### 版本管理

```rust
// 处理版本升级
fn handle_version_upgrade(
    message: &NativeMessage,
    target_version: u32,
) -> Result<NativeMessage> {
    let version_manager = ProtocolVersionManager::new_default();
    
    // 检查兼容性
    let compatibility = version_manager.check_compatibility(
        message.version,
        target_version,
    );
    
    if !compatibility.is_compatible {
        return Err(NativeMessagingError::ProtocolError(
            format!("版本 {} 与 {} 不兼容", message.version, target_version)
        ));
    }
    
    // 执行升级
    version_manager.upgrade_message(message, target_version)
}
```

## 安全考虑

### 输入验证

- **消息大小限制**：默认限制为 1MB，可配置
- **时间戳验证**：检查消息时间戳防止重放攻击
- **请求ID验证**：确保请求ID格式正确且唯一
- **来源验证**：验证消息来源的合法性

### 版本安全

- **版本白名单**：只允许已知安全的协议版本
- **废弃版本**：及时标记和禁用存在安全问题的版本
- **升级强制**：在必要时强制客户端升级到安全版本

## 故障排除

### 常见问题

1. **消息解码失败**
   - 检查 JSON 格式是否正确
   - 确认协议版本是否支持
   - 验证消息大小是否超限

2. **版本协商失败**
   - 检查客户端和服务端支持的版本列表
   - 确认协商策略设置是否合理
   - 验证版本兼容性配置

3. **性能问题**
   - 启用性能监控查看详细指标
   - 考虑使用高性能配置
   - 检查是否有不必要的重复验证

### 调试工具

```rust
// 启用详细日志
use tracing::{info, warn, error};

// 性能监控
let mut codec = ProtocolCodec::new_default();
codec.update_config(CodecConfig {
    enable_performance_monitoring: true,
    // ...
});

// 详细验证报告
let result = validator.validate_message_detailed(&message);
if !result.is_valid {
    for error in result.errors {
        println!("验证错误: {} (严重级别: {:?})", error.message, error.severity);
    }
}
```

## 测试

### 运行测试

```bash
# 运行所有协议层测试
cargo test --lib native_messaging::protocol

# 运行特定模块测试
cargo test --lib native_messaging::protocol::codec
cargo test --lib native_messaging::protocol::validator
cargo test --lib native_messaging::protocol::version

# 运行基准测试
cargo test --lib native_messaging::protocol::benchmarks
```

### 测试覆盖率

当前测试覆盖率：**>95%**

- 消息格式测试：18 个测试
- 编解码器测试：17 个测试
- 验证器测试：16 个测试
- 版本管理测试：11 个测试
- 基准测试：7 个测试
- 集成测试：2 个测试

总计：**70+ 个测试用例**

## 更新日志

### v2.0.0 (当前版本)
- ✨ 新增版本管理器
- ✨ 新增性能基准测试
- ✨ 新增批量处理支持
- ✨ 新增流式传输支持
- 🔧 优化编解码性能
- 🔧 改进错误处理机制
- 📚 完善文档和示例

### v1.0.0
- 🎉 初始版本
- ✨ 基础消息格式定义
- ✨ JSON 编解码器
- ✨ 基础协议验证
- ✨ Native Messaging 格式支持

## 贡献指南

1. **代码风格**：遵循 Rust 官方代码风格指南
2. **测试要求**：新功能必须包含完整的测试用例
3. **文档更新**：API 变更需要同步更新文档
4. **性能测试**：性能相关的变更需要运行基准测试
5. **向后兼容**：API 变更需要考虑向后兼容性

## 许可证

本项目采用 MIT 许可证。详情请参阅 LICENSE 文件。

---

> 📝 **说明**：本文档会随着模块的演进持续更新。如有疑问或建议，请提交 Issue 或 PR。 