//! Native Messaging 协议层性能基准测试
//!
//! 提供协议层各组件的性能测试和基准分析

use super::codec::{MessageCodec, ProtocolCodec};
use super::message::{MessageType, NativeMessage, OutgoingMessage, ResponseStatus};
use super::validator::ProtocolValidator;
use super::version::ProtocolVersionManager;
use std::time::{Duration, Instant};

/// 性能基准测试结果
#[derive(Debug, Clone)]
pub struct BenchmarkResult {
    /// 测试名称
    pub test_name: String,
    /// 总执行时间（纳秒）
    pub total_duration_ns: u64,
    /// 平均执行时间（纳秒）
    pub average_duration_ns: u64,
    /// 最小执行时间（纳秒）
    pub min_duration_ns: u64,
    /// 最大执行时间（纳秒）
    pub max_duration_ns: u64,
    /// 操作次数
    pub operations_count: u64,
    /// 每秒操作数
    pub operations_per_second: f64,
    /// 内存使用量（字节）
    pub memory_usage_bytes: Option<usize>,
    /// 数据大小（字节）
    pub data_size_bytes: Option<usize>,
    /// 吞吐量（MB/s）
    pub throughput_mb_per_sec: Option<f64>,
}

/// 性能基准测试套件
pub struct ProtocolBenchmarkSuite {
    /// 编解码器
    codec: ProtocolCodec,
    /// 验证器
    validator: ProtocolValidator,
    /// 版本管理器
    version_manager: ProtocolVersionManager,
    /// 测试样本大小
    sample_size: usize,
}

impl ProtocolBenchmarkSuite {
    /// 创建新的基准测试套件
    ///
    /// # 参数
    /// - `sample_size`: 测试样本大小
    ///
    /// # 返回
    /// ProtocolBenchmarkSuite - 基准测试套件
    pub fn new(sample_size: usize) -> Self {
        Self {
            codec: ProtocolCodec::new_default(),
            validator: ProtocolValidator::new_default(),
            version_manager: ProtocolVersionManager::new_default(),
            sample_size,
        }
    }

    /// 创建高性能配置的基准测试套件
    ///
    /// # 参数
    /// - `sample_size`: 测试样本大小
    ///
    /// # 返回
    /// ProtocolBenchmarkSuite - 高性能基准测试套件
    pub fn new_high_performance(sample_size: usize) -> Self {
        Self {
            codec: ProtocolCodec::new_high_performance(),
            validator: ProtocolValidator::new_performance_optimized(),
            version_manager: ProtocolVersionManager::new_default(),
            sample_size,
        }
    }

    /// 运行所有基准测试
    ///
    /// # 返回
    /// Vec<BenchmarkResult> - 所有测试结果
    pub fn run_all_benchmarks(&mut self) -> Vec<BenchmarkResult> {
        let mut results = Vec::new();

        // 编解码基准测试
        results.push(self.benchmark_message_encoding());
        results.push(self.benchmark_message_decoding());
        results.push(self.benchmark_batch_encoding());
        results.push(self.benchmark_native_format_encoding());

        // 验证基准测试
        results.push(self.benchmark_message_validation());
        results.push(self.benchmark_batch_validation());
        results.push(self.benchmark_fast_validation());

        // 版本管理基准测试
        results.push(self.benchmark_version_negotiation());
        results.push(self.benchmark_message_upgrade());
        results.push(self.benchmark_compatibility_check());

        // 综合性能测试
        results.push(self.benchmark_end_to_end_processing());

        results
    }

    /// 基准测试：消息编码
    ///
    /// # 返回
    /// BenchmarkResult - 编码性能测试结果
    pub fn benchmark_message_encoding(&self) -> BenchmarkResult {
        let test_message = self.create_test_message();
        let data_size = test_message.size_bytes();
        
        let mut durations = Vec::with_capacity(self.sample_size);
        let start_time = Instant::now();

        for _ in 0..self.sample_size {
            let encode_start = Instant::now();
            let _ = self.codec.encode(&test_message).expect("编码失败");
            durations.push(encode_start.elapsed().as_nanos() as u64);
        }

        let total_duration = start_time.elapsed();
        self.create_benchmark_result(
            "Message Encoding".to_string(),
            durations,
            total_duration,
            Some(data_size),
        )
    }

    /// 基准测试：消息解码
    ///
    /// # 返回
    /// BenchmarkResult - 解码性能测试结果
    pub fn benchmark_message_decoding(&self) -> BenchmarkResult {
        let test_message = self.create_test_message();
        let encoded_data = self.codec.encode(&test_message).expect("编码失败");
        let data_size = encoded_data.len();
        
        let mut durations = Vec::with_capacity(self.sample_size);
        let start_time = Instant::now();

        for _ in 0..self.sample_size {
            let decode_start = Instant::now();
            let _ = self.codec.decode(&encoded_data).expect("解码失败");
            durations.push(decode_start.elapsed().as_nanos() as u64);
        }

        let total_duration = start_time.elapsed();
        self.create_benchmark_result(
            "Message Decoding".to_string(),
            durations,
            total_duration,
            Some(data_size),
        )
    }

    /// 基准测试：批量编码
    ///
    /// # 返回
    /// BenchmarkResult - 批量编码性能测试结果
    pub fn benchmark_batch_encoding(&self) -> BenchmarkResult {
        let batch_size = 10;
        let test_messages = (0..batch_size)
            .map(|i| self.create_test_message_with_id(format!("batch-{}", i)))
            .collect::<Vec<_>>();
        
        let data_size = test_messages.iter().map(|m| m.size_bytes()).sum();
        
        let mut durations = Vec::with_capacity(self.sample_size);
        let start_time = Instant::now();

        for _ in 0..self.sample_size {
            let encode_start = Instant::now();
            use super::codec::BatchCodec;
            let _ = self.codec.encode_batch(&test_messages).expect("批量编码失败");
            durations.push(encode_start.elapsed().as_nanos() as u64);
        }

        let total_duration = start_time.elapsed();
        self.create_benchmark_result(
            "Batch Encoding".to_string(),
            durations,
            total_duration,
            Some(data_size),
        )
    }

    /// 基准测试：Native格式编码
    ///
    /// # 返回
    /// BenchmarkResult - Native格式编码性能测试结果
    pub fn benchmark_native_format_encoding(&mut self) -> BenchmarkResult {
        let test_message = self.create_test_message();
        let data_size = test_message.size_bytes();
        
        let mut durations = Vec::with_capacity(self.sample_size);
        let start_time = Instant::now();

        for _ in 0..self.sample_size {
            let encode_start = Instant::now();
            let _ = self.codec.encode_native_format(&test_message).expect("Native格式编码失败");
            durations.push(encode_start.elapsed().as_nanos() as u64);
        }

        let total_duration = start_time.elapsed();
        self.create_benchmark_result(
            "Native Format Encoding".to_string(),
            durations,
            total_duration,
            Some(data_size),
        )
    }

    /// 基准测试：消息验证
    ///
    /// # 返回
    /// BenchmarkResult - 消息验证性能测试结果
    pub fn benchmark_message_validation(&self) -> BenchmarkResult {
        let test_message = self.create_test_message();
        let data_size = test_message.size_bytes();
        
        let mut durations = Vec::with_capacity(self.sample_size);
        let start_time = Instant::now();

        for _ in 0..self.sample_size {
            let validate_start = Instant::now();
            let _ = self.validator.validate_message(&test_message).expect("验证失败");
            durations.push(validate_start.elapsed().as_nanos() as u64);
        }

        let total_duration = start_time.elapsed();
        self.create_benchmark_result(
            "Message Validation".to_string(),
            durations,
            total_duration,
            Some(data_size),
        )
    }

    /// 基准测试：批量验证
    ///
    /// # 返回
    /// BenchmarkResult - 批量验证性能测试结果
    pub fn benchmark_batch_validation(&self) -> BenchmarkResult {
        let batch_size = 10;
        let test_messages = (0..batch_size)
            .map(|i| self.create_test_message_with_id(format!("batch-{}", i)))
            .collect::<Vec<_>>();
        
        let data_size = test_messages.iter().map(|m| m.size_bytes()).sum();
        
        let mut durations = Vec::with_capacity(self.sample_size);
        let start_time = Instant::now();

        for _ in 0..self.sample_size {
            let validate_start = Instant::now();
            let _ = self.validator.validate_messages_batch(&test_messages);
            durations.push(validate_start.elapsed().as_nanos() as u64);
        }

        let total_duration = start_time.elapsed();
        self.create_benchmark_result(
            "Batch Validation".to_string(),
            durations,
            total_duration,
            Some(data_size),
        )
    }

    /// 基准测试：快速验证
    ///
    /// # 返回
    /// BenchmarkResult - 快速验证性能测试结果
    pub fn benchmark_fast_validation(&self) -> BenchmarkResult {
        let test_message = self.create_test_message();
        let data_size = test_message.size_bytes();
        
        let mut durations = Vec::with_capacity(self.sample_size);
        let start_time = Instant::now();

        for _ in 0..self.sample_size {
            let validate_start = Instant::now();
            let _ = self.validator.validate_message_fast(&test_message).expect("快速验证失败");
            durations.push(validate_start.elapsed().as_nanos() as u64);
        }

        let total_duration = start_time.elapsed();
        self.create_benchmark_result(
            "Fast Validation".to_string(),
            durations,
            total_duration,
            Some(data_size),
        )
    }

    /// 基准测试：版本协商
    ///
    /// # 返回
    /// BenchmarkResult - 版本协商性能测试结果
    pub fn benchmark_version_negotiation(&self) -> BenchmarkResult {
        let client_versions = vec![1, 2, 3];
        
        let mut durations = Vec::with_capacity(self.sample_size);
        let start_time = Instant::now();

        for _ in 0..self.sample_size {
            let negotiate_start = Instant::now();
            let _ = self.version_manager.negotiate_version(&client_versions, super::version::NegotiationStrategy::HighestVersion);
            durations.push(negotiate_start.elapsed().as_nanos() as u64);
        }

        let total_duration = start_time.elapsed();
        self.create_benchmark_result(
            "Version Negotiation".to_string(),
            durations,
            total_duration,
            None,
        )
    }

    /// 基准测试：消息升级
    ///
    /// # 返回
    /// BenchmarkResult - 消息升级性能测试结果
    pub fn benchmark_message_upgrade(&self) -> BenchmarkResult {
        let test_message = NativeMessage::new_with_version(
            1,
            MessageType::GetCredentials,
            "test-123".to_string(),
            serde_json::json!({"domain": "example.com"}),
            "test-extension".to_string(),
        );
        let data_size = test_message.size_bytes();
        
        let mut durations = Vec::with_capacity(self.sample_size);
        let start_time = Instant::now();

        for _ in 0..self.sample_size {
            let upgrade_start = Instant::now();
            let _ = self.version_manager.upgrade_message(&test_message, 2).expect("消息升级失败");
            durations.push(upgrade_start.elapsed().as_nanos() as u64);
        }

        let total_duration = start_time.elapsed();
        self.create_benchmark_result(
            "Message Upgrade".to_string(),
            durations,
            total_duration,
            Some(data_size),
        )
    }

    /// 基准测试：兼容性检查
    ///
    /// # 返回
    /// BenchmarkResult - 兼容性检查性能测试结果
    pub fn benchmark_compatibility_check(&self) -> BenchmarkResult {
        let mut durations = Vec::with_capacity(self.sample_size);
        let start_time = Instant::now();

        for _ in 0..self.sample_size {
            let check_start = Instant::now();
            let _ = self.version_manager.check_compatibility(1, 2);
            durations.push(check_start.elapsed().as_nanos() as u64);
        }

        let total_duration = start_time.elapsed();
        self.create_benchmark_result(
            "Compatibility Check".to_string(),
            durations,
            total_duration,
            None,
        )
    }

    /// 基准测试：端到端处理
    ///
    /// # 返回
    /// BenchmarkResult - 端到端处理性能测试结果
    pub fn benchmark_end_to_end_processing(&mut self) -> BenchmarkResult {
        let test_message = self.create_test_message();
        let data_size = test_message.size_bytes();
        
        let mut durations = Vec::with_capacity(self.sample_size);
        let start_time = Instant::now();

        for _ in 0..self.sample_size {
            let process_start = Instant::now();
            
            // 完整的处理流程
            let _ = self.validator.validate_message(&test_message).expect("验证失败");
            let encoded = self.codec.encode(&test_message).expect("编码失败");
            let native_format = self.codec.encode_native_format(&test_message).expect("Native编码失败");
            let decoded = self.codec.decode(&encoded).expect("解码失败");
            let _ = self.validator.validate_message(&decoded).expect("解码后验证失败");
            
            durations.push(process_start.elapsed().as_nanos() as u64);
        }

        let total_duration = start_time.elapsed();
        self.create_benchmark_result(
            "End-to-End Processing".to_string(),
            durations,
            total_duration,
            Some(data_size),
        )
    }

    /// 创建测试消息
    ///
    /// # 返回
    /// NativeMessage - 测试用消息
    fn create_test_message(&self) -> NativeMessage {
        self.create_test_message_with_id("benchmark-test-123".to_string())
    }

    /// 创建带指定ID的测试消息
    ///
    /// # 参数
    /// - `request_id`: 请求ID
    ///
    /// # 返回
    /// NativeMessage - 测试用消息
    fn create_test_message_with_id(&self, request_id: String) -> NativeMessage {
        NativeMessage::new(
            MessageType::GetCredentials,
            request_id,
            serde_json::json!({
                "domain": "example.com",
                "username": "test_user",
                "data": "x".repeat(1000), // 1KB的测试数据
                "metadata": {
                    "client_id": "test_client",
                    "session_id": "test_session",
                    "timestamp": 1234567890
                }
            }),
            "benchmark_test".to_string(),
        )
        .with_metadata("test_key".to_string(), "test_value".to_string())
        .with_extension("test_extension".to_string(), serde_json::json!({"enabled": true}))
    }

    /// 创建基准测试结果
    ///
    /// # 参数
    /// - `test_name`: 测试名称
    /// - `durations`: 执行时间列表（纳秒）
    /// - `total_duration`: 总执行时间
    /// - `data_size`: 数据大小（字节）
    ///
    /// # 返回
    /// BenchmarkResult - 基准测试结果
    fn create_benchmark_result(
        &self,
        test_name: String,
        durations: Vec<u64>,
        total_duration: Duration,
        data_size: Option<usize>,
    ) -> BenchmarkResult {
        let operations_count = durations.len() as u64;
        let total_duration_ns = total_duration.as_nanos() as u64;
        let average_duration_ns = durations.iter().sum::<u64>() / operations_count;
        let min_duration_ns = *durations.iter().min().unwrap_or(&0);
        let max_duration_ns = *durations.iter().max().unwrap_or(&0);
        
        let operations_per_second = if total_duration_ns > 0 {
            (operations_count as f64) / (total_duration_ns as f64 / 1_000_000_000.0)
        } else {
            0.0
        };

        let throughput_mb_per_sec = if let Some(size) = data_size {
            if total_duration_ns > 0 {
                let total_bytes = size * operations_count as usize;
                let seconds = total_duration_ns as f64 / 1_000_000_000.0;
                Some(total_bytes as f64 / (1024.0 * 1024.0) / seconds)
            } else {
                None
            }
        } else {
            None
        };

        BenchmarkResult {
            test_name,
            total_duration_ns,
            average_duration_ns,
            min_duration_ns,
            max_duration_ns,
            operations_count,
            operations_per_second,
            memory_usage_bytes: data_size,
            data_size_bytes: data_size,
            throughput_mb_per_sec,
        }
    }

    /// 打印基准测试结果
    ///
    /// # 参数
    /// - `results`: 测试结果列表
    pub fn print_benchmark_results(results: &[BenchmarkResult]) {
        println!("\n==== 协议层性能基准测试结果 ====\n");
        
        for result in results {
            println!("测试: {}", result.test_name);
            println!("  总执行时间: {:>12} ns ({:.2} ms)", 
                result.total_duration_ns, 
                result.total_duration_ns as f64 / 1_000_000.0
            );
            println!("  平均执行时间: {:>10} ns ({:.2} μs)", 
                result.average_duration_ns, 
                result.average_duration_ns as f64 / 1_000.0
            );
            println!("  最小执行时间: {:>10} ns ({:.2} μs)", 
                result.min_duration_ns, 
                result.min_duration_ns as f64 / 1_000.0
            );
            println!("  最大执行时间: {:>10} ns ({:.2} μs)", 
                result.max_duration_ns, 
                result.max_duration_ns as f64 / 1_000.0
            );
            println!("  操作次数: {:>14}", result.operations_count);
            println!("  每秒操作数: {:>10.2} ops/s", result.operations_per_second);
            
            if let Some(data_size) = result.data_size_bytes {
                println!("  数据大小: {:>13} bytes ({:.2} KB)", 
                    data_size, 
                    data_size as f64 / 1024.0
                );
            }
            
            if let Some(throughput) = result.throughput_mb_per_sec {
                println!("  吞吐量: {:>15.2} MB/s", throughput);
            }
            
            println!();
        }
    }

    /// 运行性能压力测试
    ///
    /// # 参数
    /// - `duration_seconds`: 测试持续时间（秒）
    ///
    /// # 返回
    /// BenchmarkResult - 压力测试结果
    pub fn run_stress_test(&mut self, duration_seconds: u64) -> BenchmarkResult {
        let test_message = self.create_test_message();
        let data_size = test_message.size_bytes();
        let end_time = Instant::now() + Duration::from_secs(duration_seconds);
        
        let mut operations_count = 0u64;
        let mut durations = Vec::new();
        let start_time = Instant::now();

        while Instant::now() < end_time {
            let operation_start = Instant::now();
            
            // 执行完整的操作序列
            let _ = self.validator.validate_message(&test_message);
            let encoded = self.codec.encode(&test_message).unwrap();
            let _ = self.codec.decode(&encoded);
            
            durations.push(operation_start.elapsed().as_nanos() as u64);
            operations_count += 1;
        }

        let total_duration = start_time.elapsed();
        
        BenchmarkResult {
            test_name: format!("Stress Test ({} seconds)", duration_seconds),
            total_duration_ns: total_duration.as_nanos() as u64,
            average_duration_ns: if !durations.is_empty() { 
                durations.iter().sum::<u64>() / durations.len() as u64 
            } else { 
                0 
            },
            min_duration_ns: durations.iter().min().copied().unwrap_or(0),
            max_duration_ns: durations.iter().max().copied().unwrap_or(0),
            operations_count,
            operations_per_second: operations_count as f64 / total_duration.as_secs_f64(),
            memory_usage_bytes: Some(data_size),
            data_size_bytes: Some(data_size),
            throughput_mb_per_sec: Some(
                (data_size * operations_count as usize) as f64 
                / (1024.0 * 1024.0) 
                / total_duration.as_secs_f64()
            ),
        }
    }
}

/// 运行基准测试的便捷函数
///
/// # 参数
/// - `sample_size`: 测试样本大小
///
/// # 返回
/// Vec<BenchmarkResult> - 所有测试结果
pub fn run_protocol_benchmarks(sample_size: usize) -> Vec<BenchmarkResult> {
    let mut suite = ProtocolBenchmarkSuite::new(sample_size);
    suite.run_all_benchmarks()
}

/// 运行高性能配置的基准测试
///
/// # 参数
/// - `sample_size`: 测试样本大小
///
/// # 返回
/// Vec<BenchmarkResult> - 所有测试结果
pub fn run_high_performance_benchmarks(sample_size: usize) -> Vec<BenchmarkResult> {
    let mut suite = ProtocolBenchmarkSuite::new_high_performance(sample_size);
    suite.run_all_benchmarks()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_benchmark_suite_creation() {
        let suite = ProtocolBenchmarkSuite::new(100);
        assert_eq!(suite.sample_size, 100);
    }

    #[test]
    fn test_message_encoding_benchmark() {
        let suite = ProtocolBenchmarkSuite::new(10);
        let result = suite.benchmark_message_encoding();
        
        assert_eq!(result.test_name, "Message Encoding");
        assert_eq!(result.operations_count, 10);
        assert!(result.average_duration_ns > 0);
        assert!(result.operations_per_second > 0.0);
    }

    #[test]
    fn test_message_validation_benchmark() {
        let suite = ProtocolBenchmarkSuite::new(10);
        let result = suite.benchmark_message_validation();
        
        assert_eq!(result.test_name, "Message Validation");
        assert_eq!(result.operations_count, 10);
        assert!(result.average_duration_ns > 0);
    }

    #[test]
    fn test_version_negotiation_benchmark() {
        let suite = ProtocolBenchmarkSuite::new(10);
        let result = suite.benchmark_version_negotiation();
        
        assert_eq!(result.test_name, "Version Negotiation");
        assert_eq!(result.operations_count, 10);
        assert!(result.average_duration_ns > 0);
    }

    #[test]
    fn test_benchmark_result_calculation() {
        let durations = vec![1000, 2000, 3000, 4000, 5000]; // 纳秒
        let total_duration = Duration::from_millis(1); // 1毫秒
        
        let suite = ProtocolBenchmarkSuite::new(5);
        let result = suite.create_benchmark_result(
            "Test".to_string(),
            durations,
            total_duration,
            Some(1024),
        );
        
        assert_eq!(result.operations_count, 5);
        assert_eq!(result.average_duration_ns, 3000);
        assert_eq!(result.min_duration_ns, 1000);
        assert_eq!(result.max_duration_ns, 5000);
        assert!(result.operations_per_second > 0.0);
    }

    #[test]
    fn test_stress_test() {
        let mut suite = ProtocolBenchmarkSuite::new_high_performance(0);
        let result = suite.run_stress_test(1); // 1秒压力测试
        
        assert!(result.test_name.contains("Stress Test"));
        assert!(result.operations_count > 0);
        assert!(result.operations_per_second > 0.0);
    }

    #[test]
    fn test_convenience_functions() {
        let results = run_protocol_benchmarks(5);
        assert!(!results.is_empty());
        assert!(results.iter().all(|r| r.operations_count == 5));

        let hp_results = run_high_performance_benchmarks(5);
        assert!(!hp_results.is_empty());
        assert!(hp_results.iter().all(|r| r.operations_count == 5));
    }
} 