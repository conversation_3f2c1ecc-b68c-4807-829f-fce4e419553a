//! Native Messaging 协议层模块
//!
//! 定义跨浏览器兼容的消息格式、协议验证器和编解码器

pub mod benchmarks;
pub mod codec;
pub mod message;
pub mod validator;
pub mod version;

// 公共导出
pub use benchmarks::{BenchmarkResult, ProtocolBenchmarkSuite, run_protocol_benchmarks, run_high_performance_benchmarks};
pub use codec::{MessageCodec, ProtocolCodec};
pub use message::{IncomingMessage, MessageType, NativeMessage, OutgoingMessage};
pub use validator::ProtocolValidator;
pub use version::{ProtocolVersionManager, VersionNegotiationResult, CompatibilityCheckResult};

/// 协议版本常量
pub const CURRENT_PROTOCOL_VERSION: u32 = 2;
pub const MIN_SUPPORTED_VERSION: u32 = 1;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_protocol_versions() {
        assert!(CURRENT_PROTOCOL_VERSION >= MIN_SUPPORTED_VERSION);
        assert!(CURRENT_PROTOCOL_VERSION <= 10); // 合理的版本上限
    }

    #[test]
    fn test_module_integration() {
        // 测试模块间的集成
        let version_manager = ProtocolVersionManager::new_default();
        let validator = ProtocolValidator::new_default();
        let codec = ProtocolCodec::new_default();

        // 验证版本一致性
        assert_eq!(version_manager.current_version(), CURRENT_PROTOCOL_VERSION);
        assert!(version_manager.is_version_supported(MIN_SUPPORTED_VERSION));
        
        // 验证组件可以正常工作
        let message = NativeMessage::new(
            MessageType::Ping,
            "test-123".to_string(),
            serde_json::json!({}),
            "test".to_string(),
        );

        assert!(validator.validate_message(&message).is_ok());
        assert!(codec.encode(&message).is_ok());
    }
}
