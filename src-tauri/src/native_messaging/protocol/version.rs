//! Native Messaging 协议版本管理模块
//!
//! 提供协议版本协商、兼容性检查和版本升级功能

use super::message::{ErrorCode, MessageType, NativeMessage, ProtocolCompatibility};
use crate::native_messaging::error::{NativeMessagingError, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 协议版本管理器
///
/// 负责版本协商、兼容性检查和版本升级
pub struct ProtocolVersionManager {
    /// 当前协议版本
    current_version: u32,
    /// 支持的版本列表
    supported_versions: Vec<u32>,
    /// 版本兼容性映射
    compatibility_matrix: HashMap<u32, Vec<u32>>,
    /// 版本特性映射
    version_features: HashMap<u32, Vec<String>>,
    /// 版本升级路径
    upgrade_paths: HashMap<u32, Vec<u32>>,
    /// 已废弃的版本
    deprecated_versions: Vec<u32>,
    /// 版本发布信息
    version_info: HashMap<u32, VersionInfo>,
}

/// 版本信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VersionInfo {
    /// 版本号
    pub version: u32,
    /// 发布日期
    pub release_date: String,
    /// 版本名称
    pub name: String,
    /// 版本描述
    pub description: String,
    /// 新增功能列表
    pub new_features: Vec<String>,
    /// 修复的问题列表
    pub bug_fixes: Vec<String>,
    /// 破坏性变更列表
    pub breaking_changes: Vec<String>,
    /// 迁移指南链接
    pub migration_guide: Option<String>,
    /// 支持状态
    pub support_status: SupportStatus,
    /// 最小客户端版本要求
    pub min_client_version: Option<u32>,
}

/// 版本支持状态
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq)]
pub enum SupportStatus {
    /// 当前版本
    Current,
    /// 维护中
    Maintained,
    /// 已废弃
    Deprecated,
    /// 不再支持
    EndOfLife,
}

/// 版本协商结果
#[derive(Debug, Clone)]
pub struct VersionNegotiationResult {
    /// 协商后的版本
    pub negotiated_version: u32,
    /// 协商是否成功
    pub success: bool,
    /// 协商详情
    pub details: VersionNegotiationDetails,
    /// 建议的版本
    pub recommended_version: Option<u32>,
    /// 警告信息
    pub warnings: Vec<String>,
}

/// 版本协商详情
#[derive(Debug, Clone)]
pub struct VersionNegotiationDetails {
    /// 客户端请求的版本
    pub client_versions: Vec<u32>,
    /// 服务端支持的版本
    pub server_versions: Vec<u32>,
    /// 共同支持的版本
    pub common_versions: Vec<u32>,
    /// 协商策略
    pub negotiation_strategy: NegotiationStrategy,
    /// 协商耗时（微秒）
    pub negotiation_time_micros: u64,
}

/// 版本协商策略
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum NegotiationStrategy {
    /// 选择最高版本
    HighestVersion,
    /// 选择最稳定版本
    MostStable,
    /// 选择最兼容版本
    MostCompatible,
    /// 自定义策略
    Custom,
}

/// 版本兼容性检查结果
#[derive(Debug, Clone)]
pub struct CompatibilityCheckResult {
    /// 是否兼容
    pub is_compatible: bool,
    /// 兼容性级别
    pub compatibility_level: CompatibilityLevel,
    /// 兼容性报告
    pub report: CompatibilityReport,
    /// 建议的迁移路径
    pub migration_path: Vec<u32>,
}

/// 兼容性级别
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum CompatibilityLevel {
    /// 完全兼容
    FullyCompatible = 4,
    /// 向后兼容
    BackwardCompatible = 3,
    /// 部分兼容
    PartiallyCompatible = 2,
    /// 有限兼容
    LimitedCompatible = 1,
    /// 不兼容
    Incompatible = 0,
}

/// 兼容性报告
#[derive(Debug, Clone)]
pub struct CompatibilityReport {
    /// 源版本
    pub source_version: u32,
    /// 目标版本
    pub target_version: u32,
    /// 兼容的功能列表
    pub compatible_features: Vec<String>,
    /// 不兼容的功能列表
    pub incompatible_features: Vec<String>,
    /// 需要迁移的功能列表
    pub migration_required: Vec<String>,
    /// 警告信息
    pub warnings: Vec<String>,
    /// 错误信息
    pub errors: Vec<String>,
}

impl ProtocolVersionManager {
    /// 创建新的版本管理器
    ///
    /// # 参数
    /// - `current_version`: 当前协议版本
    /// - `supported_versions`: 支持的版本列表
    ///
    /// # 返回
    /// ProtocolVersionManager - 版本管理器实例
    pub fn new(current_version: u32, supported_versions: Vec<u32>) -> Self {
        let mut manager = Self {
            current_version,
            supported_versions,
            compatibility_matrix: HashMap::new(),
            version_features: HashMap::new(),
            upgrade_paths: HashMap::new(),
            deprecated_versions: Vec::new(),
            version_info: HashMap::new(),
        };

        // 初始化默认配置
        manager.initialize_default_configuration();
        manager
    }

    /// 创建默认版本管理器
    ///
    /// # 返回
    /// ProtocolVersionManager - 使用默认配置的版本管理器
    pub fn new_default() -> Self {
        Self::new(
            super::CURRENT_PROTOCOL_VERSION,
            vec![super::MIN_SUPPORTED_VERSION, super::CURRENT_PROTOCOL_VERSION],
        )
    }

    /// 初始化默认配置
    fn initialize_default_configuration(&mut self) {
        // 初始化版本特性映射
        self.version_features.insert(1, vec![
            "basic_messaging".to_string(),
            "json_format".to_string(),
            "simple_auth".to_string(),
        ]);
        
        self.version_features.insert(2, vec![
            "basic_messaging".to_string(),
            "json_format".to_string(),
            "simple_auth".to_string(),
            "batch_operations".to_string(),
            "stream_support".to_string(),
            "enhanced_security".to_string(),
            "compression".to_string(),
            "metadata_support".to_string(),
        ]);

        // 初始化兼容性矩阵
        self.compatibility_matrix.insert(1, vec![1]);
        self.compatibility_matrix.insert(2, vec![1, 2]);

        // 初始化升级路径
        self.upgrade_paths.insert(1, vec![2]);

        // 初始化版本信息
        self.version_info.insert(1, VersionInfo {
            version: 1,
            release_date: "2024-01-01".to_string(),
            name: "基础版本".to_string(),
            description: "Native Messaging 协议的基础实现".to_string(),
            new_features: vec![
                "基础消息传递".to_string(),
                "JSON 格式支持".to_string(),
                "简单身份验证".to_string(),
            ],
            bug_fixes: vec![],
            breaking_changes: vec![],
            migration_guide: None,
            support_status: SupportStatus::Maintained,
            min_client_version: None,
        });

        self.version_info.insert(2, VersionInfo {
            version: 2,
            release_date: "2024-03-01".to_string(),
            name: "增强版本".to_string(),
            description: "增强功能和性能的协议版本".to_string(),
            new_features: vec![
                "批量操作支持".to_string(),
                "流式数据传输".to_string(),
                "增强安全性".to_string(),
                "数据压缩".to_string(),
                "元数据支持".to_string(),
            ],
            bug_fixes: vec![
                "修复消息大小限制问题".to_string(),
                "优化错误处理".to_string(),
            ],
            breaking_changes: vec![
                "消息格式字段调整".to_string(),
            ],
            migration_guide: Some("https://docs.securepassword.com/migration/v1-to-v2".to_string()),
            support_status: SupportStatus::Current,
            min_client_version: Some(1),
        });
    }

    /// 检查版本是否支持
    ///
    /// # 参数
    /// - `version`: 版本号
    ///
    /// # 返回
    /// bool - 是否支持该版本
    pub fn is_version_supported(&self, version: u32) -> bool {
        self.supported_versions.contains(&version)
    }

    /// 检查版本是否已废弃
    ///
    /// # 参数
    /// - `version`: 版本号
    ///
    /// # 返回
    /// bool - 版本是否已废弃
    pub fn is_version_deprecated(&self, version: u32) -> bool {
        self.deprecated_versions.contains(&version)
    }

    /// 获取版本特性列表
    ///
    /// # 参数
    /// - `version`: 版本号
    ///
    /// # 返回
    /// Vec<String> - 版本特性列表
    pub fn get_version_features(&self, version: u32) -> Vec<String> {
        self.version_features.get(&version).cloned().unwrap_or_default()
    }

    /// 获取兼容版本列表
    ///
    /// # 参数
    /// - `version`: 版本号
    ///
    /// # 返回
    /// Vec<u32> - 兼容版本列表
    pub fn get_compatible_versions(&self, version: u32) -> Vec<u32> {
        self.compatibility_matrix.get(&version).cloned().unwrap_or_default()
    }

    /// 版本协商
    ///
    /// # 参数
    /// - `client_versions`: 客户端支持的版本列表
    /// - `strategy`: 协商策略
    ///
    /// # 返回
    /// VersionNegotiationResult - 协商结果
    pub fn negotiate_version(
        &self,
        client_versions: &[u32],
        strategy: NegotiationStrategy,
    ) -> VersionNegotiationResult {
        let start_time = std::time::Instant::now();

        // 查找共同支持的版本
        let common_versions: Vec<u32> = client_versions
            .iter()
            .filter(|&v| self.is_version_supported(*v))
            .copied()
            .collect();

        let negotiated_version = if common_versions.is_empty() {
            // 没有共同支持的版本
            0
        } else {
            // 根据策略选择版本
            match strategy {
                NegotiationStrategy::HighestVersion => {
                    *common_versions.iter().max().unwrap()
                }
                NegotiationStrategy::MostStable => {
                    // 选择最稳定的版本（通常是非当前版本中的最高版本）
                    common_versions
                        .iter()
                        .filter(|&v| *v < self.current_version)
                        .max()
                        .copied()
                        .unwrap_or_else(|| *common_versions.iter().max().unwrap())
                }
                NegotiationStrategy::MostCompatible => {
                    // 选择兼容性最好的版本
                    self.find_most_compatible_version(&common_versions)
                }
                NegotiationStrategy::Custom => {
                    // 自定义策略，默认选择最高版本
                    *common_versions.iter().max().unwrap()
                }
            }
        };

        let success = negotiated_version > 0;
        let mut warnings = Vec::new();

        // 生成警告信息
        if !success {
            warnings.push("没有找到共同支持的协议版本".to_string());
        } else if self.is_version_deprecated(negotiated_version) {
            warnings.push(format!("协商的版本 {} 已被废弃", negotiated_version));
        }

        let negotiation_time_micros = start_time.elapsed().as_micros() as u64;

        VersionNegotiationResult {
            negotiated_version,
            success,
            details: VersionNegotiationDetails {
                client_versions: client_versions.to_vec(),
                server_versions: self.supported_versions.clone(),
                common_versions,
                negotiation_strategy: strategy,
                negotiation_time_micros,
            },
            recommended_version: if success {
                Some(self.current_version)
            } else {
                None
            },
            warnings,
        }
    }

    /// 查找兼容性最好的版本
    fn find_most_compatible_version(&self, versions: &[u32]) -> u32 {
        versions
            .iter()
            .max_by_key(|&v| {
                self.get_compatible_versions(*v).len()
            })
            .copied()
            .unwrap_or(0)
    }

    /// 检查版本兼容性
    ///
    /// # 参数
    /// - `source_version`: 源版本
    /// - `target_version`: 目标版本
    ///
    /// # 返回
    /// CompatibilityCheckResult - 兼容性检查结果
    pub fn check_compatibility(
        &self,
        source_version: u32,
        target_version: u32,
    ) -> CompatibilityCheckResult {
        let source_features = self.get_version_features(source_version);
        let target_features = self.get_version_features(target_version);

        let compatible_features: Vec<String> = source_features
            .iter()
            .filter(|f| target_features.contains(f))
            .cloned()
            .collect();

        let incompatible_features: Vec<String> = source_features
            .iter()
            .filter(|f| !target_features.contains(f))
            .cloned()
            .collect();

        let migration_required: Vec<String> = target_features
            .iter()
            .filter(|f| !source_features.contains(f))
            .cloned()
            .collect();

        let compatibility_level = self.calculate_compatibility_level(
            &compatible_features,
            &incompatible_features,
            &migration_required,
        );

        let is_compatible = compatibility_level >= CompatibilityLevel::PartiallyCompatible;

        let migration_path = if is_compatible {
            self.find_migration_path(source_version, target_version)
        } else {
            Vec::new()
        };

        let mut warnings = Vec::new();
        let mut errors = Vec::new();

        if !incompatible_features.is_empty() {
            warnings.push(format!(
                "检测到 {} 个不兼容功能",
                incompatible_features.len()
            ));
        }

        if !migration_required.is_empty() {
            warnings.push(format!(
                "需要迁移 {} 个功能",
                migration_required.len()
            ));
        }

        if !is_compatible {
            errors.push("版本不兼容，无法迁移".to_string());
        }

        CompatibilityCheckResult {
            is_compatible,
            compatibility_level,
            report: CompatibilityReport {
                source_version,
                target_version,
                compatible_features,
                incompatible_features,
                migration_required,
                warnings,
                errors,
            },
            migration_path,
        }
    }

    /// 计算兼容性级别
    fn calculate_compatibility_level(
        &self,
        compatible_features: &[String],
        incompatible_features: &[String],
        migration_required: &[String],
    ) -> CompatibilityLevel {
        let total_features = compatible_features.len() + incompatible_features.len();
        
        if total_features == 0 {
            return CompatibilityLevel::Incompatible;
        }

        let compatibility_ratio = compatible_features.len() as f64 / total_features as f64;

        match compatibility_ratio {
            r if r >= 1.0 && migration_required.is_empty() => CompatibilityLevel::FullyCompatible,
            r if r >= 0.9 => CompatibilityLevel::BackwardCompatible,
            r if r >= 0.7 => CompatibilityLevel::PartiallyCompatible,
            r if r >= 0.5 => CompatibilityLevel::LimitedCompatible,
            _ => CompatibilityLevel::Incompatible,
        }
    }

    /// 查找迁移路径
    fn find_migration_path(&self, source_version: u32, target_version: u32) -> Vec<u32> {
        if source_version == target_version {
            return vec![source_version];
        }

        // 简单的路径查找算法
        let mut path = vec![source_version];
        let mut current = source_version;

        while current != target_version {
            if let Some(next_versions) = self.upgrade_paths.get(&current) {
                if let Some(&next) = next_versions.iter().find(|&&v| v == target_version || v > current) {
                    path.push(next);
                    current = next;
                } else {
                    // 没有直接路径，尝试通过最新版本
                    if current < self.current_version && target_version <= self.current_version {
                        path.push(self.current_version);
                        current = self.current_version;
                    } else {
                        break;
                    }
                }
            } else {
                break;
            }
        }

        if current == target_version {
            path
        } else {
            Vec::new() // 无法找到迁移路径
        }
    }

    /// 升级消息到指定版本
    ///
    /// # 参数
    /// - `message`: 原始消息
    /// - `target_version`: 目标版本
    ///
    /// # 返回
    /// Result<NativeMessage> - 升级后的消息
    pub fn upgrade_message(
        &self,
        message: &NativeMessage,
        target_version: u32,
    ) -> Result<NativeMessage> {
        if !self.is_version_supported(target_version) {
            return Err(NativeMessagingError::ProtocolError(
                format!("不支持目标版本 {}", target_version)
            ));
        }

        let compatibility = self.check_compatibility(message.version, target_version);
        
        if !compatibility.is_compatible {
            return Err(NativeMessagingError::ProtocolError(
                "版本不兼容，无法升级".to_string()
            ));
        }

        let mut upgraded_message = message.clone();
        upgraded_message.version = target_version;

        // 根据版本差异进行具体的升级逻辑
        match (message.version, target_version) {
            (1, 2) => {
                // 从版本1升级到版本2的具体逻辑
                self.upgrade_v1_to_v2(&mut upgraded_message)?;
            }
            // 可以添加更多版本升级路径
            _ => {
                // 通用升级逻辑
            }
        }

        Ok(upgraded_message)
    }

    /// 从版本1升级到版本2的具体实现
    fn upgrade_v1_to_v2(&self, message: &mut NativeMessage) -> Result<()> {
        // 添加新的元数据字段
        if message.metadata.is_empty() {
            message.metadata.insert("upgraded_from".to_string(), "v1".to_string());
            message.metadata.insert("upgrade_timestamp".to_string(), 
                std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap()
                    .as_secs()
                    .to_string()
            );
        }

        // 确保优先级字段存在
        if message.priority == super::message::MessagePriority::default() {
            message.priority = super::message::MessagePriority::Normal;
        }

        Ok(())
    }

    /// 获取版本信息
    ///
    /// # 参数
    /// - `version`: 版本号
    ///
    /// # 返回
    /// Option<&VersionInfo> - 版本信息
    pub fn get_version_info(&self, version: u32) -> Option<&VersionInfo> {
        self.version_info.get(&version)
    }

    /// 获取当前版本
    ///
    /// # 返回
    /// u32 - 当前版本号
    pub fn current_version(&self) -> u32 {
        self.current_version
    }

    /// 获取支持的版本列表
    ///
    /// # 返回
    /// &[u32] - 支持的版本列表
    pub fn supported_versions(&self) -> &[u32] {
        &self.supported_versions
    }

    /// 添加新版本支持
    ///
    /// # 参数
    /// - `version`: 版本号
    /// - `features`: 版本特性列表
    /// - `info`: 版本信息
    pub fn add_version_support(
        &mut self,
        version: u32,
        features: Vec<String>,
        info: VersionInfo,
    ) {
        if !self.supported_versions.contains(&version) {
            self.supported_versions.push(version);
            self.supported_versions.sort();
        }

        self.version_features.insert(version, features);
        self.version_info.insert(version, info);

        // 自动更新兼容性矩阵
        self.update_compatibility_matrix(version);
    }

    /// 更新兼容性矩阵
    fn update_compatibility_matrix(&mut self, new_version: u32) {
        // 新版本通常与之前的版本兼容
        let mut compatible_versions = vec![new_version];
        
        for &existing_version in &self.supported_versions {
            if existing_version < new_version {
                compatible_versions.push(existing_version);
            }
        }

        self.compatibility_matrix.insert(new_version, compatible_versions);
    }

    /// 标记版本为废弃
    ///
    /// # 参数
    /// - `version`: 版本号
    pub fn deprecate_version(&mut self, version: u32) {
        if !self.deprecated_versions.contains(&version) {
            self.deprecated_versions.push(version);
        }

        // 更新版本信息中的支持状态
        if let Some(info) = self.version_info.get_mut(&version) {
            info.support_status = SupportStatus::Deprecated;
        }
    }

    /// 获取推荐的协议版本
    ///
    /// # 返回
    /// u32 - 推荐的版本号
    pub fn recommended_version(&self) -> u32 {
        // 返回当前支持的最高稳定版本
        self.supported_versions
            .iter()
            .filter(|&v| !self.is_version_deprecated(*v))
            .max()
            .copied()
            .unwrap_or(self.current_version)
    }
}

impl Default for ProtocolVersionManager {
    fn default() -> Self {
        Self::new_default()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_version_manager_creation() {
        let manager = ProtocolVersionManager::new(2, vec![1, 2]);
        assert_eq!(manager.current_version(), 2);
        assert_eq!(manager.supported_versions(), &[1, 2]);
    }

    #[test]
    fn test_version_support_check() {
        let manager = ProtocolVersionManager::new_default();
        assert!(manager.is_version_supported(1));
        assert!(manager.is_version_supported(2));
        assert!(!manager.is_version_supported(3));
    }

    #[test]
    fn test_version_negotiation() {
        let manager = ProtocolVersionManager::new_default();
        let client_versions = vec![1, 2, 3];
        
        let result = manager.negotiate_version(&client_versions, NegotiationStrategy::HighestVersion);
        assert!(result.success);
        assert_eq!(result.negotiated_version, 2);
    }

    #[test]
    fn test_version_negotiation_no_common() {
        let manager = ProtocolVersionManager::new_default();
        let client_versions = vec![3, 4, 5];
        
        let result = manager.negotiate_version(&client_versions, NegotiationStrategy::HighestVersion);
        assert!(!result.success);
        assert_eq!(result.negotiated_version, 0);
    }

    #[test]
    fn test_compatibility_check() {
        let manager = ProtocolVersionManager::new_default();
        let result = manager.check_compatibility(1, 2);
        
        assert!(result.is_compatible);
        assert!(!result.report.compatible_features.is_empty());
    }

    #[test]
    fn test_message_upgrade() {
        let manager = ProtocolVersionManager::new_default();
        // 创建版本1的消息
        let message = NativeMessage::new_with_version(
            1, // 明确指定为版本1
            MessageType::GetCredentials,
            "test-123".to_string(),
            serde_json::json!({"domain": "example.com"}),
            "test-extension".to_string(),
        );

        let upgraded = manager.upgrade_message(&message, 2).unwrap();
        assert_eq!(upgraded.version, 2);
        assert!(!upgraded.metadata.is_empty());
        assert!(upgraded.metadata.contains_key("upgraded_from"));
        assert!(upgraded.metadata.contains_key("upgrade_timestamp"));
    }

    #[test]
    fn test_version_features() {
        let manager = ProtocolVersionManager::new_default();
        let v1_features = manager.get_version_features(1);
        let v2_features = manager.get_version_features(2);
        
        assert!(!v1_features.is_empty());
        assert!(!v2_features.is_empty());
        assert!(v2_features.len() > v1_features.len());
    }

    #[test]
    fn test_version_deprecation() {
        let mut manager = ProtocolVersionManager::new_default();
        assert!(!manager.is_version_deprecated(1));
        
        manager.deprecate_version(1);
        assert!(manager.is_version_deprecated(1));
    }

    #[test]
    fn test_add_new_version() {
        let mut manager = ProtocolVersionManager::new_default();
        let features = vec!["new_feature".to_string()];
        let info = VersionInfo {
            version: 3,
            release_date: "2024-06-01".to_string(),
            name: "新版本".to_string(),
            description: "测试新版本".to_string(),
            new_features: features.clone(),
            bug_fixes: vec![],
            breaking_changes: vec![],
            migration_guide: None,
            support_status: SupportStatus::Current,
            min_client_version: Some(2),
        };

        manager.add_version_support(3, features, info);
        assert!(manager.is_version_supported(3));
        assert!(!manager.get_version_features(3).is_empty());
    }

    #[test]
    fn test_migration_path() {
        let manager = ProtocolVersionManager::new_default();
        let path = manager.find_migration_path(1, 2);
        assert!(!path.is_empty());
        assert_eq!(path.first(), Some(&1));
        assert_eq!(path.last(), Some(&2));
    }

    #[test]
    fn test_recommended_version() {
        let manager = ProtocolVersionManager::new_default();
        let recommended = manager.recommended_version();
        assert!(manager.is_version_supported(recommended));
        assert!(!manager.is_version_deprecated(recommended));
    }
} 