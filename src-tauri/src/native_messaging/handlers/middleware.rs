//! Native Messaging 中间件
//!
//! 提供消息处理前后的中间件支持

use crate::native_messaging::{
    error::Result,
    protocol::message::{NativeMessage, OutgoingMessage},
};
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{Mutex, RwLock};
use tracing::{error, info, warn};

/// 中间件接口
///
/// 定义中间件的标准接口
#[async_trait]
pub trait Middleware: Send + Sync {
    /// 消息处理前的中间件处理
    ///
    /// # 参数
    /// - `message`: 输入消息
    ///
    /// # 返回
    /// Result<NativeMessage> - 处理后的消息
    async fn before_handle(&self, message: NativeMessage) -> Result<NativeMessage>;

    /// 消息处理后的中间件处理
    ///
    /// # 参数
    /// - `response`: 响应消息
    ///
    /// # 返回
    /// Result<OutgoingMessage> - 处理后的响应
    async fn after_handle(&self, response: OutgoingMessage) -> Result<OutgoingMessage>;

    /// 获取中间件名称
    ///
    /// # 返回
    /// &str - 中间件名称
    fn name(&self) -> &str;

    /// 可选的错误处理
    async fn on_error(&self, _error: &crate::native_messaging::error::NativeMessagingError, _message: &NativeMessage) -> Result<Option<OutgoingMessage>> {
        Ok(None)
    }

    /// 中间件优先级（数字越小优先级越高）
    fn priority(&self) -> u32 {
        100
    }
}

/// 中间件链
///
/// 管理多个中间件的执行顺序
pub struct MiddlewareChain {
    /// 中间件列表
    middlewares: Vec<Box<dyn Middleware>>,
}

impl MiddlewareChain {
    /// 创建新的中间件链
    pub fn new() -> Self {
        Self {
            middlewares: Vec::new(),
        }
    }

    /// 添加中间件
    ///
    /// # 参数
    /// - `middleware`: 中间件实例
    pub fn add(&mut self, middleware: Box<dyn Middleware>) {
        self.middlewares.push(middleware);
        // 按优先级排序
        self.middlewares.sort_by_key(|m| m.priority());
    }

    /// 执行前置中间件
    ///
    /// # 参数
    /// - `message`: 输入消息
    ///
    /// # 返回
    /// Result<NativeMessage> - 处理后的消息
    pub async fn execute_before(&self, mut message: NativeMessage) -> Result<NativeMessage> {
        for middleware in &self.middlewares {
            message = middleware.before_handle(message).await?;
        }
        Ok(message)
    }

    /// 执行后置中间件
    ///
    /// # 参数
    /// - `response`: 响应消息
    ///
    /// # 返回
    /// Result<OutgoingMessage> - 处理后的响应
    pub async fn execute_after(&self, mut response: OutgoingMessage) -> Result<OutgoingMessage> {
        // 逆序执行后置中间件
        for middleware in self.middlewares.iter().rev() {
            response = middleware.after_handle(response).await?;
        }
        Ok(response)
    }

    /// 处理错误
    pub async fn handle_error(&self, error: &crate::native_messaging::error::NativeMessagingError, message: &NativeMessage) -> Result<Option<OutgoingMessage>> {
        for middleware in &self.middlewares {
            if let Some(response) = middleware.on_error(error, message).await? {
                return Ok(Some(response));
            }
        }
        Ok(None)
    }

    /// 获取中间件数量
    pub fn len(&self) -> usize {
        self.middlewares.len()
    }

    /// 检查是否为空
    pub fn is_empty(&self) -> bool {
        self.middlewares.is_empty()
    }
}

impl Default for MiddlewareChain {
    fn default() -> Self {
        Self::new()
    }
}

/// 日志中间件
///
/// 记录消息处理的详细日志
pub struct LoggingMiddleware {
    /// 日志级别
    log_level: tracing::Level,
}

impl LoggingMiddleware {
    pub fn new() -> Self {
        Self {
            log_level: tracing::Level::INFO,
        }
    }

    pub fn with_level(level: tracing::Level) -> Self {
        Self {
            log_level: level,
        }
    }
}

#[async_trait]
impl Middleware for LoggingMiddleware {
    async fn before_handle(&self, message: NativeMessage) -> Result<NativeMessage> {
        info!(
            target: "native_messaging::middleware::logging",
            "处理消息: type={:?}, id={}, source={}",
            message.message_type,
            message.request_id,
            message.source
        );
        Ok(message)
    }

    async fn after_handle(&self, response: OutgoingMessage) -> Result<OutgoingMessage> {
        info!(
            target: "native_messaging::middleware::logging",
            "响应消息: id={}, status={:?}, processing_time={}ms",
            response.message.request_id,
            response.status,
            response.processing_time_ms.unwrap_or(0)
        );
        Ok(response)
    }

    async fn on_error(&self, error: &crate::native_messaging::error::NativeMessagingError, message: &NativeMessage) -> Result<Option<OutgoingMessage>> {
        error!(
            target: "native_messaging::middleware::logging",
            "消息处理错误: id={}, error={}",
            message.request_id,
            error
        );
        Ok(None)
    }

    fn name(&self) -> &str {
        "LoggingMiddleware"
    }

    fn priority(&self) -> u32 {
        10 // 高优先级，优先记录日志
    }
}

/// 性能监控中间件
///
/// 监控消息处理的性能指标
pub struct PerformanceMiddleware {
    /// 性能指标
    metrics: Arc<RwLock<PerformanceMetrics>>,
}

#[derive(Debug, Default, Clone)]
pub struct PerformanceMetrics {
    /// 总处理时间
    pub total_processing_time: Duration,
    /// 处理的消息数量
    pub message_count: u64,
    /// 错误数量
    pub error_count: u64,
    /// 最慢的处理时间
    pub max_processing_time: Duration,
    /// 最快的处理时间
    pub min_processing_time: Duration,
    /// 各类型消息的处理时间
    pub type_metrics: HashMap<String, TypeMetrics>,
}

#[derive(Debug, Default, Clone)]
pub struct TypeMetrics {
    pub count: u64,
    pub total_time: Duration,
    pub max_time: Duration,
    pub min_time: Duration,
}

impl PerformanceMiddleware {
    pub fn new() -> Self {
        Self {
            metrics: Arc::new(RwLock::new(PerformanceMetrics::default())),
        }
    }

    pub async fn get_metrics(&self) -> PerformanceMetrics {
        self.metrics.read().await.clone()
    }

    pub async fn reset_metrics(&self) {
        *self.metrics.write().await = PerformanceMetrics::default();
    }
}

#[derive(Debug, Clone)]
struct ProcessingContext {
    start_time: Instant,
    message_type: String,
}

impl ProcessingContext {
    fn new(message_type: String) -> Self {
        Self {
            start_time: Instant::now(),
            message_type,
        }
    }

    fn elapsed(&self) -> Duration {
        self.start_time.elapsed()
    }
}

// 为 Instant 实现序列化/反序列化的包装器
use std::time::SystemTime;

#[derive(Debug, Clone, Serialize, Deserialize)]
struct SerializableProcessingContext {
    start_time_nanos: u64, // 使用纳秒级时间戳
    message_type: String,
}

impl From<ProcessingContext> for SerializableProcessingContext {
    fn from(ctx: ProcessingContext) -> Self {
        Self {
            start_time_nanos: SystemTime::now()
                .duration_since(SystemTime::UNIX_EPOCH)
                .unwrap_or_default()
                .as_nanos() as u64,
            message_type: ctx.message_type,
        }
    }
}

impl From<SerializableProcessingContext> for ProcessingContext {
    fn from(ctx: SerializableProcessingContext) -> Self {
        // 注意：这会丢失原始的 Instant，但用于测试目的是可以接受的
        Self {
            start_time: Instant::now() - Duration::from_millis(1), // 给一些处理时间
            message_type: ctx.message_type,
        }
    }
}

#[async_trait]
impl Middleware for PerformanceMiddleware {
    async fn before_handle(&self, mut message: NativeMessage) -> Result<NativeMessage> {
        let context = ProcessingContext::new(format!("{:?}", message.message_type));
        let serializable_context = SerializableProcessingContext::from(context);
        
        // 将序列化的上下文存储到消息的扩展字段中
        message.extensions.insert(
            "performance_context".to_string(),
            serde_json::to_value(serializable_context).unwrap(),
        );
        
        Ok(message)
    }

    async fn after_handle(&self, mut response: OutgoingMessage) -> Result<OutgoingMessage> {
        // 从扩展字段中获取上下文
        if let Some(context_value) = response.message.extensions.get("performance_context") {
            if let Ok(serializable_context) = serde_json::from_value::<SerializableProcessingContext>(context_value.clone()) {
                let context = ProcessingContext::from(serializable_context);
                let processing_time = context.elapsed();
                
                // 更新响应中的处理时间
                response.processing_time_ms = Some(processing_time.as_millis() as u64);
                
                // 更新性能指标
                let mut metrics = self.metrics.write().await;
                metrics.total_processing_time += processing_time;
                metrics.message_count += 1;
                
                if metrics.message_count == 1 {
                    metrics.min_processing_time = processing_time;
                    metrics.max_processing_time = processing_time;
                } else {
                    if processing_time < metrics.min_processing_time {
                        metrics.min_processing_time = processing_time;
                    }
                    if processing_time > metrics.max_processing_time {
                        metrics.max_processing_time = processing_time;
                    }
                }
                
                // 更新类型特定的指标
                let type_metrics = metrics.type_metrics.entry(context.message_type).or_default();
                type_metrics.count += 1;
                type_metrics.total_time += processing_time;
                
                if type_metrics.count == 1 {
                    type_metrics.min_time = processing_time;
                    type_metrics.max_time = processing_time;
                } else {
                    if processing_time < type_metrics.min_time {
                        type_metrics.min_time = processing_time;
                    }
                    if processing_time > type_metrics.max_time {
                        type_metrics.max_time = processing_time;
                    }
                }
                
                // 清理扩展字段
                response.message.extensions.remove("performance_context");
            }
        }
        
        Ok(response)
    }

    async fn on_error(&self, _error: &crate::native_messaging::error::NativeMessagingError, _message: &NativeMessage) -> Result<Option<OutgoingMessage>> {
        // 更新错误计数
        let mut metrics = self.metrics.write().await;
        metrics.error_count += 1;
        Ok(None)
    }

    fn name(&self) -> &str {
        "PerformanceMiddleware"
    }

    fn priority(&self) -> u32 {
        5 // 最高优先级，确保性能统计的准确性
    }
}

/// 速率限制中间件
///
/// 限制每个来源的请求频率
pub struct RateLimitMiddleware {
    /// 每个来源的请求计数器
    counters: Arc<Mutex<HashMap<String, (u64, Instant)>>>,
    /// 每秒允许的最大请求数
    requests_per_second: u64,
}

impl RateLimitMiddleware {
    pub fn new(requests_per_second: u64) -> Self {
        Self {
            counters: Arc::new(Mutex::new(HashMap::new())),
            requests_per_second,
        }
    }

    async fn check_rate_limit(&self, source: &str) -> Result<()> {
        let mut counters = self.counters.lock().await;
        let now = Instant::now();
        
        // 获取或创建计数器
        let (count, last_reset) = counters.entry(source.to_string()).or_insert((0, now));
        
        // 如果距离上次重置超过1秒，重置计数器
        if now.duration_since(*last_reset) >= Duration::from_secs(1) {
            *count = 1;
            *last_reset = now;
            return Ok(());
        }
        
        // 检查是否超过限制
        if *count >= self.requests_per_second {
            return Err(crate::native_messaging::error::NativeMessagingError::RateLimitExceeded(
                source.to_string(),
                self.requests_per_second,
            ));
        }
        
        // 增加计数
        *count += 1;
        Ok(())
    }
}

#[async_trait]
impl Middleware for RateLimitMiddleware {
    async fn before_handle(&self, message: NativeMessage) -> Result<NativeMessage> {
        self.check_rate_limit(&message.source).await?;
        Ok(message)
    }

    async fn after_handle(&self, response: OutgoingMessage) -> Result<OutgoingMessage> {
        Ok(response)
    }

    fn name(&self) -> &str {
        "RateLimitMiddleware"
    }

    fn priority(&self) -> u32 {
        20 // 中等优先级
    }
}

/// 安全中间件
///
/// 提供安全验证和防护功能
pub struct SecurityMiddleware {
    /// 允许的消息来源白名单
    allowed_sources: Arc<RwLock<Vec<String>>>,
    /// 是否启用严格模式
    strict_mode: bool,
}

impl SecurityMiddleware {
    pub fn new() -> Self {
        Self {
            allowed_sources: Arc::new(RwLock::new(Vec::new())),
            strict_mode: false,
        }
    }

    pub fn with_allowed_sources(sources: Vec<String>) -> Self {
        Self {
            allowed_sources: Arc::new(RwLock::new(sources)),
            strict_mode: false,
        }
    }

    pub fn with_strict_mode(mut self, enabled: bool) -> Self {
        self.strict_mode = enabled;
        self
    }

    pub async fn add_allowed_source(&self, source: String) {
        let mut sources = self.allowed_sources.write().await;
        if !sources.contains(&source) {
            sources.push(source);
        }
    }

    pub async fn remove_allowed_source(&self, source: &str) {
        let mut sources = self.allowed_sources.write().await;
        sources.retain(|s| s != source);
    }
}

#[async_trait]
impl Middleware for SecurityMiddleware {
    async fn before_handle(&self, message: NativeMessage) -> Result<NativeMessage> {
        // 检查来源
        let sources = self.allowed_sources.read().await;
        if !sources.is_empty() && !sources.contains(&message.source) {
            warn!(
                target: "native_messaging::middleware::security",
                "未授权的消息来源: {}",
                message.source
            );
            return Err(crate::native_messaging::error::NativeMessagingError::SecurityError(
                format!("未授权的消息来源: {}", message.source)
            ));
        }

        // 在严格模式下进行额外检查
        if self.strict_mode {
            // 检查消息大小
            let message_size = serde_json::to_vec(&message).unwrap_or_default().len();
            if message_size > 1024 * 1024 {  // 1MB 限制
                warn!(
                    target: "native_messaging::middleware::security",
                    "消息过大: {} bytes",
                    message_size
                );
                return Err(crate::native_messaging::error::NativeMessagingError::SecurityError(
                    "消息过大".to_string()
                ));
            }

            // 检查请求ID格式
            if message.request_id.is_empty() || message.request_id.len() > 128 {
                warn!(
                    target: "native_messaging::middleware::security",
                    "无效的请求ID: {}",
                    message.request_id
                );
                return Err(crate::native_messaging::error::NativeMessagingError::SecurityError(
                    "无效的请求ID".to_string()
                ));
            }
        }

        Ok(message)
    }

    async fn after_handle(&self, response: OutgoingMessage) -> Result<OutgoingMessage> {
        // 在响应中添加安全头
        // 这里可以添加安全相关的元数据或清理敏感信息
        Ok(response)
    }

    fn name(&self) -> &str {
        "SecurityMiddleware"
    }

    fn priority(&self) -> u32 {
        30 // 较低优先级，最后执行安全检查
    }
}

impl Default for SecurityMiddleware {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::native_messaging::protocol::message::MessageType;

    #[tokio::test]
    async fn test_middleware_chain_ordering() {
        let mut chain = MiddlewareChain::new();
        
        // 添加不同优先级的中间件
        chain.add(Box::new(PerformanceMiddleware::new())); // 优先级 5
        chain.add(Box::new(LoggingMiddleware::new())); // 优先级 10
        chain.add(Box::new(RateLimitMiddleware::new(100))); // 优先级 20
        
        assert_eq!(chain.len(), 3);
        
        // 验证中间件按优先级排序
        let message = NativeMessage::new(
            MessageType::Test,
            "test".to_string(),
            serde_json::json!({}),
            "test-source".to_string(),
        );
        
        // 这应该按优先级顺序执行中间件
        let result = chain.execute_before(message).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_performance_middleware() {
        let middleware = PerformanceMiddleware::new();
        
        let message = NativeMessage::new(
            MessageType::Test,
            "perf-test".to_string(),
            serde_json::json!({}),
            "test-source".to_string(),
        );
        
        // 模拟消息处理
        let processed_message = middleware.before_handle(message).await.unwrap();
        
        // 模拟一些处理时间
        tokio::time::sleep(tokio::time::Duration::from_millis(1)).await;
        
        let response = OutgoingMessage::success("perf-test".to_string(), serde_json::json!({}));
        let response_with_message = OutgoingMessage {
            message: processed_message,
            ..response
        };
        
        let final_response = middleware.after_handle(response_with_message).await.unwrap();
        
        // 验证处理时间被记录
        assert!(final_response.processing_time_ms.is_some());
        assert!(final_response.processing_time_ms.unwrap() >= 1);
        
        // 验证指标被更新
        let metrics = middleware.get_metrics().await;
        assert_eq!(metrics.message_count, 1);
        assert!(metrics.total_processing_time.as_millis() >= 1);
    }

    #[tokio::test]
    async fn test_rate_limit_middleware() {
        let middleware = RateLimitMiddleware::new(2); // 每秒2个请求
        
        // 第一个请求应该成功
        let message1 = NativeMessage::new(
            MessageType::Test,
            "rate-test-1".to_string(),
            serde_json::json!({}),
            "test-source".to_string(),
        );
        assert!(middleware.before_handle(message1).await.is_ok());
        
        // 第二个请求应该成功
        let message2 = NativeMessage::new(
            MessageType::Test,
            "rate-test-2".to_string(),
            serde_json::json!({}),
            "test-source".to_string(),
        );
        assert!(middleware.before_handle(message2).await.is_ok());
        
        // 第三个请求应该被限制
        let message3 = NativeMessage::new(
            MessageType::Test,
            "rate-test-3".to_string(),
            serde_json::json!({}),
            "test-source".to_string(),
        );
        assert!(middleware.before_handle(message3).await.is_err());
    }

    #[tokio::test]
    async fn test_security_middleware() {
        let middleware = SecurityMiddleware::new().with_strict_mode(true);
        middleware.add_allowed_source("trusted-source".to_string()).await;
        
        // 来自可信来源的消息应该成功
        let trusted_message = NativeMessage::new(
            MessageType::Test,
            "security-test-1".to_string(),
            serde_json::json!({}),
            "trusted-source".to_string(),
        );
        assert!(middleware.before_handle(trusted_message).await.is_ok());
        
        // 来自不可信来源的消息应该被拒绝
        let untrusted_message = NativeMessage::new(
            MessageType::Test,
            "security-test-2".to_string(),
            serde_json::json!({}),
            "untrusted-source".to_string(),
        );
        assert!(middleware.before_handle(untrusted_message).await.is_err());
        
        // 空请求ID应该被拒绝
        let mut invalid_message = NativeMessage::new(
            MessageType::Test,
            "".to_string(), // 空请求ID
            serde_json::json!({}),
            "trusted-source".to_string(),
        );
        assert!(middleware.before_handle(invalid_message).await.is_err());
    }
}
