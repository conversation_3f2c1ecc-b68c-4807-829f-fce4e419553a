//! Native Messaging 处理器模块集成测试
//!
//! 测试消息处理框架的完整功能，包括处理器注册、中间件执行、性能指标收集和错误处理

use super::*;
use crate::native_messaging::{
    error::NativeMessagingError,
    monitoring::metrics::PerformanceMetrics,
    protocol::message::{MessageType, NativeMessage, OutgoingMessage},
};
use async_trait::async_trait;
use std::sync::{
    atomic::{AtomicU64, Ordering},
    Arc,
};
use std::time::Duration;
use tokio::time::sleep;

/// 测试用处理器 - 模拟长时间处理
struct SlowHandler {
    name: String,
    delay: Duration,
    call_count: Arc<AtomicU64>,
}

impl SlowHandler {
    fn new(name: String, delay: Duration) -> Self {
        Self {
            name,
            delay,
            call_count: Arc::new(AtomicU64::new(0)),
        }
    }

    fn get_call_count(&self) -> u64 {
        self.call_count.load(Ordering::Relaxed)
    }
}

#[async_trait]
impl MessageHandler for SlowHandler {
    async fn handle(&self, message: NativeMessage) -> Result<OutgoingMessage> {
        self.call_count.fetch_add(1, Ordering::Relaxed);
        
        // 模拟处理延迟
        sleep(self.delay).await;
        
        Ok(OutgoingMessage::success(
            message.request_id,
            serde_json::json!({
                "handler": self.name,
                "processed_at": chrono::Utc::now().to_rfc3339(),
                "delay_ms": self.delay.as_millis()
            }),
        ))
    }

    fn message_types(&self) -> Vec<String> {
        use crate::native_messaging::protocol::message::MessageType;
        vec![MessageType::Custom("SlowTest".to_string()).as_string()]
    }

    fn name(&self) -> &str {
        &self.name
    }
}

/// 测试用处理器 - 模拟错误
struct ErrorHandler {
    should_fail: bool,
}

impl ErrorHandler {
    fn new(should_fail: bool) -> Self {
        Self { should_fail }
    }
}

#[async_trait]
impl MessageHandler for ErrorHandler {
    async fn handle(&self, _message: NativeMessage) -> Result<OutgoingMessage> {
        if self.should_fail {
            Err(NativeMessagingError::HandlerError(
                "模拟错误".to_string(),
            ))
        } else {
            Ok(OutgoingMessage::success(
                "test".to_string(),
                serde_json::json!({ "status": "success" }),
            ))
        }
    }

    fn message_types(&self) -> Vec<String> {
        vec!["custom_ErrorTest".to_string()]
    }

    fn name(&self) -> &str {
        "ErrorHandler"
    }
}

/// 测试用中间件 - 模拟处理时间记录
struct TimingMiddleware {
    metrics: Arc<PerformanceMetrics>,
}

impl TimingMiddleware {
    fn new(metrics: Arc<PerformanceMetrics>) -> Self {
        Self { metrics }
    }
}

#[async_trait]
impl Middleware for TimingMiddleware {
    async fn before_handle(&self, message: NativeMessage) -> Result<NativeMessage> {
        // 直接在中间件中记录消息处理
        let processing_time = Duration::from_millis(15); // 模拟处理时间
        self.metrics.record_message(true, processing_time);
        Ok(message)
    }

    async fn after_handle(&self, response: OutgoingMessage) -> Result<OutgoingMessage> {
        // 不需要在这里处理，前置中间件已经记录了
        Ok(response)
    }

    fn name(&self) -> &str {
        "TimingMiddleware"
    }

    fn priority(&self) -> u32 {
        1 // 最高优先级
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::test;

    #[test]
    async fn test_complete_message_processing_flow() {
        // 创建处理器管理器
        let manager = HandlerManager::new();
        
        // 注册内置处理器
        let builtin_count = manager.register_builtin_handlers().await.unwrap();
        assert!(builtin_count > 0);
        
        // 注册自定义处理器
        let slow_handler = Arc::new(SlowHandler::new("SlowHandler".to_string(), Duration::from_millis(10)));
        manager.register_handler(slow_handler.clone()).await.unwrap();

        // 添加性能中间件
        manager.add_middleware(Box::new(middleware::PerformanceMiddleware::new())).await;
        
        // 测试健康检查
        let health_message = NativeMessage::new(
            MessageType::HealthCheck,
            "health-test".to_string(),
            serde_json::json!({}),
            "test-source".to_string(),
        );
        
        let health_response = manager.dispatch_message(health_message).await.unwrap();
        assert_eq!(health_response.message.request_id, "health-test");
        
        // 测试慢处理器
        let slow_message = NativeMessage::new(
            MessageType::Custom("SlowTest".to_string()),
            "slow-test".to_string(),
            serde_json::json!({}),
            "test-source".to_string(),
        );
        
        let slow_response = manager.dispatch_message(slow_message).await.unwrap();
        assert_eq!(slow_response.message.request_id, "slow-test");
        assert_eq!(slow_handler.get_call_count(), 1);
        
        // 验证支持的消息类型
        let supported_types = manager.get_supported_types().await;
        assert!(supported_types.contains(&"health_check".to_string()));
        assert!(supported_types.contains(&"custom_SlowTest".to_string()));
    }

    #[test]
    async fn test_error_handling_flow() {
        let manager = HandlerManager::new();
        
        // 注册错误处理器
        let error_handler = Arc::new(ErrorHandler::new(true));
        manager.register_handler(error_handler).await.unwrap();
        
        // 测试错误消息
        let error_message = NativeMessage::new(
            MessageType::Custom("ErrorTest".to_string()),
            "error-test".to_string(),
            serde_json::json!({}),
            "test-source".to_string(),
        );
        
        let result = manager.dispatch_message(error_message).await;
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), NativeMessagingError::HandlerError(_)));
    }

    #[test]
    async fn test_middleware_chain_execution() {
        let manager = HandlerManager::new();
        
        // 注册处理器
        let handler = Arc::new(SlowHandler::new("TestHandler".to_string(), Duration::from_millis(5)));
        manager.register_handler(handler).await.unwrap();
        
        // 添加多个中间件
        manager.add_middleware(Box::new(middleware::LoggingMiddleware::new())).await;
        manager.add_middleware(Box::new(middleware::PerformanceMiddleware::new())).await;
        manager.add_middleware(Box::new(middleware::SecurityMiddleware::new())).await;
        
        // 测试消息处理
        let test_message = NativeMessage::new(
            MessageType::Custom("SlowTest".to_string()),
            "middleware-test".to_string(),
            serde_json::json!({}),
            "test-source".to_string(),
        );
        
        let response = manager.dispatch_message(test_message).await.unwrap();
        assert_eq!(response.message.request_id, "middleware-test");
    }

    #[test]
    async fn test_concurrent_message_processing() {
        let manager = Arc::new(HandlerManager::new());
        
        // 注册处理器
        let handler = Arc::new(SlowHandler::new("ConcurrentHandler".to_string(), Duration::from_millis(20)));
        manager.register_handler(handler.clone()).await.unwrap();
        
        // 并发发送多个消息
        let mut handles = Vec::new();
        for i in 0..10 {
            let manager_clone = manager.clone();
            let handle = tokio::spawn(async move {
                let message = NativeMessage::new(
                    MessageType::Custom("SlowTest".to_string()),
                    format!("concurrent-test-{}", i),
                    serde_json::json!({ "index": i }),
                    "test-source".to_string(),
                );
                
                manager_clone.dispatch_message(message).await
            });
            handles.push(handle);
        }
        
        // 等待所有消息处理完成
        let mut success_count = 0;
        for handle in handles {
            if handle.await.unwrap().is_ok() {
                success_count += 1;
            }
        }
        
        assert_eq!(success_count, 10);
        assert_eq!(handler.get_call_count(), 10);
    }

    #[test]
    async fn test_performance_metrics_collection() {
        let metrics = Arc::new(PerformanceMetrics::new());
        let manager = HandlerManager::new();
        
        // 注册处理器
        let handler = Arc::new(SlowHandler::new("MetricsHandler".to_string(), Duration::from_millis(15)));
        manager.register_handler(handler).await.unwrap();
        
        // 添加计时中间件
        manager.add_middleware(Box::new(TimingMiddleware::new(metrics.clone()))).await;
        
        // 处理多个消息
        for i in 0..5 {
            let message = NativeMessage::new(
                MessageType::Custom("SlowTest".to_string()),
                format!("metrics-test-{}", i),
                serde_json::json!({}),
                "test-source".to_string(),
            );
            
            let _ = manager.dispatch_message(message).await.unwrap();
        }
        
        // 检查性能指标
        let snapshot = metrics.get_snapshot();
        assert!(snapshot.total_messages >= 5);
        assert!(snapshot.avg_response_time_ms > 0.0);
        assert_eq!(snapshot.success_rate, 1.0);
    }

    #[test]
    async fn test_registry_status_reporting() {
        let manager = HandlerManager::new();
        
        // 注册内置处理器
        let _ = manager.register_builtin_handlers().await.unwrap();
        
        // 注册自定义处理器
        let custom_handler = Arc::new(SlowHandler::new("CustomHandler".to_string(), Duration::from_millis(1)));
        manager.register_handler(custom_handler).await.unwrap();
        
        // 获取注册表状态
        let status = manager.get_registry_status().await;
        assert!(status.total_handlers > 0);
        assert!(status.total_message_types > 0);
        
        // 处理一些消息以更新统计
        let message = NativeMessage::new(
            MessageType::HealthCheck,
            "status-test".to_string(),
            serde_json::json!({}),
            "test-source".to_string(),
        );
        
        let _ = manager.dispatch_message(message).await.unwrap();
        
        // 再次获取状态，应该有调用统计
        let updated_status = manager.get_registry_status().await;
        assert!(updated_status.total_calls >= 1);
        assert!(updated_status.total_successes >= 1);
    }

    #[test]
    async fn test_message_routing_accuracy() {
        let manager = HandlerManager::new();
        
        // 注册多个处理器
        let ping_handler = Arc::new(builtin::PingHandler::new());
        let health_handler = Arc::new(builtin::HealthCheckHandler::new());
        let version_handler = Arc::new(builtin::VersionHandler::new());
        
        manager.register_handler(ping_handler).await.unwrap();
        manager.register_handler(health_handler).await.unwrap();
        manager.register_handler(version_handler).await.unwrap();
        
        // 测试不同类型的消息路由
        let test_cases = vec![
            (MessageType::Ping, "Ping"),
            (MessageType::HealthCheck, "HealthCheck"),
            (MessageType::Version, "Version"),
        ];
        
        for (message_type, expected_type) in test_cases {
            let message = NativeMessage::new(
                message_type,
                format!("routing-test-{}", expected_type),
                serde_json::json!({}),
                "test-source".to_string(),
            );
            
            let response = manager.dispatch_message(message).await.unwrap();
            assert_eq!(response.message.request_id, format!("routing-test-{}", expected_type));
            
            // 验证响应内容包含预期的处理器信息
            assert!(response.message.payload.is_object());
        }
    }

    #[test]
    async fn test_unsupported_message_type_handling() {
        let manager = HandlerManager::new();
        
        // 测试不支持的消息类型
        let unsupported_message = NativeMessage::new(
            MessageType::Custom("UnsupportedType".to_string()),
            "unsupported-test".to_string(),
            serde_json::json!({}),
            "test-source".to_string(),
        );
        
        let result = manager.dispatch_message(unsupported_message).await;
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), NativeMessagingError::HandlerError(_)));
    }
} 