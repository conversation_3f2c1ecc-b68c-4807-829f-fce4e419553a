//! Native Messaging 密码管理处理器
//!
//! 提供密码生成、验证和安全操作功能

use crate::native_messaging::{
    error::{NativeMessagingError, Result},
    handlers::MessageHandler,
    protocol::message::{NativeMessage, OutgoingMessage},
};
use async_trait::async_trait;
use rand::Rng;
use serde_json;
use std::collections::HashSet;

/// 密码生成选项
#[derive(Debug, Clone)]
pub struct PasswordGenerationOptions {
    /// 密码长度
    pub length: usize,
    /// 包含大写字母
    pub include_uppercase: bool,
    /// 包含小写字母
    pub include_lowercase: bool,
    /// 包含数字
    pub include_numbers: bool,
    /// 包含特殊字符
    pub include_symbols: bool,
    /// 排除相似字符
    pub exclude_ambiguous: bool,
    /// 自定义字符集
    pub custom_charset: Option<String>,
}

impl Default for PasswordGenerationOptions {
    fn default() -> Self {
        Self {
            length: 16,
            include_uppercase: true,
            include_lowercase: true,
            include_numbers: true,
            include_symbols: true,
            exclude_ambiguous: false,
            custom_charset: None,
        }
    }
}

/// 密码强度级别
#[derive(Debug, <PERSON><PERSON>, PartialEq)]
pub enum PasswordStrength {
    VeryWeak,
    Weak,
    Fair,
    Good,
    Strong,
    VeryStrong,
}

/// 密码分析结果
#[derive(Debug, Clone)]
pub struct PasswordAnalysis {
    /// 密码强度
    pub strength: PasswordStrength,
    /// 分数（0-100）
    pub score: u32,
    /// 评估详情
    pub details: PasswordDetails,
    /// 建议
    pub suggestions: Vec<String>,
}

/// 密码详细信息
#[derive(Debug, Clone)]
pub struct PasswordDetails {
    /// 长度
    pub length: usize,
    /// 包含大写字母
    pub has_uppercase: bool,
    /// 包含小写字母
    pub has_lowercase: bool,
    /// 包含数字
    pub has_numbers: bool,
    /// 包含特殊字符
    pub has_symbols: bool,
    /// 字符种类数
    pub character_types: u32,
    /// 唯一字符数
    pub unique_characters: usize,
}

/// 密码管理处理器
///
/// 处理密码生成、验证、强度分析等操作
pub struct PasswordHandler;

impl PasswordHandler {
    /// 创建新的密码管理处理器
    pub fn new() -> Self {
        Self
    }

    /// 生成密码
    fn generate_password(&self, options: &PasswordGenerationOptions) -> String {
        let mut charset = String::new();

        // 构建字符集
        if let Some(custom) = &options.custom_charset {
            charset = custom.clone();
        } else {
            if options.include_lowercase {
                charset.push_str("abcdefghijklmnopqrstuvwxyz");
            }
            if options.include_uppercase {
                charset.push_str("ABCDEFGHIJKLMNOPQRSTUVWXYZ");
            }
            if options.include_numbers {
                charset.push_str("0123456789");
            }
            if options.include_symbols {
                charset.push_str("!@#$%^&*()_+-=[]{}|;:,.<>?");
            }
        }

        // 排除相似字符
        if options.exclude_ambiguous {
            let ambiguous = "0O1lI";
            charset = charset.chars()
                .filter(|c| !ambiguous.contains(*c))
                .collect();
        }

        if charset.is_empty() {
            charset = "abcdefghijklmnopqrstuvwxyz".to_string(); // 默认字符集
        }

        // 生成密码
        let charset_chars: Vec<char> = charset.chars().collect();
        let mut rng = rand::thread_rng();
        
        (0..options.length)
            .map(|_| charset_chars[rng.gen_range(0..charset_chars.len())])
            .collect()
    }

    /// 分析密码强度
    fn analyze_password(&self, password: &str) -> PasswordAnalysis {
        let details = self.get_password_details(password);
        let score = self.calculate_password_score(&details);
        let strength = self.determine_password_strength(score);
        let suggestions = self.generate_suggestions(&details);

        PasswordAnalysis {
            strength,
            score,
            details,
            suggestions,
        }
    }

    /// 获取密码详细信息
    fn get_password_details(&self, password: &str) -> PasswordDetails {
        let chars: Vec<char> = password.chars().collect();
        let unique_chars: HashSet<char> = chars.iter().cloned().collect();

        let has_uppercase = chars.iter().any(|c| c.is_uppercase());
        let has_lowercase = chars.iter().any(|c| c.is_lowercase());
        let has_numbers = chars.iter().any(|c| c.is_numeric());
        let has_symbols = chars.iter().any(|c| !c.is_alphanumeric());

        let character_types = [has_uppercase, has_lowercase, has_numbers, has_symbols]
            .iter()
            .filter(|&&x| x)
            .count() as u32;

        PasswordDetails {
            length: password.len(),
            has_uppercase,
            has_lowercase,
            has_numbers,
            has_symbols,
            character_types,
            unique_characters: unique_chars.len(),
        }
    }

    /// 计算密码分数
    fn calculate_password_score(&self, details: &PasswordDetails) -> u32 {
        let mut score = 0u32;

        // 长度分数
        score += match details.length {
            0..=4 => 0,
            5..=7 => 10,
            8..=11 => 20,
            12..=15 => 30,
            _ => 40,
        };

        // 字符类型分数
        score += details.character_types * 10;

        // 唯一字符分数
        let uniqueness_ratio = details.unique_characters as f32 / details.length as f32;
        score += (uniqueness_ratio * 20.0) as u32;

        // 复杂度奖励
        if details.character_types >= 3 && details.length >= 12 {
            score += 10;
        }

        if details.character_types == 4 && details.length >= 16 {
            score += 10;
        }

        score.min(100)
    }

    /// 确定密码强度
    fn determine_password_strength(&self, score: u32) -> PasswordStrength {
        match score {
            0..=20 => PasswordStrength::VeryWeak,
            21..=40 => PasswordStrength::Weak,
            41..=60 => PasswordStrength::Fair,
            61..=80 => PasswordStrength::Good,
            81..=95 => PasswordStrength::Strong,
            _ => PasswordStrength::VeryStrong,
        }
    }

    /// 生成建议
    fn generate_suggestions(&self, details: &PasswordDetails) -> Vec<String> {
        let mut suggestions = Vec::new();

        if details.length < 8 {
            suggestions.push("增加密码长度至少8个字符".to_string());
        }

        if !details.has_uppercase {
            suggestions.push("添加大写字母".to_string());
        }

        if !details.has_lowercase {
            suggestions.push("添加小写字母".to_string());
        }

        if !details.has_numbers {
            suggestions.push("添加数字".to_string());
        }

        if !details.has_symbols {
            suggestions.push("添加特殊字符".to_string());
        }

        if (details.unique_characters as f32 / details.length as f32) < 0.7 {
            suggestions.push("减少重复字符".to_string());
        }

        if suggestions.is_empty() {
            suggestions.push("密码已经很强了！".to_string());
        }

        suggestions
    }

    /// 处理密码生成请求
    async fn handle_generate(&self, message: &NativeMessage) -> Result<OutgoingMessage> {
        let mut options = PasswordGenerationOptions::default();

        // 解析选项
        if let Some(length) = message.payload.get("length").and_then(|v| v.as_u64()) {
            options.length = length as usize;
        }

        if let Some(uppercase) = message.payload.get("include_uppercase").and_then(|v| v.as_bool()) {
            options.include_uppercase = uppercase;
        }

        if let Some(lowercase) = message.payload.get("include_lowercase").and_then(|v| v.as_bool()) {
            options.include_lowercase = lowercase;
        }

        if let Some(numbers) = message.payload.get("include_numbers").and_then(|v| v.as_bool()) {
            options.include_numbers = numbers;
        }

        if let Some(symbols) = message.payload.get("include_symbols").and_then(|v| v.as_bool()) {
            options.include_symbols = symbols;
        }

        if let Some(exclude_ambiguous) = message.payload.get("exclude_ambiguous").and_then(|v| v.as_bool()) {
            options.exclude_ambiguous = exclude_ambiguous;
        }

        let password = self.generate_password(&options);
        let analysis = self.analyze_password(&password);

        let response = serde_json::json!({
            "type": "password_generated",
            "password": password,
            "analysis": {
                "strength": format!("{:?}", analysis.strength),
                "score": analysis.score,
                "length": analysis.details.length,
                "character_types": analysis.details.character_types,
                "suggestions": analysis.suggestions,
            }
        });

        Ok(OutgoingMessage::success(message.request_id.clone(), response))
    }

    /// 处理密码分析请求
    async fn handle_analyze(&self, message: &NativeMessage) -> Result<OutgoingMessage> {
        let password = message.payload.get("password")
            .and_then(|v| v.as_str())
            .ok_or_else(|| NativeMessagingError::HandlerError("缺少密码参数".to_string()))?;

        let analysis = self.analyze_password(password);

        let response = serde_json::json!({
            "type": "password_analysis",
            "analysis": {
                "strength": format!("{:?}", analysis.strength),
                "score": analysis.score,
                "details": {
                    "length": analysis.details.length,
                    "has_uppercase": analysis.details.has_uppercase,
                    "has_lowercase": analysis.details.has_lowercase,
                    "has_numbers": analysis.details.has_numbers,
                    "has_symbols": analysis.details.has_symbols,
                    "character_types": analysis.details.character_types,
                    "unique_characters": analysis.details.unique_characters,
                },
                "suggestions": analysis.suggestions,
            }
        });

        Ok(OutgoingMessage::success(message.request_id.clone(), response))
    }
}

#[async_trait]
impl MessageHandler for PasswordHandler {
    async fn handle(&self, message: NativeMessage) -> Result<OutgoingMessage> {
        let action = message.payload.get("action")
            .and_then(|v| v.as_str())
            .unwrap_or("generate");

        match action {
            "generate" => self.handle_generate(&message).await,
            "analyze" => self.handle_analyze(&message).await,
            _ => Err(NativeMessagingError::HandlerError(
                format!("不支持的密码操作: {}", action)
            )),
        }
    }

    fn message_types(&self) -> Vec<String> {
        use crate::native_messaging::protocol::message::MessageType;
        vec![MessageType::Password.as_string()]
    }

    fn name(&self) -> &str {
        "PasswordHandler"
    }
}

impl Default for PasswordHandler {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::native_messaging::protocol::message::MessageType;

    #[tokio::test]
    async fn test_password_generation() {
        let handler = PasswordHandler::new();
        let message = NativeMessage::new(
            MessageType::Password,
            "test-generate".to_string(),
            serde_json::json!({
                "action": "generate",
                "length": 12,
                "include_uppercase": true,
                "include_lowercase": true,
                "include_numbers": true,
                "include_symbols": false
            }),
            "test-source".to_string(),
        );

        let result = handler.handle(message).await;
        assert!(result.is_ok());

        let response = result.unwrap();
        let payload = &response.message.payload;
        assert_eq!(payload["type"], "password_generated");
        
        let password = payload["password"].as_str().unwrap();
        assert_eq!(password.len(), 12);
        
        let analysis = &payload["analysis"];
        assert!(analysis["score"].is_number());
        assert!(analysis["suggestions"].is_array());
    }

    #[tokio::test]
    async fn test_password_analysis() {
        let handler = PasswordHandler::new();
        let message = NativeMessage::new(
            MessageType::Password,
            "test-analyze".to_string(),
            serde_json::json!({
                "action": "analyze",
                "password": "MyStrongPassword123!"
            }),
            "test-source".to_string(),
        );

        let result = handler.handle(message).await;
        assert!(result.is_ok());

        let response = result.unwrap();
        let payload = &response.message.payload;
        assert_eq!(payload["type"], "password_analysis");
        
        let analysis = &payload["analysis"];
        assert!(analysis["score"].is_number());
        assert!(analysis["details"].is_object());
        assert!(analysis["suggestions"].is_array());
    }

    #[test]
    fn test_password_strength_calculation() {
        let handler = PasswordHandler::new();
        
        // 测试弱密码
        let weak_analysis = handler.analyze_password("123");
        assert!(matches!(weak_analysis.strength, PasswordStrength::VeryWeak | PasswordStrength::Weak));
        
        // 测试强密码
        let strong_analysis = handler.analyze_password("MyVeryStrongPassword123!");
        assert!(matches!(strong_analysis.strength, PasswordStrength::Strong | PasswordStrength::VeryStrong));
    }

    #[test]
    fn test_password_generation_options() {
        let handler = PasswordHandler::new();
        let options = PasswordGenerationOptions {
            length: 10,
            include_uppercase: true,
            include_lowercase: true,
            include_numbers: false,
            include_symbols: false,
            exclude_ambiguous: false,
            custom_charset: None,
        };

        let password = handler.generate_password(&options);
        assert_eq!(password.len(), 10);
        assert!(password.chars().any(|c| c.is_uppercase()));
        assert!(password.chars().any(|c| c.is_lowercase()));
        assert!(!password.chars().any(|c| c.is_numeric()));
    }

    #[test]
    fn test_password_handler_interface() {
        use crate::native_messaging::protocol::message::MessageType;
        let handler = PasswordHandler::new();
        assert_eq!(handler.name(), "PasswordHandler");
        assert_eq!(handler.message_types(), vec![MessageType::Password.as_string()]);
    }
} 