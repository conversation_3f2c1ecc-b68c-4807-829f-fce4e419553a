//! Native Messaging 健康检查处理器
//!
//! 提供系统健康状态检查功能

use crate::native_messaging::{
    error::Result,
    handlers::MessageHandler,
    protocol::message::{NativeMessage, OutgoingMessage},
};
use async_trait::async_trait;
use serde_json;

/// 健康检查处理器
///
/// 处理系统健康状态检查请求
pub struct HealthCheckHandler;

impl HealthCheckHandler {
    /// 创建新的健康检查处理器
    pub fn new() -> Self {
        Self
    }
}

#[async_trait]
impl MessageHandler for HealthCheckHandler {
    async fn handle(&self, message: NativeMessage) -> Result<OutgoingMessage> {
        // 返回健康状态信息
        let health_info = serde_json::json!({
            "status": "healthy",
            "timestamp": chrono::Utc::now().to_rfc3339(),
            "version": crate::native_messaging::VERSION,
            "uptime": "running"
        });

        Ok(OutgoingMessage::success(message.request_id, health_info))
    }

    fn message_types(&self) -> Vec<String> {
        use crate::native_messaging::protocol::message::MessageType;
        vec![MessageType::HealthCheck.as_string()]
    }

    fn name(&self) -> &str {
        "HealthCheckHandler"
    }
}

impl Default for HealthCheckHandler {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::native_messaging::protocol::message::MessageType;

    #[tokio::test]
    async fn test_health_check_handler() {
        let handler = HealthCheckHandler::new();
        let message = NativeMessage::new(
            MessageType::HealthCheck,
            "test-health-check".to_string(),
            serde_json::json!({}),
            "test-source".to_string(),
        );

        let result = handler.handle(message).await;
        assert!(result.is_ok());

        let response = result.unwrap();
        assert_eq!(response.message.request_id, "test-health-check");
    }
}
