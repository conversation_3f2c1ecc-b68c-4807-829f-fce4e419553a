//! Native Messaging Echo处理器
//!
//! 提供消息回声功能，用于调试和测试

use crate::native_messaging::{
    error::Result,
    handlers::MessageHandler,
    protocol::message::{NativeMessage, OutgoingMessage},
};
use async_trait::async_trait;
use serde_json;
use tauri_plugin_os::hostname;
use std::time::Instant;

/// Echo处理器
///
/// 简单地回显收到的消息，主要用于调试和测试目的
pub struct EchoHandler {
    /// 处理器创建时间
    created_at: Instant,
    /// 处理的消息计数
    message_count: std::sync::atomic::AtomicU64,
}

impl EchoHandler {
    /// 创建新的Echo处理器
    pub fn new() -> Self {
        Self {
            created_at: Instant::now(),
            message_count: std::sync::atomic::AtomicU64::new(0),
        }
    }

    /// 获取处理的消息数量
    pub fn message_count(&self) -> u64 {
        self.message_count.load(std::sync::atomic::Ordering::Relaxed)
    }

    /// 获取运行时间
    pub fn uptime(&self) -> std::time::Duration {
        self.created_at.elapsed()
    }
}

#[async_trait]
impl MessageHandler for EchoHandler {
    async fn handle(&self, message: NativeMessage) -> Result<OutgoingMessage> {
        let start_time = Instant::now();
        
        // 增加消息计数
        self.message_count.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
        
        // 构建回显响应
        let echo_response = serde_json::json!({
            "type": "echo",
            "original_message": {
                "message_type": message.message_type,
                "payload": message.payload,
                "source": message.source,
                "timestamp": message.timestamp,
                "metadata": message.metadata,
                "priority": message.priority,
            },
            "echo_info": {
                "processor": "EchoHandler",
                "processed_at": chrono::Utc::now().to_rfc3339(),
                "processing_time_ms": start_time.elapsed().as_millis(),
                "message_count": self.message_count(),
                "handler_uptime_ms": self.uptime().as_millis(),
            },
            "system_info": {
                "hostname": hostname(),
                "process_id": std::process::id(),
                "thread_id": format!("{:?}", std::thread::current().id()),
            }
        });

        Ok(OutgoingMessage::success(message.request_id, echo_response))
    }

    fn message_types(&self) -> Vec<String> {
        use crate::native_messaging::protocol::message::MessageType;
        vec![MessageType::Test.as_string()]
    }

    fn name(&self) -> &str {
        "EchoHandler"
    }
}

impl Default for EchoHandler {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::native_messaging::protocol::message::MessageType;

    #[tokio::test]
    async fn test_echo_handler() {
        let handler = EchoHandler::new();
        
        let test_payload = serde_json::json!({
            "test_data": "hello world",
            "number": 42,
            "boolean": true
        });
        
        let message = NativeMessage::new(
            MessageType::Test,
            "test-echo-001".to_string(),
            test_payload.clone(),
            "test-extension".to_string(),
        );

        let result = handler.handle(message).await;
        assert!(result.is_ok());

        let response = result.unwrap();
        assert_eq!(response.message.request_id, "test-echo-001");
        
        let payload = &response.message.payload;
        assert_eq!(payload["type"], "echo");
        
        // 验证原始消息被正确回显
        let original = &payload["original_message"];
        assert_eq!(original["payload"], test_payload);
        assert_eq!(original["source"], "test-extension");
        
        // 验证回显信息
        let echo_info = &payload["echo_info"];
        assert_eq!(echo_info["processor"], "EchoHandler");
        assert!(echo_info["processing_time_ms"].is_number());
        assert_eq!(echo_info["message_count"], 1);
        
        // 验证系统信息
        let system_info = &payload["system_info"];
        assert!(system_info["hostname"].is_string());
        assert!(system_info["process_id"].is_number());
        assert!(system_info["thread_id"].is_string());
    }

    #[tokio::test]
    async fn test_echo_handler_message_count() {
        let handler = EchoHandler::new();
        assert_eq!(handler.message_count(), 0);

        // 处理第一个消息
        let message1 = NativeMessage::new(
            MessageType::Test,
            "test-001".to_string(),
            serde_json::json!({"msg": 1}),
            "test".to_string(),
        );
        handler.handle(message1).await.unwrap();
        assert_eq!(handler.message_count(), 1);

        // 处理第二个消息
        let message2 = NativeMessage::new(
            MessageType::Test,
            "test-002".to_string(),
            serde_json::json!({"msg": 2}),
            "test".to_string(),
        );
        handler.handle(message2).await.unwrap();
        assert_eq!(handler.message_count(), 2);
    }

    #[test]
    fn test_echo_handler_interface() {
        use crate::native_messaging::protocol::message::MessageType;
        let handler = EchoHandler::new();
        assert_eq!(handler.name(), "EchoHandler");
        
        let types = handler.message_types();
        assert_eq!(types.len(), 1);
        assert!(types.contains(&MessageType::Test.as_string()));
    }

    #[test]
    fn test_echo_handler_uptime() {
        let handler = EchoHandler::new();
        std::thread::sleep(std::time::Duration::from_millis(1));
        assert!(handler.uptime().as_millis() >= 1);
    }
} 