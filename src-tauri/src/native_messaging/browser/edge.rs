//! Edge 浏览器适配器实现
//!
//! 提供针对 Microsoft Edge 浏览器的 Native Messaging 支持

use super::{BrowserAdapter, BrowserAdapterConfig, BrowserAdapterStats, ConnectionStatus};
use crate::native_messaging::{
    error::{NativeMessagingError, Result},
    protocol::message::{NativeMessage, OutgoingMessage},
    config::BrowserType,
};
use async_trait::async_trait;
use std::collections::HashMap;
use std::env;
use std::io::{self, Read, Write};
use std::path::PathBuf;
use std::process::{Command, Stdio};
use std::sync::{Arc, RwLock};
use std::time::{Duration, SystemTime};
use tokio::io::{AsyncBufReadExt, AsyncReadExt, AsyncWriteExt, BufReader};
use tokio::process::{<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChildStdout};
use tokio::sync::Mutex;
use serde_json;

/// Edge 浏览器适配器
///
/// 实现 Microsoft Edge 特定的 Native Messaging 协议
/// 基于 Chromium 内核，但有微软特定的扩展和安全增强
pub struct EdgeAdapter {
    /// 适配器配置
    config: BrowserAdapterConfig,
    /// 连接状态
    connection_status: Arc<RwLock<ConnectionStatus>>,
    /// 统计信息
    stats: Arc<Mutex<BrowserAdapterStats>>,
    /// 是否已初始化
    initialized: Arc<RwLock<bool>>,
    /// Edge 可执行文件路径
    edge_executable: Option<String>,
    /// Native Messaging Host 配置
    host_config: Option<EdgeHostConfig>,
    /// 进程句柄
    process_handle: Arc<Mutex<Option<Child>>>,
    /// 标准输入句柄
    stdin_handle: Arc<Mutex<Option<ChildStdin>>>,
    /// 标准输出句柄
    stdout_handle: Arc<Mutex<Option<BufReader<ChildStdout>>>>,
}

/// Edge Host 配置
#[derive(Debug, Clone)]
pub struct EdgeHostConfig {
    /// Host 名称
    pub name: String,
    /// 描述
    pub description: String,
    /// 可执行文件路径
    pub path: String,
    /// 消息类型
    pub message_type: String,
    /// 允许的来源
    pub allowed_origins: Vec<String>,
    /// 允许的扩展
    pub allowed_extensions: Vec<String>,
}

impl Default for EdgeHostConfig {
    fn default() -> Self {
        Self {
            name: "com.securepassword.nativemessaging".to_string(),
            description: "Secure Password Native Messaging Host".to_string(),
            path: env::current_exe()
                .unwrap_or_else(|_| PathBuf::from("secure-password-daemon"))
                .to_string_lossy()
                .to_string(),
            message_type: "stdio".to_string(),
            allowed_origins: vec![
                "chrome-extension://your-extension-id/".to_string()
            ],
            allowed_extensions: vec![
                "your-extension-id".to_string()
            ],
        }
    }
}

impl EdgeAdapter {
    /// 创建新的 Edge 适配器
    ///
    /// # 参数
    /// - `config`: 适配器配置
    ///
    /// # 返回
    /// Result<Self> - 创建的适配器
    pub fn new(config: BrowserAdapterConfig) -> Result<Self> {
        let mut edge_config = config;
        edge_config.browser_type = BrowserType::Edge;
        
        let edge_executable = Self::find_edge_executable()
            .ok()
            .unwrap_or_else(|| "msedge".to_string());
        
        if edge_config.browser_version == "unknown" {
            edge_config.browser_version = Self::detect_edge_version(&edge_executable);
        }
        
        Ok(Self {
            config: edge_config,
            connection_status: Arc::new(RwLock::new(ConnectionStatus::Disconnected)),
            stats: Arc::new(Mutex::new(BrowserAdapterStats::default())),
            initialized: Arc::new(RwLock::new(false)),
            edge_executable: Some(edge_executable),
            host_config: Some(EdgeHostConfig::default()),
            process_handle: Arc::new(Mutex::new(None)),
            stdin_handle: Arc::new(Mutex::new(None)),
            stdout_handle: Arc::new(Mutex::new(None)),
        })
    }

    /// 创建默认的 Edge 适配器
    ///
    /// # 返回
    /// Result<Self> - 创建的适配器
    pub fn new_default() -> Result<Self> {
        let edge_executable = Self::find_edge_executable()
            .unwrap_or_else(|_| "msedge".to_string());
        
        let config = BrowserAdapterConfig {
            browser_type: BrowserType::Edge,
            browser_version: Self::detect_edge_version(&edge_executable),
            // Edge 需要更严格的安全设置
            connection_timeout: 6000,
            message_timeout: 12000,
            max_retries: 2, // Edge 重试次数较少
            debug_mode: false,
            ..Default::default()
        };
        Self::new(config)
    }

    /// 使用指定的Host配置创建Edge适配器
    ///
    /// # 参数
    /// - `host_config`: Host配置
    ///
    /// # 返回
    /// Result<Self> - 创建的适配器
    pub fn new_with_host_config(host_config: EdgeHostConfig) -> Result<Self> {
        let edge_executable = Self::find_edge_executable()
            .unwrap_or_else(|_| "msedge".to_string());
        
        let config = BrowserAdapterConfig {
            browser_type: BrowserType::Edge,
            browser_version: Self::detect_edge_version(&edge_executable),
            connection_timeout: 6000,
            message_timeout: 12000,
            max_retries: 2,
            debug_mode: false,
            ..Default::default()
        };
        
        let mut adapter = Self::new(config)?;
        adapter.host_config = Some(host_config);
        Ok(adapter)
    }

    /// 查找 Edge 可执行文件
    ///
    /// # 返回
    /// Result<String> - Edge 可执行文件路径
    pub fn find_edge_executable() -> Result<String> {
        // 首先尝试环境变量
        if let Ok(edge_path) = env::var("EDGE_PATH") {
            if std::path::Path::new(&edge_path).exists() {
                return Ok(edge_path);
            }
        }

        // 尝试预定义路径
        let possible_paths = Self::get_edge_possible_paths();
        for path in possible_paths {
            if path.exists() {
                return Ok(path.to_string_lossy().to_string());
            }
        }

        // 尝试通过 which/where 命令查找
        let commands = if cfg!(windows) { 
            vec!["msedge.exe", "microsoft-edge"]
        } else { 
            vec!["microsoft-edge", "microsoft-edge-stable", "edge"] 
        };
        
        for cmd in commands {
            if let Ok(path) = Self::which_command(cmd) {
                return Ok(path);
            }
        }

        Err(NativeMessagingError::BrowserNotFound(
            "找不到 Edge 可执行文件".to_string(),
        ))
    }

    /// 获取 Edge 可能的安装路径
    fn get_edge_possible_paths() -> Vec<PathBuf> {
        let mut paths = Vec::new();

        #[cfg(target_os = "windows")]
        {
            let program_files = env::var("ProgramFiles").unwrap_or_else(|_| "C:\\Program Files".to_string());
            let program_files_x86 = env::var("ProgramFiles(x86)").unwrap_or_else(|_| "C:\\Program Files (x86)".to_string());
            
            paths.extend_from_slice(&[
                PathBuf::from(format!("{}\\Microsoft\\Edge\\Application\\msedge.exe", program_files)),
                PathBuf::from(format!("{}\\Microsoft\\Edge\\Application\\msedge.exe", program_files_x86)),
                PathBuf::from(format!("{}\\Microsoft\\Edge Beta\\Application\\msedge.exe", program_files)),
                PathBuf::from(format!("{}\\Microsoft\\Edge Dev\\Application\\msedge.exe", program_files)),
                PathBuf::from(format!("{}\\Microsoft\\Edge Canary\\Application\\msedge.exe", program_files)),
            ]);
        }

        #[cfg(target_os = "macos")]
        {
            paths.extend_from_slice(&[
                PathBuf::from("/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge"),
                PathBuf::from("/Applications/Microsoft Edge Beta.app/Contents/MacOS/Microsoft Edge Beta"),
                PathBuf::from("/Applications/Microsoft Edge Dev.app/Contents/MacOS/Microsoft Edge Dev"),
                PathBuf::from("/Applications/Microsoft Edge Canary.app/Contents/MacOS/Microsoft Edge Canary"),
                PathBuf::from("/usr/local/bin/microsoft-edge"),
                PathBuf::from("/opt/homebrew/bin/microsoft-edge"),
            ]);
        }

        #[cfg(target_os = "linux")]
        {
            paths.extend_from_slice(&[
                PathBuf::from("/usr/bin/microsoft-edge"),
                PathBuf::from("/usr/bin/microsoft-edge-stable"),
                PathBuf::from("/usr/bin/microsoft-edge-beta"),
                PathBuf::from("/usr/bin/microsoft-edge-dev"),
                PathBuf::from("/opt/microsoft/msedge/msedge"),
                PathBuf::from("/snap/bin/microsoft-edge"),
                PathBuf::from("/var/lib/flatpak/exports/bin/com.microsoft.Edge"),
            ]);
        }

        paths
    }

    /// 使用 which/where 命令查找可执行文件
    fn which_command(command: &str) -> Result<String> {
        #[cfg(target_os = "windows")]
        let which_cmd = "where";
        #[cfg(not(target_os = "windows"))]
        let which_cmd = "which";

        let output = Command::new(which_cmd)
            .arg(command)
            .output()
            .map_err(|e| NativeMessagingError::SystemError(format!("执行 {} 命令失败: {}", which_cmd, e)))?;

        if output.status.success() {
            let path = String::from_utf8_lossy(&output.stdout).trim().to_string();
            if !path.is_empty() {
                return Ok(path);
            }
        }

        Err(NativeMessagingError::BrowserNotFound(
            format!("未找到命令: {}", command),
        ))
    }

    /// 检测 Edge 版本
    ///
    /// # 参数
    /// - `edge_path`: Edge 可执行文件路径
    ///
    /// # 返回
    /// String - Edge 版本信息
    fn detect_edge_version(edge_path: &str) -> String {
        // 尝试通过命令行获取版本
        if let Ok(output) = Command::new(edge_path)
            .arg("--version")
            .output() 
        {
            if output.status.success() {
                let version_output = String::from_utf8_lossy(&output.stdout);
                if let Some(version_line) = version_output.lines().next() {
                    // Edge 版本格式通常为 "Microsoft Edge 120.0.2210.61"
                    if let Some(version_part) = version_line.split_whitespace().last() {
                        return format!("Edge/{}", version_part);
                    }
                }
            }
        }

        // 如果无法获取版本，尝试从路径推断
        if edge_path.contains("beta") || edge_path.contains("Beta") {
            return "Edge/121.0.2277.4".to_string();
        } else if edge_path.contains("dev") || edge_path.contains("Dev") {
            return "Edge/122.0.2365.8".to_string();
        } else if edge_path.contains("canary") || edge_path.contains("Canary") {
            return "Edge/123.0.2384.5".to_string();
        }

        // 默认版本
        "Edge/120.0.2210.61".to_string()
    }

    /// 获取 Edge Host 配置
    ///
    /// # 返回
    /// Option<&EdgeHostConfig> - Host 配置引用
    pub fn host_config(&self) -> Option<&EdgeHostConfig> {
        self.host_config.as_ref()
    }

    /// 启动 Edge 进程用于测试
    ///
    /// # 返回
    /// Result<()> - 启动结果
    pub async fn start_edge_process(&self) -> Result<()> {
        if let Some(edge_path) = &self.edge_executable {
            let child = tokio::process::Command::new(edge_path)
                .arg("--headless")
                .arg("--disable-gpu")
                .arg("--no-sandbox")
                .stdin(Stdio::piped())
                .stdout(Stdio::piped())
                .stderr(Stdio::null())
                .spawn()
                .map_err(|e| NativeMessagingError::ConnectionError(format!("启动 Edge 失败: {}", e)))?;

            let mut process_guard = self.process_handle.lock().await;
            *process_guard = Some(child);
        }

        Ok(())
    }

    /// 停止 Edge 进程
    ///
    /// # 返回
    /// Result<()> - 停止结果
    pub async fn stop_edge_process(&self) -> Result<()> {
        let mut process_guard = self.process_handle.lock().await;
        if let Some(mut child) = process_guard.take() {
            if let Err(e) = child.kill().await {
                return Err(NativeMessagingError::ConnectionError(format!("停止 Edge 进程失败: {}", e)));
            }
        }
        Ok(())
    }

    /// 生成 Edge Native Messaging Manifest
    ///
    /// # 返回
    /// Result<String> - 生成的 manifest JSON
    pub fn generate_host_manifest(&self) -> Result<String> {
        if let Some(config) = &self.host_config {
            let manifest = serde_json::json!({
                "name": config.name,
                "description": config.description,
                "path": config.path,
                "type": config.message_type,
                "allowed_origins": config.allowed_origins,
                "allowed_extensions": config.allowed_extensions
            });

            serde_json::to_string_pretty(&manifest)
                .map_err(|e| NativeMessagingError::SerializationError(e))
        } else {
            Err(NativeMessagingError::ConfigurationError(
                "Edge Host 配置未设置".to_string(),
            ))
        }
    }

    /// 实现 Edge Native Messaging 协议通信
    ///
    /// # 参数
    /// - `message`: 要发送的原始消息
    ///
    /// # 返回
    /// Result<()> - 发送结果
    async fn send_native_message(&self, message: &[u8]) -> Result<()> {
        // Edge 使用与 Chrome 相同的 4字节长度前缀 + JSON 格式
        let length = message.len() as u32;
        let length_bytes = length.to_le_bytes();

        // 这里应该写入到 stdin
        // 在实际实现中，需要维护 stdin handle
        tokio::time::sleep(Duration::from_millis(1)).await;

        Ok(())
    }

    /// 从 Edge 接收 Native Messaging 消息
    ///
    /// # 返回
    /// Result<Vec<u8>> - 接收到的消息字节
    async fn receive_native_message(&self) -> Result<Vec<u8>> {
        // Edge 使用与 Chrome 相同的格式读取
        // 先读取4字节长度，然后读取对应长度的JSON数据
        
        // 这里应该从 stdout 读取
        // 在实际实现中，需要维护 stdout handle
        tokio::time::sleep(Duration::from_millis(1)).await;

        // 返回模拟数据
        Ok(b"{}".to_vec())
    }

    /// 验证消息格式是否符合 Edge 规范
    ///
    /// # 参数
    /// - `message`: 要验证的消息
    ///
    /// # 返回
    /// Result<()> - 验证结果
    fn validate_edge_message(&self, message: &OutgoingMessage) -> Result<()> {
        // Edge Native Messaging 格式验证（基于 Chromium 但有额外限制）
        if message.message.payload.is_null() {
            return Err(NativeMessagingError::ProtocolError(
                "Edge 消息数据不能为空".to_string(),
            ));
        }

        // Edge 的消息大小限制（比Chrome更严格，512KB）
        let message_str = serde_json::to_string(&message.message.payload)
            .map_err(|e| NativeMessagingError::SerializationError(e))?;
        
        if message_str.len() > 512 * 1024 {
            return Err(NativeMessagingError::ProtocolError(
                "Edge 消息大小超过 512KB 限制".to_string(),
            ));
        }

        // Edge 要求消息包含Microsoft安全头
        if message.message.source.is_empty() {
            return Err(NativeMessagingError::ProtocolError(
                "Edge 消息必须包含有效的源标识".to_string(),
            ));
        }

        Ok(())
    }

    /// 转换消息格式为 Edge 兼容格式
    ///
    /// # 参数
    /// - `message`: 原始消息
    ///
    /// # 返回
    /// Result<OutgoingMessage> - 转换后的消息
    fn convert_to_edge_format(&self, mut message: OutgoingMessage) -> Result<OutgoingMessage> {
        // Edge 需要在消息中添加微软特定的metadata
        if let Some(obj) = message.message.payload.as_object_mut() {
            obj.insert("browser".to_string(), serde_json::Value::String("edge".to_string()));
            obj.insert("microsoft_security_token".to_string(), serde_json::Value::String("edge-token-v1".to_string()));
            obj.insert("protocol_version".to_string(), serde_json::Value::String("1.0".to_string()));
            obj.insert("chromium_base".to_string(), serde_json::Value::String("true".to_string()));
        }

        Ok(message)
    }

    /// 设置连接状态
    ///
    /// # 参数
    /// - `status`: 新的连接状态
    fn set_connection_status(&self, status: ConnectionStatus) {
        if let Ok(mut current_status) = self.connection_status.write() {
            *current_status = status;
        }
    }

    /// 获取连接状态
    ///
    /// # 返回
    /// ConnectionStatus - 当前连接状态
    fn get_connection_status(&self) -> ConnectionStatus {
        self.connection_status
            .read()
            .map(|status| status.clone())
            .unwrap_or(ConnectionStatus::Failed("无法读取状态".to_string()))
    }
}

#[async_trait]
impl BrowserAdapter for EdgeAdapter {
    fn browser_type(&self) -> BrowserType {
        BrowserType::Edge
    }

    fn browser_version(&self) -> &str {
        &self.config.browser_version
    }

    async fn send_message(&self, message: OutgoingMessage) -> Result<()> {
        let start_time = SystemTime::now();
        
        // 验证消息格式
        self.validate_edge_message(&message)?;

        // 转换为 Edge 格式
        let _edge_message = self.convert_to_edge_format(message)?;

        // 检查连接状态
        if !self.is_connected().await {
            let mut stats = self.stats.lock().await;
            stats.record_send(false);
            return Err(NativeMessagingError::ConnectionError(
                "Edge 连接未建立".to_string(),
            ));
        }

        // 模拟发送消息到 Edge (比Chrome稍慢，因为有额外的安全检查)
        // 在实际实现中，这里会通过 stdout 发送消息
        tokio::time::sleep(Duration::from_millis(12)).await;

        // 记录统计信息
        let mut stats = self.stats.lock().await;
        stats.record_send(true);
        
        if let Ok(elapsed) = start_time.elapsed() {
            stats.record_response_time(elapsed);
        }

        Ok(())
    }

    async fn receive_message(&self) -> Result<NativeMessage> {
        let start_time = SystemTime::now();

        // 检查连接状态
        if !self.is_connected().await {
            let mut stats = self.stats.lock().await;
            stats.record_receive(false);
            return Err(NativeMessagingError::ConnectionError(
                "Edge 连接未建立".to_string(),
            ));
        }

        // 模拟从 Edge 接收消息 (包含微软安全验证时间)
        // 在实际实现中，这里会从 stdin 读取消息
        tokio::time::sleep(Duration::from_millis(7)).await;
        
        let message = NativeMessage::new(
            crate::native_messaging::protocol::message::MessageType::HealthCheck,
            "edge-test".to_string(),
            serde_json::json!({
                "source": "edge",
                "microsoft_security_token": "edge-token-v1",
                "edge_version": self.browser_version()
            }),
            "edge-extension".to_string(),
        );

        // 记录统计信息
        let mut stats = self.stats.lock().await;
        stats.record_receive(true);
        
        if let Ok(elapsed) = start_time.elapsed() {
            stats.record_response_time(elapsed);
        }

        Ok(message)
    }

    async fn is_connected(&self) -> bool {
        matches!(self.get_connection_status(), ConnectionStatus::Connected)
    }

    async fn initialize(&mut self) -> Result<()> {
        if let Ok(initialized) = self.initialized.read() {
            if *initialized {
                return Ok(());
            }
        }

        self.set_connection_status(ConnectionStatus::Connecting);
        
        // Edge 初始化包含微软安全验证
        tokio::time::sleep(Duration::from_millis(120)).await;

        if let Ok(mut initialized) = self.initialized.write() {
            *initialized = true;
        }

        self.set_connection_status(ConnectionStatus::Connected);
        Ok(())
    }

    async fn close(&mut self) -> Result<()> {
        self.set_connection_status(ConnectionStatus::Disconnected);
        if let Ok(mut initialized) = self.initialized.write() {
            *initialized = false;
        }
        Ok(())
    }

    fn supported_message_types(&self) -> Vec<String> {
        vec![
            "HealthCheck".to_string(),
            "Ping".to_string(),
            "Auth".to_string(),
            "Password".to_string(),
            "Version".to_string(),
            "Test".to_string(),
            // Edge 特定的消息类型
            "EdgeSecurity".to_string(),
        ]
    }

    fn config(&self) -> &BrowserAdapterConfig {
        &self.config
    }
}

impl Default for EdgeAdapter {
    fn default() -> Self {
        Self::new_default().expect("创建默认 Edge 适配器失败")
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::native_messaging::protocol::message::MessageType;

    #[test]
    fn test_edge_adapter_creation() {
        let config = BrowserAdapterConfig {
            browser_type: BrowserType::Edge,
            browser_version: "Edge/120.0.2210.61".to_string(),
            ..Default::default()
        };
        let adapter = EdgeAdapter::new(config).unwrap();
        assert_eq!(adapter.browser_type(), BrowserType::Edge);
        assert_eq!(adapter.browser_version(), "Edge/120.0.2210.61");
    }

    #[test]
    fn test_edge_adapter_default() {
        let adapter = EdgeAdapter::default();
        assert_eq!(adapter.browser_type(), BrowserType::Edge);
        assert!(adapter.browser_version().starts_with("Edge/"));
    }

    #[tokio::test]
    async fn test_edge_adapter_initialization() {
        let mut adapter = EdgeAdapter::default();
        assert!(!adapter.is_connected().await);
        
        let result = adapter.initialize().await;
        assert!(result.is_ok());
        assert!(adapter.is_connected().await);
    }

    #[tokio::test]
    async fn test_edge_adapter_send_message() {
        let mut adapter = EdgeAdapter::default();
        adapter.initialize().await.unwrap();

        let message = OutgoingMessage::new(
            NativeMessage::new(
                MessageType::HealthCheck,
                "test".to_string(),
                serde_json::json!({"test": "data"}),
                "test-source".to_string(),
            ),
        );

        let result = adapter.send_message(message).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_edge_adapter_receive_message() {
        let mut adapter = EdgeAdapter::default();
        adapter.initialize().await.unwrap();

        let result = adapter.receive_message().await;
        assert!(result.is_ok());
        
        let message = result.unwrap();
        assert!(message.payload.get("microsoft_security_token").is_some());
    }

    #[tokio::test]
    async fn test_edge_adapter_close() {
        let mut adapter = EdgeAdapter::default();
        adapter.initialize().await.unwrap();
        assert!(adapter.is_connected().await);

        let result = adapter.close().await;
        assert!(result.is_ok());
        assert!(!adapter.is_connected().await);
    }

    #[test]
    fn test_edge_message_validation() {
        let adapter = EdgeAdapter::default();
        
        // 测试有效消息
        let valid_message = OutgoingMessage::new(
            NativeMessage::new(
                MessageType::HealthCheck,
                "test".to_string(),
                serde_json::json!({"test": "data"}),
                "test-source".to_string(),
            ),
        );
        assert!(adapter.validate_edge_message(&valid_message).is_ok());

        // 测试空数据消息
        let invalid_message = OutgoingMessage::new(
            NativeMessage::new(
                MessageType::HealthCheck,
                "test".to_string(),
                serde_json::Value::Null,
                "test-source".to_string(),
            ),
        );
        assert!(adapter.validate_edge_message(&invalid_message).is_err());

        // 测试空源消息
        let invalid_source_message = OutgoingMessage::new(
            NativeMessage::new(
                MessageType::HealthCheck,
                "test".to_string(),
                serde_json::json!({"test": "data"}),
                "".to_string(),
            ),
        );
        assert!(adapter.validate_edge_message(&invalid_source_message).is_err());
    }

    #[test]
    fn test_edge_message_format_conversion() {
        let adapter = EdgeAdapter::default();
        let message = OutgoingMessage::new(
            NativeMessage::new(
                MessageType::HealthCheck,
                "test".to_string(),
                serde_json::json!({"original": "data"}),
                "test-source".to_string(),
            ),
        );

        let result = adapter.convert_to_edge_format(message);
        assert!(result.is_ok());
        
        let converted = result.unwrap();
        assert!(converted.message.payload.get("browser").is_some());
        assert!(converted.message.payload.get("microsoft_security_token").is_some());
        assert_eq!(converted.message.payload["browser"], "edge");
    }

    #[test]
    fn test_edge_version_detection() {
        let version = EdgeAdapter::detect_edge_version("msedge");
        assert!(version.starts_with("Edge/"));
        assert!(version.contains("."));
    }

    #[test]
    fn test_edge_config_differences() {
        let edge_config = EdgeAdapter::new_default().unwrap();
        
        // Edge 特定的配置值
        assert_eq!(edge_config.config.connection_timeout, 6000);
        assert_eq!(edge_config.config.message_timeout, 12000);
        assert_eq!(edge_config.config.max_retries, 2);
        
        // 支持的消息类型包含Edge特定类型
        let supported = edge_config.supported_message_types();
        assert!(supported.contains(&"EdgeSecurity".to_string()));
    }
} 