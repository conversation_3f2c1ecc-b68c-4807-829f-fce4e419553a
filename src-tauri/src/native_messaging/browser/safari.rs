//! Safari 浏览器适配器实现
//!
//! 提供针对 Safari 浏览器的 Native Messaging 支持

use super::{BrowserAdapter, BrowserAdapterConfig, BrowserAdapterStats, ConnectionStatus};
use crate::native_messaging::{
    error::{NativeMessagingError, Result},
    protocol::message::{NativeMessage, OutgoingMessage},
    config::BrowserType,
};
use async_trait::async_trait;
use std::collections::HashMap;
use std::env;
use std::io::{self, Read, Write};
use std::path::PathBuf;
use std::process::{Command, Stdio};
use std::sync::{Arc, RwLock};
use std::time::{Duration, SystemTime};
use tokio::io::{AsyncBufReadExt, AsyncReadExt, AsyncWriteExt, BufReader};
use tokio::process::{<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChildStdout};
use tokio::sync::Mutex;
use serde_json;

/// Safari 浏览器适配器
///
/// 实现 Safari 特定的 Native Messaging 协议和 Web Extensions API
/// Safari 使用不同的扩展架构，需要特殊的消息处理
pub struct SafariAdapter {
    /// 适配器配置
    config: BrowserAdapterConfig,
    /// 连接状态
    connection_status: Arc<RwLock<ConnectionStatus>>,
    /// 统计信息
    stats: Arc<Mutex<BrowserAdapterStats>>,
    /// 是否已初始化
    initialized: Arc<RwLock<bool>>,
    /// Safari 可执行文件路径
    safari_executable: Option<String>,
    /// Native Messaging Host 配置
    host_config: Option<SafariHostConfig>,
    /// 进程句柄
    process_handle: Arc<Mutex<Option<Child>>>,
    /// 标准输入句柄
    stdin_handle: Arc<Mutex<Option<ChildStdin>>>,
    /// 标准输出句柄
    stdout_handle: Arc<Mutex<Option<BufReader<ChildStdout>>>>,
}

/// Safari Host 配置
#[derive(Debug, Clone)]
pub struct SafariHostConfig {
    /// Host 名称
    pub name: String,
    /// 描述
    pub description: String,
    /// 可执行文件路径
    pub path: String,
    /// 消息类型
    pub message_type: String,
    /// Safari 扩展Bundle ID
    pub safari_extension_bundle_ids: Vec<String>,
    /// App Store Connect Team ID
    pub team_identifier: Option<String>,
}

impl Default for SafariHostConfig {
    fn default() -> Self {
        Self {
            name: "com.securepassword.nativemessaging".to_string(),
            description: "Secure Password Native Messaging Host".to_string(),
            path: env::current_exe()
                .unwrap_or_else(|_| PathBuf::from("secure-password-daemon"))
                .to_string_lossy()
                .to_string(),
            message_type: "stdio".to_string(),
            safari_extension_bundle_ids: vec![
                "com.securepassword.safari-extension".to_string()
            ],
            team_identifier: Some("YOUR_TEAM_ID".to_string()),
        }
    }
}

impl SafariAdapter {
    /// 创建新的 Safari 适配器
    ///
    /// # 参数
    /// - `config`: 适配器配置
    ///
    /// # 返回
    /// Result<Self> - 创建的适配器
    pub fn new(config: BrowserAdapterConfig) -> Result<Self> {
        let mut safari_config = config;
        safari_config.browser_type = BrowserType::Safari;
        
        let safari_executable = Self::find_safari_executable()
            .ok()
            .unwrap_or_else(|| "Safari".to_string());
        
        if safari_config.browser_version == "unknown" {
            safari_config.browser_version = Self::detect_safari_version(&safari_executable);
        }
        
        Ok(Self {
            config: safari_config,
            connection_status: Arc::new(RwLock::new(ConnectionStatus::Disconnected)),
            stats: Arc::new(Mutex::new(BrowserAdapterStats::default())),
            initialized: Arc::new(RwLock::new(false)),
            safari_executable: Some(safari_executable),
            host_config: Some(SafariHostConfig::default()),
            process_handle: Arc::new(Mutex::new(None)),
            stdin_handle: Arc::new(Mutex::new(None)),
            stdout_handle: Arc::new(Mutex::new(None)),
        })
    }

    /// 创建默认的 Safari 适配器
    ///
    /// # 返回
    /// Result<Self> - 创建的适配器
    pub fn new_default() -> Result<Self> {
        let safari_executable = Self::find_safari_executable()
            .unwrap_or_else(|_| "Safari".to_string());
        
        let config = BrowserAdapterConfig {
            browser_type: BrowserType::Safari,
            browser_version: Self::detect_safari_version(&safari_executable),
            // Safari 通常需要更长的初始化时间
            connection_timeout: 10000,
            message_timeout: 20000,
            max_retries: 3,
            debug_mode: false,
            ..Default::default()
        };
        Self::new(config)
    }

    /// 使用指定的Host配置创建Safari适配器
    ///
    /// # 参数
    /// - `host_config`: Host配置
    ///
    /// # 返回
    /// Result<Self> - 创建的适配器
    pub fn new_with_host_config(host_config: SafariHostConfig) -> Result<Self> {
        let safari_executable = Self::find_safari_executable()
            .unwrap_or_else(|_| "Safari".to_string());
        
        let config = BrowserAdapterConfig {
            browser_type: BrowserType::Safari,
            browser_version: Self::detect_safari_version(&safari_executable),
            connection_timeout: 10000,
            message_timeout: 20000,
            max_retries: 3,
            debug_mode: false,
            ..Default::default()
        };
        
        let mut adapter = Self::new(config)?;
        adapter.host_config = Some(host_config);
        Ok(adapter)
    }

    /// 查找 Safari 可执行文件
    ///
    /// # 返回
    /// Result<String> - Safari 可执行文件路径
    pub fn find_safari_executable() -> Result<String> {
        // Safari 主要在 macOS 和 iOS 上运行
        #[cfg(target_os = "macos")]
        {
            // 首先尝试环境变量
            if let Ok(safari_path) = env::var("SAFARI_PATH") {
                if std::path::Path::new(&safari_path).exists() {
                    return Ok(safari_path);
                }
            }

            // 尝试预定义路径
            let possible_paths = Self::get_safari_possible_paths();
            for path in possible_paths {
                if path.exists() {
                    return Ok(path.to_string_lossy().to_string());
                }
            }

            // 尝试通过系统命令
            if let Ok(path) = Self::which_command("safari") {
                return Ok(path);
            }

            Err(NativeMessagingError::BrowserNotFound(
                "找不到 Safari 可执行文件".to_string(),
            ))
        }

        #[cfg(not(target_os = "macos"))]
        {
            // 在非 macOS 系统上，Safari 通常不可用
            Err(NativeMessagingError::BrowserNotFound(
                "Safari 仅在 macOS 和 iOS 系统上可用".to_string(),
            ))
        }
    }

    /// 获取 Safari 可能的安装路径
    fn get_safari_possible_paths() -> Vec<PathBuf> {
        #[cfg(target_os = "macos")]
        {
            vec![
                PathBuf::from("/Applications/Safari.app/Contents/MacOS/Safari"),
                PathBuf::from("/System/Applications/Safari.app/Contents/MacOS/Safari"),
                PathBuf::from("/usr/bin/safari"),
            ]
        }
        #[cfg(not(target_os = "macos"))]
        {
            vec![]
        }
    }

    /// 使用 which 命令查找可执行文件
    fn which_command(command: &str) -> Result<String> {
        let output = Command::new("which")
            .arg(command)
            .output()
            .map_err(|e| NativeMessagingError::SystemError(format!("执行 which 命令失败: {}", e)))?;

        if output.status.success() {
            let path = String::from_utf8_lossy(&output.stdout).trim().to_string();
            if !path.is_empty() {
                return Ok(path);
            }
        }

        Err(NativeMessagingError::BrowserNotFound(
            format!("未找到命令: {}", command),
        ))
    }

    /// 检测 Safari 版本
    ///
    /// # 参数
    /// - `safari_path`: Safari 可执行文件路径
    ///
    /// # 返回
    /// String - Safari 版本信息
    fn detect_safari_version(safari_path: &str) -> String {
        #[cfg(target_os = "macos")]
        {
            // 尝试从 Info.plist 读取版本信息
            if safari_path.contains("Safari.app") {
                let info_plist_path = safari_path.replace("/Contents/MacOS/Safari", "/Contents/Info.plist");
                if let Ok(output) = Command::new("defaults")
                    .arg("read")
                    .arg(&info_plist_path)
                    .arg("CFBundleShortVersionString")
                    .output() 
                {
                    if output.status.success() {
                        let version = String::from_utf8_lossy(&output.stdout).trim().to_string();
                        if !version.is_empty() {
                            return format!("Safari/{}", version);
                        }
                    }
                }
            }

            // 尝试通过系统版本推断Safari版本
            if let Ok(output) = Command::new("sw_vers")
                .arg("-productVersion")
                .output() 
            {
                if output.status.success() {
                    let os_version_str = String::from_utf8_lossy(&output.stdout);
                    let os_version = os_version_str.trim();
                    // macOS版本对应的Safari版本映射
                    if os_version.starts_with("14.") {
                        return "Safari/17.2".to_string();
                    } else if os_version.starts_with("13.") {
                        return "Safari/16.6".to_string();
                    } else if os_version.starts_with("12.") {
                        return "Safari/15.6".to_string();
                    }
                }
            }
        }

        // 默认版本
        "Safari/17.2".to_string()
    }

    /// 获取 Safari Host 配置
    ///
    /// # 返回
    /// Option<&SafariHostConfig> - Host 配置引用
    pub fn host_config(&self) -> Option<&SafariHostConfig> {
        self.host_config.as_ref()
    }

    /// 启动 Safari 进程用于测试（仅在 macOS 上可用）
    ///
    /// # 返回
    /// Result<()> - 启动结果
    pub async fn start_safari_process(&self) -> Result<()> {
        #[cfg(target_os = "macos")]
        {
            if let Some(safari_path) = &self.safari_executable {
                let child = tokio::process::Command::new("open")
                    .arg("-a")
                    .arg(safari_path)
                    .arg("--args")
                    .arg("--automation")
                    .stdin(Stdio::piped())
                    .stdout(Stdio::piped())
                    .stderr(Stdio::null())
                    .spawn()
                    .map_err(|e| NativeMessagingError::ConnectionError(format!("启动 Safari 失败: {}", e)))?;

                let mut process_guard = self.process_handle.lock().await;
                *process_guard = Some(child);
            }
            Ok(())
        }

        #[cfg(not(target_os = "macos"))]
        {
            Err(NativeMessagingError::ConfigurationError(
                "Safari 进程启动仅在 macOS 上支持".to_string(),
            ))
        }
    }

    /// 停止 Safari 进程
    ///
    /// # 返回
    /// Result<()> - 停止结果
    pub async fn stop_safari_process(&self) -> Result<()> {
        let mut process_guard = self.process_handle.lock().await;
        if let Some(mut child) = process_guard.take() {
            if let Err(e) = child.kill().await {
                return Err(NativeMessagingError::ConnectionError(format!("停止 Safari 进程失败: {}", e)));
            }
        }
        Ok(())
    }

    /// 生成 Safari Native Messaging Manifest
    ///
    /// # 返回
    /// Result<String> - 生成的 manifest JSON
    pub fn generate_host_manifest(&self) -> Result<String> {
        if let Some(config) = &self.host_config {
            let mut manifest = serde_json::json!({
                "name": config.name,
                "description": config.description,
                "path": config.path,
                "type": config.message_type,
                "safari_extension_bundle_ids": config.safari_extension_bundle_ids
            });

            // 添加 Team Identifier（如果有）
            if let Some(team_id) = &config.team_identifier {
                manifest["team_identifier"] = serde_json::Value::String(team_id.clone());
            }

            serde_json::to_string_pretty(&manifest)
                .map_err(|e| NativeMessagingError::SerializationError(e))
        } else {
            Err(NativeMessagingError::ConfigurationError(
                "Safari Host 配置未设置".to_string(),
            ))
        }
    }

    /// 实现 Safari Native Messaging 协议通信
    ///
    /// # 参数
    /// - `message`: 要发送的原始消息
    ///
    /// # 返回
    /// Result<()> - 发送结果
    async fn send_native_message(&self, message: &[u8]) -> Result<()> {
        // Safari 在某些版本中使用特殊的消息格式
        // 但大部分情况下也使用标准的长度前缀格式
        let length = message.len() as u32;
        let length_bytes = length.to_le_bytes();

        // 这里应该写入到 stdin
        // 在实际实现中，需要维护 stdin handle
        tokio::time::sleep(Duration::from_millis(2)).await;

        Ok(())
    }

    /// 从 Safari 接收 Native Messaging 消息
    ///
    /// # 返回
    /// Result<Vec<u8>> - 接收到的消息字节
    async fn receive_native_message(&self) -> Result<Vec<u8>> {
        // Safari 消息接收处理
        // 在实际实现中，需要处理Safari特定的消息格式
        tokio::time::sleep(Duration::from_millis(2)).await;

        // 返回模拟数据
        Ok(b"{}".to_vec())
    }

    /// 验证消息格式是否符合 Safari 规范
    ///
    /// # 参数
    /// - `message`: 要验证的消息
    ///
    /// # 返回
    /// Result<()> - 验证结果
    fn validate_safari_message(&self, message: &OutgoingMessage) -> Result<()> {
        // Safari Native Messaging 格式验证
        if message.message.payload.is_null() {
            return Err(NativeMessagingError::ProtocolError(
                "Safari 消息数据不能为空".to_string(),
            ));
        }

        // Safari 的消息大小限制（相对较小，256KB）
        let message_str = serde_json::to_string(&message.message.payload)
            .map_err(|e| NativeMessagingError::SerializationError(e))?;
        
        if message_str.len() > 256 * 1024 {
            return Err(NativeMessagingError::ProtocolError(
                "Safari 消息大小超过 256KB 限制".to_string(),
            ));
        }

        // Safari 要求特定的消息结构
        if message.message.source.is_empty() {
            return Err(NativeMessagingError::ProtocolError(
                "Safari 消息必须包含有效的源标识".to_string(),
            ));
        }

        // Safari 需要特定的请求ID格式
        if message.message.request_id.is_empty() {
            return Err(NativeMessagingError::ProtocolError(
                "Safari 消息必须包含请求ID".to_string(),
            ));
        }

        Ok(())
    }

    /// 转换消息格式为 Safari 兼容格式
    ///
    /// # 参数
    /// - `message`: 原始消息
    ///
    /// # 返回
    /// Result<OutgoingMessage> - 转换后的消息
    fn convert_to_safari_format(&self, mut message: OutgoingMessage) -> Result<OutgoingMessage> {
        // Safari 需要在消息中添加特定的Apple metadata
        if let Some(obj) = message.message.payload.as_object_mut() {
            obj.insert("browser".to_string(), serde_json::Value::String("safari".to_string()));
            obj.insert("webkit_version".to_string(), serde_json::Value::String("webkit-618.1.15".to_string()));
            obj.insert("safari_extension_api".to_string(), serde_json::Value::String("2.0".to_string()));
            obj.insert("apple_platform".to_string(), serde_json::Value::String(
                if cfg!(target_os = "macos") { "macos" } else { "ios" }.to_string()
            ));
            
            // 添加Safari特定的安全标识
            if let Some(config) = &self.host_config {
                if let Some(team_id) = &config.team_identifier {
                    obj.insert("team_identifier".to_string(), serde_json::Value::String(team_id.clone()));
                }
            }
        }

        Ok(message)
    }

    /// 设置连接状态
    ///
    /// # 参数
    /// - `status`: 新的连接状态
    fn set_connection_status(&self, status: ConnectionStatus) {
        if let Ok(mut current_status) = self.connection_status.write() {
            *current_status = status;
        }
    }

    /// 获取连接状态
    ///
    /// # 返回
    /// ConnectionStatus - 当前连接状态
    fn get_connection_status(&self) -> ConnectionStatus {
        self.connection_status
            .read()
            .map(|status| status.clone())
            .unwrap_or(ConnectionStatus::Failed("无法读取状态".to_string()))
    }
}

#[async_trait]
impl BrowserAdapter for SafariAdapter {
    fn browser_type(&self) -> BrowserType {
        BrowserType::Safari
    }

    fn browser_version(&self) -> &str {
        &self.config.browser_version
    }

    async fn send_message(&self, message: OutgoingMessage) -> Result<()> {
        let start_time = SystemTime::now();
        
        // 验证消息格式
        self.validate_safari_message(&message)?;

        // 转换为 Safari 格式
        let _safari_message = self.convert_to_safari_format(message)?;

        // 检查连接状态
        if !self.is_connected().await {
            let mut stats = self.stats.lock().await;
            stats.record_send(false);
            return Err(NativeMessagingError::ConnectionError(
                "Safari 连接未建立".to_string(),
            ));
        }

        // 模拟发送消息到 Safari (最慢，因为Safari的沙箱限制)
        // 在实际实现中，这里会通过 Safari 特定的通信机制
        tokio::time::sleep(Duration::from_millis(20)).await;

        // 记录统计信息
        let mut stats = self.stats.lock().await;
        stats.record_send(true);
        
        if let Ok(elapsed) = start_time.elapsed() {
            stats.record_response_time(elapsed);
        }

        Ok(())
    }

    async fn receive_message(&self) -> Result<NativeMessage> {
        let start_time = SystemTime::now();

        // 检查连接状态
        if !self.is_connected().await {
            let mut stats = self.stats.lock().await;
            stats.record_receive(false);
            return Err(NativeMessagingError::ConnectionError(
                "Safari 连接未建立".to_string(),
            ));
        }

        // 模拟从 Safari 接收消息 (包含Apple安全验证时间)
        // 在实际实现中，这里会从 Safari 扩展接收消息
        tokio::time::sleep(Duration::from_millis(15)).await;
        
        let message = NativeMessage::new(
            crate::native_messaging::protocol::message::MessageType::HealthCheck,
            "safari-test".to_string(),
            serde_json::json!({
                "source": "safari",
                "webkit_version": "webkit-618.1.15",
                "safari_version": self.browser_version(),
                "apple_platform": if cfg!(target_os = "macos") { "macos" } else { "ios" }
            }),
            "safari-extension".to_string(),
        );

        // 记录统计信息
        let mut stats = self.stats.lock().await;
        stats.record_receive(true);
        
        if let Ok(elapsed) = start_time.elapsed() {
            stats.record_response_time(elapsed);
        }

        Ok(message)
    }

    async fn is_connected(&self) -> bool {
        matches!(self.get_connection_status(), ConnectionStatus::Connected)
    }

    async fn initialize(&mut self) -> Result<()> {
        if let Ok(initialized) = self.initialized.read() {
            if *initialized {
                return Ok(());
            }
        }

        self.set_connection_status(ConnectionStatus::Connecting);
        
        // Safari 初始化最慢，包含Apple安全验证和沙箱初始化
        tokio::time::sleep(Duration::from_millis(200)).await;

        if let Ok(mut initialized) = self.initialized.write() {
            *initialized = true;
        }

        self.set_connection_status(ConnectionStatus::Connected);
        Ok(())
    }

    async fn close(&mut self) -> Result<()> {
        self.set_connection_status(ConnectionStatus::Disconnected);
        if let Ok(mut initialized) = self.initialized.write() {
            *initialized = false;
        }
        Ok(())
    }

    fn supported_message_types(&self) -> Vec<String> {
        vec![
            "HealthCheck".to_string(),
            "Ping".to_string(),
            "Auth".to_string(),
            "Password".to_string(),
            "Version".to_string(),
            "Test".to_string(),
            // Safari 特定的消息类型
            "SafariExtension".to_string(),
            "WebKitMessage".to_string(),
        ]
    }

    fn config(&self) -> &BrowserAdapterConfig {
        &self.config
    }
}

impl Default for SafariAdapter {
    fn default() -> Self {
        Self::new_default().expect("创建默认 Safari 适配器失败")
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::native_messaging::protocol::message::MessageType;

    #[test]
    fn test_safari_adapter_creation() {
        let config = BrowserAdapterConfig {
            browser_type: BrowserType::Safari,
            browser_version: "Safari/17.2".to_string(),
            ..Default::default()
        };
        let adapter = SafariAdapter::new(config).unwrap();
        assert_eq!(adapter.browser_type(), BrowserType::Safari);
        assert_eq!(adapter.browser_version(), "Safari/17.2");
    }

    #[test]
    fn test_safari_adapter_default() {
        let adapter = SafariAdapter::default();
        assert_eq!(adapter.browser_type(), BrowserType::Safari);
        assert!(adapter.browser_version().starts_with("Safari/"));
    }

    #[tokio::test]
    async fn test_safari_adapter_initialization() {
        let mut adapter = SafariAdapter::default();
        assert!(!adapter.is_connected().await);
        
        let result = adapter.initialize().await;
        assert!(result.is_ok());
        assert!(adapter.is_connected().await);
    }

    #[tokio::test]
    async fn test_safari_adapter_send_message() {
        let mut adapter = SafariAdapter::default();
        adapter.initialize().await.unwrap();

        let message = OutgoingMessage::new(
            NativeMessage::new(
                MessageType::HealthCheck,
                "test".to_string(),
                serde_json::json!({"test": "data"}),
                "test-source".to_string(),
            ),
        );

        let result = adapter.send_message(message).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_safari_adapter_receive_message() {
        let mut adapter = SafariAdapter::default();
        adapter.initialize().await.unwrap();

        let result = adapter.receive_message().await;
        assert!(result.is_ok());
        
        let message = result.unwrap();
        assert!(message.payload.get("webkit_version").is_some());
        assert!(message.payload.get("apple_platform").is_some());
    }

    #[tokio::test]
    async fn test_safari_adapter_close() {
        let mut adapter = SafariAdapter::default();
        adapter.initialize().await.unwrap();
        assert!(adapter.is_connected().await);

        let result = adapter.close().await;
        assert!(result.is_ok());
        assert!(!adapter.is_connected().await);
    }

    #[test]
    fn test_safari_message_validation() {
        let adapter = SafariAdapter::default();
        
        // 测试有效消息
        let valid_message = OutgoingMessage::new(
            NativeMessage::new(
                MessageType::HealthCheck,
                "test".to_string(),
                serde_json::json!({"test": "data"}),
                "test-source".to_string(),
            ),
        );
        assert!(adapter.validate_safari_message(&valid_message).is_ok());

        // 测试空数据消息
        let invalid_message = OutgoingMessage::new(
            NativeMessage::new(
                MessageType::HealthCheck,
                "test".to_string(),
                serde_json::Value::Null,
                "test-source".to_string(),
            ),
        );
        assert!(adapter.validate_safari_message(&invalid_message).is_err());

        // 测试空源消息
        let invalid_source_message = OutgoingMessage::new(
            NativeMessage::new(
                MessageType::HealthCheck,
                "".to_string(),
                serde_json::json!({"test": "data"}),
                "".to_string(),
            ),
        );
        assert!(adapter.validate_safari_message(&invalid_source_message).is_err());
    }

    #[test]
    fn test_safari_message_format_conversion() {
        let adapter = SafariAdapter::default();
        let message = OutgoingMessage::new(
            NativeMessage::new(
                MessageType::HealthCheck,
                "test".to_string(),
                serde_json::json!({"original": "data"}),
                "test-source".to_string(),
            ),
        );

        let result = adapter.convert_to_safari_format(message);
        assert!(result.is_ok());
        
        let converted = result.unwrap();
        assert!(converted.message.payload.get("browser").is_some());
        assert!(converted.message.payload.get("webkit_version").is_some());
        assert!(converted.message.payload.get("apple_platform").is_some());
        assert_eq!(converted.message.payload["browser"], "safari");
    }

    #[test]
    fn test_safari_version_detection() {
        let version = SafariAdapter::detect_safari_version("Safari");
        assert!(version.starts_with("Safari/"));
        assert!(version.contains("."));
    }

    #[test]
    fn test_safari_config_differences() {
        let safari_config = SafariAdapter::new_default().unwrap();
        
        // Safari 特定的配置值
        assert_eq!(safari_config.config.connection_timeout, 10000);
        assert_eq!(safari_config.config.message_timeout, 20000);
        assert_eq!(safari_config.config.max_retries, 3);
        
        // 支持的消息类型包含Safari特定类型
        let supported = safari_config.supported_message_types();
        assert!(supported.contains(&"SafariExtension".to_string()));
        assert!(supported.contains(&"WebKitMessage".to_string()));
    }

    #[test]
    fn test_safari_platform_detection() {
        let adapter = SafariAdapter::default();
        let message = OutgoingMessage::new(
            NativeMessage::new(
                MessageType::HealthCheck,
                "test".to_string(),
                serde_json::json!({"test": "data"}),
                "test-source".to_string(),
            ),
        );

        let result = adapter.convert_to_safari_format(message);
        assert!(result.is_ok());
        
        let converted = result.unwrap();
        let platform = converted.message.payload.get("apple_platform").unwrap();
        
        // 在测试环境中应该检测到正确的平台
        if cfg!(target_os = "macos") {
            assert_eq!(platform, "macos");
        } else {
            assert_eq!(platform, "ios");
        }
    }
} 