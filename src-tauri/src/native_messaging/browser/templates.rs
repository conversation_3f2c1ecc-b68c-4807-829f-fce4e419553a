//! 浏览器适配层配置模板和安装脚本
//!
//! 提供各种浏览器的配置文件模板和自动化安装脚本生成

use crate::native_messaging::{
    error::{NativeMessagingError, Result},
    config::BrowserType,
};
use serde_json::{json, Value};
use std::collections::HashMap;
use std::path::PathBuf;

/// 配置模板管理器
pub struct ConfigTemplateManager {
    /// 模板配置
    config: TemplateConfig,
    /// 生成的模板缓存
    template_cache: HashMap<String, String>,
}

/// 模板配置
#[derive(Debug, Clone)]
pub struct TemplateConfig {
    /// 应用名称
    pub app_name: String,
    /// 应用描述
    pub app_description: String,
    /// 可执行文件路径
    pub executable_path: String,
    /// Host名称
    pub host_name: String,
    /// 允许的来源
    pub allowed_origins: Vec<String>,
    /// 允许的扩展ID
    pub allowed_extensions: Vec<String>,
    /// 扩展Bundle ID（Safari）
    pub safari_bundle_ids: Vec<String>,
    /// Team Identifier（Apple）
    pub team_identifier: Option<String>,
    /// 版本号
    pub version: String,
}

impl Default for TemplateConfig {
    fn default() -> Self {
        Self {
            app_name: "Secure Password Manager".to_string(),
            app_description: "安全密码管理器 Native Messaging Host".to_string(),
            executable_path: "/usr/local/bin/secure-password-daemon".to_string(),
            host_name: "com.securepassword.nativemessaging".to_string(),
            allowed_origins: vec![
                "chrome-extension://your-extension-id/".to_string(),
                "moz-extension://your-extension-id/".to_string(),
            ],
            allowed_extensions: vec![
                "your-extension-id".to_string(),
            ],
            safari_bundle_ids: vec![
                "com.securepassword.safari.extension".to_string(),
            ],
            team_identifier: None,
            version: "1.0.0".to_string(),
        }
    }
}

impl ConfigTemplateManager {
    /// 创建新的配置模板管理器
    ///
    /// # 参数
    /// - `config`: 模板配置
    ///
    /// # 返回
    /// Self - 模板管理器实例
    pub fn new(config: TemplateConfig) -> Self {
        Self {
            config,
            template_cache: HashMap::new(),
        }
    }

    /// 创建默认的模板管理器
    ///
    /// # 返回
    /// Self - 默认模板管理器
    pub fn new_default() -> Self {
        Self::new(TemplateConfig::default())
    }

    /// 生成Chrome Native Messaging Host配置
    ///
    /// # 返回
    /// Result<String> - 生成的JSON配置
    pub fn generate_chrome_manifest(&mut self) -> Result<String> {
        let cache_key = "chrome_manifest".to_string();
        
        if let Some(cached) = self.template_cache.get(&cache_key) {
            return Ok(cached.clone());
        }

        let manifest = json!({
            "name": self.config.host_name,
            "description": self.config.app_description,
            "path": self.config.executable_path,
            "type": "stdio",
            "allowed_origins": self.config.allowed_origins
                .iter()
                .filter(|origin| origin.starts_with("chrome-extension://"))
                .collect::<Vec<_>>(),
            "allowed_extensions": self.config.allowed_extensions
        });

        let manifest_str = serde_json::to_string_pretty(&manifest)
            .map_err(|e| NativeMessagingError::SerializationError(e))?;

        self.template_cache.insert(cache_key, manifest_str.clone());
        Ok(manifest_str)
    }

    /// 生成Firefox Native Messaging Host配置
    ///
    /// # 返回
    /// Result<String> - 生成的JSON配置
    pub fn generate_firefox_manifest(&mut self) -> Result<String> {
        let cache_key = "firefox_manifest".to_string();
        
        if let Some(cached) = self.template_cache.get(&cache_key) {
            return Ok(cached.clone());
        }

        let manifest = json!({
            "name": self.config.host_name,
            "description": self.config.app_description,
            "path": self.config.executable_path,
            "type": "stdio",
            "allowed_extensions": self.config.allowed_extensions
                .iter()
                .map(|ext| format!("{}@secure-password.com", ext))
                .collect::<Vec<_>>()
        });

        let manifest_str = serde_json::to_string_pretty(&manifest)
            .map_err(|e| NativeMessagingError::SerializationError(e))?;

        self.template_cache.insert(cache_key, manifest_str.clone());
        Ok(manifest_str)
    }

    /// 生成Edge Native Messaging Host配置
    ///
    /// # 返回
    /// Result<String> - 生成的JSON配置
    pub fn generate_edge_manifest(&mut self) -> Result<String> {
        let cache_key = "edge_manifest".to_string();
        
        if let Some(cached) = self.template_cache.get(&cache_key) {
            return Ok(cached.clone());
        }

        let manifest = json!({
            "name": self.config.host_name,
            "description": self.config.app_description,
            "path": self.config.executable_path,
            "type": "stdio",
            "allowed_origins": self.config.allowed_origins
                .iter()
                .filter(|origin| origin.starts_with("extension://"))
                .collect::<Vec<_>>(),
            "allowed_extensions": self.config.allowed_extensions,
            "microsoft_security": {
                "required_capabilities": ["nativeMessaging"],
                "app_id": self.config.host_name
            }
        });

        let manifest_str = serde_json::to_string_pretty(&manifest)
            .map_err(|e| NativeMessagingError::SerializationError(e))?;

        self.template_cache.insert(cache_key, manifest_str.clone());
        Ok(manifest_str)
    }

    /// 生成Safari Native Messaging Host配置
    ///
    /// # 返回
    /// Result<String> - 生成的JSON配置
    pub fn generate_safari_manifest(&mut self) -> Result<String> {
        let cache_key = "safari_manifest".to_string();
        
        if let Some(cached) = self.template_cache.get(&cache_key) {
            return Ok(cached.clone());
        }

        let mut manifest = json!({
            "name": self.config.host_name,
            "description": self.config.app_description,
            "path": self.config.executable_path,
            "type": "stdio",
            "safari_extension_bundle_ids": self.config.safari_bundle_ids
        });

        // 添加Team Identifier（如果有）
        if let Some(team_id) = &self.config.team_identifier {
            manifest["team_identifier"] = json!(team_id);
        }

        let manifest_str = serde_json::to_string_pretty(&manifest)
            .map_err(|e| NativeMessagingError::SerializationError(e))?;

        self.template_cache.insert(cache_key, manifest_str.clone());
        Ok(manifest_str)
    }

    /// 生成指定浏览器的配置文件
    ///
    /// # 参数
    /// - `browser_type`: 浏览器类型
    ///
    /// # 返回
    /// Result<String> - 生成的配置JSON
    pub fn generate_manifest_for_browser(&mut self, browser_type: &BrowserType) -> Result<String> {
        match browser_type {
            BrowserType::Chrome => self.generate_chrome_manifest(),
            BrowserType::Firefox => self.generate_firefox_manifest(),
            BrowserType::Edge => self.generate_edge_manifest(),
            BrowserType::Safari => self.generate_safari_manifest(),
        }
    }

    /// 生成安装脚本
    ///
    /// # 参数
    /// - `platform`: 目标平台 ("windows", "macos", "linux")
    ///
    /// # 返回
    /// Result<InstallScript> - 生成的安装脚本
    pub fn generate_install_script(&self, platform: &str) -> Result<InstallScript> {
        match platform.to_lowercase().as_str() {
            "windows" => self.generate_windows_install_script(),
            "macos" => self.generate_macos_install_script(),
            "linux" => self.generate_linux_install_script(),
            _ => Err(NativeMessagingError::ConfigurationError(
                format!("不支持的平台: {}", platform)
            )),
        }
    }

    /// 生成Windows安装脚本
    fn generate_windows_install_script(&self) -> Result<InstallScript> {
        let powershell_script = format!(r#"
# Secure Password Manager Native Messaging Host 安装脚本
# 平台: Windows

Write-Host "正在安装 Secure Password Manager Native Messaging Host..."

# 定义变量
$HostName = "{host_name}"
$ExecutablePath = "{executable_path}"
$AppDescription = "{app_description}"

# Chrome 安装路径
$ChromeRegPath = "HKCU:\Software\Google\Chrome\NativeMessagingHosts\$HostName"
$ChromeManifestDir = "$env:LOCALAPPDATA\Google\Chrome\User Data\NativeMessagingHosts"

# Firefox 安装路径
$FirefoxRegPath = "HKCU:\Software\Mozilla\NativeMessagingHosts\$HostName"
$FirefoxManifestDir = "$env:APPDATA\Mozilla\NativeMessagingHosts"

# Edge 安装路径
$EdgeRegPath = "HKCU:\Software\Microsoft\Edge\NativeMessagingHosts\$HostName"
$EdgeManifestDir = "$env:LOCALAPPDATA\Microsoft\Edge\User Data\NativeMessagingHosts"

# 创建Chrome配置
function Install-ChromeHost {{
    try {{
        Write-Host "安装Chrome Native Messaging Host..."
        
        if (!(Test-Path $ChromeManifestDir)) {{
            New-Item -ItemType Directory -Path $ChromeManifestDir -Force | Out-Null
        }}
        
        $ChromeManifest = @{{
            name = $HostName
            description = $AppDescription
            path = $ExecutablePath
            type = "stdio"
            allowed_origins = @("{chrome_origins}")
        }} | ConvertTo-Json -Depth 3
        
        $ChromeManifestPath = Join-Path $ChromeManifestDir "$HostName.json"
        $ChromeManifest | Out-File -FilePath $ChromeManifestPath -Encoding UTF8
        
        # 设置注册表
        New-Item -Path $ChromeRegPath -Force | Out-Null
        Set-ItemProperty -Path $ChromeRegPath -Name "(Default)" -Value $ChromeManifestPath
        
        Write-Host "Chrome配置安装成功: $ChromeManifestPath"
    }}
    catch {{
        Write-Error "Chrome配置安装失败: $_"
    }}
}}

# 创建Firefox配置
function Install-FirefoxHost {{
    try {{
        Write-Host "安装Firefox Native Messaging Host..."
        
        if (!(Test-Path $FirefoxManifestDir)) {{
            New-Item -ItemType Directory -Path $FirefoxManifestDir -Force | Out-Null
        }}
        
        $FirefoxManifest = @{{
            name = $HostName
            description = $AppDescription
            path = $ExecutablePath
            type = "stdio"
            allowed_extensions = @("{firefox_extensions}")
        }} | ConvertTo-Json -Depth 3
        
        $FirefoxManifestPath = Join-Path $FirefoxManifestDir "$HostName.json"
        $FirefoxManifest | Out-File -FilePath $FirefoxManifestPath -Encoding UTF8
        
        # 设置注册表
        New-Item -Path $FirefoxRegPath -Force | Out-Null
        Set-ItemProperty -Path $FirefoxRegPath -Name "(Default)" -Value $FirefoxManifestPath
        
        Write-Host "Firefox配置安装成功: $FirefoxManifestPath"
    }}
    catch {{
        Write-Error "Firefox配置安装失败: $_"
    }}
}}

# 创建Edge配置
function Install-EdgeHost {{
    try {{
        Write-Host "安装Edge Native Messaging Host..."
        
        if (!(Test-Path $EdgeManifestDir)) {{
            New-Item -ItemType Directory -Path $EdgeManifestDir -Force | Out-Null
        }}
        
        $EdgeManifest = @{{
            name = $HostName
            description = $AppDescription
            path = $ExecutablePath
            type = "stdio"
            allowed_origins = @("{edge_origins}")
        }} | ConvertTo-Json -Depth 3
        
        $EdgeManifestPath = Join-Path $EdgeManifestDir "$HostName.json"
        $EdgeManifest | Out-File -FilePath $EdgeManifestPath -Encoding UTF8
        
        # 设置注册表
        New-Item -Path $EdgeRegPath -Force | Out-Null
        Set-ItemProperty -Path $EdgeRegPath -Name "(Default)" -Value $EdgeManifestPath
        
        Write-Host "Edge配置安装成功: $EdgeManifestPath"
    }}
    catch {{
        Write-Error "Edge配置安装失败: $_"
    }}
}}

# 执行安装
Install-ChromeHost
Install-FirefoxHost  
Install-EdgeHost

Write-Host "安装完成！"
Write-Host "请重启浏览器以使配置生效。"
"#,
            host_name = self.config.host_name,
            executable_path = self.config.executable_path.replace("/", "\\"),
            app_description = self.config.app_description,
            chrome_origins = self.config.allowed_origins.join("\", \""),
            firefox_extensions = self.config.allowed_extensions.join("\", \""),
            edge_origins = self.config.allowed_origins.join("\", \""),
        );

        let batch_script = format!(r#"
@echo off
echo 正在安装 Secure Password Manager Native Messaging Host...

REM 检查PowerShell可用性
powershell -Command "Get-Host" >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 需要PowerShell才能运行此安装脚本
    pause
    exit /b 1
)

REM 运行PowerShell安装脚本
powershell -ExecutionPolicy Bypass -File "%~dp0install.ps1"

pause
"#);

        Ok(InstallScript {
            platform: "windows".to_string(),
            primary_script: powershell_script,
            secondary_script: Some(batch_script),
            file_extension: "ps1".to_string(),
            secondary_extension: Some("bat".to_string()),
            instructions: "运行 install.bat 或直接运行 install.ps1（需要管理员权限）".to_string(),
        })
    }

    /// 生成macOS安装脚本
    fn generate_macos_install_script(&self) -> Result<InstallScript> {
        let shell_script = format!(r#"#!/bin/bash
# Secure Password Manager Native Messaging Host 安装脚本
# 平台: macOS

set -e

echo "正在安装 Secure Password Manager Native Messaging Host..."

# 定义变量
HOST_NAME="{host_name}"
EXECUTABLE_PATH="{executable_path}"
APP_DESCRIPTION="{app_description}"

# 安装目录
CHROME_DIR="$HOME/Library/Application Support/Google/Chrome/NativeMessagingHosts"
FIREFOX_DIR="$HOME/Library/Application Support/Mozilla/NativeMessagingHosts"
EDGE_DIR="$HOME/Library/Application Support/Microsoft Edge/NativeMessagingHosts"
SAFARI_DIR="$HOME/Library/Application Support/Safari/NativeMessagingHosts"

# 创建Chrome配置
install_chrome_host() {{
    echo "安装Chrome Native Messaging Host..."
    
    mkdir -p "$CHROME_DIR"
    
    cat > "$CHROME_DIR/$HOST_NAME.json" << EOF
{{
    "name": "$HOST_NAME",
    "description": "$APP_DESCRIPTION",
    "path": "$EXECUTABLE_PATH",
    "type": "stdio",
    "allowed_origins": [
        "{chrome_origins}"
    ]
}}
EOF
    
    echo "Chrome配置安装成功: $CHROME_DIR/$HOST_NAME.json"
}}

# 创建Firefox配置  
install_firefox_host() {{
    echo "安装Firefox Native Messaging Host..."
    
    mkdir -p "$FIREFOX_DIR"
    
    cat > "$FIREFOX_DIR/$HOST_NAME.json" << EOF
{{
    "name": "$HOST_NAME",
    "description": "$APP_DESCRIPTION", 
    "path": "$EXECUTABLE_PATH",
    "type": "stdio",
    "allowed_extensions": [
        "{firefox_extensions}"
    ]
}}
EOF
    
    echo "Firefox配置安装成功: $FIREFOX_DIR/$HOST_NAME.json"
}}

# 创建Edge配置
install_edge_host() {{
    echo "安装Edge Native Messaging Host..."
    
    mkdir -p "$EDGE_DIR"
    
    cat > "$EDGE_DIR/$HOST_NAME.json" << EOF
{{
    "name": "$HOST_NAME",
    "description": "$APP_DESCRIPTION",
    "path": "$EXECUTABLE_PATH", 
    "type": "stdio",
    "allowed_origins": [
        "{edge_origins}"
    ]
}}
EOF
    
    echo "Edge配置安装成功: $EDGE_DIR/$HOST_NAME.json"
}}

# 创建Safari配置
install_safari_host() {{
    echo "安装Safari Native Messaging Host..."
    
    mkdir -p "$SAFARI_DIR"
    
    cat > "$SAFARI_DIR/$HOST_NAME.json" << EOF
{{
    "name": "$HOST_NAME",
    "description": "$APP_DESCRIPTION",
    "path": "$EXECUTABLE_PATH",
    "type": "stdio",
    "safari_extension_bundle_ids": [
        "{safari_bundle_ids}"
    ]
}}
EOF
    
    echo "Safari配置安装成功: $SAFARI_DIR/$HOST_NAME.json"
}}

# 检查可执行文件权限
check_executable() {{
    if [ ! -x "$EXECUTABLE_PATH" ]; then
        echo "警告: 可执行文件不存在或没有执行权限: $EXECUTABLE_PATH"
        echo "请确保应用程序已正确安装。"
    fi
}}

# 执行安装
check_executable
install_chrome_host
install_firefox_host
install_edge_host
install_safari_host

echo "安装完成！"
echo "请重启浏览器以使配置生效。"
"#,
            host_name = self.config.host_name,
            executable_path = self.config.executable_path,
            app_description = self.config.app_description,
            chrome_origins = self.config.allowed_origins.join("\",\n        \""),
            firefox_extensions = self.config.allowed_extensions.join("\",\n        \""),
            edge_origins = self.config.allowed_origins.join("\",\n        \""),
            safari_bundle_ids = self.config.safari_bundle_ids.join("\",\n        \""),
        );

        Ok(InstallScript {
            platform: "macos".to_string(),
            primary_script: shell_script,
            secondary_script: None,
            file_extension: "sh".to_string(),
            secondary_extension: None,
            instructions: "运行 chmod +x install.sh && ./install.sh".to_string(),
        })
    }

    /// 生成Linux安装脚本
    fn generate_linux_install_script(&self) -> Result<InstallScript> {
        let shell_script = format!(r#"#!/bin/bash
# Secure Password Manager Native Messaging Host 安装脚本
# 平台: Linux

set -e

echo "正在安装 Secure Password Manager Native Messaging Host..."

# 定义变量
HOST_NAME="{host_name}"
EXECUTABLE_PATH="{executable_path}"
APP_DESCRIPTION="{app_description}"

# 安装目录
CHROME_DIR="$HOME/.config/google-chrome/NativeMessagingHosts"
FIREFOX_DIR="$HOME/.mozilla/native-messaging-hosts"
EDGE_DIR="$HOME/.config/microsoft-edge/NativeMessagingHosts"

# 创建Chrome配置
install_chrome_host() {{
    echo "安装Chrome Native Messaging Host..."
    
    mkdir -p "$CHROME_DIR"
    
    cat > "$CHROME_DIR/$HOST_NAME.json" << EOF
{{
    "name": "$HOST_NAME",
    "description": "$APP_DESCRIPTION",
    "path": "$EXECUTABLE_PATH",
    "type": "stdio",
    "allowed_origins": [
        "{chrome_origins}"
    ]
}}
EOF
    
    echo "Chrome配置安装成功: $CHROME_DIR/$HOST_NAME.json"
}}

# 创建Firefox配置
install_firefox_host() {{
    echo "安装Firefox Native Messaging Host..."
    
    mkdir -p "$FIREFOX_DIR"
    
    cat > "$FIREFOX_DIR/$HOST_NAME.json" << EOF
{{
    "name": "$HOST_NAME",
    "description": "$APP_DESCRIPTION",
    "path": "$EXECUTABLE_PATH",
    "type": "stdio",
    "allowed_extensions": [
        "{firefox_extensions}"
    ]
}}
EOF
    
    echo "Firefox配置安装成功: $FIREFOX_DIR/$HOST_NAME.json"
}}

# 创建Edge配置
install_edge_host() {{
    echo "安装Edge Native Messaging Host..."
    
    mkdir -p "$EDGE_DIR"
    
    cat > "$EDGE_DIR/$HOST_NAME.json" << EOF
{{
    "name": "$HOST_NAME",
    "description": "$APP_DESCRIPTION",
    "path": "$EXECUTABLE_PATH",
    "type": "stdio",
    "allowed_origins": [
        "{edge_origins}"
    ]
}}
EOF
    
    echo "Edge配置安装成功: $EDGE_DIR/$HOST_NAME.json"
}}

# 检查依赖
check_dependencies() {{
    echo "检查系统依赖..."
    
    # 检查可执行文件
    if [ ! -f "$EXECUTABLE_PATH" ]; then
        echo "错误: 可执行文件不存在: $EXECUTABLE_PATH"
        echo "请先安装 Secure Password Manager 应用程序。"
        exit 1
    fi
    
    if [ ! -x "$EXECUTABLE_PATH" ]; then
        echo "设置可执行权限..."
        chmod +x "$EXECUTABLE_PATH"
    fi
    
    echo "依赖检查完成。"
}}

# 检测已安装的浏览器
detect_browsers() {{
    echo "检测已安装的浏览器..."
    
    INSTALL_CHROME=false
    INSTALL_FIREFOX=false
    INSTALL_EDGE=false
    
    # 检测Chrome
    if command -v google-chrome >/dev/null 2>&1 || command -v google-chrome-stable >/dev/null 2>&1; then
        echo "检测到Chrome浏览器"
        INSTALL_CHROME=true
    fi
    
    # 检测Firefox
    if command -v firefox >/dev/null 2>&1; then
        echo "检测到Firefox浏览器"
        INSTALL_FIREFOX=true
    fi
    
    # 检测Edge
    if command -v microsoft-edge >/dev/null 2>&1 || command -v microsoft-edge-stable >/dev/null 2>&1; then
        echo "检测到Edge浏览器"
        INSTALL_EDGE=true
    fi
}}

# 执行安装
check_dependencies
detect_browsers

if [ "$INSTALL_CHROME" = true ]; then
    install_chrome_host
fi

if [ "$INSTALL_FIREFOX" = true ]; then
    install_firefox_host
fi

if [ "$INSTALL_EDGE" = true ]; then
    install_edge_host
fi

if [ "$INSTALL_CHROME" = false ] && [ "$INSTALL_FIREFOX" = false ] && [ "$INSTALL_EDGE" = false ]; then
    echo "警告: 未检测到支持的浏览器。"
    echo "正在为所有浏览器安装配置文件..."
    install_chrome_host
    install_firefox_host
    install_edge_host
fi

echo "安装完成！"
echo "请重启浏览器以使配置生效。"
"#,
            host_name = self.config.host_name,
            executable_path = self.config.executable_path,
            app_description = self.config.app_description,
            chrome_origins = self.config.allowed_origins.join("\",\n        \""),
            firefox_extensions = self.config.allowed_extensions.join("\",\n        \""),
            edge_origins = self.config.allowed_origins.join("\",\n        \""),
        );

        Ok(InstallScript {
            platform: "linux".to_string(),
            primary_script: shell_script,
            secondary_script: None,
            file_extension: "sh".to_string(),
            secondary_extension: None,
            instructions: "运行 chmod +x install.sh && ./install.sh".to_string(),
        })
    }

    /// 生成卸载脚本
    ///
    /// # 参数
    /// - `platform`: 目标平台
    ///
    /// # 返回
    /// Result<InstallScript> - 生成的卸载脚本
    pub fn generate_uninstall_script(&self, platform: &str) -> Result<InstallScript> {
        match platform.to_lowercase().as_str() {
            "windows" => self.generate_windows_uninstall_script(),
            "macos" => self.generate_macos_uninstall_script(),
            "linux" => self.generate_linux_uninstall_script(),
            _ => Err(NativeMessagingError::ConfigurationError(
                format!("不支持的平台: {}", platform)
            )),
        }
    }

    /// 生成Windows卸载脚本
    fn generate_windows_uninstall_script(&self) -> Result<InstallScript> {
        let powershell_script = format!(r#"
# Secure Password Manager Native Messaging Host 卸载脚本
# 平台: Windows

Write-Host "正在卸载 Secure Password Manager Native Messaging Host..."

$HostName = "{host_name}"

# 注册表路径
$ChromeRegPath = "HKCU:\Software\Google\Chrome\NativeMessagingHosts\$HostName"
$FirefoxRegPath = "HKCU:\Software\Mozilla\NativeMessagingHosts\$HostName"  
$EdgeRegPath = "HKCU:\Software\Microsoft\Edge\NativeMessagingHosts\$HostName"

# 文件路径
$ChromeManifestPath = "$env:LOCALAPPDATA\Google\Chrome\User Data\NativeMessagingHosts\$HostName.json"
$FirefoxManifestPath = "$env:APPDATA\Mozilla\NativeMessagingHosts\$HostName.json"
$EdgeManifestPath = "$env:LOCALAPPDATA\Microsoft\Edge\User Data\NativeMessagingHosts\$HostName.json"

# 卸载Chrome配置
if (Test-Path $ChromeRegPath) {{
    Remove-Item -Path $ChromeRegPath -Force
    Write-Host "已删除Chrome注册表项"
}}

if (Test-Path $ChromeManifestPath) {{
    Remove-Item -Path $ChromeManifestPath -Force
    Write-Host "已删除Chrome配置文件"
}}

# 卸载Firefox配置
if (Test-Path $FirefoxRegPath) {{
    Remove-Item -Path $FirefoxRegPath -Force
    Write-Host "已删除Firefox注册表项"
}}

if (Test-Path $FirefoxManifestPath) {{
    Remove-Item -Path $FirefoxManifestPath -Force
    Write-Host "已删除Firefox配置文件"
}}

# 卸载Edge配置
if (Test-Path $EdgeRegPath) {{
    Remove-Item -Path $EdgeRegPath -Force
    Write-Host "已删除Edge注册表项"
}}

if (Test-Path $EdgeManifestPath) {{
    Remove-Item -Path $EdgeManifestPath -Force
    Write-Host "已删除Edge配置文件"
}}

Write-Host "卸载完成！"
"#, host_name = self.config.host_name);

        Ok(InstallScript {
            platform: "windows".to_string(),
            primary_script: powershell_script,
            secondary_script: None,
            file_extension: "ps1".to_string(),
            secondary_extension: None,
            instructions: "运行 uninstall.ps1（需要管理员权限）".to_string(),
        })
    }

    /// 生成macOS卸载脚本
    fn generate_macos_uninstall_script(&self) -> Result<InstallScript> {
        let shell_script = format!(r#"#!/bin/bash
# Secure Password Manager Native Messaging Host 卸载脚本
# 平台: macOS

echo "正在卸载 Secure Password Manager Native Messaging Host..."

HOST_NAME="{host_name}"

# 配置文件路径
CHROME_MANIFEST="$HOME/Library/Application Support/Google/Chrome/NativeMessagingHosts/$HOST_NAME.json"
FIREFOX_MANIFEST="$HOME/Library/Application Support/Mozilla/NativeMessagingHosts/$HOST_NAME.json"
EDGE_MANIFEST="$HOME/Library/Application Support/Microsoft Edge/NativeMessagingHosts/$HOST_NAME.json"
SAFARI_MANIFEST="$HOME/Library/Application Support/Safari/NativeMessagingHosts/$HOST_NAME.json"

# 删除配置文件
if [ -f "$CHROME_MANIFEST" ]; then
    rm "$CHROME_MANIFEST"
    echo "已删除Chrome配置文件"
fi

if [ -f "$FIREFOX_MANIFEST" ]; then
    rm "$FIREFOX_MANIFEST"
    echo "已删除Firefox配置文件"
fi

if [ -f "$EDGE_MANIFEST" ]; then
    rm "$EDGE_MANIFEST"
    echo "已删除Edge配置文件"
fi

if [ -f "$SAFARI_MANIFEST" ]; then
    rm "$SAFARI_MANIFEST"
    echo "已删除Safari配置文件"
fi

echo "卸载完成！"
"#, host_name = self.config.host_name);

        Ok(InstallScript {
            platform: "macos".to_string(),
            primary_script: shell_script,
            secondary_script: None,
            file_extension: "sh".to_string(),
            secondary_extension: None,
            instructions: "运行 chmod +x uninstall.sh && ./uninstall.sh".to_string(),
        })
    }

    /// 生成Linux卸载脚本
    fn generate_linux_uninstall_script(&self) -> Result<InstallScript> {
        let shell_script = format!(r#"#!/bin/bash
# Secure Password Manager Native Messaging Host 卸载脚本
# 平台: Linux

echo "正在卸载 Secure Password Manager Native Messaging Host..."

HOST_NAME="{host_name}"

# 配置文件路径
CHROME_MANIFEST="$HOME/.config/google-chrome/NativeMessagingHosts/$HOST_NAME.json"
FIREFOX_MANIFEST="$HOME/.mozilla/native-messaging-hosts/$HOST_NAME.json"  
EDGE_MANIFEST="$HOME/.config/microsoft-edge/NativeMessagingHosts/$HOST_NAME.json"

# 删除配置文件
if [ -f "$CHROME_MANIFEST" ]; then
    rm "$CHROME_MANIFEST"
    echo "已删除Chrome配置文件"
fi

if [ -f "$FIREFOX_MANIFEST" ]; then
    rm "$FIREFOX_MANIFEST"
    echo "已删除Firefox配置文件"
fi

if [ -f "$EDGE_MANIFEST" ]; then
    rm "$EDGE_MANIFEST"
    echo "已删除Edge配置文件"
fi

echo "卸载完成！"
"#, host_name = self.config.host_name);

        Ok(InstallScript {
            platform: "linux".to_string(),
            primary_script: shell_script,
            secondary_script: None,
            file_extension: "sh".to_string(),
            secondary_extension: None,
            instructions: "运行 chmod +x uninstall.sh && ./uninstall.sh".to_string(),
        })
    }

    /// 生成README文档
    ///
    /// # 返回
    /// String - README文档内容
    pub fn generate_readme(&self) -> String {
        format!(r#"# Secure Password Manager Native Messaging Host

## 概述

{app_description}

这是一个跨平台的Native Messaging Host，支持以下浏览器：
- Google Chrome
- Mozilla Firefox  
- Microsoft Edge
- Safari (仅限macOS)

## 安装

### 自动安装

**Windows:**
```cmd
# 使用PowerShell (推荐)
install.ps1

# 或使用批处理文件
install.bat
```

**macOS:**
```bash
chmod +x install.sh
./install.sh
```

**Linux:**
```bash
chmod +x install.sh
./install.sh
```

### 手动安装

#### Chrome
配置文件位置：
- Windows: `%LOCALAPPDATA%\Google\Chrome\User Data\NativeMessagingHosts\{host_name}.json`
- macOS: `~/Library/Application Support/Google/Chrome/NativeMessagingHosts/{host_name}.json`
- Linux: `~/.config/google-chrome/NativeMessagingHosts/{host_name}.json`

#### Firefox
配置文件位置：
- Windows: `%APPDATA%\Mozilla\NativeMessagingHosts\{host_name}.json`
- macOS: `~/Library/Application Support/Mozilla/NativeMessagingHosts/{host_name}.json`
- Linux: `~/.mozilla/native-messaging-hosts/{host_name}.json`

#### Edge
配置文件位置：
- Windows: `%LOCALAPPDATA%\Microsoft\Edge\User Data\NativeMessagingHosts\{host_name}.json`
- macOS: `~/Library/Application Support/Microsoft Edge/NativeMessagingHosts/{host_name}.json`
- Linux: `~/.config/microsoft-edge/NativeMessagingHosts/{host_name}.json`

#### Safari (仅限macOS)
配置文件位置：
- macOS: `~/Library/Application Support/Safari/NativeMessagingHosts/{host_name}.json`

## 配置

### Host配置
```json
{{
  "name": "{host_name}",
  "description": "{app_description}",
  "path": "{executable_path}",
  "type": "stdio"
}}
```

### 扩展配置
请确保您的浏览器扩展ID在以下列表中：
- 允许的扩展ID: {allowed_extensions}
- Safari Bundle ID: {safari_bundle_ids}

## 卸载

**Windows:**
```cmd
uninstall.ps1
```

**macOS/Linux:**
```bash
chmod +x uninstall.sh
./uninstall.sh
```

## 故障排除

### 常见问题

1. **扩展无法连接到Native Host**
   - 检查配置文件是否存在且格式正确
   - 确认可执行文件路径是否正确
   - 检查扩展ID是否在允许列表中

2. **权限问题**
   - 确保可执行文件有执行权限
   - 在Windows上，可能需要管理员权限

3. **路径问题**
   - 检查可执行文件路径是否使用绝对路径
   - 确认文件确实存在

### 日志检查

Chrome日志位置：
- Windows: `%LOCALAPPDATA%\Google\Chrome\User Data\Default\Extensions\[extension-id]\`
- macOS: `~/Library/Application Support/Google/Chrome/Default/Extensions/[extension-id]/`
- Linux: `~/.config/google-chrome/Default/Extensions/[extension-id]/`

Firefox日志：
打开 `about:debugging` → This Firefox → 您的扩展 → Inspect

## 技术支持

如果遇到问题，请提供以下信息：
- 操作系统版本
- 浏览器版本
- 错误信息
- 配置文件内容

版本: {version}
"#,
            app_description = self.config.app_description,
            host_name = self.config.host_name,
            executable_path = self.config.executable_path,
            allowed_extensions = self.config.allowed_extensions.join(", "),
            safari_bundle_ids = self.config.safari_bundle_ids.join(", "),
            version = self.config.version,
        )
    }

    /// 清空模板缓存
    pub fn clear_cache(&mut self) {
        self.template_cache.clear();
    }

    /// 获取配置信息
    pub fn get_config(&self) -> &TemplateConfig {
        &self.config
    }

    /// 更新配置信息
    pub fn update_config(&mut self, config: TemplateConfig) {
        self.config = config;
        self.clear_cache(); // 清空缓存以确保使用新配置
    }
}

/// 安装脚本
#[derive(Debug, Clone)]
pub struct InstallScript {
    /// 目标平台
    pub platform: String,
    /// 主要脚本内容
    pub primary_script: String,
    /// 次要脚本内容（如批处理文件）
    pub secondary_script: Option<String>,
    /// 主要文件扩展名
    pub file_extension: String,
    /// 次要文件扩展名
    pub secondary_extension: Option<String>,
    /// 安装说明
    pub instructions: String,
}

impl InstallScript {
    /// 获取主要脚本文件名
    pub fn primary_filename(&self) -> String {
        format!("install.{}", self.file_extension)
    }

    /// 获取次要脚本文件名
    pub fn secondary_filename(&self) -> Option<String> {
        self.secondary_extension.as_ref().map(|ext| format!("install.{}", ext))
    }

    /// 获取卸载脚本文件名
    pub fn uninstall_filename(&self) -> String {
        format!("uninstall.{}", self.file_extension)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_template_manager_creation() {
        let manager = ConfigTemplateManager::new_default();
        assert!(!manager.config.app_name.is_empty());
        assert!(!manager.config.host_name.is_empty());
    }

    #[test]
    fn test_chrome_manifest_generation() {
        let mut manager = ConfigTemplateManager::new_default();
        let manifest = manager.generate_chrome_manifest().unwrap();
        
        assert!(manifest.contains("com.securepassword.nativemessaging"));
        assert!(manifest.contains("stdio"));
        
        // 验证JSON格式
        let parsed: Value = serde_json::from_str(&manifest).unwrap();
        assert!(parsed.get("name").is_some());
        assert!(parsed.get("path").is_some());
    }

    #[test]
    fn test_firefox_manifest_generation() {
        let mut manager = ConfigTemplateManager::new_default();
        let manifest = manager.generate_firefox_manifest().unwrap();
        
        assert!(manifest.contains("com.securepassword.nativemessaging"));
        
        let parsed: Value = serde_json::from_str(&manifest).unwrap();
        assert!(parsed.get("allowed_extensions").is_some());
    }

    #[test]
    fn test_install_script_generation() {
        let manager = ConfigTemplateManager::new_default();
        
        // 测试Windows脚本
        let windows_script = manager.generate_install_script("windows").unwrap();
        assert_eq!(windows_script.platform, "windows");
        assert!(windows_script.primary_script.contains("PowerShell"));
        assert!(windows_script.secondary_script.is_some());
        
        // 测试macOS脚本
        let macos_script = manager.generate_install_script("macos").unwrap();
        assert_eq!(macos_script.platform, "macos");
        assert!(macos_script.primary_script.contains("#!/bin/bash"));
        
        // 测试Linux脚本
        let linux_script = manager.generate_install_script("linux").unwrap();
        assert_eq!(linux_script.platform, "linux");
        assert!(linux_script.primary_script.contains("#!/bin/bash"));
    }

    #[test]
    fn test_uninstall_script_generation() {
        let manager = ConfigTemplateManager::new_default();
        
        for platform in &["windows", "macos", "linux"] {
            let uninstall_script = manager.generate_uninstall_script(platform).unwrap();
            assert_eq!(uninstall_script.platform, *platform);
            assert!(uninstall_script.primary_script.contains("卸载"));
        }
    }

    #[test]
    fn test_readme_generation() {
        let manager = ConfigTemplateManager::new_default();
        let readme = manager.generate_readme();
        
        assert!(readme.contains("Secure Password Manager"));
        assert!(readme.contains("## 安装"));
        assert!(readme.contains("## 配置"));
        assert!(readme.contains("## 故障排除"));
    }

    #[test]
    fn test_template_caching() {
        let mut manager = ConfigTemplateManager::new_default();
        
        // 第一次生成
        let manifest1 = manager.generate_chrome_manifest().unwrap();
        assert_eq!(manager.template_cache.len(), 1);
        
        // 第二次生成（应该使用缓存）
        let manifest2 = manager.generate_chrome_manifest().unwrap();
        assert_eq!(manifest1, manifest2);
        assert_eq!(manager.template_cache.len(), 1);
        
        // 清空缓存
        manager.clear_cache();
        assert_eq!(manager.template_cache.len(), 0);
    }

    #[test]
    fn test_config_update() {
        let mut manager = ConfigTemplateManager::new_default();
        
        // 生成初始配置
        let _manifest1 = manager.generate_chrome_manifest().unwrap();
        assert_eq!(manager.template_cache.len(), 1);
        
        // 更新配置
        let mut new_config = TemplateConfig::default();
        new_config.host_name = "com.example.newhost".to_string();
        
        manager.update_config(new_config);
        assert_eq!(manager.template_cache.len(), 0); // 缓存应该被清空
        assert_eq!(manager.config.host_name, "com.example.newhost");
    }
} 