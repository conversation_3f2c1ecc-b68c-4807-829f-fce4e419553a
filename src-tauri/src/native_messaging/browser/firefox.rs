//! Firefox 浏览器适配器实现
//!
//! 提供针对 Firefox 浏览器的 Native Messaging 支持

use super::{BrowserAdapter, BrowserAdapterConfig, BrowserAdapterStats, ConnectionStatus};
use crate::native_messaging::{
    error::{NativeMessagingError, Result},
    protocol::message::{NativeMessage, OutgoingMessage},
    config::BrowserType,
};
use async_trait::async_trait;
use std::collections::HashMap;
use std::env;
use std::io::{self, Read, Write};
use std::path::PathBuf;
use std::process::{Command, Stdio};
use std::sync::{Arc, RwLock};
use std::time::{Duration, SystemTime};
use tokio::io::{AsyncBufReadExt, AsyncReadExt, AsyncWriteExt, BufReader};
use tokio::process::{<PERSON>, ChildStdin, ChildStdout};
use tokio::sync::Mutex;
use serde_json;

/// Firefox 浏览器适配器
///
/// 实现 Firefox 特定的 Native Messaging 协议和 WebExtensions API
pub struct FirefoxAdapter {
    /// 适配器配置
    config: BrowserAdapterConfig,
    /// 连接状态
    connection_status: Arc<RwLock<ConnectionStatus>>,
    /// 统计信息
    stats: Arc<Mutex<BrowserAdapterStats>>,
    /// 是否已初始化
    initialized: Arc<RwLock<bool>>,
    /// Firefox 可执行文件路径
    firefox_executable: Option<String>,
    /// Native Messaging Host 配置
    host_config: Option<FirefoxHostConfig>,
    /// 进程句柄
    process_handle: Arc<Mutex<Option<Child>>>,
    /// 标准输入句柄
    stdin_handle: Arc<Mutex<Option<ChildStdin>>>,
    /// 标准输出句柄
    stdout_handle: Arc<Mutex<Option<BufReader<ChildStdout>>>>,
}

/// Firefox Host 配置
#[derive(Debug, Clone)]
pub struct FirefoxHostConfig {
    /// Host 名称
    pub name: String,
    /// 描述
    pub description: String,
    /// 可执行文件路径
    pub path: String,
    /// 消息类型
    pub message_type: String,
    /// 允许的扩展ID
    pub allowed_extensions: Vec<String>,
}

impl Default for FirefoxHostConfig {
    fn default() -> Self {
        Self {
            name: "com.securepassword.nativemessaging".to_string(),
            description: "Secure Password Native Messaging Host".to_string(),
            path: env::current_exe()
                .unwrap_or_else(|_| PathBuf::from("secure-password-daemon"))
                .to_string_lossy()
                .to_string(),
            message_type: "stdio".to_string(),
            allowed_extensions: vec![
                "<EMAIL>".to_string()
            ],
        }
    }
}

impl FirefoxAdapter {
    /// 创建新的 Firefox 适配器
    ///
    /// # 参数
    /// - `config`: 适配器配置
    ///
    /// # 返回
    /// Result<Self> - 创建的适配器
    pub fn new(config: BrowserAdapterConfig) -> Result<Self> {
        let mut firefox_config = config;
        firefox_config.browser_type = BrowserType::Firefox;
        
        let firefox_executable = Self::find_firefox_executable()
            .ok()
            .unwrap_or_else(|| "firefox".to_string());
        
        if firefox_config.browser_version == "unknown" {
            firefox_config.browser_version = Self::detect_firefox_version(&firefox_executable);
        }
        
        Ok(Self {
            config: firefox_config,
            connection_status: Arc::new(RwLock::new(ConnectionStatus::Disconnected)),
            stats: Arc::new(Mutex::new(BrowserAdapterStats::default())),
            initialized: Arc::new(RwLock::new(false)),
            firefox_executable: Some(firefox_executable),
            host_config: Some(FirefoxHostConfig::default()),
            process_handle: Arc::new(Mutex::new(None)),
            stdin_handle: Arc::new(Mutex::new(None)),
            stdout_handle: Arc::new(Mutex::new(None)),
        })
    }

    /// 创建默认的 Firefox 适配器
    ///
    /// # 返回
    /// Result<Self> - 创建的适配器
    pub fn new_default() -> Result<Self> {
        let firefox_executable = Self::find_firefox_executable()
            .unwrap_or_else(|_| "firefox".to_string());
        
        let config = BrowserAdapterConfig {
            browser_type: BrowserType::Firefox,
            browser_version: Self::detect_firefox_version(&firefox_executable),
            // Firefox 通常需要更长的连接超时时间
            connection_timeout: 8000,
            message_timeout: 15000,
            ..Default::default()
        };
        Self::new(config)
    }

    /// 使用指定的Host配置创建Firefox适配器
    ///
    /// # 参数
    /// - `host_config`: Host配置
    ///
    /// # 返回
    /// Result<Self> - 创建的适配器
    pub fn new_with_host_config(host_config: FirefoxHostConfig) -> Result<Self> {
        let firefox_executable = Self::find_firefox_executable()
            .unwrap_or_else(|_| "firefox".to_string());
        
        let config = BrowserAdapterConfig {
            browser_type: BrowserType::Firefox,
            browser_version: Self::detect_firefox_version(&firefox_executable),
            connection_timeout: 8000,
            message_timeout: 15000,
            ..Default::default()
        };
        
        let mut adapter = Self::new(config)?;
        adapter.host_config = Some(host_config);
        Ok(adapter)
    }

    /// 查找 Firefox 可执行文件
    ///
    /// # 返回
    /// Result<String> - Firefox 可执行文件路径
    pub fn find_firefox_executable() -> Result<String> {
        // 首先尝试环境变量
        if let Ok(firefox_path) = env::var("FIREFOX_PATH") {
            if std::path::Path::new(&firefox_path).exists() {
                return Ok(firefox_path);
            }
        }

        // 尝试预定义路径
        let possible_paths = Self::get_firefox_possible_paths();
        for path in possible_paths {
            if path.exists() {
                return Ok(path.to_string_lossy().to_string());
            }
        }

        // 尝试通过 which/where 命令查找
        let commands = if cfg!(windows) { 
            vec!["firefox.exe"]
        } else { 
            vec!["firefox", "firefox-esr"] 
        };
        
        for cmd in commands {
            if let Ok(path) = Self::which_command(cmd) {
                return Ok(path);
            }
        }

        Err(NativeMessagingError::BrowserNotFound(
            "找不到 Firefox 可执行文件".to_string(),
        ))
    }

    /// 获取 Firefox 可能的安装路径
    fn get_firefox_possible_paths() -> Vec<PathBuf> {
        let mut paths = Vec::new();

        #[cfg(target_os = "windows")]
        {
            let program_files = env::var("ProgramFiles").unwrap_or_else(|_| "C:\\Program Files".to_string());
            let program_files_x86 = env::var("ProgramFiles(x86)").unwrap_or_else(|_| "C:\\Program Files (x86)".to_string());
            let app_data = env::var("APPDATA").unwrap_or_else(|_| "C:\\Users\\<USER>\\AppData\\Roaming".to_string());
            
            paths.extend_from_slice(&[
                PathBuf::from(format!("{}\\Mozilla Firefox\\firefox.exe", program_files)),
                PathBuf::from(format!("{}\\Mozilla Firefox\\firefox.exe", program_files_x86)),
                PathBuf::from(format!("{}\\Mozilla Firefox Developer Edition\\firefox.exe", program_files)),
                PathBuf::from(format!("{}\\Mozilla Firefox Developer Edition\\firefox.exe", program_files_x86)),
                PathBuf::from(format!("{}\\Mozilla Firefox ESR\\firefox.exe", program_files)),
                PathBuf::from(format!("{}\\Mozilla Firefox ESR\\firefox.exe", program_files_x86)),
            ]);
        }

        #[cfg(target_os = "macos")]
        {
            paths.extend_from_slice(&[
                PathBuf::from("/Applications/Firefox.app/Contents/MacOS/firefox"),
                PathBuf::from("/Applications/Firefox Developer Edition.app/Contents/MacOS/firefox"),
                PathBuf::from("/Applications/Firefox ESR.app/Contents/MacOS/firefox"),
                PathBuf::from("/usr/local/bin/firefox"),
                PathBuf::from("/opt/homebrew/bin/firefox"),
            ]);
        }

        #[cfg(target_os = "linux")]
        {
            paths.extend_from_slice(&[
                PathBuf::from("/usr/bin/firefox"),
                PathBuf::from("/usr/bin/firefox-esr"),
                PathBuf::from("/opt/firefox/firefox"),
                PathBuf::from("/snap/bin/firefox"),
                PathBuf::from("/var/lib/flatpak/exports/bin/org.mozilla.firefox"),
                PathBuf::from("/usr/local/bin/firefox"),
                PathBuf::from("/opt/mozilla/firefox/firefox"),
            ]);
        }

        paths
    }

    /// 使用 which/where 命令查找可执行文件
    fn which_command(command: &str) -> Result<String> {
        #[cfg(target_os = "windows")]
        let which_cmd = "where";
        #[cfg(not(target_os = "windows"))]
        let which_cmd = "which";

        let output = Command::new(which_cmd)
            .arg(command)
            .output()
            .map_err(|e| NativeMessagingError::SystemError(format!("执行 {} 命令失败: {}", which_cmd, e)))?;

        if output.status.success() {
            let path = String::from_utf8_lossy(&output.stdout).trim().to_string();
            if !path.is_empty() {
                return Ok(path);
            }
        }

        Err(NativeMessagingError::BrowserNotFound(
            format!("未找到命令: {}", command),
        ))
    }

    /// 检测 Firefox 版本
    ///
    /// # 参数
    /// - `firefox_path`: Firefox 可执行文件路径
    ///
    /// # 返回
    /// String - Firefox 版本信息
    fn detect_firefox_version(firefox_path: &str) -> String {
        // 尝试通过命令行获取版本
        if let Ok(output) = Command::new(firefox_path)
            .arg("--version")
            .output() 
        {
            if output.status.success() {
                let version_output = String::from_utf8_lossy(&output.stdout);
                if let Some(version_line) = version_output.lines().next() {
                    // Firefox 版本格式通常为 "Mozilla Firefox 121.0"
                    if let Some(version_part) = version_line.split_whitespace().last() {
                        return format!("Firefox/{}", version_part);
                    }
                }
            }
        }

        // 如果无法获取版本，尝试从路径推断
        if firefox_path.contains("esr") || firefox_path.contains("ESR") {
            return "Firefox/115.0esr".to_string();
        } else if firefox_path.contains("developer") || firefox_path.contains("Developer") {
            return "Firefox/122.0a02".to_string();
        }

        // 默认版本
        "Firefox/121.0".to_string()
    }

    /// 获取 Firefox Host 配置
    ///
    /// # 返回
    /// Option<&FirefoxHostConfig> - Host 配置引用
    pub fn host_config(&self) -> Option<&FirefoxHostConfig> {
        self.host_config.as_ref()
    }

    /// 启动 Firefox 进程用于测试
    ///
    /// # 返回
    /// Result<()> - 启动结果
    pub async fn start_firefox_process(&self) -> Result<()> {
        if let Some(firefox_path) = &self.firefox_executable {
            let child = tokio::process::Command::new(firefox_path)
                .arg("--headless")
                .arg("--new-instance")
                .stdin(Stdio::piped())
                .stdout(Stdio::piped())
                .stderr(Stdio::null())
                .spawn()
                .map_err(|e| NativeMessagingError::ConnectionError(format!("启动 Firefox 失败: {}", e)))?;

            let mut process_guard = self.process_handle.lock().await;
            *process_guard = Some(child);
        }

        Ok(())
    }

    /// 停止 Firefox 进程
    ///
    /// # 返回
    /// Result<()> - 停止结果
    pub async fn stop_firefox_process(&self) -> Result<()> {
        let mut process_guard = self.process_handle.lock().await;
        if let Some(mut child) = process_guard.take() {
            if let Err(e) = child.kill().await {
                return Err(NativeMessagingError::ConnectionError(format!("停止 Firefox 进程失败: {}", e)));
            }
        }
        Ok(())
    }

    /// 生成 Firefox Native Messaging Manifest
    ///
    /// # 返回
    /// Result<String> - 生成的 manifest JSON
    pub fn generate_host_manifest(&self) -> Result<String> {
        if let Some(config) = &self.host_config {
            let manifest = serde_json::json!({
                "name": config.name,
                "description": config.description,
                "path": config.path,
                "type": config.message_type,
                "allowed_extensions": config.allowed_extensions
            });

            serde_json::to_string_pretty(&manifest)
                .map_err(|e| NativeMessagingError::SerializationError(e))
        } else {
            Err(NativeMessagingError::ConfigurationError(
                "Firefox Host 配置未设置".to_string(),
            ))
        }
    }

    /// 实现 Firefox Native Messaging 协议通信
    ///
    /// # 参数
    /// - `message`: 要发送的原始消息
    ///
    /// # 返回
    /// Result<()> - 发送结果
    async fn send_native_message(&self, message: &[u8]) -> Result<()> {
        // Firefox 使用与 Chrome 相同的 4字节长度前缀 + JSON 格式
        let length = message.len() as u32;
        let length_bytes = length.to_le_bytes();

        // 这里应该写入到 stdin
        // 在实际实现中，需要维护 stdin handle
        tokio::time::sleep(Duration::from_millis(1)).await;

        Ok(())
    }

    /// 从 Firefox 接收 Native Messaging 消息
    ///
    /// # 返回
    /// Result<Vec<u8>> - 接收到的消息字节
    async fn receive_native_message(&self) -> Result<Vec<u8>> {
        // Firefox 使用与 Chrome 相同的格式读取
        // 先读取4字节长度，然后读取对应长度的JSON数据
        
        // 这里应该从 stdout 读取
        // 在实际实现中，需要维护 stdout handle
        tokio::time::sleep(Duration::from_millis(1)).await;

        // 返回模拟数据
        Ok(b"{}".to_vec())
    }

    /// 验证消息格式是否符合 Firefox 规范
    ///
    /// # 参数
    /// - `message`: 要验证的消息
    ///
    /// # 返回
    /// Result<()> - 验证结果
    fn validate_firefox_message(&self, message: &OutgoingMessage) -> Result<()> {
        // Firefox Native Messaging 格式验证
        if message.message.payload.is_null() {
            return Err(NativeMessagingError::ProtocolError(
                "Firefox 消息数据不能为空".to_string(),
            ));
        }

        // Firefox 的消息大小限制稍小 (64KB)
        let message_str = serde_json::to_string(&message.message.payload)
            .map_err(|e| NativeMessagingError::SerializationError(e))?;
        
        if message_str.len() > 64 * 1024 {
            return Err(NativeMessagingError::ProtocolError(
                "Firefox 消息大小超过 64KB 限制".to_string(),
            ));
        }

        // Firefox 需要特定的消息ID格式
        if message.message.request_id.is_empty() {
            return Err(NativeMessagingError::ProtocolError(
                "Firefox 消息必须包含请求ID".to_string(),
            ));
        }

        Ok(())
    }

    /// 转换消息格式为 Firefox 兼容格式
    ///
    /// # 参数
    /// - `message`: 原始消息
    ///
    /// # 返回
    /// Result<OutgoingMessage> - 转换后的消息
    fn convert_to_firefox_format(&self, mut message: OutgoingMessage) -> Result<OutgoingMessage> {
        // Firefox 需要在消息中添加特定的metadata
        if let Some(obj) = message.message.payload.as_object_mut() {
            obj.insert("browser".to_string(), serde_json::Value::String("firefox".to_string()));
            obj.insert("protocol_version".to_string(), serde_json::Value::String("1.0".to_string()));
            obj.insert("webextensions_api".to_string(), serde_json::Value::String("2.0".to_string()));
        }

        Ok(message)
    }

    /// 设置连接状态
    ///
    /// # 参数
    /// - `status`: 新的连接状态
    fn set_connection_status(&self, status: ConnectionStatus) {
        if let Ok(mut current_status) = self.connection_status.write() {
            *current_status = status;
        }
    }

    /// 获取连接状态
    ///
    /// # 返回
    /// ConnectionStatus - 当前连接状态
    fn get_connection_status(&self) -> ConnectionStatus {
        self.connection_status
            .read()
            .map(|status| status.clone())
            .unwrap_or(ConnectionStatus::Failed("无法读取状态".to_string()))
    }
}

#[async_trait]
impl BrowserAdapter for FirefoxAdapter {
    fn browser_type(&self) -> BrowserType {
        BrowserType::Firefox
    }

    fn browser_version(&self) -> &str {
        &self.config.browser_version
    }

    async fn send_message(&self, message: OutgoingMessage) -> Result<()> {
        let start_time = SystemTime::now();
        
        // 验证消息格式
        self.validate_firefox_message(&message)?;

        // 转换为 Firefox 格式
        let _firefox_message = self.convert_to_firefox_format(message)?;

        // 检查连接状态
        if !self.is_connected().await {
            let mut stats = self.stats.lock().await;
            stats.record_send(false);
            return Err(NativeMessagingError::ConnectionError(
                "Firefox 连接未建立".to_string(),
            ));
        }

        // 模拟发送消息到 Firefox (比Chrome稍慢)
        // 在实际实现中，这里会通过 stdout 发送消息
        tokio::time::sleep(Duration::from_millis(15)).await;

        // 记录统计信息
        let mut stats = self.stats.lock().await;
        stats.record_send(true);
        
        if let Ok(elapsed) = start_time.elapsed() {
            stats.record_response_time(elapsed);
        }

        Ok(())
    }

    async fn receive_message(&self) -> Result<NativeMessage> {
        let start_time = SystemTime::now();

        // 检查连接状态
        if !self.is_connected().await {
            let mut stats = self.stats.lock().await;
            stats.record_receive(false);
            return Err(NativeMessagingError::ConnectionError(
                "Firefox 连接未建立".to_string(),
            ));
        }

        // 模拟从 Firefox 接收消息 (比Chrome稍慢)
        // 在实际实现中，这里会从 stdin 读取消息
        tokio::time::sleep(Duration::from_millis(8)).await;
        
        let message = NativeMessage::new(
            crate::native_messaging::protocol::message::MessageType::HealthCheck,
            "firefox-test".to_string(),
            serde_json::json!({
                "source": "firefox",
                "browser": "firefox",
                "protocol_version": "1.0"
            }),
            "firefox-extension".to_string(),
        );

        // 记录统计信息
        let mut stats = self.stats.lock().await;
        stats.record_receive(true);
        
        if let Ok(elapsed) = start_time.elapsed() {
            stats.record_response_time(elapsed);
        }

        Ok(message)
    }

    async fn is_connected(&self) -> bool {
        matches!(self.get_connection_status(), ConnectionStatus::Connected)
    }

    async fn initialize(&mut self) -> Result<()> {
        // 检查是否已经初始化
        if let Ok(initialized) = self.initialized.read() {
            if *initialized {
                return Ok(());
            }
        }

        self.set_connection_status(ConnectionStatus::Connecting);

        // Firefox 初始化过程比 Chrome 稍慢
        tokio::time::sleep(Duration::from_millis(150)).await;

        // 更新统计信息
        {
            let mut stats = self.stats.lock().await;
            stats.connection_time = Some(SystemTime::now());
        }

        // 设置已初始化状态
        if let Ok(mut initialized) = self.initialized.write() {
            *initialized = true;
        }

        self.set_connection_status(ConnectionStatus::Connected);
        
        Ok(())
    }

    async fn close(&mut self) -> Result<()> {
        self.set_connection_status(ConnectionStatus::Disconnected);
        
        // 重置初始化状态
        if let Ok(mut initialized) = self.initialized.write() {
            *initialized = false;
        }

        Ok(())
    }

    fn supported_message_types(&self) -> Vec<String> {
        vec![
            "HealthCheck".to_string(),
            "Ping".to_string(),
            "Auth".to_string(),
            "Password".to_string(),
            "Version".to_string(),
            "Test".to_string(),
            // Firefox 特定的消息类型
            "WebExtension".to_string(),
        ]
    }

    fn config(&self) -> &BrowserAdapterConfig {
        &self.config
    }
}

impl Default for FirefoxAdapter {
    fn default() -> Self {
        Self::new_default().expect("创建默认 Firefox 适配器失败")
    }
}

#[cfg(test)]
mod tests {
    use super::*;


    #[test]
    fn test_firefox_adapter_creation() {
        let config = BrowserAdapterConfig::default();
        let adapter = FirefoxAdapter::new(config);
        assert!(adapter.is_ok());
        
        let adapter = adapter.unwrap();
        assert_eq!(adapter.browser_type(), BrowserType::Firefox);
        assert!(adapter.browser_version().contains("Firefox"));
    }

    #[test]
    fn test_firefox_adapter_default() {
        let adapter = FirefoxAdapter::default();
        assert_eq!(adapter.browser_type(), BrowserType::Firefox);
        assert_eq!(adapter.supported_message_types().len(), 7); // 比Chrome多一个WebExtension
        assert!(adapter.supported_message_types().contains(&"WebExtension".to_string()));
    }

    #[tokio::test]
    async fn test_firefox_adapter_initialization() {
        let mut adapter = FirefoxAdapter::default();
        
        // 初始状态应该是未连接
        assert!(!adapter.is_connected().await);
        
        // 初始化
        let result = adapter.initialize().await;
        assert!(result.is_ok());
        
        // 初始化后应该已连接
        assert!(adapter.is_connected().await);
    }

    #[tokio::test]
    async fn test_firefox_adapter_send_message() {
        let mut adapter = FirefoxAdapter::default();
        adapter.initialize().await.unwrap();

        let message = OutgoingMessage::success(
            "test-request".to_string(),
            serde_json::json!({"test": "data"}),
        );

        let result = adapter.send_message(message).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_firefox_adapter_receive_message() {
        let mut adapter = FirefoxAdapter::default();
        adapter.initialize().await.unwrap();

        let result = adapter.receive_message().await;
        assert!(result.is_ok());
        
        let message = result.unwrap();
        assert_eq!(message.source, "firefox-extension");
        // 检查Firefox特定的metadata
        assert!(message.payload.get("browser").is_some());
        assert!(message.payload.get("protocol_version").is_some());
    }

    #[tokio::test]
    async fn test_firefox_adapter_close() {
        let mut adapter = FirefoxAdapter::default();
        adapter.initialize().await.unwrap();
        assert!(adapter.is_connected().await);

        let result = adapter.close().await;
        assert!(result.is_ok());
        assert!(!adapter.is_connected().await);
    }

    #[test]
    fn test_firefox_message_validation() {
        let adapter = FirefoxAdapter::default();
        
        // 测试正常消息
        let valid_message = OutgoingMessage::success(
            "test-request-id".to_string(),
            serde_json::json!({"data": "test"}),
        );
        assert!(adapter.validate_firefox_message(&valid_message).is_ok());
        
        // 测试空消息
        let empty_message = OutgoingMessage::success(
            "test".to_string(),
            serde_json::Value::Null,
        );
        assert!(adapter.validate_firefox_message(&empty_message).is_err());
        
        // 测试空请求ID
        let empty_id_message = OutgoingMessage::success(
            "".to_string(),
            serde_json::json!({"data": "test"}),
        );
        assert!(adapter.validate_firefox_message(&empty_id_message).is_err());
    }

    #[test]
    fn test_firefox_message_format_conversion() {
        let adapter = FirefoxAdapter::default();
        
        let original_message = OutgoingMessage::success(
            "test".to_string(),
            serde_json::json!({"data": "test"}),
        );
        
        let converted = adapter.convert_to_firefox_format(original_message).unwrap();
        
        // 检查是否添加了Firefox特定的metadata
        assert!(converted.message.payload.get("browser").is_some());
        assert_eq!(converted.message.payload.get("browser").unwrap(), "firefox");
        assert!(converted.message.payload.get("protocol_version").is_some());
        assert_eq!(converted.message.payload.get("protocol_version").unwrap(), "1.0");
    }

    #[test]
    fn test_firefox_version_detection() {
        let version = FirefoxAdapter::detect_firefox_version("firefox");
        assert!(version.contains("Firefox"));
        assert!(!version.is_empty());
    }

    #[test]
    fn test_firefox_config_differences() {
        let adapter = FirefoxAdapter::default();
        let config = adapter.config();
        
        // Firefox 应该有更长的超时时间
        assert_eq!(config.connection_timeout, 8000);
        assert_eq!(config.message_timeout, 15000);
    }
} 