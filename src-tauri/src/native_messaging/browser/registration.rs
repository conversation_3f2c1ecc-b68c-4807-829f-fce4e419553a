//! 浏览器注册管理器
//!
//! 提供跨浏览器的Native Messaging Host注册和配置管理

use super::{
    chrome::{ChromeAdapter, ChromeHostConfig},
    firefox::{FirefoxAdapter, FirefoxHostConfig}, 
    edge::{EdgeAdapter, EdgeHostConfig},
    safari::{SafariAdapter, SafariHostConfig},
    BrowserDetector,
};
use crate::native_messaging::{
    error::{NativeMessagingError, Result},
    config::BrowserType,
};
use std::collections::HashMap;
use std::env;
use std::fs;
use std::path::PathBuf;
use serde_json;

/// 浏览器注册管理器
///
/// 负责在系统中注册和管理Native Messaging Host配置
pub struct BrowserRegistrationManager {
    /// 注册配置
    config: RegistrationConfig,
    /// 已注册的浏览器
    registered_browsers: HashMap<BrowserType, RegistrationInfo>,
}

/// 注册配置
#[derive(Debug, Clone)]
pub struct RegistrationConfig {
    /// Host名称
    pub host_name: String,
    /// Host描述
    pub host_description: String,
    /// Host可执行文件路径
    pub host_path: String,
    /// 是否自动注册已安装的浏览器
    pub auto_register: bool,
    /// 是否覆盖现有注册
    pub force_overwrite: bool,
    /// 备份现有配置
    pub backup_existing: bool,
}

impl Default for RegistrationConfig {
    fn default() -> Self {
        Self {
            host_name: "com.securepassword.nativemessaging".to_string(),
            host_description: "Secure Password Native Messaging Host".to_string(),
            host_path: env::current_exe()
                .unwrap_or_else(|_| PathBuf::from("secure-password-daemon"))
                .to_string_lossy()
                .to_string(),
            auto_register: true,
            force_overwrite: false,
            backup_existing: true,
        }
    }
}

/// 注册信息
#[derive(Debug, Clone)]
pub struct RegistrationInfo {
    /// 浏览器类型
    pub browser_type: BrowserType,
    /// 注册文件路径
    pub manifest_path: PathBuf,
    /// 注册状态
    pub status: RegistrationStatus,
    /// 注册时间
    pub registered_at: Option<std::time::SystemTime>,
    /// 备份文件路径（如果有）
    pub backup_path: Option<PathBuf>,
}

/// 注册状态
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum RegistrationStatus {
    /// 未注册
    NotRegistered,
    /// 已注册
    Registered,
    /// 注册失败
    Failed(String),
    /// 需要更新
    NeedsUpdate,
    /// 备份已存在
    BackupExists,
}

impl BrowserRegistrationManager {
    /// 创建新的注册管理器
    ///
    /// # 参数
    /// - `config`: 注册配置
    ///
    /// # 返回
    /// Self - 注册管理器
    pub fn new(config: RegistrationConfig) -> Self {
        Self {
            config,
            registered_browsers: HashMap::new(),
        }
    }

    /// 创建默认的注册管理器
    ///
    /// # 返回
    /// Self - 默认注册管理器
    pub fn new_default() -> Self {
        Self::new(RegistrationConfig::default())
    }

    /// 自动检测并注册所有已安装的浏览器
    ///
    /// # 返回
    /// Result<Vec<BrowserType>> - 成功注册的浏览器列表
    pub async fn auto_register_all(&mut self) -> Result<Vec<BrowserType>> {
        let mut registered = Vec::new();
        
        // 检测所有已安装的浏览器
        let available_browsers = BrowserDetector::detect_all_browsers().await;
        
        for browser_result in available_browsers {
            let browser_type = browser_result.browser_type;
            match self.register_browser(&browser_type).await {
                Ok(()) => {
                    registered.push(browser_type);
                    println!("✅ 成功注册 {:?} 浏览器", browser_type);
                },
                Err(e) => {
                    eprintln!("❌ 注册 {:?} 浏览器失败: {}", browser_type, e);
                }
            }
        }
        
        Ok(registered)
    }

    /// 注册指定浏览器
    ///
    /// # 参数
    /// - `browser_type`: 浏览器类型
    ///
    /// # 返回
    /// Result<()> - 注册结果
    pub async fn register_browser(&mut self, browser_type: &BrowserType) -> Result<()> {
        let manifest_content = self.generate_manifest(browser_type)?;
        let manifest_path = self.get_manifest_path(browser_type)?;
        
        // 检查是否需要备份现有文件
        if manifest_path.exists() && self.config.backup_existing {
            self.backup_existing_manifest(&manifest_path, browser_type)?;
        }
        
        // 确保目录存在
        if let Some(parent) = manifest_path.parent() {
            fs::create_dir_all(parent).map_err(|e| {
                NativeMessagingError::SystemError(format!("创建目录失败: {}", e))
            })?;
        }
        
        // 写入manifest文件
        fs::write(&manifest_path, manifest_content).map_err(|e| {
            NativeMessagingError::SystemError(format!("写入manifest文件失败: {}", e))
        })?;
        
        // 更新注册信息
        let registration_info = RegistrationInfo {
            browser_type: browser_type.clone(),
            manifest_path: manifest_path.clone(),
            status: RegistrationStatus::Registered,
            registered_at: Some(std::time::SystemTime::now()),
            backup_path: None,
        };
        
        self.registered_browsers.insert(browser_type.clone(), registration_info);
        
        // 执行浏览器特定的注册步骤
        self.post_registration_setup(browser_type, &manifest_path).await?;
        
        Ok(())
    }

    /// 卸载指定浏览器的注册
    ///
    /// # 参数
    /// - `browser_type`: 浏览器类型
    ///
    /// # 返回
    /// Result<()> - 卸载结果
    pub fn unregister_browser(&mut self, browser_type: &BrowserType) -> Result<()> {
        if let Some(info) = self.registered_browsers.get(browser_type) {
            // 删除manifest文件
            if info.manifest_path.exists() {
                fs::remove_file(&info.manifest_path).map_err(|e| {
                    NativeMessagingError::SystemError(format!("删除manifest文件失败: {}", e))
                })?;
            }
            
            // 恢复备份（如果有）
            if let Some(backup_path) = &info.backup_path {
                if backup_path.exists() {
                    fs::copy(backup_path, &info.manifest_path).map_err(|e| {
                        NativeMessagingError::SystemError(format!("恢复备份失败: {}", e))
                    })?;
                    fs::remove_file(backup_path).ok(); // 删除备份文件
                }
            }
            
            self.registered_browsers.remove(browser_type);
        }
        
        Ok(())
    }

    /// 生成指定浏览器的manifest内容
    ///
    /// # 参数
    /// - `browser_type`: 浏览器类型
    ///
    /// # 返回
    /// Result<String> - manifest JSON内容
    fn generate_manifest(&self, browser_type: &BrowserType) -> Result<String> {
        match browser_type {
            BrowserType::Chrome => {
                let config = ChromeHostConfig {
                    name: self.config.host_name.clone(),
                    description: self.config.host_description.clone(),
                    path: self.config.host_path.clone(),
                    ..Default::default()
                };
                let adapter = ChromeAdapter::new_with_host_config(config)?;
                adapter.generate_host_manifest()
            },
            BrowserType::Firefox => {
                let config = FirefoxHostConfig {
                    name: self.config.host_name.clone(),
                    description: self.config.host_description.clone(),
                    path: self.config.host_path.clone(),
                    ..Default::default()
                };
                let adapter = FirefoxAdapter::new_with_host_config(config)?;
                adapter.generate_host_manifest()
            },
            BrowserType::Edge => {
                let config = EdgeHostConfig {
                    name: self.config.host_name.clone(),
                    description: self.config.host_description.clone(),
                    path: self.config.host_path.clone(),
                    ..Default::default()
                };
                let adapter = EdgeAdapter::new_with_host_config(config)?;
                adapter.generate_host_manifest()
            },
            BrowserType::Safari => {
                let config = SafariHostConfig {
                    name: self.config.host_name.clone(),
                    description: self.config.host_description.clone(),
                    path: self.config.host_path.clone(),
                    ..Default::default()
                };
                let adapter = SafariAdapter::new_with_host_config(config)?;
                adapter.generate_host_manifest()
            },
        }
    }

    /// 获取指定浏览器的manifest文件路径
    ///
    /// # 参数
    /// - `browser_type`: 浏览器类型
    ///
    /// # 返回
    /// Result<PathBuf> - manifest文件路径
    fn get_manifest_path(&self, browser_type: &BrowserType) -> Result<PathBuf> {
        let home_dir = dirs::home_dir().ok_or_else(|| {
            NativeMessagingError::SystemError("无法获取用户主目录".to_string())
        })?;
        
        let manifest_filename = format!("{}.json", self.config.host_name);
        
        match browser_type {
            BrowserType::Chrome => {
                #[cfg(target_os = "windows")]
                {
                    Ok(home_dir
                        .join("AppData")
                        .join("Local")
                        .join("Google")
                        .join("Chrome")
                        .join("User Data")
                        .join("NativeMessagingHosts")
                        .join(manifest_filename))
                }
                #[cfg(target_os = "macos")]
                {
                    Ok(home_dir
                        .join("Library")
                        .join("Application Support")
                        .join("Google")
                        .join("Chrome")
                        .join("NativeMessagingHosts")
                        .join(manifest_filename))
                }
                #[cfg(target_os = "linux")]
                {
                    Ok(home_dir
                        .join(".config")
                        .join("google-chrome")
                        .join("NativeMessagingHosts")
                        .join(manifest_filename))
                }
                #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
                {
                    Err(NativeMessagingError::ConfigurationError(
                        "不支持的操作系统".to_string(),
                    ))
                }
            },
            BrowserType::Firefox => {
                #[cfg(target_os = "windows")]
                {
                    Ok(home_dir
                        .join("AppData")
                        .join("Roaming")
                        .join("Mozilla")
                        .join("NativeMessagingHosts")
                        .join(manifest_filename))
                }
                #[cfg(target_os = "macos")]
                {
                    Ok(home_dir
                        .join("Library")
                        .join("Application Support")
                        .join("Mozilla")
                        .join("NativeMessagingHosts")
                        .join(manifest_filename))
                }
                #[cfg(target_os = "linux")]
                {
                    Ok(home_dir
                        .join(".mozilla")
                        .join("native-messaging-hosts")
                        .join(manifest_filename))
                }
                #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
                {
                    Err(NativeMessagingError::ConfigurationError(
                        "不支持的操作系统".to_string(),
                    ))
                }
            },
            BrowserType::Edge => {
                #[cfg(target_os = "windows")]
                {
                    Ok(home_dir
                        .join("AppData")
                        .join("Local")
                        .join("Microsoft")
                        .join("Edge")
                        .join("User Data")
                        .join("NativeMessagingHosts")
                        .join(manifest_filename))
                }
                #[cfg(target_os = "macos")]
                {
                    Ok(home_dir
                        .join("Library")
                        .join("Application Support")
                        .join("Microsoft Edge")
                        .join("NativeMessagingHosts")
                        .join(manifest_filename))
                }
                #[cfg(target_os = "linux")]
                {
                    Ok(home_dir
                        .join(".config")
                        .join("microsoft-edge")
                        .join("NativeMessagingHosts")
                        .join(manifest_filename))
                }
                #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
                {
                    Err(NativeMessagingError::ConfigurationError(
                        "不支持的操作系统".to_string(),
                    ))
                }
            },
            BrowserType::Safari => {
                #[cfg(target_os = "macos")]
                {
                    Ok(home_dir
                        .join("Library")
                        .join("Application Support")
                        .join("Safari")
                        .join("NativeMessagingHosts")
                        .join(manifest_filename))
                }
                #[cfg(not(target_os = "macos"))]
                {
                    Err(NativeMessagingError::ConfigurationError(
                        "Safari 仅在 macOS 上支持".to_string(),
                    ))
                }
            },
        }
    }

    /// 备份现有的manifest文件
    ///
    /// # 参数
    /// - `manifest_path`: manifest文件路径
    /// - `browser_type`: 浏览器类型
    ///
    /// # 返回
    /// Result<()> - 备份结果
    fn backup_existing_manifest(
        &mut self,
        manifest_path: &PathBuf,
        browser_type: &BrowserType,
    ) -> Result<()> {
        let backup_path = manifest_path.with_extension("json.backup");
        
        fs::copy(manifest_path, &backup_path).map_err(|e| {
            NativeMessagingError::SystemError(format!("备份manifest文件失败: {}", e))
        })?;
        
        // 更新注册信息
        if let Some(info) = self.registered_browsers.get_mut(browser_type) {
            info.backup_path = Some(backup_path);
        }
        
        Ok(())
    }

    /// 执行注册后的设置
    ///
    /// # 参数
    /// - `browser_type`: 浏览器类型
    /// - `manifest_path`: manifest文件路径
    ///
    /// # 返回
    /// Result<()> - 设置结果
    async fn post_registration_setup(
        &self,
        browser_type: &BrowserType,
        manifest_path: &PathBuf,
    ) -> Result<()> {
        match browser_type {
            BrowserType::Chrome | BrowserType::Edge => {
                // Chrome和Edge可能需要重启浏览器来识别新的Native Messaging Host
                println!("💡 提示: 请重启 {:?} 浏览器以使Native Messaging Host生效", browser_type);
            },
            BrowserType::Firefox => {
                // Firefox可能需要特殊的设置
                println!("💡 提示: Firefox可能需要在about:config中启用native messaging支持");
            },
            BrowserType::Safari => {
                // Safari需要签名和特殊权限
                #[cfg(target_os = "macos")]
                {
                    println!("💡 提示: Safari需要扩展签名，请确保团队ID配置正确");
                }
            },
        }
        
        Ok(())
    }

    /// 获取注册状态
    ///
    /// # 返回
    /// &HashMap<BrowserType, RegistrationInfo> - 注册信息映射
    pub fn get_registration_status(&self) -> &HashMap<BrowserType, RegistrationInfo> {
        &self.registered_browsers
    }

    /// 检查指定浏览器是否已注册
    ///
    /// # 参数
    /// - `browser_type`: 浏览器类型
    ///
    /// # 返回
    /// bool - 是否已注册
    pub fn is_registered(&self, browser_type: &BrowserType) -> bool {
        self.registered_browsers
            .get(browser_type)
            .map(|info| info.status == RegistrationStatus::Registered)
            .unwrap_or(false)
    }

    /// 验证所有注册的浏览器状态
    ///
    /// # 返回
    /// Result<Vec<(BrowserType, RegistrationStatus)>> - 验证结果
    pub fn validate_registrations(&mut self) -> Result<Vec<(BrowserType, RegistrationStatus)>> {
        let mut results = Vec::new();
        
        for (browser_type, info) in &mut self.registered_browsers {
            let status = if info.manifest_path.exists() {
                // 检查文件内容是否有效
                match fs::read_to_string(&info.manifest_path) {
                    Ok(content) => {
                        match serde_json::from_str::<serde_json::Value>(&content) {
                            Ok(_) => RegistrationStatus::Registered,
                            Err(_) => RegistrationStatus::Failed("Manifest文件格式无效".to_string()),
                        }
                    },
                    Err(e) => RegistrationStatus::Failed(format!("无法读取manifest文件: {}", e)),
                }
            } else {
                RegistrationStatus::NotRegistered
            };
            
            info.status = status.clone();
            results.push((browser_type.clone(), status));
        }
        
        Ok(results)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_registration_manager_creation() {
        let manager = BrowserRegistrationManager::new_default();
        assert_eq!(manager.config.host_name, "com.securepassword.nativemessaging");
        assert_eq!(manager.registered_browsers.len(), 0);
    }

    #[test]
    fn test_registration_config_default() {
        let config = RegistrationConfig::default();
        assert!(config.auto_register);
        assert!(!config.force_overwrite);
        assert!(config.backup_existing);
    }

    #[tokio::test]
    async fn test_manifest_generation() {
        let manager = BrowserRegistrationManager::new_default();
        
        // 测试Chrome manifest生成
        let chrome_manifest = manager.generate_manifest(&BrowserType::Chrome);
        assert!(chrome_manifest.is_ok());
        
        let manifest_content = chrome_manifest.unwrap();
        assert!(manifest_content.contains("com.securepassword.nativemessaging"));
        assert!(manifest_content.contains("stdio"));
    }

    #[test]
    fn test_manifest_path_generation() {
        let manager = BrowserRegistrationManager::new_default();
        
        // 测试各浏览器的路径生成
        for browser_type in &[BrowserType::Chrome, BrowserType::Firefox, BrowserType::Edge] {
            let path = manager.get_manifest_path(browser_type);
            assert!(path.is_ok());
            let path = path.unwrap();
            assert!(path.to_string_lossy().contains("NativeMessagingHosts"));
            assert!(path.extension().unwrap() == "json");
        }
    }

    #[test]
    fn test_registration_status() {
        let manager = BrowserRegistrationManager::new_default();
        
        // 测试初始状态
        assert!(!manager.is_registered(&BrowserType::Chrome));
        assert_eq!(manager.get_registration_status().len(), 0);
    }

    #[test]
    fn test_registration_info() {
        let info = RegistrationInfo {
            browser_type: BrowserType::Chrome,
            manifest_path: PathBuf::from("/test/path"),
            status: RegistrationStatus::Registered,
            registered_at: Some(std::time::SystemTime::now()),
            backup_path: None,
        };
        
        assert_eq!(info.browser_type, BrowserType::Chrome);
        assert_eq!(info.status, RegistrationStatus::Registered);
        assert!(info.registered_at.is_some());
    }
} 