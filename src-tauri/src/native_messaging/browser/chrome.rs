//! Chrome 浏览器适配器实现
//!
//! 提供针对 Chrome 浏览器的 Native Messaging 支持

use super::{BrowserAdapter, BrowserAdapterConfig, BrowserAdapterStats, ConnectionStatus};
use crate::native_messaging::{
    error::{NativeMessagingError, Result},
    protocol::message::{NativeMessage, OutgoingMessage},
    config::BrowserType,
};
use async_trait::async_trait;
use std::env;
use std::path::PathBuf;
use std::process::{Command, Stdio};
use std::sync::{Arc, RwLock};
use std::time::SystemTime;
use tokio::io::{AsyncReadExt, AsyncWriteExt, BufReader};
use tokio::process::{Child, ChildStdin, ChildStdout};
use tokio::sync::Mutex;
use serde_json;

/// Chrome 浏览器适配器
///
/// 实现 Chrome 特定的 Native Messaging 协议
pub struct ChromeAdapter {
    /// 适配器配置
    config: BrowserAdapterConfig,
    /// 连接状态
    connection_status: Arc<RwLock<ConnectionStatus>>,
    /// 统计信息
    stats: Arc<Mutex<BrowserAdapterStats>>,
    /// 是否已初始化
    initialized: Arc<RwLock<bool>>,
    /// Chrome 可执行文件路径
    chrome_executable: Option<String>,
    /// Native Messaging Host 配置
    host_config: Option<ChromeHostConfig>,
    /// 进程句柄
    process_handle: Arc<Mutex<Option<Child>>>,
    /// 标准输入句柄
    stdin_handle: Arc<Mutex<Option<ChildStdin>>>,
    /// 标准输出句柄
    stdout_handle: Arc<Mutex<Option<BufReader<ChildStdout>>>>,
}

/// Chrome Host 配置
#[derive(Debug, Clone)]
pub struct ChromeHostConfig {
    /// Host 名称
    pub name: String,
    /// 描述
    pub description: String,
    /// 可执行文件路径
    pub path: String,
    /// 消息类型
    pub message_type: String,
    /// 允许的来源
    pub allowed_origins: Vec<String>,
    /// 允许的扩展
    pub allowed_extensions: Vec<String>,
}

impl Default for ChromeHostConfig {
    fn default() -> Self {
        Self {
            name: "com.securepassword.nativemessaging".to_string(),
            description: "Secure Password Native Messaging Host".to_string(),
            path: env::current_exe()
                .unwrap_or_else(|_| PathBuf::from("secure-password-daemon"))
                .to_string_lossy()
                .to_string(),
            message_type: "stdio".to_string(),
            allowed_origins: vec![
                "chrome-extension://your-extension-id/".to_string()
            ],
            allowed_extensions: vec![
                "your-extension-id".to_string()
            ],
        }
    }
}

impl ChromeAdapter {
    /// 创建新的 Chrome 适配器
    ///
    /// # 参数
    /// - `config`: 适配器配置
    ///
    /// # 返回
    /// Result<Self> - 创建的适配器
    pub fn new(config: BrowserAdapterConfig) -> Result<Self> {
        let mut chrome_config = config;
        chrome_config.browser_type = BrowserType::Chrome;
        
        // 尝试检测真实的 Chrome 版本
        let chrome_executable = Self::find_chrome_executable();
        let version = if let Ok(ref exe) = chrome_executable {
            Self::detect_chrome_version_from_executable(exe)
                .unwrap_or_else(|_| "unknown".to_string())
        } else {
            "unknown".to_string()
        };
        
        if chrome_config.browser_version == "unknown" {
            chrome_config.browser_version = version;
        }
        
        Ok(Self {
            config: chrome_config,
            connection_status: Arc::new(RwLock::new(ConnectionStatus::Disconnected)),
            stats: Arc::new(Mutex::new(BrowserAdapterStats::default())),
            initialized: Arc::new(RwLock::new(false)),
            chrome_executable: chrome_executable.ok(),
            host_config: Some(ChromeHostConfig::default()),
            process_handle: Arc::new(Mutex::new(None)),
            stdin_handle: Arc::new(Mutex::new(None)),
            stdout_handle: Arc::new(Mutex::new(None)),
        })
    }

    /// 创建默认的 Chrome 适配器
    ///
    /// # 返回
    /// Result<Self> - 创建的适配器
    pub fn new_default() -> Result<Self> {
        let chrome_executable = Self::find_chrome_executable()
            .unwrap_or_else(|_| "chrome".to_string());
        
        let config = BrowserAdapterConfig {
            browser_type: BrowserType::Chrome,
            browser_version: Self::detect_chrome_version_from_executable(&chrome_executable)
                .unwrap_or_else(|_| "Chrome/Unknown".to_string()),
            // Chrome 默认配置
            connection_timeout: 5000,
            message_timeout: 10000,
            ..Default::default()
        };
        Self::new(config)
    }

    /// 使用指定的Host配置创建Chrome适配器
    ///
    /// # 参数
    /// - `host_config`: Host配置
    ///
    /// # 返回
    /// Result<Self> - 创建的适配器
    pub fn new_with_host_config(host_config: ChromeHostConfig) -> Result<Self> {
        let chrome_executable = Self::find_chrome_executable()
            .unwrap_or_else(|_| "chrome".to_string());
        
        let config = BrowserAdapterConfig {
            browser_type: BrowserType::Chrome,
            browser_version: Self::detect_chrome_version_from_executable(&chrome_executable)
                .unwrap_or_else(|_| "Chrome/Unknown".to_string()),
            connection_timeout: 5000,
            message_timeout: 10000,
            ..Default::default()
        };
        
        let mut adapter = Self::new(config)?;
        adapter.host_config = Some(host_config);
        Ok(adapter)
    }

    /// 查找 Chrome 可执行文件
    ///
    /// # 返回
    /// Result<String> - Chrome 可执行文件路径
    fn find_chrome_executable() -> Result<String> {
        let possible_names = Self::get_chrome_executable_names();
        
        // 首先尝试从环境变量
        if let Ok(chrome_path) = env::var("CHROME_EXECUTABLE") {
            if !chrome_path.is_empty() && std::path::Path::new(&chrome_path).exists() {
                return Ok(chrome_path);
            }
        }

        // 然后尝试常见路径
        for path in Self::get_chrome_possible_paths() {
            if path.exists() {
                return Ok(path.to_string_lossy().to_string());
            }
        }

        // 最后尝试 which/where 命令
        for name in possible_names {
            if let Ok(path) = Self::which_command(&name) {
                return Ok(path);
            }
        }

        Err(NativeMessagingError::BrowserNotFound(
            "未找到 Chrome 浏览器".to_string(),
        ))
    }

    /// 获取 Chrome 可执行文件名称列表
    fn get_chrome_executable_names() -> Vec<String> {
        #[cfg(target_os = "windows")]
        return vec![
            "chrome.exe".to_string(),
            "google-chrome.exe".to_string(),
            "chromium.exe".to_string(),
        ];

        #[cfg(target_os = "macos")]
        return vec![
            "Google Chrome".to_string(),
            "Chromium".to_string(),
        ];

        #[cfg(target_os = "linux")]
        return vec![
            "google-chrome".to_string(),
            "google-chrome-stable".to_string(),
            "chromium".to_string(),
            "chromium-browser".to_string(),
        ];

        #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
        return vec!["chrome".to_string()];
    }

    /// 获取 Chrome 可能的安装路径
    fn get_chrome_possible_paths() -> Vec<PathBuf> {
        let mut paths = Vec::new();

        #[cfg(target_os = "windows")]
        {
            let program_files = env::var("ProgramFiles").unwrap_or_else(|_| "C:\\Program Files".to_string());
            let program_files_x86 = env::var("ProgramFiles(x86)").unwrap_or_else(|_| "C:\\Program Files (x86)".to_string());
            let local_app_data = env::var("LOCALAPPDATA").unwrap_or_else(|_| "C:\\Users\\<USER>\\AppData\\Local".to_string());
            
            paths.extend_from_slice(&[
                PathBuf::from(format!("{}\\Google\\Chrome\\Application\\chrome.exe", program_files)),
                PathBuf::from(format!("{}\\Google\\Chrome\\Application\\chrome.exe", program_files_x86)),
                PathBuf::from(format!("{}\\Google\\Chrome\\Application\\chrome.exe", local_app_data)),
                PathBuf::from(format!("{}\\Chromium\\Application\\chrome.exe", program_files)),
                PathBuf::from(format!("{}\\Chromium\\Application\\chrome.exe", program_files_x86)),
            ]);
        }

        #[cfg(target_os = "macos")]
        {
            paths.extend_from_slice(&[
                PathBuf::from("/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"),
                PathBuf::from("/Applications/Chromium.app/Contents/MacOS/Chromium"),
                PathBuf::from("/usr/local/bin/chrome"),
                PathBuf::from("/opt/homebrew/bin/chrome"),
            ]);
        }

        #[cfg(target_os = "linux")]
        {
            paths.extend_from_slice(&[
                PathBuf::from("/usr/bin/google-chrome"),
                PathBuf::from("/usr/bin/google-chrome-stable"),
                PathBuf::from("/usr/bin/chromium"),
                PathBuf::from("/usr/bin/chromium-browser"),
                PathBuf::from("/opt/google/chrome/chrome"),
                PathBuf::from("/snap/bin/chromium"),
                PathBuf::from("/usr/local/bin/chrome"),
            ]);
        }

        paths
    }

    /// 使用 which/where 命令查找可执行文件
    fn which_command(command: &str) -> Result<String> {
        #[cfg(target_os = "windows")]
        let which_cmd = "where";
        #[cfg(not(target_os = "windows"))]
        let which_cmd = "which";

        let output = Command::new(which_cmd)
            .arg(command)
            .output()
            .map_err(|e| NativeMessagingError::SystemError(format!("执行 {} 命令失败: {}", which_cmd, e)))?;

        if output.status.success() {
            let path = String::from_utf8_lossy(&output.stdout).trim().to_string();
            if !path.is_empty() {
                return Ok(path);
            }
        }

        Err(NativeMessagingError::BrowserNotFound(
            format!("未找到命令: {}", command),
        ))
    }

    /// 从可执行文件检测 Chrome 版本
    ///
    /// # 参数
    /// - `executable`: Chrome 可执行文件路径
    ///
    /// # 返回
    /// Result<String> - Chrome 版本信息
    fn detect_chrome_version_from_executable(executable: &str) -> Result<String> {
        let output = Command::new(executable)
            .arg("--version")
            .output()
            .map_err(|e| NativeMessagingError::SystemError(format!("获取 Chrome 版本失败: {}", e)))?;

        if output.status.success() {
            let version_str = String::from_utf8_lossy(&output.stdout);
            // Chrome 版本输出格式通常是: "Google Chrome 120.0.6099.109"
            if let Some(version) = version_str.split_whitespace().last() {
                return Ok(format!("Chrome/{}", version.trim()));
            }
        }

        Err(NativeMessagingError::ProtocolError(
            "无法解析 Chrome 版本信息".to_string(),
        ))
    }

    /// 验证消息格式是否符合 Chrome 规范
    ///
    /// # 参数
    /// - `message`: 要验证的消息
    ///
    /// # 返回
    /// Result<()> - 验证结果
    fn validate_chrome_message(&self, message: &OutgoingMessage) -> Result<()> {
        // Chrome Native Messaging 格式验证
        if message.message.payload.is_null() {
            return Err(NativeMessagingError::ProtocolError(
                "Chrome 消息数据不能为空".to_string(),
            ));
        }

        // 检查消息大小限制 (Chrome 限制为 1MB)
        let message_str = serde_json::to_string(&message.message.payload)
            .map_err(|e| NativeMessagingError::SerializationError(e))?;
        
        if message_str.len() > 1024 * 1024 {
            return Err(NativeMessagingError::ProtocolError(
                "Chrome 消息大小超过 1MB 限制".to_string(),
            ));
        }

        // 验证消息ID格式
        if message.message.request_id.is_empty() {
            return Err(NativeMessagingError::ProtocolError(
                "Chrome 消息ID不能为空".to_string(),
            ));
        }

        Ok(())
    }

    /// 设置连接状态
    ///
    /// # 参数
    /// - `status`: 新的连接状态
    fn set_connection_status(&self, status: ConnectionStatus) {
        if let Ok(mut current_status) = self.connection_status.write() {
            *current_status = status;
        }
    }

    /// 获取连接状态
    ///
    /// # 返回
    /// ConnectionStatus - 当前连接状态
    fn get_connection_status(&self) -> ConnectionStatus {
        self.connection_status
            .read()
            .map(|status| status.clone())
            .unwrap_or(ConnectionStatus::Failed("无法读取状态".to_string()))
    }

    /// 写入 Native Messaging 消息
    ///
    /// # 参数
    /// - `message`: 要写入的消息
    ///
    /// # 返回
    /// Result<()> - 写入结果
    async fn write_native_message(&self, message: &serde_json::Value) -> Result<()> {
        let mut stdin_guard = self.stdin_handle.lock().await;
        if let Some(ref mut stdin) = stdin_guard.as_mut() {
            // Chrome Native Messaging 协议：先写4字节长度，再写JSON数据
            let json_str = serde_json::to_string(message)
                .map_err(|e| NativeMessagingError::SerializationError(e))?;
            
            let length = json_str.len() as u32;
            let length_bytes = length.to_le_bytes();
            
            stdin.write_all(&length_bytes).await
                .map_err(|e| NativeMessagingError::ConnectionError(format!("写入消息长度失败: {}", e)))?;
            
            stdin.write_all(json_str.as_bytes()).await
                .map_err(|e| NativeMessagingError::ConnectionError(format!("写入消息内容失败: {}", e)))?;
            
            stdin.flush().await
                .map_err(|e| NativeMessagingError::ConnectionError(format!("刷新输出失败: {}", e)))?;
            
            return Ok(());
        }
        
        Err(NativeMessagingError::ConnectionError(
            "标准输入流未建立".to_string(),
        ))
    }

    /// 读取 Native Messaging 消息
    ///
    /// # 返回
    /// Result<serde_json::Value> - 读取的消息
    async fn read_native_message(&self) -> Result<serde_json::Value> {
        let mut stdout_guard = self.stdout_handle.lock().await;
        if let Some(ref mut stdout) = stdout_guard.as_mut() {
            // 读取4字节长度
            let mut length_bytes = [0u8; 4];
            stdout.read_exact(&mut length_bytes).await
                .map_err(|e| NativeMessagingError::ConnectionError(format!("读取消息长度失败: {}", e)))?;
            
            let length = u32::from_le_bytes(length_bytes);
            
            // 防止过大的消息
            if length > 1024 * 1024 { // 1MB 限制
                return Err(NativeMessagingError::ProtocolError(
                    "消息长度超过限制".to_string(),
                ));
            }
            
            // 读取JSON数据
            let mut json_bytes = vec![0u8; length as usize];
            stdout.read_exact(&mut json_bytes).await
                .map_err(|e| NativeMessagingError::ConnectionError(format!("读取消息内容失败: {}", e)))?;
            
            let json_str = String::from_utf8(json_bytes)
                .map_err(|e| NativeMessagingError::ProtocolError(format!("消息编码错误: {}", e)))?;
            
            let message: serde_json::Value = serde_json::from_str(&json_str)
                .map_err(|e| NativeMessagingError::SerializationError(e))?;
            
            return Ok(message);
        }
        
        Err(NativeMessagingError::ConnectionError(
            "标准输出流未建立".to_string(),
        ))
    }

    /// 启动 Chrome 进程用于测试
    ///
    /// # 返回
    /// Result<()> - 启动结果
    async fn start_chrome_process(&self) -> Result<()> {
        if let Some(ref executable) = self.chrome_executable {
            let mut cmd = tokio::process::Command::new(executable);
            cmd.stdin(Stdio::piped())
               .stdout(Stdio::piped())
               .stderr(Stdio::null())
               .arg("--headless")
               .arg("--no-sandbox")
               .arg("--disable-gpu");

            let mut child = cmd.spawn()
                .map_err(|e| NativeMessagingError::ConnectionError(format!("启动 Chrome 进程失败: {}", e)))?;

            let stdin = child.stdin.take()
                .ok_or_else(|| NativeMessagingError::ConnectionError("无法获取stdin".to_string()))?;
            
            let stdout = child.stdout.take()
                .ok_or_else(|| NativeMessagingError::ConnectionError("无法获取stdout".to_string()))?;

            // 存储进程句柄和IO流
            {
                let mut process_guard = self.process_handle.lock().await;
                *process_guard = Some(child);
            }

            {
                let mut stdin_guard = self.stdin_handle.lock().await;
                *stdin_guard = Some(stdin);
            }

            {
                let mut stdout_guard = self.stdout_handle.lock().await;
                *stdout_guard = Some(BufReader::new(stdout));
            }

            Ok(())
        } else {
            Err(NativeMessagingError::BrowserNotFound(
                "Chrome 可执行文件未找到".to_string(),
            ))
        }
    }

    /// 停止 Chrome 进程
    ///
    /// # 返回
    /// Result<()> - 停止结果
    async fn stop_chrome_process(&self) -> Result<()> {
        // 清理IO流
        {
            let mut stdin_guard = self.stdin_handle.lock().await;
            *stdin_guard = None;
        }

        {
            let mut stdout_guard = self.stdout_handle.lock().await;
            *stdout_guard = None;
        }

        // 终止进程
        {
            let mut process_guard = self.process_handle.lock().await;
            if let Some(mut child) = process_guard.take() {
                let _ = child.kill().await;
                let _ = child.wait().await;
            }
        }

        Ok(())
    }

    /// 获取 Chrome Host 配置
    ///
    /// # 返回
    /// Option<&ChromeHostConfig> - Host 配置
    pub fn get_host_config(&self) -> Option<&ChromeHostConfig> {
        self.host_config.as_ref()
    }

    /// 设置 Chrome Host 配置
    ///
    /// # 参数
    /// - `config`: 新的 Host 配置
    pub fn set_host_config(&mut self, config: ChromeHostConfig) {
        self.host_config = Some(config);
    }

    /// 生成 Chrome Native Messaging Host 注册文件内容
    ///
    /// # 返回
    /// Result<String> - JSON 格式的注册文件内容
    pub fn generate_host_manifest(&self) -> Result<String> {
        if let Some(ref host_config) = self.host_config {
            let manifest = serde_json::json!({
                "name": host_config.name,
                "description": host_config.description,
                "path": host_config.path,
                "type": host_config.message_type,
                "allowed_origins": host_config.allowed_origins,
                "allowed_extensions": host_config.allowed_extensions,
            });

            serde_json::to_string_pretty(&manifest)
                .map_err(|e| NativeMessagingError::SerializationError(e))
        } else {
            Err(NativeMessagingError::ConfigurationError(
                "Host 配置未设置".to_string(),
            ))
        }
    }

    /// 解析消息类型字符串为 MessageType 枚举
    ///
    /// # 参数
    /// - `type_str`: 消息类型字符串
    ///
    /// # 返回
    /// MessageType - 解析后的消息类型
    fn parse_message_type(type_str: &str) -> crate::native_messaging::protocol::message::MessageType {
        use crate::native_messaging::protocol::message::MessageType;

        match type_str.to_lowercase().as_str() {
            "getcredentials" | "get_credentials" => MessageType::GetCredentials,
            "savecredentials" | "save_credentials" => MessageType::SaveCredentials,
            "deletecredentials" | "delete_credentials" => MessageType::DeleteCredentials,
            "updatecredentials" | "update_credentials" => MessageType::UpdateCredentials,
            "healthcheck" | "health_check" => MessageType::HealthCheck,
            "getconfig" | "get_config" => MessageType::GetConfig,
            "updateconfig" | "update_config" => MessageType::UpdateConfig,
            "getstatus" | "get_status" => MessageType::GetStatus,
            "error" => MessageType::Error,
            "success" => MessageType::Success,
            "handshake" => MessageType::Handshake,
            "protocolupgrade" | "protocol_upgrade" => MessageType::ProtocolUpgrade,
            "batchoperation" | "batch_operation" => MessageType::BatchOperation,
            "streamdata" | "stream_data" => MessageType::StreamData,
            "auth" => MessageType::Auth,
            "ping" => MessageType::Ping,
            "password" => MessageType::Password,
            "version" => MessageType::Version,
            "test" => MessageType::Test,
            custom if custom.starts_with("custom_") => {
                MessageType::Custom(custom.strip_prefix("custom_").unwrap_or(custom).to_string())
            }
            _ => MessageType::Custom(type_str.to_string()),
        }
    }
}

#[async_trait]
impl BrowserAdapter for ChromeAdapter {
    fn browser_type(&self) -> BrowserType {
        BrowserType::Chrome
    }

    fn browser_version(&self) -> &str {
        &self.config.browser_version
    }

    async fn send_message(&self, message: OutgoingMessage) -> Result<()> {
        let start_time = SystemTime::now();
        
        // 验证消息格式
        self.validate_chrome_message(&message)?;

        // 检查连接状态
        if !self.is_connected().await {
            let mut stats = self.stats.lock().await;
            stats.record_send(false);
            return Err(NativeMessagingError::ConnectionError(
                "Chrome 连接未建立".to_string(),
            ));
        }

        // 转换为 Chrome Native Messaging 格式
        let chrome_message = serde_json::json!({
            "id": message.message.request_id,
            "type": format!("{:?}", message.message.message_type),
            "payload": message.message.payload,
            "timestamp": message.message.timestamp,
            "source": message.message.source,
        });

        // 发送消息
        let send_result = self.write_native_message(&chrome_message).await;

        // 记录统计信息
        let mut stats = self.stats.lock().await;
        stats.record_send(send_result.is_ok());
        
        if let Ok(elapsed) = start_time.elapsed() {
            stats.record_response_time(elapsed);
        }

        send_result
    }

    async fn receive_message(&self) -> Result<NativeMessage> {
        let start_time = SystemTime::now();

        // 检查连接状态
        if !self.is_connected().await {
            let mut stats = self.stats.lock().await;
            stats.record_receive(false);
            return Err(NativeMessagingError::ConnectionError(
                "Chrome 连接未建立".to_string(),
            ));
        }

        // 从 Chrome 接收消息
        let receive_result = self.read_native_message().await;

        let mut stats = self.stats.lock().await;
        
        match &receive_result {
            Ok(json_message) => {
                stats.record_receive(true);
                
                if let Ok(elapsed) = start_time.elapsed() {
                    stats.record_response_time(elapsed);
                }

                // 转换为内部消息格式
                let message_type = json_message.get("type")
                    .and_then(|t| t.as_str())
                    .unwrap_or("Unknown");

                let parsed_message_type = Self::parse_message_type(message_type);

                let native_message = NativeMessage::new(
                    parsed_message_type,
                    json_message.get("id")
                        .and_then(|id| id.as_str())
                        .unwrap_or("unknown")
                        .to_string(),
                    json_message.get("payload")
                        .cloned()
                        .unwrap_or(serde_json::Value::Null),
                    json_message.get("source")
                        .and_then(|s| s.as_str())
                        .unwrap_or("chrome-extension")
                        .to_string(),
                );

                Ok(native_message)
            }
            Err(_) => {
                stats.record_receive(false);
                receive_result.map(|_| unreachable!()) // 这行不会执行，只是为了类型匹配
            }
        }
    }

    async fn is_connected(&self) -> bool {
        matches!(self.get_connection_status(), ConnectionStatus::Connected)
    }

    async fn initialize(&mut self) -> Result<()> {
        // 检查是否已经初始化
        if let Ok(initialized) = self.initialized.read() {
            if *initialized {
                return Ok(());
            }
        }

        self.set_connection_status(ConnectionStatus::Connecting);

        // 验证 Chrome 可执行文件是否存在
        if self.chrome_executable.is_none() {
            self.set_connection_status(ConnectionStatus::Failed(
                "Chrome 可执行文件未找到".to_string(),
            ));
            return Err(NativeMessagingError::BrowserNotFound(
                "Chrome 可执行文件未找到，请确保已安装 Chrome 浏览器".to_string(),
            ));
        }

        // 在测试模式下启动 Chrome 进程
        if self.config.debug_mode {
            if let Err(e) = self.start_chrome_process().await {
                self.set_connection_status(ConnectionStatus::Failed(
                    format!("启动 Chrome 进程失败: {}", e),
                ));
                return Err(e);
            }
        }

        // 更新统计信息
        {
            let mut stats = self.stats.lock().await;
            stats.connection_time = Some(SystemTime::now());
        }

        // 设置已初始化状态
        if let Ok(mut initialized) = self.initialized.write() {
            *initialized = true;
        }

        self.set_connection_status(ConnectionStatus::Connected);
        
        Ok(())
    }

    async fn close(&mut self) -> Result<()> {
        // 在测试模式下停止 Chrome 进程
        if self.config.debug_mode {
            let _ = self.stop_chrome_process().await;
        }

        self.set_connection_status(ConnectionStatus::Disconnected);
        
        // 重置初始化状态
        if let Ok(mut initialized) = self.initialized.write() {
            *initialized = false;
        }

        Ok(())
    }

    fn supported_message_types(&self) -> Vec<String> {
        vec![
            "HealthCheck".to_string(),
            "Ping".to_string(),
            "Auth".to_string(),
            "Password".to_string(),
            "Version".to_string(),
            "Test".to_string(),
            "Extension".to_string(),  // Chrome 特有
            "ContentScript".to_string(),  // Chrome 特有
        ]
    }

    fn config(&self) -> &BrowserAdapterConfig {
        &self.config
    }
}

impl Default for ChromeAdapter {
    fn default() -> Self {
        Self::new_default().expect("创建默认 Chrome 适配器失败")
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_chrome_adapter_creation() {
        let config = BrowserAdapterConfig::default();
        let adapter = ChromeAdapter::new(config);
        assert!(adapter.is_ok());
        
        let adapter = adapter.unwrap();
        assert_eq!(adapter.browser_type(), BrowserType::Chrome);
        assert!(adapter.browser_version().contains("Chrome") || adapter.browser_version() == "unknown");
    }

    #[test]
    fn test_chrome_adapter_default() {
        let adapter = ChromeAdapter::default();
        assert_eq!(adapter.browser_type(), BrowserType::Chrome);
        assert_eq!(adapter.supported_message_types().len(), 8); // 更新数量
    }

    #[test]
    fn test_chrome_executable_detection() {
        // 测试 Chrome 可执行文件检测
        let possible_names = ChromeAdapter::get_chrome_executable_names();
        assert!(!possible_names.is_empty());

        let possible_paths = ChromeAdapter::get_chrome_possible_paths();
        assert!(!possible_paths.is_empty());
    }

    #[test]
    fn test_chrome_host_config() {
        let config = ChromeHostConfig::default();
        assert_eq!(config.name, "com.securepassword.nativemessaging");
        assert_eq!(config.message_type, "stdio");
        assert!(!config.allowed_origins.is_empty());
        assert!(!config.allowed_extensions.is_empty());
    }

    #[test]
    fn test_chrome_host_manifest_generation() {
        let adapter = ChromeAdapter::default();
        let manifest = adapter.generate_host_manifest();
        assert!(manifest.is_ok());
        
        let manifest_json = manifest.unwrap();
        assert!(manifest_json.contains("com.securepassword.nativemessaging"));
        assert!(manifest_json.contains("stdio"));
        
        // 验证生成的JSON是否有效
        let parsed: serde_json::Value = serde_json::from_str(&manifest_json).unwrap();
        assert!(parsed.get("name").is_some());
        assert!(parsed.get("description").is_some());
        assert!(parsed.get("path").is_some());
        assert!(parsed.get("type").is_some());
        assert!(parsed.get("allowed_origins").is_some());
        assert!(parsed.get("allowed_extensions").is_some());
    }

    #[test]
    fn test_message_type_parsing() {
        // 测试消息类型解析
        assert_eq!(
            ChromeAdapter::parse_message_type("HealthCheck"),
            crate::native_messaging::protocol::message::MessageType::HealthCheck
        );
        
        assert_eq!(
            ChromeAdapter::parse_message_type("auth"),
            crate::native_messaging::protocol::message::MessageType::Auth
        );
        
        assert_eq!(
            ChromeAdapter::parse_message_type("custom_test"),
            crate::native_messaging::protocol::message::MessageType::Custom("test".to_string())
        );
        
        assert_eq!(
            ChromeAdapter::parse_message_type("unknown_type"),
            crate::native_messaging::protocol::message::MessageType::Custom("unknown_type".to_string())
        );
    }

    #[tokio::test]
    async fn test_chrome_adapter_initialization() {
        let mut adapter = ChromeAdapter::default();
        
        // 初始状态应该是未连接
        assert!(!adapter.is_connected().await);
        
        // 初始化
        let result = adapter.initialize().await;
        assert!(result.is_ok());
        
        // 初始化后应该已连接
        assert!(adapter.is_connected().await);
    }

    #[tokio::test]
    async fn test_chrome_adapter_send_message() {
        let mut adapter = ChromeAdapter::default();
        adapter.initialize().await.unwrap();

        let message = OutgoingMessage::success(
            "test-request".to_string(),
            serde_json::json!({"test": "data"}),
        );

        let result = adapter.send_message(message).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_chrome_adapter_receive_message() {
        let mut adapter = ChromeAdapter::default();
        adapter.initialize().await.unwrap();

        let result = adapter.receive_message().await;
        assert!(result.is_ok());
        
        let message = result.unwrap();
        assert_eq!(message.source, "chrome-extension");
    }

    #[tokio::test]
    async fn test_chrome_adapter_close() {
        let mut adapter = ChromeAdapter::default();
        adapter.initialize().await.unwrap();
        assert!(adapter.is_connected().await);

        let result = adapter.close().await;
        assert!(result.is_ok());
        assert!(!adapter.is_connected().await);
    }

    #[test]
    fn test_chrome_message_validation() {
        let adapter = ChromeAdapter::default();
        
        // 测试正常消息
        let valid_message = OutgoingMessage::success(
            "test".to_string(),
            serde_json::json!({"data": "test"}),
        );
        assert!(adapter.validate_chrome_message(&valid_message).is_ok());
        
        // 测试空消息
        let empty_message = OutgoingMessage::success(
            "test".to_string(),
            serde_json::Value::Null,
        );
        assert!(adapter.validate_chrome_message(&empty_message).is_err());
    }

    #[test]
    fn test_chrome_version_detection() {
        let chrome_executable = ChromeAdapter::find_chrome_executable()
            .unwrap_or_else(|_| "chrome".to_string());
        let version = ChromeAdapter::detect_chrome_version_from_executable(&chrome_executable)
            .unwrap_or_else(|_| "Chrome/Unknown".to_string());
        assert!(version.contains("Chrome"));
        assert!(!version.is_empty());
    }
} 