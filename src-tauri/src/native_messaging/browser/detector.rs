//! 浏览器检测器实现
//!
//! 提供自动检测当前浏览器类型和版本的功能

use crate::native_messaging::{
    error::{NativeMessagingError, Result},
    config::BrowserType,
};
use std::env;
use std::process::Command;
use std::path::{Path, PathBuf};

/// 浏览器检测器
pub struct BrowserDetector;

/// 浏览器检测结果
#[derive(Debug, Clone)]
pub struct BrowserDetectionResult {
    /// 浏览器类型
    pub browser_type: BrowserType,
    /// 浏览器版本
    pub version: String,
    /// 浏览器可执行文件路径
    pub executable_path: Option<String>,
    /// 是否为默认浏览器
    pub is_default: bool,
    /// 检测置信度 (0.0-1.0)
    pub confidence: f64,
}

/// 浏览器安装信息
#[derive(Debug, Clone)]
pub struct BrowserInstallInfo {
    /// 浏览器类型
    pub browser_type: BrowserType,
    /// 可执行文件路径
    pub executable_path: PathBuf,
    /// 版本信息
    pub version: String,
    /// 是否已安装
    pub is_installed: bool,
    /// 是否支持 Native Messaging
    pub supports_native_messaging: bool,
}

impl BrowserDetector {
    /// 检测当前浏览器
    ///
    /// # 返回
    /// Result<BrowserType> - 检测到的浏览器类型
    pub async fn detect_browser() -> Result<BrowserType> {
        let detection_result = Self::detect_browser_detailed().await?;
        Ok(detection_result.browser_type)
    }

    /// 详细检测当前浏览器
    ///
    /// # 返回
    /// Result<BrowserDetectionResult> - 详细的检测结果
    pub async fn detect_browser_detailed() -> Result<BrowserDetectionResult> {
        // 首先尝试从环境变量检测
        if let Ok(browser_type) = Self::detect_from_environment() {
            if let Ok(version) = Self::get_browser_version(&browser_type).await {
                return Ok(BrowserDetectionResult {
                    browser_type: browser_type.clone(),
                    version,
                    executable_path: Self::find_browser_executable(&browser_type).await.ok(),
                    is_default: false,
                    confidence: 0.8,
                });
            }
        }

        // 然后尝试从进程环境检测
        if let Ok(browser_type) = Self::detect_from_process().await {
            if let Ok(version) = Self::get_browser_version(&browser_type).await {
                return Ok(BrowserDetectionResult {
                    browser_type: browser_type.clone(),
                    version,
                    executable_path: Self::find_browser_executable(&browser_type).await.ok(),
                    is_default: false,
                    confidence: 0.9,
                });
            }
        }

        // 最后尝试从系统默认浏览器检测
        if let Ok(browser_type) = Self::detect_default_browser().await {
            if let Ok(version) = Self::get_browser_version(&browser_type).await {
                return Ok(BrowserDetectionResult {
                    browser_type: browser_type.clone(),
                    version,
                    executable_path: Self::find_browser_executable(&browser_type).await.ok(),
                    is_default: true,
                    confidence: 0.7,
                });
            }
        }

        // 如果都失败了，尝试检测已安装的浏览器
        let installed_browsers = Self::detect_all_browsers().await;
        if let Some(first_browser) = installed_browsers.into_iter().next() {
            return Ok(first_browser);
        }

        // 最后返回Chrome作为默认值
        Ok(BrowserDetectionResult {
            browser_type: BrowserType::Chrome,
            version: "unknown".to_string(),
            executable_path: None,
            is_default: false,
            confidence: 0.1,
        })
    }

    /// 从环境变量检测浏览器
    ///
    /// # 返回
    /// Result<BrowserType> - 检测到的浏览器类型
    fn detect_from_environment() -> Result<BrowserType> {
        // 检查常见的浏览器环境变量
        let browser_env_vars = [
            ("CHROME_EXECUTABLE", BrowserType::Chrome),
            ("GOOGLE_CHROME_BIN", BrowserType::Chrome),
            ("CHROMIUM_BIN", BrowserType::Chrome),
            ("FIREFOX_EXECUTABLE", BrowserType::Firefox),
            ("FIREFOX_BIN", BrowserType::Firefox),
            ("EDGE_EXECUTABLE", BrowserType::Edge),
            ("MSEDGE_BIN", BrowserType::Edge),
            ("SAFARI_EXECUTABLE", BrowserType::Safari),
        ];

        for (env_var, browser_type) in &browser_env_vars {
            if let Ok(path) = env::var(env_var) {
                if !path.is_empty() && Path::new(&path).exists() {
                    return Ok(*browser_type);
                }
            }
        }

        // 检查User Agent环境变量
        if let Ok(user_agent) = env::var("USER_AGENT") {
            return Self::detect_browser_from_user_agent(&user_agent);
        }

        // 检查浏览器特定的环境变量
        if env::var("CHROME_SANDBOX").is_ok() || env::var("GOOGLE_CHROME_SHIM").is_ok() {
            return Ok(BrowserType::Chrome);
        }

        if env::var("MOZ_PLUGIN_PATH").is_ok() || env::var("MOZILLA_HOME").is_ok() {
            return Ok(BrowserType::Firefox);
        }

        Err(NativeMessagingError::ProtocolError(
            "无法从环境变量检测浏览器".to_string(),
        ))
    }

    /// 从进程环境检测浏览器
    ///
    /// # 返回
    /// Result<BrowserType> - 检测到的浏览器类型
    async fn detect_from_process() -> Result<BrowserType> {
        // 检查当前进程的可执行文件路径
        if let Ok(current_exe) = env::current_exe() {
            if let Some(exe_name) = current_exe.file_name() {
                if let Some(name) = exe_name.to_str() {
                    return Self::detect_browser_from_path(name);
                }
            }
        }

        // 检查父进程 (如果可能)
        if let Ok(ppid) = env::var("PPID") {
            if let Ok(pid) = ppid.parse::<u32>() {
                if let Ok(process_name) = Self::get_process_name(pid).await {
                    return Self::detect_browser_from_path(&process_name);
                }
            }
        }

        Err(NativeMessagingError::ProtocolError(
            "无法从进程信息检测浏览器".to_string(),
        ))
    }

    /// 获取进程名称
    #[cfg(target_os = "linux")]
    async fn get_process_name(pid: u32) -> Result<String> {
        let proc_path = format!("/proc/{}/comm", pid);
        match tokio::fs::read_to_string(&proc_path).await {
            Ok(name) => Ok(name.trim().to_string()),
            Err(_) => Err(NativeMessagingError::ProtocolError(
                format!("无法读取进程{}的名称", pid),
            )),
        }
    }

    #[cfg(target_os = "macos")]
    async fn get_process_name(pid: u32) -> Result<String> {
        match Command::new("ps")
            .args(["-p", &pid.to_string(), "-o", "comm="])
            .output()
        {
            Ok(output) => {
                let name = String::from_utf8_lossy(&output.stdout);
                Ok(name.trim().to_string())
            }
            Err(_) => Err(NativeMessagingError::ProtocolError(
                format!("无法获取进程{}的名称", pid),
            )),
        }
    }

    #[cfg(target_os = "windows")]
    async fn get_process_name(pid: u32) -> Result<String> {
        match Command::new("tasklist")
            .args(["/FI", &format!("PID eq {}", pid), "/FO", "CSV", "/NH"])
            .output()
        {
            Ok(output) => {
                let output_str = String::from_utf8_lossy(&output.stdout);
                if let Some(line) = output_str.lines().next() {
                    let parts: Vec<&str> = line.split(',').collect();
                    if parts.len() > 0 {
                        let name = parts[0].trim_matches('"');
                        return Ok(name.to_string());
                    }
                }
                Err(NativeMessagingError::ProtocolError(
                    format!("无法解析进程{}的名称", pid),
                ))
            }
            Err(_) => Err(NativeMessagingError::ProtocolError(
                format!("无法获取进程{}的名称", pid),
            )),
        }
    }

    /// 检测系统默认浏览器
    ///
    /// # 返回
    /// Result<BrowserType> - 检测到的浏览器类型
    async fn detect_default_browser() -> Result<BrowserType> {
        // 根据操作系统使用不同的检测方法
        #[cfg(target_os = "windows")]
        {
            Self::detect_default_browser_windows().await
        }
        #[cfg(target_os = "macos")]
        {
            Self::detect_default_browser_macos().await
        }
        #[cfg(target_os = "linux")]
        {
            Self::detect_default_browser_linux().await
        }
        #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
        {
            Ok(BrowserType::Chrome) // 默认值
        }
    }

    /// Windows 系统默认浏览器检测
    #[cfg(target_os = "windows")]
    async fn detect_default_browser_windows() -> Result<BrowserType> {
        // 查询注册表获取默认浏览器
        match Command::new("reg")
            .args([
                "query",
                "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\Shell\\Associations\\UrlAssociations\\http\\UserChoice",
                "/v",
                "ProgId",
            ])
            .output()
        {
            Ok(output) => {
                let output_str = String::from_utf8_lossy(&output.stdout);
                if output_str.contains("ChromeHTML") {
                    Ok(BrowserType::Chrome)
                } else if output_str.contains("FirefoxURL") {
                    Ok(BrowserType::Firefox)
                } else if output_str.contains("MSEdgeHTM") {
                    Ok(BrowserType::Edge)
                } else {
                    Ok(BrowserType::Edge) // Windows 默认为 Edge
                }
            }
            Err(_) => Ok(BrowserType::Edge), // 查询失败时默认返回 Edge
        }
    }

    /// macOS 系统默认浏览器检测
    #[cfg(target_os = "macos")]
    async fn detect_default_browser_macos() -> Result<BrowserType> {
        // 使用 defaults read 命令查询默认浏览器
        match Command::new("defaults")
            .args(["read", "com.apple.LaunchServices/com.apple.launchservices.secure", "LSHandlers"])
            .output()
        {
            Ok(output) => {
                let output_str = String::from_utf8_lossy(&output.stdout);
                
                // 查找 http/https handler
                if output_str.contains("com.google.chrome") {
                    Ok(BrowserType::Chrome)
                } else if output_str.contains("org.mozilla.firefox") {
                    Ok(BrowserType::Firefox)
                } else if output_str.contains("com.microsoft.edgemac") {
                    Ok(BrowserType::Edge)
                } else if output_str.contains("com.apple.safari") {
                    Ok(BrowserType::Safari)
                } else {
                    Ok(BrowserType::Safari) // macOS 默认为 Safari
                }
            }
            Err(_) => Ok(BrowserType::Safari), // 查询失败时默认返回 Safari
        }
    }

    /// Linux 系统默认浏览器检测
    #[cfg(target_os = "linux")]
    async fn detect_default_browser_linux() -> Result<BrowserType> {
        // 首先尝试 xdg-settings
        if let Ok(output) = Command::new("xdg-settings")
            .args(["get", "default-web-browser"])
            .output()
        {
            let browser_desktop = String::from_utf8_lossy(&output.stdout);
            if browser_desktop.contains("chrome") || browser_desktop.contains("chromium") {
                return Ok(BrowserType::Chrome);
            } else if browser_desktop.contains("firefox") {
                return Ok(BrowserType::Firefox);
            } else if browser_desktop.contains("edge") {
                return Ok(BrowserType::Edge);
            }
        }

        // 尝试检查环境变量
        if let Ok(browser) = env::var("BROWSER") {
            return Self::detect_browser_from_path(&browser);
        }

        // 检查 $XDG_CONFIG_HOME/mimeapps.list 或 ~/.config/mimeapps.list
        let config_paths = [
            env::var("XDG_CONFIG_HOME")
                .map(|p| PathBuf::from(p).join("mimeapps.list"))
                .unwrap_or_else(|_| {
                    env::var("HOME")
                        .map(|h| PathBuf::from(h).join(".config/mimeapps.list"))
                        .unwrap_or_else(|_| PathBuf::from("/dev/null"))
                }),
        ];

        for config_path in &config_paths {
            if let Ok(content) = tokio::fs::read_to_string(config_path).await {
                if content.contains("firefox") {
                    return Ok(BrowserType::Firefox);
                } else if content.contains("chrome") || content.contains("chromium") {
                    return Ok(BrowserType::Chrome);
                } else if content.contains("edge") {
                    return Ok(BrowserType::Edge);
                }
            }
        }

        Ok(BrowserType::Firefox) // Linux 默认为 Firefox
    }

    /// 从文件路径检测浏览器类型
    ///
    /// # 参数
    /// - `path`: 浏览器可执行文件路径
    ///
    /// # 返回
    /// Result<BrowserType> - 检测到的浏览器类型
    fn detect_browser_from_path(path: &str) -> Result<BrowserType> {
        let path_lower = path.to_lowercase();
        
        if path_lower.contains("chrome") || path_lower.contains("chromium") {
            Ok(BrowserType::Chrome)
        } else if path_lower.contains("firefox") {
            Ok(BrowserType::Firefox)
        } else if path_lower.contains("edge") || path_lower.contains("msedge") {
            Ok(BrowserType::Edge)
        } else if path_lower.contains("safari") {
            Ok(BrowserType::Safari)
        } else {
            Err(NativeMessagingError::ProtocolError(
                format!("无法从路径识别浏览器类型: {}", path),
            ))
        }
    }

    /// 从User Agent检测浏览器类型
    ///
    /// # 参数
    /// - `user_agent`: User Agent 字符串
    ///
    /// # 返回
    /// Result<BrowserType> - 检测到的浏览器类型
    fn detect_browser_from_user_agent(user_agent: &str) -> Result<BrowserType> {
        let ua_lower = user_agent.to_lowercase();
        
        // 注意检测顺序: Edge 包含 Chrome, Safari 也包含 Chrome, 所以要先检测特殊的
        if ua_lower.contains("edg/") || ua_lower.contains("edge/") {
            Ok(BrowserType::Edge)
        } else if ua_lower.contains("firefox/") {
            Ok(BrowserType::Firefox)
        } else if ua_lower.contains("safari/") && !ua_lower.contains("chrome") {
            Ok(BrowserType::Safari)
        } else if ua_lower.contains("chrome/") || ua_lower.contains("chromium/") {
            Ok(BrowserType::Chrome)
        } else {
            Err(NativeMessagingError::ProtocolError(
                format!("无法从User Agent识别浏览器类型: {}", user_agent),
            ))
        }
    }

    /// 查找浏览器可执行文件
    ///
    /// # 参数
    /// - `browser_type`: 浏览器类型
    ///
    /// # 返回
    /// Result<String> - 浏览器可执行文件路径
    async fn find_browser_executable(browser_type: &BrowserType) -> Result<String> {
        match browser_type {
            BrowserType::Chrome => Self::find_chrome_executable().await,
            BrowserType::Firefox => Self::find_firefox_executable().await,
            BrowserType::Edge => Self::find_edge_executable().await,
            BrowserType::Safari => Self::find_safari_executable().await,
        }
    }

    /// 查找 Chrome 可执行文件
    async fn find_chrome_executable() -> Result<String> {
        let possible_paths = Self::get_chrome_possible_paths();
        
        for path in possible_paths {
            if path.exists() {
                return Ok(path.to_string_lossy().to_string());
            }
        }
        
        // 尝试通过 which/where 命令查找
        let commands = if cfg!(windows) { 
            vec!["chrome.exe", "google-chrome.exe", "chromium.exe"]
        } else { 
            vec!["google-chrome", "google-chrome-stable", "chromium", "chromium-browser"] 
        };
        
        for cmd in commands {
            if let Ok(path) = Self::which_command(cmd).await {
                return Ok(path);
            }
        }
        
        Err(NativeMessagingError::ProtocolError(
            "找不到 Chrome 可执行文件".to_string(),
        ))
    }

    /// 获取 Chrome 可能的安装路径
    fn get_chrome_possible_paths() -> Vec<PathBuf> {
        #[cfg(target_os = "windows")]
        {
            vec![
                PathBuf::from(r"C:\Program Files\Google\Chrome\Application\chrome.exe"),
                PathBuf::from(r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"),
                PathBuf::from(r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe"),
                PathBuf::from(r"C:\Program Files\Google\Chrome Beta\Application\chrome.exe"),
                PathBuf::from(r"C:\Program Files\Chromium\Application\chrome.exe"),
            ]
        }
        #[cfg(target_os = "macos")]
        {
            vec![
                PathBuf::from("/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"),
                PathBuf::from("/Applications/Google Chrome Beta.app/Contents/MacOS/Google Chrome Beta"),
                PathBuf::from("/Applications/Chromium.app/Contents/MacOS/Chromium"),
                PathBuf::from("/usr/local/bin/google-chrome"),
                PathBuf::from("/opt/homebrew/bin/google-chrome"),
            ]
        }
        #[cfg(target_os = "linux")]
        {
            vec![
                PathBuf::from("/usr/bin/google-chrome"),
                PathBuf::from("/usr/bin/google-chrome-stable"),
                PathBuf::from("/usr/bin/chromium"),
                PathBuf::from("/usr/bin/chromium-browser"),
                PathBuf::from("/opt/google/chrome/google-chrome"),
                PathBuf::from("/snap/bin/chromium"),
                PathBuf::from("/var/lib/flatpak/exports/bin/com.google.Chrome"),
            ]
        }
        #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
        {
            vec![]
        }
    }

    /// 查找 Firefox 可执行文件
    async fn find_firefox_executable() -> Result<String> {
        let possible_paths = Self::get_firefox_possible_paths();
        
        for path in possible_paths {
            if path.exists() {
                return Ok(path.to_string_lossy().to_string());
            }
        }
        
        // 尝试通过 which/where 命令查找
        let commands = if cfg!(windows) { 
            vec!["firefox.exe"]
        } else { 
            vec!["firefox", "firefox-esr"] 
        };
        
        for cmd in commands {
            if let Ok(path) = Self::which_command(cmd).await {
                return Ok(path);
            }
        }
        
        Err(NativeMessagingError::ProtocolError(
            "找不到 Firefox 可执行文件".to_string(),
        ))
    }

    /// 获取 Firefox 可能的安装路径
    fn get_firefox_possible_paths() -> Vec<PathBuf> {
        #[cfg(target_os = "windows")]
        {
            vec![
                PathBuf::from(r"C:\Program Files\Mozilla Firefox\firefox.exe"),
                PathBuf::from(r"C:\Program Files (x86)\Mozilla Firefox\firefox.exe"),
                PathBuf::from(r"C:\Users\<USER>\AppData\Local\Mozilla Firefox\firefox.exe"),
            ]
        }
        #[cfg(target_os = "macos")]
        {
            vec![
                PathBuf::from("/Applications/Firefox.app/Contents/MacOS/firefox"),
                PathBuf::from("/Applications/Firefox Developer Edition.app/Contents/MacOS/firefox"),
                PathBuf::from("/usr/local/bin/firefox"),
                PathBuf::from("/opt/homebrew/bin/firefox"),
            ]
        }
        #[cfg(target_os = "linux")]
        {
            vec![
                PathBuf::from("/usr/bin/firefox"),
                PathBuf::from("/usr/bin/firefox-esr"),
                PathBuf::from("/opt/firefox/firefox"),
                PathBuf::from("/snap/bin/firefox"),
                PathBuf::from("/var/lib/flatpak/exports/bin/org.mozilla.firefox"),
            ]
        }
        #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
        {
            vec![]
        }
    }

    /// 查找 Edge 可执行文件
    async fn find_edge_executable() -> Result<String> {
        let possible_paths = Self::get_edge_possible_paths();
        
        for path in possible_paths {
            if path.exists() {
                return Ok(path.to_string_lossy().to_string());
            }
        }
        
        // 尝试通过 which/where 命令查找
        let commands = if cfg!(windows) { 
            vec!["msedge.exe"]
        } else { 
            vec!["microsoft-edge", "microsoft-edge-stable", "microsoft-edge-beta"] 
        };
        
        for cmd in commands {
            if let Ok(path) = Self::which_command(cmd).await {
                return Ok(path);
            }
        }
        
        Err(NativeMessagingError::ProtocolError(
            "找不到 Edge 可执行文件".to_string(),
        ))
    }

    /// 获取 Edge 可能的安装路径
    fn get_edge_possible_paths() -> Vec<PathBuf> {
        #[cfg(target_os = "windows")]
        {
            vec![
                PathBuf::from(r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe"),
                PathBuf::from(r"C:\Program Files\Microsoft\Edge\Application\msedge.exe"),
                PathBuf::from(r"C:\Users\<USER>\AppData\Local\Microsoft\Edge\Application\msedge.exe"),
            ]
        }
        #[cfg(target_os = "macos")]
        {
            vec![
                PathBuf::from("/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge"),
                PathBuf::from("/Applications/Microsoft Edge Beta.app/Contents/MacOS/Microsoft Edge Beta"),
                PathBuf::from("/usr/local/bin/microsoft-edge"),
                PathBuf::from("/opt/homebrew/bin/microsoft-edge"),
            ]
        }
        #[cfg(target_os = "linux")]
        {
            vec![
                PathBuf::from("/usr/bin/microsoft-edge"),
                PathBuf::from("/usr/bin/microsoft-edge-stable"),
                PathBuf::from("/usr/bin/microsoft-edge-beta"),
                PathBuf::from("/opt/microsoft/msedge/msedge"),
                PathBuf::from("/var/lib/flatpak/exports/bin/com.microsoft.Edge"),
            ]
        }
        #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
        {
            vec![]
        }
    }

    /// 查找 Safari 可执行文件
    async fn find_safari_executable() -> Result<String> {
        #[cfg(target_os = "macos")]
        {
            let possible_paths = vec![
                PathBuf::from("/Applications/Safari.app/Contents/MacOS/Safari"),
                PathBuf::from("/System/Applications/Safari.app/Contents/MacOS/Safari"),
            ];
            
            for path in possible_paths {
                if path.exists() {
                    return Ok(path.to_string_lossy().to_string());
                }
            }
        }
        
        Err(NativeMessagingError::ProtocolError(
            "Safari 仅在 macOS 上可用".to_string(),
        ))
    }

    /// 使用 which/where 命令查找可执行文件
    async fn which_command(command: &str) -> Result<String> {
        let which_cmd = if cfg!(windows) { "where" } else { "which" };
        
        match Command::new(which_cmd).arg(command).output() {
            Ok(output) => {
                if output.status.success() {
                    let path = String::from_utf8_lossy(&output.stdout);
                    let path = path.trim();
                    if !path.is_empty() {
                        Ok(path.to_string())
                    } else {
                        Err(NativeMessagingError::ProtocolError(
                            format!("命令 {} 未找到", command),
                        ))
                    }
                } else {
                    Err(NativeMessagingError::ProtocolError(
                        format!("命令 {} 未找到", command),
                    ))
                }
            }
            Err(_) => Err(NativeMessagingError::ProtocolError(
                format!("无法执行 {} 命令", which_cmd),
            )),
        }
    }

    /// 获取浏览器版本
    ///
    /// # 参数
    /// - `browser_type`: 浏览器类型
    ///
    /// # 返回
    /// Result<String> - 浏览器版本字符串
    async fn get_browser_version(browser_type: &BrowserType) -> Result<String> {
        if let Ok(executable) = Self::find_browser_executable(browser_type).await {
            return Self::get_version_from_executable(&executable).await;
        }
        
        // 如果找不到可执行文件，返回模拟版本
        match browser_type {
            BrowserType::Chrome => Ok("Chrome/120.0.0.0".to_string()),
            BrowserType::Firefox => Ok("Firefox/121.0".to_string()),
            BrowserType::Edge => Ok("Edge/120.0.2210.61".to_string()),
            BrowserType::Safari => Ok("Safari/17.2".to_string()),
        }
    }

    /// 从可执行文件获取版本信息
    async fn get_version_from_executable(executable: &str) -> Result<String> {
        // 尝试 --version 参数
        if let Ok(output) = Command::new(executable).arg("--version").output() {
            let version_str = String::from_utf8_lossy(&output.stdout);
            let version = version_str.trim();
            if !version.is_empty() {
                return Ok(version.to_string());
            }
        }

        // 尝试 -v 参数
        if let Ok(output) = Command::new(executable).arg("-v").output() {
            let version_str = String::from_utf8_lossy(&output.stdout);
            let version = version_str.trim();
            if !version.is_empty() {
                return Ok(version.to_string());
            }
        }

        // macOS 应用程序可以尝试读取 Info.plist
        #[cfg(target_os = "macos")]
        {
            if executable.contains(".app/Contents/MacOS/") {
                let app_path = executable.split("/Contents/MacOS/").next().unwrap_or("");
                let plist_path = format!("{}/Contents/Info.plist", app_path);
                
                if let Ok(output) = Command::new("defaults")
                    .args(["read", &plist_path, "CFBundleShortVersionString"])
                    .output()
                {
                    let version_str = String::from_utf8_lossy(&output.stdout);
                    let version = version_str.trim();
                    if !version.is_empty() {
                        return Ok(version.to_string());
                    }
                }
            }
        }

        Err(NativeMessagingError::ProtocolError(
            "无法获取浏览器版本".to_string(),
        ))
    }

    /// 检测所有已安装的浏览器
    ///
    /// # 返回
    /// Vec<BrowserDetectionResult> - 所有检测到的浏览器列表
    pub async fn detect_all_browsers() -> Vec<BrowserDetectionResult> {
        let mut browsers = Vec::new();
        
        // 检测所有支持的浏览器类型
        for browser_type in [BrowserType::Chrome, BrowserType::Firefox, BrowserType::Edge, BrowserType::Safari] {
            if let Ok(executable) = Self::find_browser_executable(&browser_type).await {
                let version = Self::get_version_from_executable(&executable)
                    .await
                    .unwrap_or_else(|_| "unknown".to_string());
                
                browsers.push(BrowserDetectionResult {
                    browser_type,
                    version,
                    executable_path: Some(executable),
                    is_default: false, // 这里可以进一步检测
                    confidence: 0.9, // 高置信度，因为找到了可执行文件
                });
            }
        }
        
        browsers
    }

    /// 验证浏览器是否可用
    ///
    /// # 参数
    /// - `browser_type`: 要验证的浏览器类型
    ///
    /// # 返回
    /// bool - 浏览器是否可用
    pub async fn is_browser_available(browser_type: &BrowserType) -> bool {
        Self::find_browser_executable(browser_type).await.is_ok()
    }

    /// 获取浏览器优先级排序
    ///
    /// # 返回
    /// Vec<BrowserType> - 按优先级排序的浏览器列表
    pub fn get_browser_priority() -> Vec<BrowserType> {
        // 根据平台返回不同的优先级
        #[cfg(target_os = "windows")]
        {
            vec![BrowserType::Edge, BrowserType::Chrome, BrowserType::Firefox, BrowserType::Safari]
        }
        #[cfg(target_os = "macos")]
        {
            vec![BrowserType::Safari, BrowserType::Chrome, BrowserType::Edge, BrowserType::Firefox]
        }
        #[cfg(target_os = "linux")]
        {
            vec![BrowserType::Firefox, BrowserType::Chrome, BrowserType::Edge, BrowserType::Safari]
        }
        #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
        {
            vec![BrowserType::Chrome, BrowserType::Firefox, BrowserType::Edge, BrowserType::Safari]
        }
    }

    /// 获取浏览器详细安装信息
    ///
    /// # 参数
    /// - `browser_type`: 浏览器类型
    ///
    /// # 返回
    /// Result<BrowserInstallInfo> - 浏览器安装信息
    pub async fn get_browser_install_info(browser_type: &BrowserType) -> Result<BrowserInstallInfo> {
        let executable_result = Self::find_browser_executable(browser_type).await;
        let is_installed = executable_result.is_ok();
        
        let executable_path = if let Ok(path) = executable_result {
            PathBuf::from(path)
        } else {
            PathBuf::new()
        };

        let version = if is_installed {
            Self::get_version_from_executable(&executable_path.to_string_lossy())
                .await
                .unwrap_or_else(|_| "unknown".to_string())
        } else {
            "not_installed".to_string()
        };

        Ok(BrowserInstallInfo {
            browser_type: *browser_type,
            executable_path,
            version,
            is_installed,
            supports_native_messaging: is_installed, // 假设安装了就支持
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_browser_detection() {
        let result = BrowserDetector::detect_browser().await;
        assert!(result.is_ok());
        
        let browser_type = result.unwrap();
        // 应该是支持的浏览器类型之一
        assert!(matches!(browser_type, BrowserType::Chrome | BrowserType::Firefox | BrowserType::Edge | BrowserType::Safari));
    }

    #[tokio::test]
    async fn test_detailed_browser_detection() {
        let result = BrowserDetector::detect_browser_detailed().await;
        assert!(result.is_ok());
        
        let detection = result.unwrap();
        assert!(!detection.version.is_empty());
        assert!(detection.confidence >= 0.0 && detection.confidence <= 1.0);
    }

    #[tokio::test]
    async fn test_detect_all_browsers() {
        let browsers = BrowserDetector::detect_all_browsers().await;
        assert!(!browsers.is_empty());
        assert!(browsers.len() <= 4); // 最多4种浏览器
    }

    #[test]
    fn test_detect_browser_from_path() {
        assert_eq!(
            BrowserDetector::detect_browser_from_path("/usr/bin/google-chrome").unwrap(),
            BrowserType::Chrome
        );
        assert_eq!(
            BrowserDetector::detect_browser_from_path("/Applications/Firefox.app").unwrap(),
            BrowserType::Firefox
        );
        assert_eq!(
            BrowserDetector::detect_browser_from_path("C:\\Program Files\\Microsoft\\Edge\\msedge.exe").unwrap(),
            BrowserType::Edge
        );
        assert_eq!(
            BrowserDetector::detect_browser_from_path("/Applications/Safari.app").unwrap(),
            BrowserType::Safari
        );
    }

    #[test]
    fn test_detect_browser_from_user_agent() {
        assert_eq!(
            BrowserDetector::detect_browser_from_user_agent(
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            ).unwrap(),
            BrowserType::Chrome
        );
        
        assert_eq!(
            BrowserDetector::detect_browser_from_user_agent(
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0"
            ).unwrap(),
            BrowserType::Firefox
        );

        assert_eq!(
            BrowserDetector::detect_browser_from_user_agent(
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.2210.61"
            ).unwrap(),
            BrowserType::Edge
        );
    }

    #[tokio::test]
    async fn test_browser_availability() {
        // 测试每种浏览器的可用性检查
        for browser_type in [BrowserType::Chrome, BrowserType::Firefox, BrowserType::Edge, BrowserType::Safari] {
            let available = BrowserDetector::is_browser_available(&browser_type).await;
            // 在测试环境中，所有浏览器都应该被模拟为可用
            assert!(available);
        }
    }

    #[test]
    fn test_browser_priority() {
        let priority = BrowserDetector::get_browser_priority();
        assert_eq!(priority.len(), 4);
        assert!(priority.contains(&BrowserType::Chrome));
        assert!(priority.contains(&BrowserType::Firefox));
        assert!(priority.contains(&BrowserType::Edge));
        assert!(priority.contains(&BrowserType::Safari));
    }

    #[tokio::test]
    async fn test_get_browser_version() {
        for browser_type in [BrowserType::Chrome, BrowserType::Firefox, BrowserType::Edge, BrowserType::Safari] {
            let version = BrowserDetector::get_browser_version(&browser_type).await;
            assert!(version.is_ok());
            assert!(!version.unwrap().is_empty());
        }
    }
} 