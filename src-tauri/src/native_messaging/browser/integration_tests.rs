//! 浏览器适配层集成测试
//!
//! 提供完整的浏览器适配层功能测试，包括：
//! - 多浏览器适配器测试
//! - 注册管理器测试
//! - 浏览器检测器测试
//! - 跨平台兼容性测试

use super::{
    BrowserAdapter, BrowserAdapterConfig, BrowserType, ConnectionStatus,
    chrome::{ChromeAdapter, ChromeHostConfig},
    firefox::{FirefoxAdapter, FirefoxHostConfig},
    edge::{EdgeAdapter, EdgeHostConfig},
    safari::{SafariAdapter, SafariHostConfig},
    detector::{BrowserDetector, BrowserDetectionResult},
    registration::{BrowserRegistrationManager, RegistrationConfig, RegistrationStatus},
};
use crate::native_messaging::{
    error::{NativeMessagingError, Result},
    protocol::message::{MessageType, NativeMessage, OutgoingMessage, ErrorCode},
};
use std::collections::HashMap;
use std::path::PathBuf;
use std::time::Duration;
use tokio::time::timeout;

/// 浏览器适配器集成测试
pub struct BrowserIntegrationTestSuite {
    /// 测试配置
    test_config: IntegrationTestConfig,
    /// 测试结果
    test_results: HashMap<String, TestResult>,
}

/// 集成测试配置
#[derive(Debug, Clone)]
pub struct IntegrationTestConfig {
    /// 测试超时时间（毫秒）
    pub timeout_ms: u64,
    /// 是否跳过不支持的浏览器
    pub skip_unsupported: bool,
    /// 是否运行真实浏览器测试
    pub run_real_browser_tests: bool,
    /// 测试数据目录
    pub test_data_dir: PathBuf,
    /// 是否清理测试数据
    pub cleanup_after_test: bool,
}

impl Default for IntegrationTestConfig {
    fn default() -> Self {
        Self {
            timeout_ms: 30000, // 30秒
            skip_unsupported: true,
            run_real_browser_tests: false, // 默认不运行真实浏览器测试
            test_data_dir: PathBuf::from("test_data"),
            cleanup_after_test: true,
        }
    }
}

/// 测试结果
#[derive(Debug, Clone)]
pub struct TestResult {
    /// 测试名称
    pub test_name: String,
    /// 是否通过
    pub passed: bool,
    /// 执行时间（毫秒）
    pub duration_ms: u64,
    /// 错误信息（如果有）
    pub error_message: Option<String>,
    /// 测试详情
    pub details: HashMap<String, String>,
}

impl BrowserIntegrationTestSuite {
    /// 创建新的集成测试套件
    ///
    /// # 参数
    /// - `config`: 测试配置
    ///
    /// # 返回
    /// Self - 测试套件实例
    pub fn new(config: IntegrationTestConfig) -> Self {
        Self {
            test_config: config,
            test_results: HashMap::new(),
        }
    }

    /// 创建默认的测试套件
    ///
    /// # 返回
    /// Self - 默认测试套件
    pub fn new_default() -> Self {
        Self::new(IntegrationTestConfig::default())
    }

    /// 运行所有集成测试
    ///
    /// # 返回
    /// Result<HashMap<String, TestResult>> - 所有测试结果
    pub async fn run_all_tests(&mut self) -> Result<HashMap<String, TestResult>> {
        println!("🚀 开始运行浏览器适配层集成测试...");

        // 1. 浏览器检测测试
        self.run_browser_detection_tests().await;

        // 2. 适配器创建测试
        self.run_adapter_creation_tests().await;

        // 3. 消息通信测试
        self.run_message_communication_tests().await;

        // 4. 注册管理器测试
        self.run_registration_manager_tests().await;

        // 5. 跨平台兼容性测试
        self.run_cross_platform_tests().await;

        // 6. 性能基准测试
        self.run_performance_tests().await;

        // 7. 错误处理测试
        self.run_error_handling_tests().await;

        println!("✅ 集成测试完成，共运行 {} 个测试", self.test_results.len());
        self.print_test_summary();

        Ok(self.test_results.clone())
    }

    /// 运行浏览器检测测试
    async fn run_browser_detection_tests(&mut self) {
        println!("🔍 运行浏览器检测测试...");

        // 测试基本浏览器检测
        self.run_test("browser_detection_basic", || async {
            let browser_type = BrowserDetector::detect_browser().await?;
            assert!(matches!(
                browser_type,
                BrowserType::Chrome | BrowserType::Firefox | BrowserType::Edge | BrowserType::Safari
            ));
            Ok(())
        }).await;

        // 测试详细浏览器检测
        self.run_test("browser_detection_detailed", || async {
            let detection = BrowserDetector::detect_browser_detailed().await?;
            assert!(!detection.version.is_empty());
            assert!(detection.confidence >= 0.0);
            assert!(detection.confidence <= 1.0);
            Ok(())
        }).await;

        // 测试所有浏览器检测
        self.run_test("detect_all_browsers", || async {
            let browsers = BrowserDetector::detect_all_browsers().await;
            assert!(!browsers.is_empty());
            assert!(browsers.len() <= 4);
            
            // 验证每个检测到的浏览器
            for browser in browsers {
                assert!(!browser.version.is_empty());
                assert!(browser.confidence > 0.0);
            }
            Ok(())
        }).await;

        // 测试浏览器可用性检查
        self.run_test("browser_availability_check", || async {
            for browser_type in [BrowserType::Chrome, BrowserType::Firefox, BrowserType::Edge, BrowserType::Safari] {
                let available = BrowserDetector::is_browser_available(&browser_type).await;
                // 在CI环境中，可能没有真实浏览器，所以这里只记录结果
                println!("  {} 可用性: {}", format!("{:?}", browser_type), available);
            }
            Ok(())
        }).await;

        // 测试浏览器安装信息
        self.run_test("browser_install_info", || async {
            for browser_type in [BrowserType::Chrome, BrowserType::Firefox] {
                if let Ok(install_info) = BrowserDetector::get_browser_install_info(&browser_type).await {
                    println!("  {} 安装信息: {:?}", format!("{:?}", browser_type), install_info);
                    if install_info.is_installed {
                        assert!(!install_info.version.is_empty());
                        assert!(install_info.supports_native_messaging);
                    }
                }
            }
            Ok(())
        }).await;
    }

    /// 运行适配器创建测试
    async fn run_adapter_creation_tests(&mut self) {
        println!("🏗️ 运行适配器创建测试...");

        // 测试Chrome适配器创建
        self.run_test("chrome_adapter_creation", || async {
            let config = BrowserAdapterConfig::default();
            let adapter = ChromeAdapter::new(config)?;
            assert_eq!(adapter.browser_type(), BrowserType::Chrome);
            assert!(!adapter.browser_version().is_empty());
            assert!(!adapter.supported_message_types().is_empty());
            Ok(())
        }).await;

        // 测试Firefox适配器创建
        self.run_test("firefox_adapter_creation", || async {
            let config = BrowserAdapterConfig::default();
            let adapter = FirefoxAdapter::new(config)?;
            assert_eq!(adapter.browser_type(), BrowserType::Firefox);
            assert!(!adapter.browser_version().is_empty());
            Ok(())
        }).await;

        // 测试Edge适配器创建
        self.run_test("edge_adapter_creation", || async {
            let config = BrowserAdapterConfig::default();
            let adapter = EdgeAdapter::new(config)?;
            assert_eq!(adapter.browser_type(), BrowserType::Edge);
            assert!(!adapter.browser_version().is_empty());
            Ok(())
        }).await;

        // 测试Safari适配器创建
        self.run_test("safari_adapter_creation", || async {
            let config = BrowserAdapterConfig::default();
            let adapter = SafariAdapter::new(config)?;
            assert_eq!(adapter.browser_type(), BrowserType::Safari);
            assert!(!adapter.browser_version().is_empty());
            Ok(())
        }).await;

        // 测试Host配置创建
        self.run_test("host_config_creation", || async {
            let chrome_config = ChromeHostConfig::default();
            assert!(!chrome_config.name.is_empty());
            assert!(!chrome_config.description.is_empty());

            let firefox_config = FirefoxHostConfig::default();
            assert!(!firefox_config.name.is_empty());
            
            let edge_config = EdgeHostConfig::default();
            assert!(!edge_config.name.is_empty());

            let safari_config = SafariHostConfig::default();
            assert!(!safari_config.name.is_empty());
            Ok(())
        }).await;
    }

    /// 运行消息通信测试
    async fn run_message_communication_tests(&mut self) {
        println!("📨 运行消息通信测试...");

        // 测试消息创建和验证
        self.run_test("message_creation_validation", || async {
            let message = OutgoingMessage::success(
                "test-request".to_string(),
                serde_json::json!({"test": "data"}),
            );
            
            assert_eq!(message.message.request_id, "test-request");
            assert_eq!(message.message.message_type, MessageType::Success);
            assert!(!message.message.payload.is_null());
            Ok(())
        }).await;

        // 测试不同类型的消息
        self.run_test("different_message_types", || async {
            let ping_message = NativeMessage::new(
                MessageType::Ping,
                "ping-1".to_string(),
                serde_json::json!({}),
                "test".to_string()
            );
            assert_eq!(ping_message.message_type, MessageType::Ping);

            let error = OutgoingMessage::error(
                "error-1".to_string(),
                ErrorCode::InvalidMessageFormat,
                "Test error".to_string(),
            );
            assert_eq!(error.message.message_type, MessageType::Error);
            Ok(())
        }).await;

        // 测试消息序列化
        self.run_test("message_serialization", || async {
            let message = OutgoingMessage::success(
                "serialize-test".to_string(),
                serde_json::json!({"key": "value", "number": 42}),
            );

            let serialized = serde_json::to_string(&message.message)?;
            assert!(serialized.contains("serialize-test"));
            assert!(serialized.contains("value"));
            assert!(serialized.contains("42"));

            // 测试反序列化
            let _deserialized: NativeMessage = serde_json::from_str(&serialized)?;
            Ok(())
        }).await;
    }

    /// 运行注册管理器测试
    async fn run_registration_manager_tests(&mut self) {
        println!("📋 运行注册管理器测试...");

        // 测试注册管理器创建
        self.run_test("registration_manager_creation", || async {
            let config = RegistrationConfig::default();
            let manager = BrowserRegistrationManager::new(config);
            
            // 验证初始状态
            let status = manager.get_registration_status();
            assert!(status.is_empty()); // 初始时没有注册信息
            Ok(())
        }).await;

        // 测试注册验证
        self.run_test("registration_validation", || async {
            let config = RegistrationConfig::default();
            let mut manager = BrowserRegistrationManager::new(config);

            // 验证注册状态
            let validation_results = manager.validate_registrations()?;
            
            // 验证结果格式
            for (browser_type, status) in validation_results {
                println!("  {} 验证状态: {:?}", format!("{:?}", browser_type), status);
                assert!(matches!(
                    status,
                    RegistrationStatus::NotRegistered | 
                    RegistrationStatus::Registered | 
                    RegistrationStatus::Failed(_) |
                    RegistrationStatus::NeedsUpdate |
                    RegistrationStatus::BackupExists
                ));
            }
            Ok(())
        }).await;
    }

    /// 运行跨平台兼容性测试
    async fn run_cross_platform_tests(&mut self) {
        println!("🌐 运行跨平台兼容性测试...");

        // 测试平台特定的浏览器检测
        self.run_test("platform_specific_detection", || async {
            let priority = BrowserDetector::get_browser_priority();
            assert_eq!(priority.len(), 4);
            
            // 验证平台优先级
            #[cfg(target_os = "windows")]
            {
                assert_eq!(priority[0], BrowserType::Edge);
            }
            #[cfg(target_os = "macos")]
            {
                assert_eq!(priority[0], BrowserType::Safari);
            }
            #[cfg(target_os = "linux")]
            {
                assert_eq!(priority[0], BrowserType::Firefox);
            }
            Ok(())
        }).await;

        // 测试浏览器类型字符串转换
        self.run_test("browser_type_string_conversion", || async {
            let chrome_type = MessageType::Custom("chrome".to_string());
            assert_eq!(chrome_type.as_string(), "custom_chrome");

            let ping_type = MessageType::Ping;
            assert_eq!(ping_type.as_string(), "ping");
            Ok(())
        }).await;
    }

    /// 运行性能基准测试
    async fn run_performance_tests(&mut self) {
        println!("⚡ 运行性能基准测试...");

        // 测试适配器创建性能
        self.run_test("adapter_creation_performance", || async {
            let start = std::time::Instant::now();
            
            for _ in 0..100 {
                let config = BrowserAdapterConfig::default();
                let _adapter = ChromeAdapter::new(config)?;
            }
            
            let duration = start.elapsed();
            println!("  100个适配器创建耗时: {:?}", duration);
            
            // 平均每个适配器创建应该小于10ms
            assert!(duration.as_millis() < 1000);
            Ok(())
        }).await;

        // 测试浏览器检测性能
        self.run_test("browser_detection_performance", || async {
            let start = std::time::Instant::now();
            
            for _ in 0..10 {
                let _browser = BrowserDetector::detect_browser().await?;
            }
            
            let duration = start.elapsed();
            println!("  10次浏览器检测耗时: {:?}", duration);
            
            // 平均每次检测应该小于100ms
            assert!(duration.as_millis() < 1000);
            Ok(())
        }).await;

        // 测试消息序列化性能
        self.run_test("message_serialization_performance", || async {
            let message = OutgoingMessage::success(
                "perf-test".to_string(),
                serde_json::json!({"data": "test".repeat(1000)}),
            );
            
            let start = std::time::Instant::now();
            
            for _ in 0..1000 {
                let _serialized = serde_json::to_string(&message.message)?;
            }
            
            let duration = start.elapsed();
            println!("  1000次消息序列化耗时: {:?}", duration);
            
            // 应该小于100ms
            assert!(duration.as_millis() < 100);
            Ok(())
        }).await;
    }

    /// 运行错误处理测试
    async fn run_error_handling_tests(&mut self) {
        println!("🚨 运行错误处理测试...");

        // 测试无效配置处理
        self.run_test("invalid_config_handling", || async {
            // 测试空的浏览器版本
            let mut config = BrowserAdapterConfig::default();
            config.browser_version = "".to_string();
            
            // 应该能够处理空版本
            let adapter = ChromeAdapter::new(config)?;
            assert!(!adapter.browser_version().is_empty()); // 应该有默认值
            Ok(())
        }).await;

        // 测试消息验证错误
        self.run_test("message_validation_errors", || async {
            // 测试空的消息ID
            let message = OutgoingMessage::success(
                "".to_string(), // 空ID
                serde_json::json!({"test": "data"}),
            );
            
            // 某些适配器可能会验证消息格式
            // 这里只是确保能够创建消息
            assert!(message.message.request_id.is_empty());
            Ok(())
        }).await;

        // 测试连接超时处理
        self.run_test("connection_timeout_handling", || async {
            let config = BrowserAdapterConfig {
                connection_timeout: 1, // 1ms，很容易超时
                ..Default::default()
            };
            
            let adapter = ChromeAdapter::new(config)?;
            
            // 测试初始化超时
            let result = timeout(
                Duration::from_millis(100),
                async { adapter.is_connected().await }
            ).await;
            
            assert!(result.is_ok()); // 应该在超时时间内完成
            Ok(())
        }).await;
    }

    /// 运行单个测试
    async fn run_test<F, Fut>(&mut self, test_name: &str, test_fn: F)
    where
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = Result<()>>,
    {
        let start_time = std::time::Instant::now();
        
        let result = timeout(
            Duration::from_millis(self.test_config.timeout_ms),
            test_fn()
        ).await;
        
        let duration = start_time.elapsed();
        
        let test_result = match result {
            Ok(Ok(())) => TestResult {
                test_name: test_name.to_string(),
                passed: true,
                duration_ms: duration.as_millis() as u64,
                error_message: None,
                details: HashMap::new(),
            },
            Ok(Err(e)) => TestResult {
                test_name: test_name.to_string(),
                passed: false,
                duration_ms: duration.as_millis() as u64,
                error_message: Some(format!("测试失败: {}", e)),
                details: HashMap::new(),
            },
            Err(_) => TestResult {
                test_name: test_name.to_string(),
                passed: false,
                duration_ms: self.test_config.timeout_ms,
                error_message: Some("测试超时".to_string()),
                details: HashMap::new(),
            },
        };
        
        let status_icon = if test_result.passed { "✅" } else { "❌" };
        println!("  {} {} ({}ms)", status_icon, test_name, test_result.duration_ms);
        
        if let Some(ref error) = test_result.error_message {
            println!("    错误: {}", error);
        }
        
        self.test_results.insert(test_name.to_string(), test_result);
    }

    /// 打印测试总结
    fn print_test_summary(&self) {
        let total_tests = self.test_results.len();
        let passed_tests = self.test_results.values().filter(|r| r.passed).count();
        let failed_tests = total_tests - passed_tests;
        
        println!("\n📊 测试总结:");
        println!("  总测试数: {}", total_tests);
        println!("  通过: {} ✅", passed_tests);
        println!("  失败: {} ❌", failed_tests);
        
        if failed_tests > 0 {
            println!("\n❌ 失败的测试:");
            for (name, result) in &self.test_results {
                if !result.passed {
                    println!("  - {}: {}", name, result.error_message.as_deref().unwrap_or("未知错误"));
                }
            }
        }
        
        let total_duration: u64 = self.test_results.values().map(|r| r.duration_ms).sum();
        println!("  总耗时: {}ms", total_duration);
        
        let success_rate = (passed_tests as f64 / total_tests as f64) * 100.0;
        println!("  成功率: {:.1}%", success_rate);
    }

    /// 获取测试结果
    ///
    /// # 返回
    /// &HashMap<String, TestResult> - 测试结果引用
    pub fn get_test_results(&self) -> &HashMap<String, TestResult> {
        &self.test_results
    }

    /// 获取失败的测试
    ///
    /// # 返回
    /// Vec<&TestResult> - 失败的测试列表
    pub fn get_failed_tests(&self) -> Vec<&TestResult> {
        self.test_results.values().filter(|r| !r.passed).collect()
    }

    /// 获取测试成功率
    ///
    /// # 返回
    /// f64 - 成功率 (0.0-1.0)
    pub fn get_success_rate(&self) -> f64 {
        if self.test_results.is_empty() {
            return 0.0;
        }
        
        let passed = self.test_results.values().filter(|r| r.passed).count();
        passed as f64 / self.test_results.len() as f64
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_integration_test_suite() {
        let mut suite = BrowserIntegrationTestSuite::new_default();
        let results = suite.run_all_tests().await.unwrap();
        
        assert!(!results.is_empty());
        
        // 验证至少有一些测试通过
        let success_rate = suite.get_success_rate();
        assert!(success_rate > 0.0);
        
        println!("集成测试成功率: {:.1}%", success_rate * 100.0);
    }

    #[tokio::test]
    async fn test_browser_detection_integration() {
        let mut suite = BrowserIntegrationTestSuite::new_default();
        suite.run_browser_detection_tests().await;
        
        let detection_tests = suite.get_test_results()
            .iter()
            .filter(|(name, _)| name.contains("detection"))
            .count();
        
        assert!(detection_tests > 0);
    }

    #[tokio::test]
    async fn test_adapter_creation_integration() {
        let mut suite = BrowserIntegrationTestSuite::new_default();
        suite.run_adapter_creation_tests().await;
        
        let adapter_tests = suite.get_test_results()
            .iter()
            .filter(|(name, _)| name.contains("adapter"))
            .count();
        
        assert!(adapter_tests > 0);
    }

    #[tokio::test]
    async fn test_performance_integration() {
        let mut suite = BrowserIntegrationTestSuite::new_default();
        suite.run_performance_tests().await;
        
        let perf_tests = suite.get_test_results()
            .iter()
            .filter(|(name, _)| name.contains("performance"))
            .count();
        
        assert!(perf_tests > 0);
        
        // 验证性能测试都通过了
        let failed_perf_tests = suite.get_test_results()
            .iter()
            .filter(|(name, result)| name.contains("performance") && !result.passed)
            .count();
        
        assert_eq!(failed_perf_tests, 0, "性能测试不应该失败");
    }
} 