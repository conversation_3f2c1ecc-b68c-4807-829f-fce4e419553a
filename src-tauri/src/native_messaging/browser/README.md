# Native Messaging 浏览器适配层模块

## 概述

浏览器适配层模块是企业级 Native Messaging 系统的核心组件之一，提供跨主流浏览器的统一适配接口和实现。该模块支持 Chrome、Firefox、Edge 和 Safari 四种主流浏览器，实现真实的 Native Messaging 协议通信。

### 核心特性

- 🌐 **多浏览器支持**: Chrome、Firefox、Edge、Safari 四种主流浏览器
- 🔄 **统一接口**: 提供一致的 API 接口，屏蔽浏览器差异
- 🎯 **真实实现**: 基于实际的 Native Messaging 协议，非模拟实现
- 🔍 **自动检测**: 智能检测系统已安装的浏览器和版本
- 📦 **自动注册**: 自动生成和注册 Native Messaging Host 配置
- 🚀 **高性能**: 异步非阻塞通信，优化的消息处理
- 🛡️ **错误处理**: 完善的错误处理和故障转移机制
- 📊 **监控统计**: 详细的性能统计和健康监控

## 架构设计

### 模块结构

```
browser/
├── mod.rs                  # 模块入口和公共类型
├── detector.rs            # 浏览器检测器
├── chrome.rs              # Chrome 适配器
├── firefox.rs             # Firefox 适配器
├── edge.rs                # Edge 适配器
├── safari.rs              # Safari 适配器
├── manager.rs             # 浏览器管理器
├── registration.rs        # 注册管理器
├── templates.rs           # 配置模板生成器
├── integration_tests.rs   # 集成测试套件
└── README.md              # 模块文档
```

### 核心组件

#### 1. BrowserAdapter Trait
统一的浏览器适配器接口，定义所有浏览器适配器必须实现的方法：

```rust
#[async_trait]
pub trait BrowserAdapter: Send + Sync {
    fn browser_type(&self) -> BrowserType;
    fn browser_version(&self) -> &str;
    async fn send_message(&self, message: OutgoingMessage) -> Result<()>;
    async fn receive_message(&self) -> Result<NativeMessage>;
    async fn is_connected(&self) -> bool;
    async fn initialize(&mut self) -> Result<()>;
    async fn close(&mut self) -> Result<()>;
    fn supported_message_types(&self) -> Vec<String>;
    fn config(&self) -> &BrowserAdapterConfig;
}
```

#### 2. 浏览器检测器 (BrowserDetector)
智能检测系统中已安装的浏览器：

- **多重检测策略**: 环境变量 → 进程信息 → 系统默认浏览器 → 安装检测
- **跨平台支持**: Windows、macOS、Linux 平台特定检测逻辑
- **版本信息**: 通过命令行或系统调用获取真实版本信息
- **置信度评估**: 为检测结果提供置信度评分

#### 3. 浏览器管理器 (BrowserManager)
统一管理多个浏览器适配器：

- **自动检测注册**: 自动检测并注册所有可用浏览器
- **故障转移**: 当活跃浏览器不可用时自动切换到备用浏览器
- **负载均衡**: 支持多浏览器负载分担（可选）
- **健康监控**: 定期检查浏览器连接状态

#### 4. 注册管理器 (BrowserRegistrationManager)
自动化 Native Messaging Host 注册：

- **自动注册**: 检测已安装浏览器并自动注册配置
- **跨平台路径**: 为每种浏览器生成正确的配置文件路径
- **备份恢复**: 自动备份现有配置，支持恢复
- **状态跟踪**: 跟踪注册状态和历史记录

## 快速开始

### 基本使用

```rust
use crate::native_messaging::browser::{
    BrowserAdapterFactory, BrowserDetector, BrowserType
};

// 1. 自动检测浏览器并创建适配器
let adapter = BrowserAdapterFactory::auto_detect_adapter().await?;

// 2. 手动指定浏览器类型
let chrome_adapter = BrowserAdapterFactory::create_adapter(
    BrowserType::Chrome,
    BrowserAdapterConfig::default()
)?;

// 3. 发送消息
let message = OutgoingMessage::new("test_message", serde_json::json!({
    "action": "ping",
    "data": "hello"
}));
adapter.send_message(message).await?;

// 4. 接收消息
let response = adapter.receive_message().await?;
println!("收到响应: {:?}", response);
```

### 使用浏览器管理器

```rust
use crate::native_messaging::browser::{
    BrowserManager, BrowserManagerConfig
};

// 1. 创建浏览器管理器
let config = BrowserManagerConfig {
    auto_detection: true,
    enable_failover: true,
    ..Default::default()
};
let manager = BrowserManager::new(config);

// 2. 初始化（自动检测和注册浏览器）
manager.initialize().await?;

// 3. 发送消息（自动选择最佳浏览器）
let message = OutgoingMessage::new("test", serde_json::json!({"ping": true}));
manager.send_message(message).await?;

// 4. 获取统计信息
let stats = manager.get_stats().await;
println!("活跃适配器数量: {}", stats.active_adapters);
```

### 自动注册 Native Messaging Host

```rust
use crate::native_messaging::browser::{
    BrowserRegistrationManager, RegistrationConfig
};

// 1. 创建注册管理器
let config = RegistrationConfig {
    auto_register: true,
    force_overwrite: false,
    backup_existing: true,
    ..Default::default()
};
let manager = BrowserRegistrationManager::new(config);

// 2. 自动注册所有支持的浏览器
manager.auto_register_all().await?;

// 3. 检查注册状态
let status = manager.get_registration_status(&BrowserType::Chrome).await?;
println!("Chrome 注册状态: {:?}", status);
```

## API 参考

### BrowserDetector API

#### 主要方法

- `detect_browser()` - 检测主要浏览器
- `detect_all_browsers()` - 检测所有已安装浏览器  
- `find_browser_executable(browser_type)` - 查找浏览器可执行文件
- `get_browser_install_info(browser_type)` - 获取浏览器安装信息
- `is_browser_available(browser_type)` - 检查浏览器是否可用

#### 检测策略

1. **环境变量检测**: 检查 BROWSER、CHROME_BIN 等环境变量
2. **进程信息检测**: 检查当前运行的浏览器进程
3. **系统默认浏览器**: 查询系统默认浏览器设置
4. **安装路径检测**: 检查标准安装路径

### 浏览器适配器 API

#### Chrome 适配器 (ChromeAdapter)

```rust
// 创建适配器
let adapter = ChromeAdapter::new(config)?;
let adapter = ChromeAdapter::new_default()?;
let adapter = ChromeAdapter::new_with_host_config(host_config)?;

// Host 配置
let host_config = ChromeHostConfig {
    name: "com.secure_password.native_host".to_string(),
    description: "Secure Password Native Messaging Host".to_string(),
    path: "/path/to/host/executable".to_string(),
    message_type: "stdio".to_string(),
    allowed_origins: vec!["chrome-extension://extension-id/".to_string()],
    allowed_extensions: vec!["extension-id".to_string()],
};
```

#### Firefox 适配器 (FirefoxAdapter)

```rust
// Firefox 特定配置
let host_config = FirefoxHostConfig {
    name: "com.secure_password.native_host".to_string(),
    description: "Secure Password Native Messaging Host".to_string(),
    path: "/path/to/host/executable".to_string(),
    message_type: "stdio".to_string(),
    allowed_extensions: vec!["<EMAIL>".to_string()],
};

// 支持 ESR 和 Developer 版本检测
let firefox_path = FirefoxAdapter::find_firefox_executable()?;
```

#### Edge 适配器 (EdgeAdapter)

```rust
// Edge 特定安全配置
let host_config = EdgeHostConfig {
    name: "com.secure_password.native_host".to_string(),
    description: "Secure Password Native Messaging Host".to_string(),
    path: "/path/to/host/executable".to_string(),
    message_type: "stdio".to_string(),
    allowed_origins: vec!["extension://extension-id/".to_string()],
    allowed_extensions: vec!["extension-id".to_string()],
    microsoft_security_token: Some("security-token".to_string()),
};
```

#### Safari 适配器 (SafariAdapter)

```rust
// Safari 特定配置（仅 macOS）
let host_config = SafariHostConfig {
    name: "com.secure_password.native_host".to_string(),
    description: "Secure Password Native Messaging Host".to_string(),
    path: "/path/to/host/executable".to_string(),
    message_type: "stdio".to_string(),
    bundle_id: "com.secure-password.app".to_string(),
    team_identifier: "TEAM123456".to_string(),
    allowed_origins: vec!["safari-web-extension://extension-id/".to_string()],
};
```

### BrowserManager API

#### 配置选项

```rust
let config = BrowserManagerConfig {
    auto_detection: true,              // 启用自动检测
    enable_failover: true,             // 启用故障转移
    failover_timeout: 5000,            // 故障转移超时（毫秒）
    max_retries: 3,                    // 最大重试次数
    health_check_interval: 30000,      // 健康检查间隔（毫秒）
    enable_load_balancing: false,      // 启用负载均衡
};
```

#### 主要方法

- `initialize()` - 初始化管理器
- `register_adapter(browser_type, adapter)` - 注册适配器
- `unregister_adapter(browser_type)` - 注销适配器
- `send_message(message)` - 发送消息
- `receive_message()` - 接收消息
- `get_active_adapter()` - 获取活跃适配器
- `health_check()` - 执行健康检查
- `get_stats()` - 获取统计信息

### BrowserRegistrationManager API

#### 注册配置

```rust
let config = RegistrationConfig {
    auto_register: true,               // 自动注册
    force_overwrite: false,            // 强制覆盖现有配置
    backup_existing: true,             // 备份现有配置
    host_executable_path: PathBuf::from("/path/to/host"),
    host_name: "com.secure_password.native_host".to_string(),
    host_description: "Secure Password Native Messaging Host".to_string(),
};
```

#### 主要方法

- `auto_register_all()` - 自动注册所有浏览器
- `register_browser(browser_type)` - 注册指定浏览器
- `unregister_browser(browser_type)` - 注销指定浏览器
- `get_registration_status(browser_type)` - 获取注册状态
- `backup_existing_config(browser_type)` - 备份现有配置
- `restore_config(browser_type)` - 恢复配置

## 平台支持

### Windows 平台

#### 支持的浏览器
- ✅ Chrome (包括 Stable、Beta、Dev、Canary)
- ✅ Firefox (包括 Standard、ESR、Developer)
- ✅ Edge (包括 Stable、Beta、Dev、Canary)
- ❌ Safari (不支持)

#### 检测机制
- **注册表查询**: 通过注册表获取默认浏览器和安装路径
- **进程检测**: 使用 `tasklist` 命令检测运行中的浏览器
- **标准路径**: 检查 Program Files、AppData 等标准安装路径

#### 配置路径
- Chrome: `%LOCALAPPDATA%\Google\Chrome\User Data\NativeMessagingHosts`
- Firefox: `%APPDATA%\Mozilla\NativeMessagingHosts`
- Edge: `%LOCALAPPDATA%\Microsoft\Edge\User Data\NativeMessagingHosts`

### macOS 平台

#### 支持的浏览器
- ✅ Chrome (包括 Stable、Beta、Dev、Canary)
- ✅ Firefox (包括 Standard、ESR、Developer)
- ✅ Edge (包括 Stable、Beta、Dev、Canary)
- ✅ Safari (包括 Standard、Technology Preview)

#### 检测机制
- **defaults 命令**: 使用 `defaults read` 查询默认浏览器
- **进程检测**: 使用 `ps` 命令检测运行中的浏览器
- **应用包检测**: 检查 /Applications 和 /System/Applications
- **Info.plist 解析**: 读取应用包中的版本信息

#### 配置路径
- Chrome: `~/Library/Application Support/Google/Chrome/NativeMessagingHosts`
- Firefox: `~/Library/Application Support/Mozilla/NativeMessagingHosts`
- Edge: `~/Library/Application Support/Microsoft Edge/NativeMessagingHosts`
- Safari: `~/Library/Safari/NativeMessagingHosts`

### Linux 平台

#### 支持的浏览器
- ✅ Chrome (包括 Google Chrome、Chromium)
- ✅ Firefox (包括 Firefox、Firefox ESR)
- ✅ Edge (Microsoft Edge for Linux)
- ❌ Safari (不支持)

#### 检测机制
- **xdg-settings**: 使用 xdg-settings 查询默认浏览器
- **which/whereis**: 使用命令行工具查找可执行文件
- **desktop 文件**: 解析 .desktop 文件获取应用信息
- **mimeapps.list**: 检查 MIME 类型关联

#### 配置路径
- Chrome: `~/.config/google-chrome/NativeMessagingHosts`
- Chromium: `~/.config/chromium/NativeMessagingHosts`
- Firefox: `~/.mozilla/native-messaging-hosts`
- Edge: `~/.config/microsoft-edge/NativeMessagingHosts`

## 配置和模板

### 配置模板生成器

模块提供了 `ConfigTemplateManager` 用于生成各种配置文件和安装脚本：

```rust
use crate::native_messaging::browser::templates::{
    ConfigTemplateManager, TemplateConfig
};

let config = TemplateConfig {
    host_name: "com.secure_password.native_host".to_string(),
    host_description: "Secure Password Native Messaging Host".to_string(),
    host_executable_path: PathBuf::from("/usr/local/bin/secure-password-host"),
    allowed_origins: vec!["chrome-extension://abc123/".to_string()],
    allowed_extensions: vec!["abc123".to_string()],
};

let manager = ConfigTemplateManager::new(config);

// 生成 Chrome manifest
let chrome_manifest = manager.generate_chrome_manifest()?;

// 生成安装脚本
let install_script = manager.generate_install_script_for_platform(&Platform::Linux)?;

// 生成 README 文档
let readme = manager.generate_readme()?;
```

### Native Messaging Manifest 格式

#### Chrome/Edge Manifest
```json
{
  "name": "com.secure_password.native_host",
  "description": "Secure Password Native Messaging Host",
  "path": "/usr/local/bin/secure-password-host",
  "type": "stdio",
  "allowed_origins": [
    "chrome-extension://extension-id/"
  ]
}
```

#### Firefox Manifest
```json
{
  "name": "com.secure_password.native_host",
  "description": "Secure Password Native Messaging Host",
  "path": "/usr/local/bin/secure-password-host",
  "type": "stdio",
  "allowed_extensions": [
    "<EMAIL>"
  ]
}
```

#### Safari Manifest
```json
{
  "name": "com.secure_password.native_host",
  "description": "Secure Password Native Messaging Host",
  "path": "/usr/local/bin/secure-password-host",
  "type": "stdio",
  "allowed_origins": [
    "safari-web-extension://extension-id/"
  ],
  "bundle_identifier": "com.secure-password.app",
  "team_identifier": "TEAM123456"
}
```

## 性能优化

### 连接池和缓存

```rust
// 启用连接池（在 BrowserManager 中）
let config = BrowserManagerConfig {
    enable_load_balancing: true,
    health_check_interval: 10000, // 更频繁的健康检查
    ..Default::default()
};

// 配置适配器缓存
let adapter_config = BrowserAdapterConfig {
    connection_timeout: 3000,     // 较短的连接超时
    message_timeout: 8000,        // 适中的消息超时
    max_retries: 2,               // 较少的重试次数
    debug_mode: false,            // 关闭调试模式
    ..Default::default()
};
```

### 异步处理优化

```rust
// 并行初始化多个适配器
let browsers = vec![BrowserType::Chrome, BrowserType::Firefox, BrowserType::Edge];
let init_tasks: Vec<_> = browsers.into_iter().map(|browser_type| {
    tokio::spawn(async move {
        BrowserAdapterFactory::create_adapter(browser_type, config.clone())
    })
}).collect();

let adapters = futures::future::join_all(init_tasks).await;
```

### 消息批处理

```rust
// 批量发送消息
let messages = vec![
    OutgoingMessage::new("msg1", json!({"data": 1})),
    OutgoingMessage::new("msg2", json!({"data": 2})),
    OutgoingMessage::new("msg3", json!({"data": 3})),
];

for message in messages {
    tokio::spawn(async move {
        adapter.send_message(message).await
    });
}
```

## 错误处理和故障排除

### 常见错误类型

#### 1. 浏览器未找到错误
```rust
match adapter.initialize().await {
    Err(NativeMessagingError::BrowserNotFound(browser)) => {
        eprintln!("浏览器 {} 未安装或不可访问", browser);
        // 尝试故障转移到其他浏览器
    },
    Ok(_) => {
        println!("浏览器连接成功");
    },
    Err(e) => {
        eprintln!("其他错误: {}", e);
    }
}
```

#### 2. 配置错误
```rust
match manager.register_browser(&BrowserType::Chrome).await {
    Err(NativeMessagingError::ConfigurationError(msg)) => {
        eprintln!("配置错误: {}", msg);
        // 检查 manifest 文件格式
        // 验证路径和权限
    },
    Ok(_) => {
        println!("浏览器注册成功");
    },
    Err(e) => {
        eprintln!("注册失败: {}", e);
    }
}
```

#### 3. 通信错误
```rust
match adapter.send_message(message).await {
    Err(NativeMessagingError::ProtocolError(msg)) => {
        eprintln!("协议错误: {}", msg);
        // 检查消息格式
        // 验证浏览器连接状态
    },
    Err(NativeMessagingError::TimeoutError) => {
        eprintln!("消息发送超时");
        // 增加超时时间或重试
    },
    Ok(_) => {
        println!("消息发送成功");
    },
    Err(e) => {
        eprintln!("发送失败: {}", e);
    }
}
```

### 故障排除步骤

#### 1. 浏览器检测问题

```bash
# 检查浏览器是否已安装
which chrome
which firefox
which msedge

# 检查浏览器版本
chrome --version
firefox --version
msedge --version
```

```rust
// 使用检测器调试
let detection_results = BrowserDetector::detect_all_browsers().await;
for result in detection_results {
    println!("检测到浏览器: {:?}", result);
    println!("置信度: {:.2}", result.confidence);
}
```

#### 2. 配置文件问题

```rust
// 检查配置文件路径
let chrome_path = BrowserRegistrationManager::get_manifest_path(&BrowserType::Chrome)?;
println!("Chrome manifest 路径: {:?}", chrome_path);

// 验证配置文件内容
let manifest_content = std::fs::read_to_string(&chrome_path)?;
let manifest: serde_json::Value = serde_json::from_str(&manifest_content)?;
println!("Manifest 内容: {:#}", manifest);
```

#### 3. 权限问题

```bash
# 检查文件权限
ls -la ~/.config/google-chrome/NativeMessagingHosts/
ls -la /usr/local/bin/secure-password-host

# 修复权限
chmod 755 /usr/local/bin/secure-password-host
chmod 644 ~/.config/google-chrome/NativeMessagingHosts/com.secure_password.native_host.json
```

#### 4. 进程和连接问题

```rust
// 检查适配器连接状态
if !adapter.is_connected().await {
    println!("适配器未连接，尝试重新初始化");
    adapter.initialize().await?;
}

// 获取详细统计信息
let stats = adapter.get_stats().await;
println!("发送成功率: {:.2}%", stats.success_rate() * 100.0);
println!("平均响应时间: {:?}", stats.average_response_time);
```

### 调试模式

```rust
// 启用调试模式
let config = BrowserAdapterConfig {
    debug_mode: true,
    ..Default::default()
};

// 调试模式下会输出详细的通信日志
let adapter = ChromeAdapter::new(config)?;
```

## 测试

### 单元测试

```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_browser_detection() {
        let browser = BrowserDetector::detect_browser().await.unwrap();
        assert!(!browser.version.is_empty());
    }

    #[tokio::test]
    async fn test_adapter_creation() {
        let config = BrowserAdapterConfig::default();
        let adapter = ChromeAdapter::new(config).unwrap();
        assert_eq!(adapter.browser_type(), BrowserType::Chrome);
    }
}
```

### 集成测试

```rust
// 使用集成测试套件
let mut test_suite = BrowserIntegrationTestSuite::new_default();
let results = test_suite.run_all_tests().await?;

// 检查测试结果
for (test_name, result) in results {
    if result.passed {
        println!("✅ {}: 通过 ({}ms)", test_name, result.duration_ms);
    } else {
        println!("❌ {}: 失败 - {}", test_name, 
                result.error_message.unwrap_or("未知错误".to_string()));
    }
}
```

### 性能测试

```rust
// 性能基准测试
let start = std::time::Instant::now();
for _ in 0..1000 {
    let _adapter = ChromeAdapter::new(config.clone())?;
}
let duration = start.elapsed();
println!("1000个适配器创建耗时: {:?}", duration);
assert!(duration.as_millis() < 1000); // 应该小于1秒
```

## 最佳实践

### 1. 适配器生命周期管理

```rust
// 推荐：使用 BrowserManager 管理适配器生命周期
let manager = BrowserManager::new_default();
manager.initialize().await?;

// 在应用结束时正确关闭
tokio::spawn(async move {
    tokio::signal::ctrl_c().await.unwrap();
    manager.close().await.unwrap();
});
```

### 2. 错误处理策略

```rust
// 推荐：分层错误处理
async fn send_message_with_retry(
    manager: &BrowserManager,
    message: OutgoingMessage,
    max_retries: u32,
) -> Result<()> {
    let mut retries = 0;
    
    loop {
        match manager.send_message(message.clone()).await {
            Ok(_) => return Ok(()),
            Err(NativeMessagingError::TimeoutError) if retries < max_retries => {
                retries += 1;
                tokio::time::sleep(Duration::from_millis(1000)).await;
                continue;
            },
            Err(NativeMessagingError::BrowserNotFound(_)) => {
                // 尝试故障转移
                manager.try_failover().await?;
                if retries < max_retries {
                    retries += 1;
                    continue;
                }
                return Err(NativeMessagingError::ProtocolError(
                    "所有浏览器都不可用".to_string()
                ));
            },
            Err(e) => return Err(e),
        }
    }
}
```

### 3. 性能优化建议

```rust
// 推荐：连接池复用
struct ConnectionPool {
    adapters: HashMap<BrowserType, Arc<dyn BrowserAdapter>>,
}

impl ConnectionPool {
    async fn get_adapter(&self, browser_type: &BrowserType) -> Option<Arc<dyn BrowserAdapter>> {
        self.adapters.get(browser_type).cloned()
    }
    
    async fn return_adapter(&self, browser_type: BrowserType, adapter: Arc<dyn BrowserAdapter>) {
        // 检查连接状态，决定是否保留或丢弃
        if adapter.is_connected().await {
            // 保留连接
        } else {
            // 丢弃并创建新连接
        }
    }
}
```

### 4. 配置管理

```rust
// 推荐：统一配置管理
#[derive(Debug, Clone)]
struct AppConfig {
    browser_config: BrowserManagerConfig,
    registration_config: RegistrationConfig,
    template_config: TemplateConfig,
}

impl AppConfig {
    fn from_file(path: &Path) -> Result<Self> {
        let content = std::fs::read_to_string(path)?;
        let config: Self = serde_json::from_str(&content)?;
        Ok(config)
    }
    
    fn validate(&self) -> Result<()> {
        // 验证配置的有效性
        if self.browser_config.connection_timeout == 0 {
            return Err(NativeMessagingError::ConfigurationError(
                "连接超时不能为0".to_string()
            ));
        }
        Ok(())
    }
}
```

### 5. 监控和日志

```rust
// 推荐：结构化日志
use tracing::{info, warn, error, debug};

impl BrowserAdapter for ChromeAdapter {
    async fn send_message(&self, message: OutgoingMessage) -> Result<()> {
        debug!(
            browser = "chrome",
            message_type = %message.message_type,
            message_id = %message.request_id,
            "发送消息"
        );
        
        let start = std::time::Instant::now();
        let result = self.do_send_message(message).await;
        let duration = start.elapsed();
        
        match &result {
            Ok(_) => {
                info!(
                    browser = "chrome",
                    duration_ms = duration.as_millis(),
                    "消息发送成功"
                );
            },
            Err(e) => {
                error!(
                    browser = "chrome",
                    error = %e,
                    duration_ms = duration.as_millis(),
                    "消息发送失败"
                );
            }
        }
        
        result
    }
}
```

## 贡献指南

### 开发环境设置

1. 安装 Rust 和 Cargo
2. 安装目标浏览器（用于测试）
3. 运行测试：`cargo test`
4. 运行集成测试：`cargo test integration_tests`

### 代码规范

- 遵循 Rust 官方代码风格
- 使用 `cargo fmt` 格式化代码
- 使用 `cargo clippy` 检查代码质量
- 所有公共 API 必须有文档注释
- 测试覆盖率应达到 95% 以上

### 添加新浏览器支持

1. 创建新的适配器文件（如 `brave.rs`）
2. 实现 `BrowserAdapter` trait
3. 在 `BrowserType` 枚举中添加新类型
4. 更新 `BrowserDetector` 添加检测逻辑
5. 更新 `BrowserAdapterFactory` 添加创建逻辑
6. 添加相应的测试用例

### 提交 Pull Request

1. Fork 仓库
2. 创建特性分支
3. 提交代码并编写测试
4. 确保所有测试通过
5. 提交 Pull Request

## 版本历史

### v1.0.0 (当前版本)
- ✅ 实现四种主流浏览器适配器
- ✅ 浏览器自动检测和版本识别
- ✅ Native Messaging Host 自动注册
- ✅ 浏览器管理器和故障转移
- ✅ 配置模板生成器
- ✅ 完整的集成测试套件
- ✅ 跨平台支持（Windows、macOS、Linux）

### 规划中的功能
- 🔄 WebDriver 协议支持
- 🔄 浏览器扩展自动安装
- 🔄 远程浏览器支持
- 🔄 性能监控仪表板
- 🔄 浏览器沙箱集成

## 许可证

本模块使用与主项目相同的许可证。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 Issue
- 发送 Pull Request
- 项目讨论区

---

*最后更新: 2024年12月* 