//! Native Messaging 错误类型定义
//!
//! 定义了所有可能出现的错误类型和相应的错误处理机制

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use thiserror::Error;

/// Native Messaging 错误类型定义
///
/// 定义了所有可能出现的错误类型和相应的错误信息
#[derive(Debug, Error)]
pub enum NativeMessagingError {
    /// 配置相关错误
    #[error("配置错误: {0}")]
    ConfigError(String),

    /// 连接相关错误
    #[error("连接错误: {0}")]
    ConnectionError(String),

    /// 协议相关错误
    #[error("协议错误: {0}")]
    ProtocolError(String),

    /// 处理器相关错误
    #[error("处理器错误: {0}")]
    HandlerError(String),

    /// 安全验证失败
    #[error("安全验证失败: {0}")]
    SecurityError(String),

    /// 监控系统错误
    #[error("监控系统错误: {0}")]
    MonitoringError(String),

    /// 浏览器适配错误
    #[error("浏览器适配错误: {0}")]
    BrowserError(String),

    /// 浏览器未找到
    #[error("浏览器未找到: {0}")]
    BrowserNotFound(String),

    /// 系统错误
    #[error("系统错误: {0}")]
    SystemError(String),

    /// 配置错误
    #[error("配置错误: {0}")]
    ConfigurationError(String),

    /// I/O 相关错误
    #[error("I/O 错误: {0}")]
    IoError(#[from] std::io::Error),

    /// 序列化/反序列化错误
    #[error("序列化错误: {0}")]
    SerializationError(#[from] serde_json::Error),

    /// 任务取消错误
    #[error("任务已取消: {0}")]
    TaskCancelled(String),

    /// 超时错误
    #[error("操作超时: {0}")]
    TimeoutError(String),

    /// 内部错误
    #[error("内部错误: {0}")]
    InternalError(String),

    /// 速率限制错误
    #[error("速率限制: 来源 '{0}' 超过了每秒 {1} 请求的限制")]
    RateLimitExceeded(String, u64),
}

impl NativeMessagingError {
    /// 判断错误是否可恢复
    ///
    /// 确定错误类型是否允许系统继续运行并尝试恢复
    ///
    /// # 返回
    /// bool - true 表示可恢复，false 表示不可恢复
    ///
    /// # 可恢复错误类型
    /// - ConnectionError: 连接相关错误
    /// - HandlerError: 处理器错误
    /// - IoError: I/O 错误
    /// - TimeoutError: 超时错误
    pub fn is_recoverable(&self) -> bool {
        matches!(
            self,
            NativeMessagingError::ConnectionError(_)
                | NativeMessagingError::HandlerError(_)
                | NativeMessagingError::IoError(_)
                | NativeMessagingError::TimeoutError(_)
                | NativeMessagingError::BrowserError(_)
                | NativeMessagingError::MonitoringError(_)
                | NativeMessagingError::SystemError(_)
        )
    }

    /// 获取错误的严重程度
    ///
    /// 返回错误的严重程度级别，用于日志记录和监控
    ///
    /// # 返回
    /// ErrorSeverity - 错误严重程度枚举
    pub fn severity(&self) -> ErrorSeverity {
        match self {
            NativeMessagingError::SecurityError(_) => ErrorSeverity::Critical,
            NativeMessagingError::InternalError(_) => ErrorSeverity::Critical,
            NativeMessagingError::ConfigError(_) => ErrorSeverity::High,
            NativeMessagingError::ConfigurationError(_) => ErrorSeverity::High,
            NativeMessagingError::ProtocolError(_) => ErrorSeverity::High,
            NativeMessagingError::ConnectionError(_) => ErrorSeverity::Medium,
            NativeMessagingError::HandlerError(_) => ErrorSeverity::Medium,
            NativeMessagingError::TimeoutError(_) => ErrorSeverity::Medium,
            NativeMessagingError::BrowserError(_) => ErrorSeverity::Medium,
            NativeMessagingError::BrowserNotFound(_) => ErrorSeverity::Medium,
            NativeMessagingError::SystemError(_) => ErrorSeverity::Medium,
            NativeMessagingError::MonitoringError(_) => ErrorSeverity::Low,
            NativeMessagingError::IoError(_) => ErrorSeverity::Medium,
            NativeMessagingError::SerializationError(_) => ErrorSeverity::Medium,
            NativeMessagingError::TaskCancelled(_) => ErrorSeverity::Low,
            NativeMessagingError::RateLimitExceeded(_, _) => ErrorSeverity::Medium,
        }
    }

    /// 获取错误类别
    ///
    /// 返回错误的分类信息，用于统计和分析
    ///
    /// # 返回
    /// ErrorCategory - 错误类别
    pub fn category(&self) -> ErrorCategory {
        match self {
            NativeMessagingError::ConfigError(_) => ErrorCategory::Configuration,
            NativeMessagingError::ConfigurationError(_) => ErrorCategory::Configuration,
            NativeMessagingError::ConnectionError(_) => ErrorCategory::Network,
            NativeMessagingError::ProtocolError(_) => ErrorCategory::Protocol,
            NativeMessagingError::HandlerError(_) => ErrorCategory::Business,
            NativeMessagingError::SecurityError(_) => ErrorCategory::Security,
            NativeMessagingError::MonitoringError(_) => ErrorCategory::System,
            NativeMessagingError::BrowserError(_) => ErrorCategory::External,
            NativeMessagingError::BrowserNotFound(_) => ErrorCategory::External,
            NativeMessagingError::SystemError(_) => ErrorCategory::System,
            NativeMessagingError::IoError(_) => ErrorCategory::System,
            NativeMessagingError::SerializationError(_) => ErrorCategory::Protocol,
            NativeMessagingError::TaskCancelled(_) => ErrorCategory::System,
            NativeMessagingError::TimeoutError(_) => ErrorCategory::Network,
            NativeMessagingError::InternalError(_) => ErrorCategory::System,
            NativeMessagingError::RateLimitExceeded(_, _) => ErrorCategory::Network,
        }
    }

    /// 获取推荐的恢复策略
    ///
    /// 根据错误类型返回推荐的恢复策略
    ///
    /// # 返回
    /// RecoveryStrategy - 推荐的恢复策略
    pub fn recommended_recovery_strategy(&self) -> RecoveryStrategy {
        match self {
            NativeMessagingError::ConnectionError(_) => RecoveryStrategy::Retry {
                max_attempts: 3,
                backoff: BackoffStrategy::Exponential {
                    initial_delay: Duration::from_millis(100),
                    max_delay: Duration::from_secs(5),
                    multiplier: 2.0,
                },
            },
            NativeMessagingError::TimeoutError(_) => RecoveryStrategy::Retry {
                max_attempts: 2,
                backoff: BackoffStrategy::Linear {
                    delay: Duration::from_millis(500),
                },
            },
            NativeMessagingError::HandlerError(_) => RecoveryStrategy::Fallback,
            NativeMessagingError::BrowserError(_) => RecoveryStrategy::Retry {
                max_attempts: 2,
                backoff: BackoffStrategy::Fixed {
                    delay: Duration::from_secs(1),
                },
            },
            NativeMessagingError::IoError(_) => RecoveryStrategy::Retry {
                max_attempts: 3,
                backoff: BackoffStrategy::Exponential {
                    initial_delay: Duration::from_millis(50),
                    max_delay: Duration::from_secs(2),
                    multiplier: 1.5,
                },
            },
            _ => RecoveryStrategy::Abort,
        }
    }

    /// 创建带上下文的错误
    ///
    /// 包装错误并添加上下文信息
    ///
    /// # 参数
    /// - `context`: 错误上下文信息
    ///
    /// # 返回
    /// ErrorWithContext - 包含上下文的错误
    pub fn with_context(self, context: ErrorContext) -> ErrorWithContext {
        ErrorWithContext::new(self, context)
    }
}

/// 错误严重程度枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub enum ErrorSeverity {
    /// 低级错误 - 不影响核心功能
    Low,
    /// 中级错误 - 可能影响部分功能
    Medium,
    /// 高级错误 - 影响主要功能
    High,
    /// 严重错误 - 系统无法正常运行
    Critical,
}

/// 错误类别枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ErrorCategory {
    /// 配置相关错误
    Configuration,
    /// 网络连接错误
    Network,
    /// 协议解析错误
    Protocol,
    /// 业务逻辑错误
    Business,
    /// 安全相关错误
    Security,
    /// 系统级错误
    System,
    /// 外部依赖错误
    External,
}

/// 恢复策略枚举
#[derive(Debug, Clone, PartialEq)]
pub enum RecoveryStrategy {
    /// 重试策略
    Retry {
        max_attempts: u32,
        backoff: BackoffStrategy,
    },
    /// 降级策略
    Fallback,
    /// 中止操作
    Abort,
    /// 忽略错误
    Ignore,
}

/// 退避策略枚举
#[derive(Debug, Clone, PartialEq)]
pub enum BackoffStrategy {
    /// 固定延迟
    Fixed { delay: Duration },
    /// 线性递增延迟
    Linear { delay: Duration },
    /// 指数递增延迟
    Exponential {
        initial_delay: Duration,
        max_delay: Duration,
        multiplier: f64,
    },
}

impl BackoffStrategy {
    /// 计算指定尝试次数的延迟时间
    ///
    /// # 参数
    /// - `attempt`: 当前尝试次数（从1开始）
    ///
    /// # 返回
    /// Duration - 延迟时间
    pub fn calculate_delay(&self, attempt: u32) -> Duration {
        match self {
            BackoffStrategy::Fixed { delay } => *delay,
            BackoffStrategy::Linear { delay } => {
                Duration::from_millis(delay.as_millis() as u64 * attempt as u64)
            }
            BackoffStrategy::Exponential {
                initial_delay,
                max_delay,
                multiplier,
            } => {
                let delay_ms =
                    initial_delay.as_millis() as f64 * multiplier.powi(attempt as i32 - 1);
                let delay = Duration::from_millis(delay_ms as u64);
                std::cmp::min(delay, *max_delay)
            }
        }
    }
}

/// 错误上下文信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorContext {
    /// 操作名称
    pub operation: String,
    /// 模块名称
    pub module: String,
    /// 请求ID（如果有）
    pub request_id: Option<String>,
    /// 会话ID（如果有）
    pub session_id: Option<String>,
    /// 浏览器类型（如果有）
    pub browser_type: Option<String>,
    /// 错误发生时间
    pub timestamp: SystemTime,
    /// 额外的上下文数据
    pub metadata: HashMap<String, String>,
}

impl ErrorContext {
    /// 创建新的错误上下文
    ///
    /// # 参数
    /// - `operation`: 操作名称
    /// - `module`: 模块名称
    ///
    /// # 返回
    /// ErrorContext - 错误上下文实例
    pub fn new(operation: String, module: String) -> Self {
        Self {
            operation,
            module,
            request_id: None,
            session_id: None,
            browser_type: None,
            timestamp: SystemTime::now(),
            metadata: HashMap::new(),
        }
    }

    /// 设置请求ID
    pub fn with_request_id(mut self, request_id: String) -> Self {
        self.request_id = Some(request_id);
        self
    }

    /// 设置会话ID
    pub fn with_session_id(mut self, session_id: String) -> Self {
        self.session_id = Some(session_id);
        self
    }

    /// 设置浏览器类型
    pub fn with_browser_type(mut self, browser_type: String) -> Self {
        self.browser_type = Some(browser_type);
        self
    }

    /// 添加元数据
    pub fn with_metadata(mut self, key: String, value: String) -> Self {
        self.metadata.insert(key, value);
        self
    }
}

/// 带上下文的错误
#[derive(Debug)]
pub struct ErrorWithContext {
    /// 原始错误
    pub error: NativeMessagingError,
    /// 错误上下文
    pub context: ErrorContext,
    /// 错误链
    pub chain: Vec<NativeMessagingError>,
}

impl ErrorWithContext {
    /// 创建带上下文的错误
    ///
    /// # 参数
    /// - `error`: 原始错误
    /// - `context`: 错误上下文
    ///
    /// # 返回
    /// ErrorWithContext - 带上下文的错误实例
    pub fn new(error: NativeMessagingError, context: ErrorContext) -> Self {
        Self {
            error,
            context,
            chain: Vec::new(),
        }
    }

    /// 添加错误到错误链
    ///
    /// # 参数
    /// - `error`: 要添加的错误
    ///
    /// # 返回
    /// Self - 链式调用
    pub fn chain_error(mut self, error: NativeMessagingError) -> Self {
        self.chain.push(error);
        self
    }

    /// 获取根本原因错误
    ///
    /// # 返回
    /// &NativeMessagingError - 错误链中的根本原因
    pub fn root_cause(&self) -> &NativeMessagingError {
        self.chain.last().unwrap_or(&self.error)
    }

    /// 判断是否可恢复
    pub fn is_recoverable(&self) -> bool {
        self.error.is_recoverable()
    }

    /// 获取严重程度
    pub fn severity(&self) -> ErrorSeverity {
        self.error.severity()
    }

    /// 获取推荐恢复策略
    pub fn recommended_recovery_strategy(&self) -> RecoveryStrategy {
        self.error.recommended_recovery_strategy()
    }
}

impl std::fmt::Display for ErrorWithContext {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "[{}:{}] {} - {}",
            self.context.module,
            self.context.operation,
            self.error,
            self.context
                .timestamp
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs()
        )?;

        if !self.chain.is_empty() {
            write!(f, " | 错误链: ")?;
            for (i, chained_error) in self.chain.iter().enumerate() {
                if i > 0 {
                    write!(f, " -> ")?;
                }
                write!(f, "{}", chained_error)?;
            }
        }

        Ok(())
    }
}

impl std::error::Error for ErrorWithContext {
    fn source(&self) -> Option<&(dyn std::error::Error + 'static)> {
        Some(&self.error)
    }
}

/// 错误指标收集器
#[derive(Debug)]
pub struct ErrorMetrics {
    /// 错误计数器（按类别）
    error_counts: Arc<Mutex<HashMap<ErrorCategory, AtomicU64>>>,
    /// 恢复成功计数器
    recovery_success_count: AtomicU64,
    /// 恢复失败计数器
    recovery_failure_count: AtomicU64,
    /// 总错误数量
    total_errors: AtomicU64,
    /// 错误发生时间记录
    error_timestamps: Arc<Mutex<Vec<Instant>>>,
    /// 严重错误计数器（按严重程度）
    severity_counts: Arc<Mutex<HashMap<ErrorSeverity, AtomicU64>>>,
}

impl ErrorMetrics {
    /// 创建新的错误指标收集器
    pub fn new() -> Self {
        Self {
            error_counts: Arc::new(Mutex::new(HashMap::new())),
            recovery_success_count: AtomicU64::new(0),
            recovery_failure_count: AtomicU64::new(0),
            total_errors: AtomicU64::new(0),
            error_timestamps: Arc::new(Mutex::new(Vec::new())),
            severity_counts: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    /// 记录错误发生
    ///
    /// # 参数
    /// - `error`: 发生的错误
    pub fn record_error(&self, error: &NativeMessagingError) {
        let category = error.category();
        let severity = error.severity();

        // 增加总错误计数
        self.total_errors.fetch_add(1, Ordering::Relaxed);

        // 记录错误时间
        if let Ok(mut timestamps) = self.error_timestamps.lock() {
            timestamps.push(Instant::now());
            // 只保留最近一小时的时间戳
            let one_hour_ago = Instant::now() - Duration::from_secs(3600);
            timestamps.retain(|&t| t > one_hour_ago);
        }

        // 增加分类计数
        if let Ok(mut counts) = self.error_counts.lock() {
            counts
                .entry(category)
                .or_insert_with(|| AtomicU64::new(0))
                .fetch_add(1, Ordering::Relaxed);
        }

        // 增加严重程度计数
        if let Ok(mut severity_counts) = self.severity_counts.lock() {
            severity_counts
                .entry(severity)
                .or_insert_with(|| AtomicU64::new(0))
                .fetch_add(1, Ordering::Relaxed);
        }

        tracing::error!(
            error_type = ?category,
            severity = ?severity,
            message = %error,
            "错误已记录"
        );
    }

    /// 记录恢复成功
    pub fn record_recovery_success(&self) {
        self.recovery_success_count.fetch_add(1, Ordering::Relaxed);
        tracing::info!("错误恢复成功");
    }

    /// 记录恢复失败
    pub fn record_recovery_failure(&self) {
        self.recovery_failure_count.fetch_add(1, Ordering::Relaxed);
        tracing::warn!("错误恢复失败");
    }

    /// 获取错误统计信息
    ///
    /// # 返回
    /// ErrorStatistics - 错误统计数据
    pub fn get_statistics(&self) -> ErrorStatistics {
        let total_errors = self.total_errors.load(Ordering::Relaxed);
        let recovery_successes = self.recovery_success_count.load(Ordering::Relaxed);
        let recovery_failures = self.recovery_failure_count.load(Ordering::Relaxed);

        let recovery_success_rate = if recovery_successes + recovery_failures > 0 {
            recovery_successes as f64 / (recovery_successes + recovery_failures) as f64
        } else {
            0.0
        };

        let error_rate_per_hour = if let Ok(timestamps) = self.error_timestamps.lock() {
            timestamps.len() as f64
        } else {
            0.0
        };

        let mut category_breakdown = HashMap::new();
        if let Ok(counts) = self.error_counts.lock() {
            for (category, count) in counts.iter() {
                category_breakdown.insert(*category, count.load(Ordering::Relaxed));
            }
        }

        let mut severity_breakdown = HashMap::new();
        if let Ok(severity_counts) = self.severity_counts.lock() {
            for (severity, count) in severity_counts.iter() {
                severity_breakdown.insert(*severity, count.load(Ordering::Relaxed));
            }
        }

        ErrorStatistics {
            total_errors,
            recovery_success_rate,
            error_rate_per_hour,
            category_breakdown,
            severity_breakdown,
            recovery_successes,
            recovery_failures,
        }
    }

    /// 重置所有指标
    pub fn reset(&self) {
        self.total_errors.store(0, Ordering::Relaxed);
        self.recovery_success_count.store(0, Ordering::Relaxed);
        self.recovery_failure_count.store(0, Ordering::Relaxed);

        if let Ok(mut counts) = self.error_counts.lock() {
            counts.clear();
        }

        if let Ok(mut timestamps) = self.error_timestamps.lock() {
            timestamps.clear();
        }

        if let Ok(mut severity_counts) = self.severity_counts.lock() {
            severity_counts.clear();
        }

        tracing::info!("错误指标已重置");
    }
}

impl Default for ErrorMetrics {
    fn default() -> Self {
        Self::new()
    }
}

/// 错误统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorStatistics {
    /// 总错误数量
    pub total_errors: u64,
    /// 恢复成功率 (0.0-1.0)
    pub recovery_success_rate: f64,
    /// 每小时错误率
    pub error_rate_per_hour: f64,
    /// 按类别的错误分解
    pub category_breakdown: HashMap<ErrorCategory, u64>,
    /// 按严重程度的错误分解
    pub severity_breakdown: HashMap<ErrorSeverity, u64>,
    /// 恢复成功次数
    pub recovery_successes: u64,
    /// 恢复失败次数
    pub recovery_failures: u64,
}

/// Native Messaging 结果类型别名
pub type Result<T> = std::result::Result<T, NativeMessagingError>;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_recoverability() {
        let recoverable_error = NativeMessagingError::ConnectionError("test".to_string());
        assert!(recoverable_error.is_recoverable());

        let non_recoverable_error = NativeMessagingError::SecurityError("test".to_string());
        assert!(!non_recoverable_error.is_recoverable());
    }

    #[test]
    fn test_error_severity() {
        let critical_error = NativeMessagingError::SecurityError("test".to_string());
        assert_eq!(critical_error.severity(), ErrorSeverity::Critical);

        let medium_error = NativeMessagingError::ConnectionError("test".to_string());
        assert_eq!(medium_error.severity(), ErrorSeverity::Medium);
    }

    #[test]
    fn test_error_display() {
        let error = NativeMessagingError::ConfigError("Invalid timeout".to_string());
        assert!(error.to_string().contains("配置错误"));
        assert!(error.to_string().contains("Invalid timeout"));
    }

    #[test]
    fn test_error_category() {
        let connection_error = NativeMessagingError::ConnectionError("test".to_string());
        assert_eq!(connection_error.category(), ErrorCategory::Network);

        let security_error = NativeMessagingError::SecurityError("test".to_string());
        assert_eq!(security_error.category(), ErrorCategory::Security);

        let config_error = NativeMessagingError::ConfigError("test".to_string());
        assert_eq!(config_error.category(), ErrorCategory::Configuration);
    }

    #[test]
    fn test_recovery_strategy() {
        let connection_error = NativeMessagingError::ConnectionError("test".to_string());
        match connection_error.recommended_recovery_strategy() {
            RecoveryStrategy::Retry { max_attempts, .. } => {
                assert_eq!(max_attempts, 3);
            }
            _ => panic!("连接错误应该推荐重试策略"),
        }

        let security_error = NativeMessagingError::SecurityError("test".to_string());
        assert_eq!(
            security_error.recommended_recovery_strategy(),
            RecoveryStrategy::Abort
        );
    }

    #[test]
    fn test_backoff_strategy() {
        let fixed = BackoffStrategy::Fixed {
            delay: Duration::from_millis(100),
        };
        assert_eq!(fixed.calculate_delay(1), Duration::from_millis(100));
        assert_eq!(fixed.calculate_delay(5), Duration::from_millis(100));

        let linear = BackoffStrategy::Linear {
            delay: Duration::from_millis(100),
        };
        assert_eq!(linear.calculate_delay(1), Duration::from_millis(100));
        assert_eq!(linear.calculate_delay(3), Duration::from_millis(300));

        let exponential = BackoffStrategy::Exponential {
            initial_delay: Duration::from_millis(100),
            max_delay: Duration::from_secs(5),
            multiplier: 2.0,
        };
        assert_eq!(exponential.calculate_delay(1), Duration::from_millis(100));
        assert_eq!(exponential.calculate_delay(2), Duration::from_millis(200));
        assert_eq!(exponential.calculate_delay(3), Duration::from_millis(400));
    }

    #[test]
    fn test_error_context() {
        let context = ErrorContext::new("send_message".to_string(), "listener".to_string())
            .with_request_id("req-123".to_string())
            .with_browser_type("Chrome".to_string())
            .with_metadata("url".to_string(), "https://example.com".to_string());

        assert_eq!(context.operation, "send_message");
        assert_eq!(context.module, "listener");
        assert_eq!(context.request_id, Some("req-123".to_string()));
        assert_eq!(context.browser_type, Some("Chrome".to_string()));
        assert_eq!(
            context.metadata.get("url"),
            Some(&"https://example.com".to_string())
        );
    }

    #[test]
    fn test_error_with_context() {
        let error = NativeMessagingError::ConnectionError("Network timeout".to_string());
        let context = ErrorContext::new("connect".to_string(), "network".to_string());
        let error_with_context = error.with_context(context);

        assert!(error_with_context.is_recoverable());
        assert_eq!(error_with_context.severity(), ErrorSeverity::Medium);
        assert!(error_with_context.to_string().contains("Network timeout"));
        assert!(error_with_context.to_string().contains("network:connect"));
    }

    #[test]
    fn test_error_chain() {
        let original_error = NativeMessagingError::IoError(std::io::Error::new(
            std::io::ErrorKind::ConnectionRefused,
            "Connection refused",
        ));
        let context = ErrorContext::new("read_socket".to_string(), "network".to_string());

        let chained_error = original_error.with_context(context).chain_error(
            NativeMessagingError::ConnectionError("Failed to connect".to_string()),
        );

        assert_eq!(chained_error.chain.len(), 1);
        match chained_error.root_cause() {
            NativeMessagingError::ConnectionError(msg) => {
                assert_eq!(msg, "Failed to connect");
            }
            _ => panic!("根本原因应该是连接错误"),
        }
    }

    #[test]
    fn test_error_metrics() {
        let metrics = ErrorMetrics::new();

        // 记录一些错误
        metrics.record_error(&NativeMessagingError::ConnectionError("test1".to_string()));
        metrics.record_error(&NativeMessagingError::SecurityError("test2".to_string()));
        metrics.record_error(&NativeMessagingError::ConnectionError("test3".to_string()));

        // 记录恢复情况
        metrics.record_recovery_success();
        metrics.record_recovery_failure();

        let stats = metrics.get_statistics();
        assert_eq!(stats.total_errors, 3);
        assert_eq!(stats.recovery_success_rate, 0.5); // 1 成功 / 2 总计
        assert_eq!(stats.recovery_successes, 1);
        assert_eq!(stats.recovery_failures, 1);

        // 检查分类统计
        assert_eq!(
            stats.category_breakdown.get(&ErrorCategory::Network),
            Some(&2)
        );
        assert_eq!(
            stats.category_breakdown.get(&ErrorCategory::Security),
            Some(&1)
        );

        // 检查严重程度统计
        assert_eq!(
            stats.severity_breakdown.get(&ErrorSeverity::Medium),
            Some(&2)
        );
        assert_eq!(
            stats.severity_breakdown.get(&ErrorSeverity::Critical),
            Some(&1)
        );
    }

    #[test]
    fn test_error_metrics_reset() {
        let metrics = ErrorMetrics::new();

        metrics.record_error(&NativeMessagingError::ConnectionError("test".to_string()));
        metrics.record_recovery_success();

        let stats_before = metrics.get_statistics();
        assert_eq!(stats_before.total_errors, 1);
        assert_eq!(stats_before.recovery_successes, 1);

        metrics.reset();

        let stats_after = metrics.get_statistics();
        assert_eq!(stats_after.total_errors, 0);
        assert_eq!(stats_after.recovery_successes, 0);
        assert_eq!(stats_after.recovery_failures, 0);
    }

    #[test]
    fn test_comprehensive_error_handling() {
        let metrics = ErrorMetrics::new();

        // 测试完整的错误处理流程
        let error = NativeMessagingError::ConnectionError("Connection lost".to_string());
        let context = ErrorContext::new(
            "send_message".to_string(),
            "background_listener".to_string(),
        )
        .with_request_id("req-456".to_string())
        .with_browser_type("Firefox".to_string());

        let error_with_context = error.with_context(context);

        // 记录错误
        metrics.record_error(&error_with_context.error);

        // 检查恢复策略
        match error_with_context.recommended_recovery_strategy() {
            RecoveryStrategy::Retry {
                max_attempts,
                backoff,
            } => {
                assert_eq!(max_attempts, 3);
                assert!(matches!(backoff, BackoffStrategy::Exponential { .. }));
            }
            _ => panic!("连接错误应该推荐重试策略"),
        }

        // 模拟恢复尝试
        for attempt in 1..=3 {
            if let RecoveryStrategy::Retry { backoff, .. } =
                error_with_context.recommended_recovery_strategy()
            {
                let delay = backoff.calculate_delay(attempt);
                assert!(delay.as_millis() > 0);
            }
        }

        // 假设第3次尝试成功
        metrics.record_recovery_success();

        let final_stats = metrics.get_statistics();
        assert_eq!(final_stats.total_errors, 1);
        assert_eq!(final_stats.recovery_success_rate, 1.0);
        assert_eq!(
            final_stats.category_breakdown.get(&ErrorCategory::Network),
            Some(&1)
        );
    }

    #[test]
    fn test_error_severity_ordering() {
        assert!(ErrorSeverity::Critical > ErrorSeverity::High);
        assert!(ErrorSeverity::High > ErrorSeverity::Medium);
        assert!(ErrorSeverity::Medium > ErrorSeverity::Low);
    }

    #[tokio::test]
    async fn test_error_handling_in_async_context() {
        let metrics = Arc::new(ErrorMetrics::new());

        // 模拟异步错误处理
        let metrics_clone = metrics.clone();
        let handle = tokio::spawn(async move {
            let error = NativeMessagingError::TimeoutError("Async operation timeout".to_string());
            metrics_clone.record_error(&error);

            // 模拟恢复尝试
            tokio::time::sleep(Duration::from_millis(10)).await;
            metrics_clone.record_recovery_success();
        });

        handle.await.unwrap();

        let stats = metrics.get_statistics();
        assert_eq!(stats.total_errors, 1);
        assert_eq!(stats.recovery_successes, 1);
    }
}
