//! 守护进程构建脚本
//! 
//! 负责平台特定的构建配置和资源文件处理

use std::env;
use std::path::Path;

fn main() {
    // 获取目标平台信息
    let target_os = env::var("CARGO_CFG_TARGET_OS").unwrap();
    let target_arch = env::var("CARGO_CFG_TARGET_ARCH").unwrap();
    
    println!("cargo:rerun-if-changed=build.rs");
    println!("cargo:rerun-if-changed=resources/");
    
    // 设置平台特定的编译标志
    match target_os.as_str() {
        "windows" => {
            configure_windows_build();
        }
        "macos" => {
            configure_macos_build();
        }
        "linux" => {
            configure_linux_build();
        }
        _ => {
            println!("cargo:warning=不支持的目标平台: {}", target_os);
        }
    }
    
    // 处理资源文件
    process_resource_files(&target_os);
    
    // 设置版本信息
    set_version_info();
    
    println!("构建配置完成 - 目标平台: {} ({})", target_os, target_arch);
}

/// 配置 Windows 构建
fn configure_windows_build() {
    println!("cargo:rustc-link-lib=user32");
    println!("cargo:rustc-link-lib=advapi32");
    println!("cargo:rustc-link-lib=kernel32");
    println!("cargo:rustc-link-lib=shell32");
    println!("cargo:rustc-link-lib=ole32");
    println!("cargo:rustc-link-lib=oleaut32");
    println!("cargo:rustc-link-lib=psapi");

    // 设置 Windows 子系统
    #[cfg(not(debug_assertions))]
    println!("cargo:rustc-link-arg=/SUBSYSTEM:CONSOLE");

    // 设置 Windows 版本信息
    println!("cargo:rustc-env=WINDOWS_TARGET_VERSION=10.0");

    // 嵌入 Windows 资源
    if Path::new("resources/windows/daemon.rc").exists() {
        println!("cargo:rerun-if-changed=resources/windows/daemon.rc");
        compile_windows_resources();
    }

    // 设置 Windows 特定的编译标志
    println!("cargo:rustc-cfg=windows_service");
}

/// 编译 Windows 资源文件
fn compile_windows_resources() {
    // 这里可以添加资源文件编译逻辑
    // 例如使用 winres crate 或调用 rc.exe
    println!("cargo:warning=Windows 资源文件编译功能待实现");
}

/// 配置 macOS 构建
fn configure_macos_build() {
    println!("cargo:rustc-link-lib=framework=Foundation");
    println!("cargo:rustc-link-lib=framework=CoreServices");
    println!("cargo:rustc-link-lib=framework=Security");
    println!("cargo:rustc-link-lib=framework=SystemConfiguration");
    println!("cargo:rustc-link-lib=framework=IOKit");

    // 设置 macOS 部署目标
    println!("cargo:rustc-env=MACOSX_DEPLOYMENT_TARGET=10.15");

    // 设置 macOS 特定的编译标志
    println!("cargo:rustc-cfg=macos_launchd");

    // 检查 Xcode 版本
    check_xcode_version();
}

/// 检查 Xcode 版本
fn check_xcode_version() {
    use std::process::Command;

    if let Ok(output) = Command::new("xcodebuild").arg("-version").output() {
        let version_info = String::from_utf8_lossy(&output.stdout);
        println!("cargo:warning=Xcode 版本: {}", version_info.lines().next().unwrap_or("未知"));
    } else {
        println!("cargo:warning=未检测到 Xcode");
    }
}

/// 配置 Linux 构建
fn configure_linux_build() {
    // 检查 systemd 开发库
    if pkg_config::probe_library("libsystemd").is_ok() {
        println!("cargo:rustc-link-lib=systemd");
        println!("cargo:rustc-cfg=systemd_available");
    } else {
        println!("cargo:warning=未找到 libsystemd 开发库，某些功能可能不可用");
    }

    // 链接其他必要的库
    println!("cargo:rustc-link-lib=pthread");
    println!("cargo:rustc-link-lib=dl");
    println!("cargo:rustc-link-lib=m");

    // 设置 Linux 特定的编译标志
    println!("cargo:rustc-cfg=linux_systemd");

    // 检查发行版信息
    check_linux_distribution();
}

/// 检查 Linux 发行版
fn check_linux_distribution() {
    use std::fs;

    if let Ok(content) = fs::read_to_string("/etc/os-release") {
        for line in content.lines() {
            if line.starts_with("NAME=") {
                let distro = line.trim_start_matches("NAME=").trim_matches('"');
                println!("cargo:warning=Linux 发行版: {}", distro);
                break;
            }
        }
    } else {
        println!("cargo:warning=无法检测 Linux 发行版");
    }
}

/// 处理资源文件
fn process_resource_files(target_os: &str) {
    let resources_dir = Path::new("resources");
    if !resources_dir.exists() {
        return;
    }
    
    // 复制平台特定的资源文件到输出目录
    let platform_resources = resources_dir.join(target_os);
    if platform_resources.exists() {
        copy_resources(&platform_resources, target_os);
    }
}

/// 复制资源文件
fn copy_resources(source_dir: &Path, target_os: &str) {
    let out_dir = env::var("OUT_DIR").unwrap();
    let target_dir = Path::new(&out_dir).join("resources").join(target_os);

    if let Err(e) = std::fs::create_dir_all(&target_dir) {
        println!("cargo:warning=创建资源目录失败: {}", e);
        return;
    }

    // 递归复制资源文件
    if let Err(e) = copy_dir_recursive(source_dir, &target_dir) {
        println!("cargo:warning=复制资源文件失败: {}", e);
    } else {
        println!("cargo:warning=资源文件复制到: {:?}", target_dir);
    }
}

/// 递归复制目录
fn copy_dir_recursive(src: &Path, dst: &Path) -> std::io::Result<()> {
    use std::fs;

    if !dst.exists() {
        fs::create_dir_all(dst)?;
    }

    for entry in fs::read_dir(src)? {
        let entry = entry?;
        let src_path = entry.path();
        let dst_path = dst.join(entry.file_name());

        if src_path.is_dir() {
            copy_dir_recursive(&src_path, &dst_path)?;
        } else {
            fs::copy(&src_path, &dst_path)?;
        }
    }

    Ok(())
}

/// 设置版本信息
fn set_version_info() {
    // 从 Cargo.toml 获取版本信息
    let version = env::var("CARGO_PKG_VERSION").unwrap();
    let name = env::var("CARGO_PKG_NAME").unwrap();
    let description = env::var("CARGO_PKG_DESCRIPTION").unwrap_or_default();
    
    println!("cargo:rustc-env=DAEMON_VERSION={}", version);
    println!("cargo:rustc-env=DAEMON_NAME={}", name);
    println!("cargo:rustc-env=DAEMON_DESCRIPTION={}", description);
    
    // 设置构建时间戳
    let build_timestamp = chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC");
    println!("cargo:rustc-env=BUILD_TIMESTAMP={}", build_timestamp);
    
    // 设置 Git 信息（如果可用）
    if let Ok(git_hash) = std::process::Command::new("git")
        .args(&["rev-parse", "--short", "HEAD"])
        .output()
    {
        if git_hash.status.success() {
            let hash = String::from_utf8_lossy(&git_hash.stdout).trim().to_string();
            println!("cargo:rustc-env=GIT_HASH={}", hash);
        }
    }
}
