//! 错误处理测试

use secure_password_daemon::{
    error::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RecoveryStrategy},
};

/// 测试错误类型转换
#[test]
fn test_error_conversions() {
    // 测试 std::io::Error 转换
    let io_error = std::io::Error::new(std::io::ErrorKind::NotFound, "文件未找到");
    let daemon_error: DaemonError = io_error.into();
    
    match daemon_error {
        DaemonError::FileSystemError(_) => {
            // 预期的错误类型
        }
        other => panic!("意外的错误类型: {:?}", other),
    }
    
    // 测试 serde_json::Error 转换
    let json_error = serde_json::from_str::<serde_json::Value>("invalid json").unwrap_err();
    let daemon_error: DaemonError = json_error.into();
    
    match daemon_error {
        DaemonError::Config<PERSON>arseError(_) => {
            // 预期的错误类型
        }
        other => panic!("意外的错误类型: {:?}", other),
    }
}

/// 测试错误报告器
#[test]
fn test_error_reporter() {
    let reporter = ErrorReporter::new(false);
    let error = DaemonError::ConfigLoadError("测试错误".to_string());
    
    let simple_report = reporter.report(&error);
    assert!(simple_report.contains("配置加载失败"));
    
    let verbose_reporter = ErrorReporter::new(true);
    let verbose_report = verbose_reporter.report(&error);
    assert!(verbose_report.contains("建议"));
    assert!(verbose_report.len() > simple_report.len());
}

/// 测试错误恢复管理器
#[test]
fn test_error_recovery_manager() {
    let mut manager = ErrorRecoveryManager::new(3);
    
    // 测试错误记录
    assert!(manager.record_error("test_error"));
    assert!(manager.record_error("test_error"));
    assert!(manager.record_error("test_error"));
    
    // 第四次应该失败
    assert!(!manager.record_error("test_error"));
    
    // 重置后应该可以再次记录
    manager.reset_error_count("test_error");
    assert!(manager.record_error("test_error"));
}

/// 测试恢复策略
#[test]
fn test_recovery_strategies() {
    let manager = ErrorRecoveryManager::new(5);
    
    // 测试不同错误类型的恢复策略
    let config_error = DaemonError::ConfigLoadError("配置加载失败".to_string());
    let strategy = manager.get_recovery_strategy(&config_error);
    match strategy {
        RecoveryStrategy::UseDefault => {
            // 预期的策略
        }
        other => panic!("意外的恢复策略: {:?}", other),
    }
    
    let ipc_error = DaemonError::IpcConnectionError("连接失败".to_string());
    let strategy = manager.get_recovery_strategy(&ipc_error);
    match strategy {
        RecoveryStrategy::Retry { max_attempts, delay_ms } => {
            assert_eq!(max_attempts, 3);
            assert_eq!(delay_ms, 1000);
        }
        other => panic!("意外的恢复策略: {:?}", other),
    }
    
    let permission_error = DaemonError::PermissionError("权限不足".to_string());
    let strategy = manager.get_recovery_strategy(&permission_error);
    match strategy {
        RecoveryStrategy::Stop => {
            // 预期的策略
        }
        other => panic!("意外的恢复策略: {:?}", other),
    }
}

/// 测试错误链
#[test]
fn test_error_chain() {
    let root_cause = std::io::Error::new(std::io::ErrorKind::PermissionDenied, "权限被拒绝");
    let daemon_error: DaemonError = root_cause.into();
    
    // 验证错误信息包含原始错误
    let error_string = daemon_error.to_string();
    assert!(error_string.contains("权限被拒绝"));
}

/// 测试错误格式化
#[test]
fn test_error_formatting() {
    let errors = vec![
        DaemonError::ConfigLoadError("配置文件不存在".to_string()),
        DaemonError::ServiceStartError("端口被占用".to_string()),
        DaemonError::IpcConnectionError("网络不可达".to_string()),
        DaemonError::SecurityValidationError("证书无效".to_string()),
    ];
    
    for error in errors {
        let error_string = error.to_string();
        assert!(!error_string.is_empty());
        assert!(error_string.len() > 10); // 确保有意义的错误信息
        
        // 验证错误信息是中文的
        assert!(error_string.chars().any(|c| c as u32 > 127));
    }
}

/// 测试错误分类
#[test]
fn test_error_categorization() {
    // 配置相关错误
    let config_errors = vec![
        DaemonError::ConfigLoadError("".to_string()),
        DaemonError::ConfigParseError("".to_string()),
        DaemonError::ConfigValidationError("".to_string()),
    ];
    
    for error in config_errors {
        let error_string = error.to_string();
        assert!(error_string.contains("配置"));
    }
    
    // 服务相关错误
    let service_errors = vec![
        DaemonError::ServiceStartError("".to_string()),
        DaemonError::ServiceStopError("".to_string()),
    ];
    
    for error in service_errors {
        let error_string = error.to_string();
        assert!(error_string.contains("服务"));
    }
    
    // 网络相关错误
    let network_errors = vec![
        DaemonError::IpcConnectionError("".to_string()),
        DaemonError::NetworkError("".to_string()),
    ];
    
    for error in network_errors {
        let error_string = error.to_string();
        // 网络错误应该包含相关关键词
        assert!(error_string.contains("连接") || error_string.contains("网络"));
    }
}

/// 测试错误的调试信息
#[test]
fn test_error_debug_info() {
    let error = DaemonError::InternalError("内部错误".to_string());
    let debug_string = format!("{:?}", error);
    
    // 调试信息应该包含错误类型和消息
    assert!(debug_string.contains("InternalError"));
    assert!(debug_string.contains("内部错误"));
}

/// 测试错误的序列化（如果需要）
#[test]
fn test_error_serialization() {
    // 这里可以测试错误的序列化，如果错误类型实现了 Serialize
    // 目前我们的错误类型没有实现 Serialize，所以这个测试暂时跳过
    
    let error = DaemonError::ConfigLoadError("测试错误".to_string());
    let error_string = error.to_string();
    assert!(!error_string.is_empty());
}

/// 测试错误恢复的实际场景
#[test]
fn test_error_recovery_scenarios() {
    let mut manager = ErrorRecoveryManager::new(3);
    
    // 模拟配置加载失败的场景
    let config_error = DaemonError::ConfigLoadError("配置文件损坏".to_string());
    let strategy = manager.get_recovery_strategy(&config_error);
    
    match strategy {
        RecoveryStrategy::UseDefault => {
            // 在实际应用中，这里会使用默认配置
            assert!(true);
        }
        _ => panic!("配置错误应该使用默认值策略"),
    }
    
    // 模拟网络连接失败的场景
    let network_error = DaemonError::IpcConnectionError("连接超时".to_string());
    let strategy = manager.get_recovery_strategy(&network_error);
    
    match strategy {
        RecoveryStrategy::Retry { max_attempts, delay_ms } => {
            // 在实际应用中，这里会进行重试
            assert!(max_attempts > 0);
            assert!(delay_ms > 0);
        }
        _ => panic!("网络错误应该使用重试策略"),
    }
}

/// 测试错误上下文
#[test]
fn test_error_context() {
    use secure_password_daemon::error::ErrorContext;
    
    // 测试成功的情况
    let result: Result<i32, std::io::Error> = Ok(42);
    let with_context = result.with_context(|| "操作成功".to_string());
    assert!(with_context.is_ok());
    assert_eq!(with_context.unwrap(), 42);
    
    // 测试失败的情况
    let result: Result<i32, std::io::Error> = Err(std::io::Error::new(
        std::io::ErrorKind::NotFound,
        "文件未找到"
    ));
    let with_context = result.with_context(|| "读取配置文件时".to_string());
    assert!(with_context.is_err());
    
    let error = with_context.unwrap_err();
    let error_string = error.to_string();
    assert!(error_string.contains("读取配置文件时"));
    assert!(error_string.contains("文件未找到"));
}
