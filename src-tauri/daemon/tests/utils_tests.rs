//! 工具函数测试

use std::time::Duration;
use secure_password_daemon::utils::{
    NetworkUtils, SystemUtils, ProcessManager,
    LogRotationManager, PerformanceLogger,
};

/// 测试网络工具
#[tokio::test]
async fn test_network_utils() {
    // 测试端口可用性检查
    let available_port = NetworkUtils::find_available_port(8000, 8100);
    assert!(available_port.is_some());
    
    let port = available_port.unwrap();
    assert!(NetworkUtils::is_port_available(port));
    
    // 测试IP地址验证
    assert!(NetworkUtils::is_valid_ip("127.0.0.1"));
    assert!(NetworkUtils::is_valid_ip("::1"));
    assert!(!NetworkUtils::is_valid_ip("invalid_ip"));
    
    // 测试地址解析
    let addr = NetworkUtils::parse_socket_addr("127.0.0.1:8080");
    assert!(addr.is_ok());
    
    let invalid_addr = NetworkUtils::parse_socket_addr("invalid:address");
    assert!(invalid_addr.is_err());
}

/// 测试TCP连接
#[tokio::test]
async fn test_tcp_connection() {
    // 测试连接到不存在的服务
    let result = NetworkUtils::test_tcp_connection("127.0.0.1", 9999, 1).await;
    assert!(result.is_ok());
    assert_eq!(result.unwrap(), false);
    
    // 测试连接超时
    let result = NetworkUtils::test_tcp_connection("*********", 80, 1).await;
    assert!(result.is_ok());
    // 结果可能是 false（连接失败）或 true（意外连接成功）
}

/// 测试系统信息获取
#[tokio::test]
async fn test_system_info() {
    let system_info = SystemUtils::get_system_info();
    assert!(system_info.is_ok());
    
    let info = system_info.unwrap();
    assert!(!info.os.is_empty());
    assert!(!info.arch.is_empty());
    assert!(!info.hostname.is_empty());
    assert!(info.cpu_cores > 0);
    assert!(info.total_memory > 0);
}

/// 测试时间戳生成
#[test]
fn test_timestamps() {
    let timestamp1 = SystemUtils::get_timestamp();
    let timestamp2 = SystemUtils::get_timestamp_millis();
    
    assert!(timestamp1 > 0);
    assert!(timestamp2 > 0);
    assert!(timestamp2 > timestamp1); // 毫秒时间戳应该更大
    
    // 测试时间戳的合理性（应该接近当前时间）
    let current_time = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_secs();
    
    assert!((timestamp1 as i64 - current_time as i64).abs() < 5); // 5秒内的差异是可接受的
}

/// 测试环境变量操作
#[test]
fn test_env_vars() {
    let test_key = "SECURE_PASSWORD_TEST_VAR";
    let test_value = "test_value_123";
    
    // 设置环境变量
    SystemUtils::set_env_var(test_key, test_value);
    
    // 获取环境变量
    let retrieved_value = SystemUtils::get_env_var(test_key);
    assert!(retrieved_value.is_some());
    assert_eq!(retrieved_value.unwrap(), test_value);
    
    // 测试不存在的环境变量
    let nonexistent = SystemUtils::get_env_var("NONEXISTENT_VAR_12345");
    assert!(nonexistent.is_none());
}

/// 测试进程管理（模拟）
#[tokio::test]
async fn test_process_management() {
    // 测试进程运行状态检查（使用当前进程ID）
    let current_pid = std::process::id();
    assert!(ProcessManager::is_process_running(current_pid));
    
    // 测试不存在的进程
    assert!(!ProcessManager::is_process_running(99999));
}

/// 测试日志轮转管理器
#[tokio::test]
async fn test_log_rotation() {
    use tempfile::TempDir;
    
    let temp_dir = TempDir::new().expect("无法创建临时目录");
    let log_file = temp_dir.path().join("test.log");
    
    // 创建一个小的测试日志文件
    tokio::fs::write(&log_file, "test log content").await.unwrap();
    
    let manager = LogRotationManager::new(
        log_file.to_string_lossy().to_string(),
        10, // 10字节的限制
        3,  // 保留3个文件
    );
    
    // 检查是否需要轮转
    let should_rotate = manager.should_rotate();
    assert!(should_rotate.is_ok());
    assert!(should_rotate.unwrap()); // 文件大小超过10字节，应该轮转
    
    // 执行轮转
    let rotate_result = manager.rotate();
    assert!(rotate_result.is_ok());
}

/// 测试性能日志记录器
#[test]
fn test_performance_logger() {
    let logger = PerformanceLogger::start("test_operation");
    
    // 模拟一些工作
    std::thread::sleep(Duration::from_millis(10));
    
    // 记录检查点
    logger.checkpoint("middle_point");
    
    // 模拟更多工作
    std::thread::sleep(Duration::from_millis(10));
    
    // 完成操作（这会记录总时间）
    logger.finish();
}

/// 测试网络安全检查
#[test]
fn test_network_security() {
    use secure_password_daemon::utils::NetworkSecurityChecker;
    
    let whitelist = vec!["127.0.0.1".to_string(), "***********".to_string()];
    let blacklist = vec!["********".to_string(), "**********".to_string()];
    
    // 测试白名单
    assert!(NetworkSecurityChecker::is_ip_whitelisted("127.0.0.1", &whitelist));
    assert!(!NetworkSecurityChecker::is_ip_whitelisted("*******", &whitelist));
    
    // 测试黑名单
    assert!(NetworkSecurityChecker::is_ip_blacklisted("********", &blacklist));
    assert!(!NetworkSecurityChecker::is_ip_blacklisted("*******", &blacklist));
    
    // 测试频率限制
    assert!(NetworkSecurityChecker::check_rate_limit("127.0.0.1", 100, 50));
    assert!(!NetworkSecurityChecker::check_rate_limit("127.0.0.1", 100, 150));
    
    // 测试可疑模式检测
    assert!(NetworkSecurityChecker::detect_suspicious_pattern(1000, 60, 10));
    assert!(!NetworkSecurityChecker::detect_suspicious_pattern(100, 60, 10));
}

/// 测试连接池
#[test]
fn test_connection_pool() {
    use secure_password_daemon::utils::ConnectionPool;
    
    let mut pool: ConnectionPool<String> = ConnectionPool::new(3);
    
    // 测试初始状态
    let stats = pool.get_stats();
    assert_eq!(stats.max_connections, 3);
    assert_eq!(stats.total_connections, 0);
    assert_eq!(stats.active_connections, 0);
    
    // 测试获取连接（池为空时）
    let conn = pool.get_connection();
    assert!(conn.is_none());
    
    // 归还连接
    pool.return_connection("connection1".to_string());
    pool.return_connection("connection2".to_string());
    
    // 现在应该能获取连接
    let conn = pool.get_connection();
    assert!(conn.is_some());
    assert_eq!(conn.unwrap(), "connection2"); // LIFO 顺序
    
    let stats = pool.get_stats();
    assert_eq!(stats.total_connections, 1);
    assert_eq!(stats.active_connections, 1);
}

/// 测试带宽测试器
#[tokio::test]
async fn test_bandwidth_tester() {
    use secure_password_daemon::utils::BandwidthTester;
    
    // 测试下载速度测试（模拟）
    let speed = BandwidthTester::test_download_speed("http://example.com", 1).await;
    assert!(speed.is_ok());
    
    let speed_value = speed.unwrap();
    assert!(speed_value >= 0.0); // 速度应该是非负数
}

/// 测试网络监控器
#[tokio::test]
async fn test_network_monitor() {
    use std::net::SocketAddr;
    use secure_password_daemon::utils::NetworkMonitor;
    
    let addresses = vec![
        "127.0.0.1:80".parse::<SocketAddr>().unwrap(),
        "127.0.0.1:443".parse::<SocketAddr>().unwrap(),
    ];
    
    let monitor = NetworkMonitor::new(
        addresses,
        Duration::from_secs(1),
        Duration::from_millis(100),
    );
    
    // 启动监控（运行很短时间）
    let handle = monitor.start_monitoring().await;
    
    // 等待一小段时间
    tokio::time::sleep(Duration::from_millis(200)).await;
    
    // 停止监控
    handle.abort();
}

/// 测试系统资源监控器
#[tokio::test]
async fn test_system_resource_monitor() {
    use secure_password_daemon::utils::SystemResourceMonitor;
    
    let monitor = SystemResourceMonitor::new(Duration::from_millis(100));
    
    // 启动监控（运行很短时间）
    let handle = monitor.start_monitoring().await;
    
    // 等待一小段时间让监控器运行
    tokio::time::sleep(Duration::from_millis(200)).await;
    
    // 停止监控
    handle.abort();
}

/// 测试本地IP获取
#[tokio::test]
async fn test_local_ip() {
    let local_ip = NetworkUtils::get_local_ip();
    
    // 在某些环境中可能会失败，所以我们只检查结果格式
    match local_ip {
        Ok(ip) => {
            assert!(NetworkUtils::is_valid_ip(&ip));
            assert!(!ip.is_empty());
        }
        Err(_) => {
            // 在某些测试环境中可能无法获取本地IP，这是可以接受的
        }
    }
}
