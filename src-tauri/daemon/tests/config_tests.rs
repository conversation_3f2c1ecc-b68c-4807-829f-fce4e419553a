//! 配置管理测试

use std::time::Duration;
use tempfile::TempDir;
use tokio::time::timeout;

use secure_password_daemon::{
    config::{DaemonConfig, IpcTransportType, LogFormat},
    error::DaemonError,
};

/// 测试默认配置
#[tokio::test]
async fn test_default_config() {
    let config = DaemonConfig::default();
    
    // 验证默认值
    assert_eq!(config.service.name, "secure-password-daemon");
    assert_eq!(config.service.auto_start, true);
    assert_eq!(config.ipc.max_connections, 100);
    assert_eq!(config.logging.level, "info");
    
    // 验证配置有效性
    assert!(config.validate().is_ok());
}

/// 测试配置文件加载
#[tokio::test]
async fn test_config_file_loading() {
    let temp_dir = TempDir::new().expect("无法创建临时目录");
    let config_path = temp_dir.path().join("test_config.toml");
    
    // 创建测试配置文件
    let test_config_content = r#"
[service]
name = "test-daemon"
display_name = "Test Daemon"
description = "测试守护进程"
auto_start = false

[ipc]
transport = "Tcp"
bind_address = "127.0.0.1"
port = 9999
max_connections = 50
connection_timeout = 15

[native_messaging]
enabled = true
host_name = "com.test.host"
supported_browsers = ["chrome", "firefox"]
extension_whitelist = []

[app_manager]
app_path = "./test-app"
startup_timeout = 30
health_check_interval = 15
max_restart_attempts = 2

[security]
enabled = true
encryption_algorithm = "AES-256-GCM"
key_length = 256

[monitoring]
enabled = false
metrics_interval = 30
monitoring_port = 9091

[logging]
level = "debug"
file_path = "test.log"
console = true
format = "Json"
"#;
    
    tokio::fs::write(&config_path, test_config_content)
        .await
        .expect("无法写入配置文件");
    
    // 加载配置
    let config = DaemonConfig::load_from_file(&config_path)
        .await
        .expect("配置加载失败");
    
    // 验证加载的配置
    assert_eq!(config.service.name, "test-daemon");
    assert_eq!(config.service.auto_start, false);
    assert_eq!(config.ipc.port, Some(9999));
    assert_eq!(config.ipc.max_connections, 50);
    assert_eq!(config.logging.level, "debug");
    assert_eq!(config.logging.format, LogFormat::Json);
    assert_eq!(config.monitoring.enabled, false);
}

/// 测试配置验证
#[tokio::test]
async fn test_config_validation() {
    // 测试有效配置
    let valid_config = DaemonConfig::default();
    assert!(valid_config.validate().is_ok());
    
    // 测试无效配置
    let mut invalid_config = DaemonConfig::default();
    
    // 无效的服务名称
    invalid_config.service.name = "".to_string();
    assert!(invalid_config.validate().is_err());
    
    // 重置服务名称
    invalid_config.service.name = "test-daemon".to_string();
    
    // 无效的端口
    invalid_config.ipc.port = Some(0);
    assert!(invalid_config.validate().is_err());
    
    // 重置端口
    invalid_config.ipc.port = Some(8080);
    
    // 无效的连接数
    invalid_config.ipc.max_connections = 0;
    assert!(invalid_config.validate().is_err());
}

/// 测试配置保存
#[tokio::test]
async fn test_config_save() {
    let temp_dir = TempDir::new().expect("无法创建临时目录");
    let config_path = temp_dir.path().join("save_test.toml");
    
    let config = DaemonConfig::default();
    
    // 保存配置
    config.save_to_file(&config_path)
        .await
        .expect("配置保存失败");
    
    // 验证文件存在
    assert!(config_path.exists());
    
    // 重新加载并验证
    let loaded_config = DaemonConfig::load_from_file(&config_path)
        .await
        .expect("重新加载配置失败");
    
    assert_eq!(config.service.name, loaded_config.service.name);
    assert_eq!(config.ipc.port, loaded_config.ipc.port);
}

/// 测试配置差异检测
#[tokio::test]
async fn test_config_diff() {
    let config1 = DaemonConfig::default();
    let mut config2 = DaemonConfig::default();
    
    // 修改一些配置项
    config2.service.name = "modified-daemon".to_string();
    config2.ipc.port = Some(9090);
    config2.logging.level = "debug".to_string();
    
    let differences = config1.diff(&config2);
    
    // 验证检测到的差异
    assert!(!differences.is_empty());
    assert!(differences.iter().any(|d| d.contains("service.name")));
    assert!(differences.iter().any(|d| d.contains("ipc.port")));
    assert!(differences.iter().any(|d| d.contains("logging.level")));
}

/// 测试配置热重载
#[tokio::test]
async fn test_config_hot_reload() {
    let mut config = DaemonConfig::default();
    let new_config = {
        let mut c = DaemonConfig::default();
        c.service.name = "reloaded-daemon".to_string();
        c.logging.level = "debug".to_string();
        c
    };
    
    // 执行热重载
    config.reload(new_config.clone())
        .await
        .expect("配置热重载失败");
    
    // 验证配置已更新
    assert_eq!(config.service.name, "reloaded-daemon");
    assert_eq!(config.logging.level, "debug");
}

/// 测试无效配置文件处理
#[tokio::test]
async fn test_invalid_config_file() {
    let temp_dir = TempDir::new().expect("无法创建临时目录");
    let config_path = temp_dir.path().join("invalid_config.toml");
    
    // 创建无效的配置文件
    let invalid_content = "invalid toml content [[[";
    tokio::fs::write(&config_path, invalid_content)
        .await
        .expect("无法写入配置文件");
    
    // 尝试加载配置
    let result = DaemonConfig::load_from_file(&config_path).await;
    assert!(result.is_err());
    
    match result.unwrap_err() {
        DaemonError::ConfigParseError(_) => {
            // 预期的错误类型
        }
        other => panic!("意外的错误类型: {:?}", other),
    }
}

/// 测试不存在的配置文件
#[tokio::test]
async fn test_nonexistent_config_file() {
    let temp_dir = TempDir::new().expect("无法创建临时目录");
    let config_path = temp_dir.path().join("nonexistent.toml");
    
    // 尝试加载不存在的配置文件
    let result = DaemonConfig::load_from_file(&config_path).await;
    
    // 应该返回默认配置
    assert!(result.is_ok());
    let config = result.unwrap();
    
    // 验证是默认配置
    let default_config = DaemonConfig::default();
    assert_eq!(config.service.name, default_config.service.name);
}

/// 测试配置加载超时
#[tokio::test]
async fn test_config_loading_timeout() {
    let temp_dir = TempDir::new().expect("无法创建临时目录");
    let config_path = temp_dir.path().join("timeout_test.toml");
    
    // 创建正常的配置文件
    let config_content = r#"
[service]
name = "timeout-test"
display_name = "Timeout Test Daemon"
description = "测试超时的守护进程"
auto_start = true

[ipc]
transport = "Auto"
bind_address = "127.0.0.1"
port = 8080
max_connections = 100
connection_timeout = 30

[native_messaging]
enabled = true
host_name = "com.test.host"
supported_browsers = ["chrome"]
extension_whitelist = []

[app_manager]
app_path = "./test-app"
startup_timeout = 60
health_check_interval = 30
max_restart_attempts = 3

[security]
enabled = true
encryption_algorithm = "AES-256-GCM"
key_length = 256

[monitoring]
enabled = true
metrics_interval = 60
monitoring_port = 9090

[logging]
level = "info"
console = true
format = "Text"
"#;
    
    tokio::fs::write(&config_path, config_content)
        .await
        .expect("无法写入配置文件");
    
    // 使用超时加载配置
    let result = timeout(
        Duration::from_millis(100),
        DaemonConfig::load_from_file(&config_path)
    ).await;
    
    // 应该在超时前完成
    assert!(result.is_ok());
    let config = result.unwrap().expect("配置加载失败");
    assert_eq!(config.service.name, "timeout-test");
}

/// 测试配置验证的边界情况
#[tokio::test]
async fn test_config_validation_edge_cases() {
    let mut config = DaemonConfig::default();
    
    // 测试最大长度的服务名称
    config.service.name = "a".repeat(64);
    assert!(config.validate().is_ok());
    
    // 测试超长的服务名称
    config.service.name = "a".repeat(65);
    assert!(config.validate().is_err());
    
    // 重置服务名称
    config.service.name = "test".to_string();
    
    // 测试最大连接数
    config.ipc.max_connections = 10000;
    assert!(config.validate().is_ok());
    
    // 测试超过最大连接数
    config.ipc.max_connections = 10001;
    assert!(config.validate().is_err());
}
