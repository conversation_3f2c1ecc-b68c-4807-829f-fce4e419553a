//! 浏览器适配层独立测试

use secure_password_daemon::native_messaging::browser::{BrowserType, BrowserAdapterConfig};

#[test]
fn test_browser_type_basic() {
    // 测试浏览器类型的基本方法
    assert_eq!(BrowserType::Chrome.name(), "Chrome");
    assert_eq!(BrowserType::Firefox.name(), "Firefox");
    assert_eq!(BrowserType::Edge.name(), "Edge");
    assert_eq!(BrowserType::Safari.name(), "Safari");
}

#[test]
fn test_browser_type_host_names() {
    // 测试主机名生成
    assert_eq!(BrowserType::Chrome.host_name(), "com.securepassword.chrome");
    assert_eq!(BrowserType::Firefox.host_name(), "com.securepassword.firefox");
    assert_eq!(BrowserType::Edge.host_name(), "com.securepassword.edge");
    assert_eq!(BrowserType::Safari.host_name(), "com.securepassword.safari");
}

#[test]
fn test_browser_adapter_config_default() {
    let config = BrowserAdapterConfig::default();
    
    assert_eq!(config.browser_type, BrowserType::Chrome);
    assert_eq!(config.connection_timeout, 30);
    assert_eq!(config.max_retries, 3);
    assert!(!config.debug_mode);
    assert!(config.allowed_extensions.is_empty());
}

#[test]
fn test_browser_adapter_config_clone() {
    let config1 = BrowserAdapterConfig::default();
    let config2 = config1.clone();
    
    assert_eq!(config1.browser_type, config2.browser_type);
    assert_eq!(config1.connection_timeout, config2.connection_timeout);
    assert_eq!(config1.max_retries, config2.max_retries);
    assert_eq!(config1.debug_mode, config2.debug_mode);
}

#[test]
fn test_browser_adapter_config_custom() {
    let mut config = BrowserAdapterConfig::default();
    config.browser_type = BrowserType::Firefox;
    config.connection_timeout = 60;
    config.max_retries = 5;
    config.debug_mode = true;
    config.allowed_extensions = vec!["test-extension".to_string()];
    
    assert_eq!(config.browser_type, BrowserType::Firefox);
    assert_eq!(config.connection_timeout, 60);
    assert_eq!(config.max_retries, 5);
    assert!(config.debug_mode);
    assert_eq!(config.allowed_extensions.len(), 1);
    assert_eq!(config.allowed_extensions[0], "test-extension");
}

#[test]
fn test_browser_type_debug_format() {
    assert_eq!(format!("{:?}", BrowserType::Chrome), "Chrome");
    assert_eq!(format!("{:?}", BrowserType::Firefox), "Firefox");
    assert_eq!(format!("{:?}", BrowserType::Edge), "Edge");
    assert_eq!(format!("{:?}", BrowserType::Safari), "Safari");
}

#[test]
fn test_browser_type_equality() {
    assert_eq!(BrowserType::Chrome, BrowserType::Chrome);
    assert_ne!(BrowserType::Chrome, BrowserType::Firefox);
    assert_ne!(BrowserType::Firefox, BrowserType::Edge);
    assert_ne!(BrowserType::Edge, BrowserType::Safari);
}

#[test]
fn test_browser_type_clone() {
    let browser1 = BrowserType::Chrome;
    let browser2 = browser1.clone();
    
    assert_eq!(browser1, browser2);
}

#[test]
fn test_browser_adapter_config_serialization() {
    // 测试配置的序列化/反序列化
    let config = BrowserAdapterConfig::default();
    
    // 序列化为JSON
    let json = serde_json::to_string(&config).unwrap();
    assert!(json.contains("Chrome"));
    assert!(json.contains("30"));
    assert!(json.contains("3"));
    
    // 反序列化
    let deserialized: BrowserAdapterConfig = serde_json::from_str(&json).unwrap();
    assert_eq!(config.browser_type, deserialized.browser_type);
    assert_eq!(config.connection_timeout, deserialized.connection_timeout);
    assert_eq!(config.max_retries, deserialized.max_retries);
} 