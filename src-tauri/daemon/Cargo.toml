[package]
name = "secure-password-daemon"
version = "0.1.0"
edition = "2021"
authors = ["Secure Password Team"]
description = "企业级独立守护进程 - 负责Native Messaging代理、IPC通信和应用管理"
license = "MIT"
repository = "https://github.com/your-org/secure-password"
keywords = ["daemon", "native-messaging", "ipc", "security", "password-manager"]
categories = ["authentication", "cryptography", "network-programming"]

[dependencies]
# 异步运行时
tokio = { workspace = true }
tokio-util = "0.7"

# 序列化和反序列化
serde = { workspace = true }
serde_json = { workspace = true }
toml = "0.8"
rmp-serde = "1.1"
bincode = "1.3"

# 日志和错误处理
tracing = { workspace = true }
tracing-subscriber = { workspace = true, features = ["env-filter", "json"] }
anyhow = { workspace = true }
thiserror = { workspace = true }

# 命令行参数解析
clap = { version = "4.0", features = ["derive"] }

# 配置管理
config = "0.14"

# 时间处理
chrono = { workspace = true, features = ["serde"] }

# UUID生成
uuid = { workspace = true }

# 随机数生成
rand = "0.8"

# 并发和同步
parking_lot = "0.12"

# 哈希算法
sha2 = "0.10"
blake3 = "1.5"

# 加密库
aes-gcm = "0.10"
chacha20poly1305 = "0.10"
hmac = "0.12"

# 系统信息
hostname = "0.3"
num_cpus = "1.0"
regex = "1.10"

# 新增依赖项
async-trait = "0.1"
futures = "0.3"
tracing-appender = "0.2"
dirs = "5.0"
reqwest = { version = "0.11", features = ["json"] }
signal-hook = "0.3"
signal-hook-tokio = { version = "0.3", features = ["futures-v0_3"] }
sysinfo = "0.30"

# 跨平台系统API
[target.'cfg(windows)'.dependencies]
windows = { version = "0.52", features = [
    "Win32_Foundation",
    "Win32_System_Services",
    "Win32_System_Registry",
    "Win32_Security",
    "Win32_System_Threading",
    "Win32_System_Pipes",
] }
winapi = { version = "0.3", features = ["winuser", "winsvc"] }

[target.'cfg(unix)'.dependencies]
nix = { version = "0.27", features = ["signal", "process", "socket"] }
libc = "0.2"

[target.'cfg(target_os = "macos")'.dependencies]
core-foundation = "0.9"
core-services = "0.2"
plist = "1.5"

# 开发依赖
[dev-dependencies]
tempfile = "3.0"
serial_test = "3.0"
mockall = "0.12"
tokio-test = "0.4"
regex = "1.10"

# 构建配置
[build-dependencies]
tauri-build = { version = "2.0", features = [] }
chrono = { workspace = true }
pkg-config = "0.3"

# 发布配置
[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true

# 开发配置
[profile.dev]
opt-level = 0
debug = true
overflow-checks = true

# 库目标
[lib]
name = "secure_password_daemon"
path = "src/lib.rs"

# 二进制目标
[[bin]]
name = "secure-password-daemon"
path = "src/main.rs"

# 特性标志
[features]
default = ["system-service"]
system-service = []
debug-mode = []
enterprise = ["system-service"]

[target.'cfg(target_os = "linux")'.dependencies]
which = "4.4"
