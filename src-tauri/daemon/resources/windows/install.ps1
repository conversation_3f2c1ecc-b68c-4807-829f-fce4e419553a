# Secure Password Daemon Windows 安装脚本
# 需要管理员权限运行

param(
    [string]$ServiceName = "SecurePasswordDaemon",
    [string]$DisplayName = "Secure Password Daemon",
    [string]$BinaryPath = "",
    [string]$ConfigPath = "",
    [switch]$Uninstall
)

# 检查管理员权限
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

if (-not (Test-Administrator)) {
    Write-Error "此脚本需要管理员权限运行"
    exit 1
}

# 设置默认路径
if ([string]::IsNullOrEmpty($BinaryPath)) {
    $BinaryPath = Join-Path $PSScriptRoot "secure-password-daemon.exe"
}

if ([string]::IsNullOrEmpty($ConfigPath)) {
    $ConfigPath = Join-Path $env:PROGRAMDATA "SecurePassword\daemon.toml"
}

# 卸载服务
if ($Uninstall) {
    Write-Host "卸载 $ServiceName 服务..."
    
    # 停止服务
    $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
    if ($service) {
        if ($service.Status -eq "Running") {
            Write-Host "停止服务..."
            Stop-Service -Name $ServiceName -Force
            Start-Sleep -Seconds 5
        }
        
        # 删除服务
        Write-Host "删除服务..."
        sc.exe delete $ServiceName
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "服务卸载成功" -ForegroundColor Green
        } else {
            Write-Error "服务卸载失败"
            exit 1
        }
    } else {
        Write-Host "服务不存在" -ForegroundColor Yellow
    }
    
    exit 0
}

# 安装服务
Write-Host "安装 $ServiceName 服务..."

# 检查二进制文件是否存在
if (-not (Test-Path $BinaryPath)) {
    Write-Error "二进制文件不存在: $BinaryPath"
    exit 1
}

# 创建配置目录
$configDir = Split-Path $ConfigPath -Parent
if (-not (Test-Path $configDir)) {
    Write-Host "创建配置目录: $configDir"
    New-Item -ItemType Directory -Path $configDir -Force | Out-Null
}

# 复制默认配置文件（如果不存在）
if (-not (Test-Path $ConfigPath)) {
    $defaultConfig = Join-Path $PSScriptRoot "daemon.toml"
    if (Test-Path $defaultConfig) {
        Write-Host "复制默认配置文件..."
        Copy-Item $defaultConfig $ConfigPath
    }
}

# 构建服务命令行
$serviceArgs = "--daemon --config `"$ConfigPath`""
$serviceBinaryPath = "`"$BinaryPath`" $serviceArgs"

# 检查服务是否已存在
$existingService = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
if ($existingService) {
    Write-Host "服务已存在，先删除旧服务..."
    if ($existingService.Status -eq "Running") {
        Stop-Service -Name $ServiceName -Force
        Start-Sleep -Seconds 5
    }
    sc.exe delete $ServiceName
    Start-Sleep -Seconds 2
}

# 创建服务
Write-Host "创建服务..."
sc.exe create $ServiceName binPath= $serviceBinaryPath DisplayName= $DisplayName start= auto

if ($LASTEXITCODE -ne 0) {
    Write-Error "服务创建失败"
    exit 1
}

# 设置服务描述
sc.exe description $ServiceName "企业级密码管理守护进程 - 负责Native Messaging代理、IPC通信和应用管理"

# 设置服务恢复选项
sc.exe failure $ServiceName reset= 86400 actions= restart/10000/restart/20000/restart/30000

# 设置服务为延迟启动
sc.exe config $ServiceName start= delayed-auto

# 启动服务
Write-Host "启动服务..."
Start-Service -Name $ServiceName

# 检查服务状态
Start-Sleep -Seconds 3
$service = Get-Service -Name $ServiceName
if ($service.Status -eq "Running") {
    Write-Host "服务安装并启动成功!" -ForegroundColor Green
    Write-Host "服务名称: $ServiceName"
    Write-Host "显示名称: $DisplayName"
    Write-Host "二进制路径: $BinaryPath"
    Write-Host "配置文件: $ConfigPath"
} else {
    Write-Warning "服务已安装但启动失败，请检查日志"
    Write-Host "可以使用以下命令查看服务状态:"
    Write-Host "  Get-Service -Name $ServiceName"
    Write-Host "  Get-EventLog -LogName System -Source 'Service Control Manager' -Newest 10"
}

Write-Host ""
Write-Host "管理命令:"
Write-Host "  启动服务: Start-Service -Name $ServiceName"
Write-Host "  停止服务: Stop-Service -Name $ServiceName"
Write-Host "  重启服务: Restart-Service -Name $ServiceName"
Write-Host "  查看状态: Get-Service -Name $ServiceName"
Write-Host "  卸载服务: .\install.ps1 -Uninstall"
