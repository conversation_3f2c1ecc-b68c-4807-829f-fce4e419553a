<?xml version="1.0" encoding="UTF-8"?>
<!-- Windows 服务配置文件 -->
<service>
    <id>SecurePasswordDaemon</id>
    <name>Secure Password Daemon</name>
    <description>企业级密码管理守护进程 - 负责Native Messaging代理、IPC通信和应用管理</description>
    <executable>secure-password-daemon.exe</executable>
    <arguments>--daemon --config daemon.toml</arguments>
    
    <!-- 服务配置 -->
    <startmode>Automatic</startmode>
    <delayedAutoStart>true</delayedAutoStart>
    <interactive>false</interactive>
    <depend>Tcpip</depend>
    
    <!-- 故障恢复配置 -->
    <onfailure action="restart" delay="10 sec"/>
    <onfailure action="restart" delay="20 sec"/>
    <onfailure action="none" delay="0 sec"/>
    <resetfailure>1 day</resetfailure>
    
    <!-- 日志配置 -->
    <logpath>%PROGRAMDATA%\SecurePassword\logs</logpath>
    <logmode>roll</logmode>
    <logSizeThreshold>10240</logSizeThreshold>
    <logKeepFiles>8</logKeepFiles>
    
    <!-- 安全配置 -->
    <serviceaccount>
        <domain>NT AUTHORITY</domain>
        <user>LocalService</user>
        <allowservicelogon>true</allowservicelogon>
    </serviceaccount>
    
    <!-- 环境变量 -->
    <env name="RUST_LOG" value="info"/>
    <env name="DAEMON_CONFIG_PATH" value="%PROGRAMDATA%\SecurePassword\daemon.toml"/>
    
    <!-- 工作目录 -->
    <workingdirectory>%PROGRAMDATA%\SecurePassword</workingdirectory>
    
    <!-- 优先级 -->
    <priority>Normal</priority>
    
    <!-- 停止超时 -->
    <stoptimeout>15 sec</stoptimeout>
    <killtimeout>15 sec</killtimeout>
</service>
