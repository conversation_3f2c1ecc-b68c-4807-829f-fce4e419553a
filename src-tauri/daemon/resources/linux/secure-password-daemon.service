[Unit]
Description=Secure Password Daemon
Documentation=https://github.com/your-org/secure-password
After=network.target network-online.target
Wants=network-online.target
Requires=network.target

[Service]
Type=notify
ExecStart=/usr/local/bin/secure-password-daemon --daemon --config /etc/secure-password/daemon.toml
ExecReload=/bin/kill -HUP $MAINPID
ExecStop=/bin/kill -TERM $MAINPID

# 重启配置
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# 用户和组
User=secure-password
Group=secure-password

# 工作目录
WorkingDirectory=/var/lib/secure-password

# 环境变量
Environment=RUST_LOG=info
Environment=DAEMON_CONFIG_PATH=/etc/secure-password/daemon.toml

# 安全配置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/lib/secure-password /var/log/secure-password
PrivateTmp=true
PrivateDevices=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictRealtime=true
RestrictNamespaces=true
LockPersonality=true
MemoryDenyWriteExecute=true
RestrictAddressFamilies=AF_UNIX AF_INET AF_INET6
SystemCallFilter=@system-service
SystemCallErrorNumber=EPERM

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096
MemoryMax=512M
CPUQuota=50%

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=secure-password-daemon

# 超时配置
TimeoutStartSec=60
TimeoutStopSec=30
TimeoutAbortSec=30

# 看门狗
WatchdogSec=30

# 能力配置
CapabilityBoundingSet=CAP_NET_BIND_SERVICE
AmbientCapabilities=CAP_NET_BIND_SERVICE

[Install]
WantedBy=multi-user.target
Alias=secure-password.service
