#!/bin/bash
# Secure Password Daemon Linux 安装脚本
# 需要 sudo 权限运行

set -e

# 配置变量
SERVICE_NAME="secure-password-daemon"
BINARY_NAME="secure-password-daemon"
INSTALL_DIR="/usr/local/bin"
CONFIG_DIR="/etc/secure-password"
DATA_DIR="/var/lib/secure-password"
LOG_DIR="/var/log/secure-password"
SYSTEMD_DIR="/etc/systemd/system"
USER_NAME="secure-password"
GROUP_NAME="secure-password"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要 sudo 权限运行"
        exit 1
    fi
}

# 检查 systemd
check_systemd() {
    if ! command -v systemctl &> /dev/null; then
        log_error "此系统不支持 systemd"
        exit 1
    fi
}

# 创建用户和组
create_user() {
    log_info "创建用户和组..."
    
    # 创建组
    if ! getent group $GROUP_NAME &>/dev/null; then
        log_info "创建组: $GROUP_NAME"
        groupadd --system $GROUP_NAME
    fi
    
    # 创建用户
    if ! getent passwd $USER_NAME &>/dev/null; then
        log_info "创建用户: $USER_NAME"
        useradd --system --gid $GROUP_NAME --home-dir $DATA_DIR \
                --shell /usr/sbin/nologin --comment "Secure Password Daemon" \
                $USER_NAME
    fi
}

# 创建目录
create_directories() {
    log_info "创建目录..."
    
    mkdir -p "$CONFIG_DIR"
    mkdir -p "$DATA_DIR"
    mkdir -p "$LOG_DIR"
    
    # 设置权限
    chown -R $USER_NAME:$GROUP_NAME "$DATA_DIR"
    chown -R $USER_NAME:$GROUP_NAME "$LOG_DIR"
    chmod 755 "$CONFIG_DIR"
    chmod 750 "$DATA_DIR"
    chmod 750 "$LOG_DIR"
}

# 安装二进制文件
install_binary() {
    local binary_path="$1"
    
    if [[ -z "$binary_path" ]]; then
        binary_path="$(dirname "$0")/$BINARY_NAME"
    fi
    
    if [[ ! -f "$binary_path" ]]; then
        log_error "二进制文件不存在: $binary_path"
        exit 1
    fi
    
    log_info "安装二进制文件到 $INSTALL_DIR/$BINARY_NAME"
    cp "$binary_path" "$INSTALL_DIR/$BINARY_NAME"
    chmod 755 "$INSTALL_DIR/$BINARY_NAME"
    chown root:root "$INSTALL_DIR/$BINARY_NAME"
}

# 安装配置文件
install_config() {
    local config_path="$CONFIG_DIR/daemon.toml"
    
    if [[ ! -f "$config_path" ]]; then
        local default_config="$(dirname "$0")/daemon.toml"
        if [[ -f "$default_config" ]]; then
            log_info "复制默认配置文件..."
            cp "$default_config" "$config_path"
        else
            log_info "创建默认配置文件..."
            cat > "$config_path" << 'EOF'
[service]
name = "secure-password-daemon"
display_name = "Secure Password Daemon"
description = "企业级密码管理守护进程"
auto_start = true

[ipc]
transport = "Auto"
bind_address = "127.0.0.1"
port = 8080
max_connections = 100
connection_timeout = 30

[logging]
level = "info"
file_path = "/var/log/secure-password/daemon.log"
console = false
format = "Text"

[linux]
user = "secure-password"
group = "secure-password"
systemd_notify = true
EOF
        fi
        
        chmod 644 "$config_path"
        chown root:root "$config_path"
    fi
}

# 安装 systemd 服务
install_systemd() {
    local service_path="$SYSTEMD_DIR/$SERVICE_NAME.service"
    
    log_info "安装 systemd 服务..."
    
    # 创建服务文件
    cat > "$service_path" << EOF
[Unit]
Description=Secure Password Daemon
Documentation=https://github.com/your-org/secure-password
After=network.target network-online.target
Wants=network-online.target
Requires=network.target

[Service]
Type=notify
ExecStart=$INSTALL_DIR/$BINARY_NAME --daemon --config $CONFIG_DIR/daemon.toml
ExecReload=/bin/kill -HUP \$MAINPID
ExecStop=/bin/kill -TERM \$MAINPID

# 重启配置
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# 用户和组
User=$USER_NAME
Group=$GROUP_NAME

# 工作目录
WorkingDirectory=$DATA_DIR

# 环境变量
Environment=RUST_LOG=info
Environment=DAEMON_CONFIG_PATH=$CONFIG_DIR/daemon.toml

# 安全配置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$DATA_DIR $LOG_DIR
PrivateTmp=true
PrivateDevices=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictRealtime=true
RestrictNamespaces=true
LockPersonality=true
MemoryDenyWriteExecute=true
RestrictAddressFamilies=AF_UNIX AF_INET AF_INET6
SystemCallFilter=@system-service
SystemCallErrorNumber=EPERM

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096
MemoryMax=512M
CPUQuota=50%

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

# 超时配置
TimeoutStartSec=60
TimeoutStopSec=30
TimeoutAbortSec=30

# 看门狗
WatchdogSec=30

# 能力配置
CapabilityBoundingSet=CAP_NET_BIND_SERVICE
AmbientCapabilities=CAP_NET_BIND_SERVICE

[Install]
WantedBy=multi-user.target
Alias=secure-password.service
EOF
    
    chmod 644 "$service_path"
    chown root:root "$service_path"
}

# 启动服务
start_service() {
    log_info "启动服务..."
    
    # 重新加载 systemd
    systemctl daemon-reload
    
    # 启用服务
    systemctl enable "$SERVICE_NAME"
    
    # 启动服务
    systemctl start "$SERVICE_NAME"
    
    # 检查状态
    sleep 3
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_info "服务启动成功!"
    else
        log_warn "服务可能启动失败，请检查状态和日志"
        systemctl status "$SERVICE_NAME" --no-pager || true
    fi
}

# 卸载服务
uninstall_service() {
    log_info "卸载服务..."
    
    # 停止并禁用服务
    systemctl stop "$SERVICE_NAME" 2>/dev/null || true
    systemctl disable "$SERVICE_NAME" 2>/dev/null || true
    
    # 删除服务文件
    rm -f "$SYSTEMD_DIR/$SERVICE_NAME.service"
    
    # 重新加载 systemd
    systemctl daemon-reload
    
    # 删除二进制文件
    rm -f "$INSTALL_DIR/$BINARY_NAME"
    
    log_info "服务卸载完成"
    log_warn "配置文件和数据目录未删除，如需完全清理请手动删除:"
    echo "  $CONFIG_DIR"
    echo "  $DATA_DIR"
    echo "  $LOG_DIR"
    log_warn "用户和组未删除，如需删除请手动执行:"
    echo "  sudo userdel $USER_NAME"
    echo "  sudo groupdel $GROUP_NAME"
}

# 显示帮助
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -b, --binary PATH    指定二进制文件路径"
    echo "  -u, --uninstall      卸载服务"
    echo "  -h, --help           显示帮助信息"
    echo ""
    echo "示例:"
    echo "  sudo $0                              # 安装服务"
    echo "  sudo $0 -b /path/to/daemon           # 指定二进制文件安装"
    echo "  sudo $0 --uninstall                 # 卸载服务"
}

# 主函数
main() {
    local binary_path=""
    local uninstall=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -b|--binary)
                binary_path="$2"
                shift 2
                ;;
            -u|--uninstall)
                uninstall=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    check_permissions
    check_systemd
    
    if [[ "$uninstall" == true ]]; then
        uninstall_service
    else
        create_user
        create_directories
        install_binary "$binary_path"
        install_config
        install_systemd
        start_service
        
        log_info "安装完成!"
        echo ""
        echo "管理命令:"
        echo "  启动服务: sudo systemctl start $SERVICE_NAME"
        echo "  停止服务: sudo systemctl stop $SERVICE_NAME"
        echo "  重启服务: sudo systemctl restart $SERVICE_NAME"
        echo "  查看状态: systemctl status $SERVICE_NAME"
        echo "  查看日志: journalctl -u $SERVICE_NAME -f"
        echo "  卸载服务: sudo $0 --uninstall"
    fi
}

# 运行主函数
main "$@"
