#!/bin/bash
# Secure Password Daemon macOS 安装脚本
# 需要 sudo 权限运行

set -e

# 配置变量
SERVICE_NAME="com.securepassword.daemon"
BINARY_NAME="secure-password-daemon"
INSTALL_DIR="/usr/local/bin"
CONFIG_DIR="/usr/local/etc/secure-password"
DATA_DIR="/usr/local/var/lib/secure-password"
LOG_DIR="/usr/local/var/log/secure-password"
PLIST_DIR="/Library/LaunchDaemons"
USER_NAME="_securepassword"
GROUP_NAME="_securepassword"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要 sudo 权限运行"
        exit 1
    fi
}

# 创建用户和组
create_user() {
    log_info "创建用户和组..."
    
    # 检查组是否存在
    if ! dscl . -read /Groups/$GROUP_NAME &>/dev/null; then
        log_info "创建组: $GROUP_NAME"
        dscl . -create /Groups/$GROUP_NAME
        dscl . -create /Groups/$GROUP_NAME PrimaryGroupID 200
    fi
    
    # 检查用户是否存在
    if ! dscl . -read /Users/<USER>/dev/null; then
        log_info "创建用户: $USER_NAME"
        dscl . -create /Users/<USER>
        dscl . -create /Users/<USER>/usr/bin/false
        dscl . -create /Users/<USER>"Secure Password Daemon"
        dscl . -create /Users/<USER>
        dscl . -create /Users/<USER>
        dscl . -create /Users/<USER>/var/empty
    fi
}

# 创建目录
create_directories() {
    log_info "创建目录..."
    
    mkdir -p "$CONFIG_DIR"
    mkdir -p "$DATA_DIR"
    mkdir -p "$LOG_DIR"
    
    # 设置权限
    chown -R $USER_NAME:$GROUP_NAME "$DATA_DIR"
    chown -R $USER_NAME:$GROUP_NAME "$LOG_DIR"
    chmod 755 "$CONFIG_DIR"
    chmod 750 "$DATA_DIR"
    chmod 750 "$LOG_DIR"
}

# 安装二进制文件
install_binary() {
    local binary_path="$1"
    
    if [[ -z "$binary_path" ]]; then
        binary_path="$(dirname "$0")/$BINARY_NAME"
    fi
    
    if [[ ! -f "$binary_path" ]]; then
        log_error "二进制文件不存在: $binary_path"
        exit 1
    fi
    
    log_info "安装二进制文件到 $INSTALL_DIR/$BINARY_NAME"
    cp "$binary_path" "$INSTALL_DIR/$BINARY_NAME"
    chmod 755 "$INSTALL_DIR/$BINARY_NAME"
    chown root:wheel "$INSTALL_DIR/$BINARY_NAME"
}

# 安装配置文件
install_config() {
    local config_path="$CONFIG_DIR/daemon.toml"
    
    if [[ ! -f "$config_path" ]]; then
        local default_config="$(dirname "$0")/daemon.toml"
        if [[ -f "$default_config" ]]; then
            log_info "复制默认配置文件..."
            cp "$default_config" "$config_path"
        else
            log_info "创建默认配置文件..."
            cat > "$config_path" << 'EOF'
[service]
name = "secure-password-daemon"
display_name = "Secure Password Daemon"
description = "企业级密码管理守护进程"
auto_start = true

[ipc]
transport = "Auto"
bind_address = "127.0.0.1"
port = 8080
max_connections = 100
connection_timeout = 30

[logging]
level = "info"
file_path = "/usr/local/var/log/secure-password/daemon.log"
console = false
format = "Text"
EOF
        fi
        
        chmod 644 "$config_path"
        chown root:wheel "$config_path"
    fi
}

# 安装 LaunchDaemon
install_launchd() {
    local plist_path="$PLIST_DIR/$SERVICE_NAME.plist"
    
    log_info "安装 LaunchDaemon..."
    
    # 创建 plist 文件
    cat > "$plist_path" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>$SERVICE_NAME</string>
    
    <key>Program</key>
    <string>$INSTALL_DIR/$BINARY_NAME</string>
    
    <key>ProgramArguments</key>
    <array>
        <string>$INSTALL_DIR/$BINARY_NAME</string>
        <string>--daemon</string>
        <string>--config</string>
        <string>$CONFIG_DIR/daemon.toml</string>
    </array>
    
    <key>RunAtLoad</key>
    <true/>
    
    <key>KeepAlive</key>
    <dict>
        <key>SuccessfulExit</key>
        <false/>
        <key>Crashed</key>
        <true/>
    </dict>
    
    <key>WorkingDirectory</key>
    <string>$DATA_DIR</string>
    
    <key>UserName</key>
    <string>$USER_NAME</string>
    <key>GroupName</key>
    <string>$GROUP_NAME</string>
    
    <key>StandardOutPath</key>
    <string>$LOG_DIR/daemon.log</string>
    <key>StandardErrorPath</key>
    <string>$LOG_DIR/daemon.error.log</string>
    
    <key>ProcessType</key>
    <string>Background</string>
    
    <key>ExitTimeOut</key>
    <integer>30</integer>
    
    <key>ThrottleInterval</key>
    <integer>10</integer>
</dict>
</plist>
EOF
    
    chmod 644 "$plist_path"
    chown root:wheel "$plist_path"
}

# 启动服务
start_service() {
    log_info "启动服务..."
    
    # 加载服务
    launchctl load "$PLIST_DIR/$SERVICE_NAME.plist"
    
    # 启动服务
    launchctl start "$SERVICE_NAME"
    
    # 检查状态
    sleep 3
    if launchctl list | grep -q "$SERVICE_NAME"; then
        log_info "服务启动成功!"
    else
        log_warn "服务可能启动失败，请检查日志"
    fi
}

# 卸载服务
uninstall_service() {
    log_info "卸载服务..."
    
    # 停止并卸载服务
    launchctl stop "$SERVICE_NAME" 2>/dev/null || true
    launchctl unload "$PLIST_DIR/$SERVICE_NAME.plist" 2>/dev/null || true
    
    # 删除文件
    rm -f "$PLIST_DIR/$SERVICE_NAME.plist"
    rm -f "$INSTALL_DIR/$BINARY_NAME"
    
    log_info "服务卸载完成"
    log_warn "配置文件和数据目录未删除，如需完全清理请手动删除:"
    echo "  $CONFIG_DIR"
    echo "  $DATA_DIR"
    echo "  $LOG_DIR"
}

# 显示帮助
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -b, --binary PATH    指定二进制文件路径"
    echo "  -u, --uninstall      卸载服务"
    echo "  -h, --help           显示帮助信息"
    echo ""
    echo "示例:"
    echo "  sudo $0                              # 安装服务"
    echo "  sudo $0 -b /path/to/daemon           # 指定二进制文件安装"
    echo "  sudo $0 --uninstall                 # 卸载服务"
}

# 主函数
main() {
    local binary_path=""
    local uninstall=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -b|--binary)
                binary_path="$2"
                shift 2
                ;;
            -u|--uninstall)
                uninstall=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    check_permissions
    
    if [[ "$uninstall" == true ]]; then
        uninstall_service
    else
        create_user
        create_directories
        install_binary "$binary_path"
        install_config
        install_launchd
        start_service
        
        log_info "安装完成!"
        echo ""
        echo "管理命令:"
        echo "  启动服务: sudo launchctl start $SERVICE_NAME"
        echo "  停止服务: sudo launchctl stop $SERVICE_NAME"
        echo "  查看状态: launchctl list | grep $SERVICE_NAME"
        echo "  查看日志: tail -f $LOG_DIR/daemon.log"
        echo "  卸载服务: sudo $0 --uninstall"
    fi
}

# 运行主函数
main "$@"
