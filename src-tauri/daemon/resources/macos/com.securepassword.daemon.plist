<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- 服务标识 -->
    <key>Label</key>
    <string>com.securepassword.daemon</string>
    
    <!-- 程序路径和参数 -->
    <key>Program</key>
    <string>/usr/local/bin/secure-password-daemon</string>
    
    <key>ProgramArguments</key>
    <array>
        <string>/usr/local/bin/secure-password-daemon</string>
        <string>--daemon</string>
        <string>--config</string>
        <string>/usr/local/etc/secure-password/daemon.toml</string>
    </array>
    
    <!-- 自动启动配置 -->
    <key>RunAtLoad</key>
    <true/>
    
    <key>KeepAlive</key>
    <dict>
        <key>SuccessfulExit</key>
        <false/>
        <key>Crashed</key>
        <true/>
    </dict>
    
    <!-- 工作目录 -->
    <key>WorkingDirectory</key>
    <string>/usr/local/var/lib/secure-password</string>
    
    <!-- 用户和组 -->
    <key>UserName</key>
    <string>_securepassword</string>
    <key>GroupName</key>
    <string>_securepassword</string>
    
    <!-- 环境变量 -->
    <key>EnvironmentVariables</key>
    <dict>
        <key>RUST_LOG</key>
        <string>info</string>
        <key>DAEMON_CONFIG_PATH</key>
        <string>/usr/local/etc/secure-password/daemon.toml</string>
        <key>PATH</key>
        <string>/usr/local/bin:/usr/bin:/bin</string>
    </dict>
    
    <!-- 日志配置 -->
    <key>StandardOutPath</key>
    <string>/usr/local/var/log/secure-password/daemon.log</string>
    
    <key>StandardErrorPath</key>
    <string>/usr/local/var/log/secure-password/daemon.error.log</string>
    
    <!-- 资源限制 -->
    <key>SoftResourceLimits</key>
    <dict>
        <key>NumberOfFiles</key>
        <integer>1024</integer>
        <key>NumberOfProcesses</key>
        <integer>100</integer>
    </dict>
    
    <key>HardResourceLimits</key>
    <dict>
        <key>NumberOfFiles</key>
        <integer>2048</integer>
        <key>NumberOfProcesses</key>
        <integer>200</integer>
    </dict>
    
    <!-- 进程管理 -->
    <key>ProcessType</key>
    <string>Background</string>
    
    <key>ExitTimeOut</key>
    <integer>30</integer>
    
    <!-- 网络访问 -->
    <key>Sockets</key>
    <dict>
        <key>Listeners</key>
        <dict>
            <key>SockServiceName</key>
            <string>8080</string>
            <key>SockType</key>
            <string>stream</string>
            <key>SockFamily</key>
            <string>IPv4</string>
        </dict>
    </dict>
    
    <!-- 启动间隔 -->
    <key>ThrottleInterval</key>
    <integer>10</integer>
    
    <!-- 调试模式 -->
    <key>Debug</key>
    <false/>
    
    <!-- 禁用在安全模式下运行 -->
    <key>Disabled</key>
    <false/>
</dict>
</plist>
