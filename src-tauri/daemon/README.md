# Secure Password Daemon

企业级独立守护进程 - 负责Native Messaging代理、IPC通信和应用管理

## 概述

Secure Password Daemon 是一个独立运行的系统服务，提供以下核心功能：

- **Native Messaging 代理**: 处理浏览器扩展与主应用之间的通信
- **IPC 通信服务**: 提供高性能的进程间通信
- **应用生命周期管理**: 管理 Tauri 主应用的启动、监控和恢复
- **企业级安全防护**: 提供全方位的安全验证和防护
- **性能监控**: 实时监控系统性能和健康状态

## 特性

### 🔧 核心功能
- ✅ 跨平台支持 (Windows/macOS/Linux)
- ✅ 系统服务集成
- ✅ 配置热重载
- ✅ 优雅关闭
- ✅ 错误恢复

### 🛡️ 安全特性
- ✅ 进程隔离
- ✅ 权限控制
- ✅ 加密通信
- ✅ 审计日志

### 📊 监控特性
- ✅ 健康检查
- ✅ 性能指标
- ✅ 告警系统
- ✅ 日志管理

## 快速开始

### 构建

```bash
# 开发构建
cargo build

# 发布构建
cargo build --release

# 运行测试
cargo test
```

### 运行

```bash
# 交互模式运行
./target/release/secure-password-daemon

# 守护进程模式运行
./target/release/secure-password-daemon --daemon

# 指定配置文件
./target/release/secure-password-daemon --config /path/to/daemon.toml

# 设置日志级别
./target/release/secure-password-daemon --log-level debug
```

### 安装为系统服务

#### Windows
```cmd
# 安装服务
sc create SecurePasswordDaemon binPath= "C:\path\to\secure-password-daemon.exe --daemon"
sc config SecurePasswordDaemon start= auto
sc start SecurePasswordDaemon
```

#### macOS
```bash
# 复制 plist 文件
sudo cp resources/macos/com.securepassword.daemon.plist /Library/LaunchDaemons/

# 加载服务
sudo launchctl load /Library/LaunchDaemons/com.securepassword.daemon.plist

# 启动服务
sudo launchctl start com.securepassword.daemon
```

#### Linux (systemd)
```bash
# 复制服务文件
sudo cp resources/linux/secure-password-daemon.service /etc/systemd/system/

# 重新加载 systemd
sudo systemctl daemon-reload

# 启用并启动服务
sudo systemctl enable secure-password-daemon
sudo systemctl start secure-password-daemon
```

## 配置

守护进程使用 TOML 格式的配置文件。默认配置文件为 `daemon.toml`。

### 配置示例

```toml
[service]
name = "secure-password-daemon"
display_name = "Secure Password Daemon"
description = "企业级密码管理守护进程"
auto_start = true

[ipc]
transport = "Auto"
bind_address = "127.0.0.1"
port = 8080
max_connections = 100
connection_timeout = 30

[native_messaging]
enabled = true
host_name = "com.securepassword.host"
supported_browsers = ["chrome", "firefox", "edge"]

[logging]
level = "info"
file_path = "daemon.log"
console = true
format = "Text"
```

### 配置选项

详细的配置选项说明请参考 [配置文档](docs/configuration.md)。

## 架构

```
┌─────────────────────────────────────────────────────────────┐
│                独立守护进程 (系统服务)                        │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                Native Messaging 代理                    │ │
│  │  ├─ 🌐 处理浏览器扩展请求 (Chrome/Firefox/Edge/Safari)  │ │
│  │  ├─ 🔧 IPC 通信服务 (TCP/Unix Socket/Named Pipe)       │ │
│  │  ├─ 🚀 Tauri 应用启动和生命周期管理                     │ │
│  │  ├─ 🔐 企业级安全验证和权限控制                         │ │
│  │  └─ 📊 性能监控和健康检查                               │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                             ↓ IPC 通信
┌─────────────────────────────────────────────────────────────┐
│                    Tauri 主应用                              │
│  ├─ 🎨 用户界面和交互                                        │
│  ├─ 💼 密码管理业务逻辑                                      │
│  ├─ 📡 接收守护进程 IPC 请求                                 │
│  └─ 🔒 加密存储和数据处理                                    │
└─────────────────────────────────────────────────────────────┘
```

## 开发

### 项目结构

```
src-tauri/daemon/
├── Cargo.toml                    # 项目配置
├── src/
│   ├── main.rs                   # 程序入口
│   ├── daemon_core.rs            # 核心逻辑
│   ├── config/                   # 配置管理
│   ├── platform/                 # 平台特定实现
│   ├── ipc/                      # IPC 通信
│   ├── native_messaging/         # Native Messaging
│   ├── app_manager/              # 应用管理
│   ├── security/                 # 安全模块
│   ├── monitoring/               # 监控模块
│   ├── error.rs                  # 错误处理
│   └── utils/                    # 工具函数
├── resources/                    # 系统服务配置
│   ├── windows/
│   ├── macos/
│   └── linux/
├── tests/                        # 测试文件
└── daemon.toml                   # 默认配置
```

### 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 详情请参阅 [LICENSE](LICENSE) 文件。

## 支持

如有问题或建议，请：

1. 查看 [文档](docs/)
2. 搜索 [Issues](https://github.com/your-org/secure-password/issues)
3. 创建新的 Issue

## 更新日志

详细的更新日志请参阅 [CHANGELOG.md](CHANGELOG.md)。
