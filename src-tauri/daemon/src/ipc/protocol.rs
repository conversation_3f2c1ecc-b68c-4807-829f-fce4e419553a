//! IPC 协议定义
//! 
//! 定义进程间通信的消息格式、错误类型和版本管理

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{SystemTime, UNIX_EPOCH};
use uuid::Uuid;
use thiserror::Error;

/// IPC 协议版本
pub const IPC_PROTOCOL_VERSION: u32 = 1;

/// IPC 消息定义
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct IpcMessage {
    /// 协议版本
    pub version: u32,
    /// 消息ID (用于请求-响应配对)
    pub message_id: String,
    /// 消息类型
    pub message_type: IpcMessageType,
    /// 消息负载
    pub payload: serde_json::Value,
    /// 时间戳 (Unix时间戳)
    pub timestamp: u64,
    /// 消息来源
    pub source: String,
    /// 消息目标 (可选)
    pub target: Option<String>,
    /// 是否需要响应
    pub response_required: bool,
    /// 消息优先级
    pub priority: MessagePriority,
    /// 自定义头部信息
    pub headers: HashMap<String, String>,
}

/// IPC 消息类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum IpcMessageType {
    // 系统控制消息
    /// Ping 消息 (健康检查)
    Ping,
    /// Pong 消息 (Ping 响应)
    Pong,
    /// 关闭连接
    Close,
    /// 心跳消息
    Heartbeat,

    // 应用管理消息
    /// 启动应用
    LaunchApp,
    /// 关闭应用
    ShutdownApp,
    /// 应用状态查询
    AppStatus,
    /// 应用健康检查
    AppHealthCheck,

    // Native Messaging 代理消息
    /// 浏览器请求
    BrowserRequest,
    /// 浏览器响应
    BrowserResponse,
    /// 扩展注册
    ExtensionRegister,
    /// 扩展注销
    ExtensionUnregister,

    // 安全验证消息
    /// 认证请求
    AuthRequest,
    /// 认证响应
    AuthResponse,
    /// 权限验证
    PermissionCheck,
    /// 安全事件通知
    SecurityEvent,

    // 监控和统计消息
    /// 指标上报
    MetricsReport,
    /// 健康状态报告
    HealthReport,
    /// 日志消息
    LogMessage,
    /// 告警消息
    AlertMessage,

    // 通用消息
    /// 成功响应
    Success,
    /// 错误响应
    Error,
    /// 通知消息
    Notification,
    /// 自定义消息
    Custom(String),
}

/// 消息优先级
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum MessagePriority {
    /// 低优先级
    Low = 0,
    /// 普通优先级
    Normal = 1,
    /// 高优先级
    High = 2,
    /// 紧急优先级
    Critical = 3,
}

/// IPC 响应消息
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct IpcResponse {
    /// 原始请求的消息ID
    pub request_id: String,
    /// 响应状态
    pub status: ResponseStatus,
    /// 响应数据
    pub data: serde_json::Value,
    /// 错误信息 (如果有)
    pub error: Option<String>,
    /// 响应时间戳
    pub timestamp: u64,
    /// 响应来源
    pub source: String,
}

impl IpcResponse {
    /// 创建成功响应
    pub fn success(request_id: String, data: serde_json::Value) -> Self {
        Self {
            request_id,
            status: ResponseStatus::Success,
            data,
            error: None,
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            source: "server".to_string(),
        }
    }
    
    /// 创建错误响应
    pub fn error(request_id: String, error: String) -> Self {
        Self {
            request_id,
            status: ResponseStatus::Error,
            data: serde_json::Value::Null,
            error: Some(error),
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            source: "server".to_string(),
        }
    }
    
    /// 检查响应是否成功
    pub fn is_success(&self) -> bool {
        self.status == ResponseStatus::Success
    }
}

/// 响应状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ResponseStatus {
    /// 成功
    Success,
    /// 失败
    Error,
    /// 超时
    Timeout,
    /// 未找到
    NotFound,
    /// 权限不足
    PermissionDenied,
    /// 服务不可用
    ServiceUnavailable,
}

/// IPC 错误类型
#[derive(Error, Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum IpcError {
    /// 协议版本不支持
    #[error("不支持的协议版本: {version}")]
    UnsupportedVersion { version: u32 },

    /// 消息格式错误
    #[error("消息格式错误: {message}")]
    InvalidMessageFormat { message: String },

    /// 消息序列化错误
    #[error("消息序列化失败: {error}")]
    SerializationError { error: String },

    /// 消息反序列化错误
    #[error("消息反序列化失败: {error}")]
    DeserializationError { error: String },

    /// 连接错误
    #[error("连接错误: {error}")]
    ConnectionError { error: String },

    /// 网络错误
    #[error("网络错误: {error}")]
    NetworkError { error: String },

    /// 超时错误
    #[error("操作超时: {operation}")]
    TimeoutError { operation: String },

    /// 权限错误
    #[error("权限不足: {operation}")]
    PermissionError { operation: String },

    /// 服务不可用
    #[error("服务不可用: {service}")]
    ServiceUnavailable { service: String },

    /// 资源不足
    #[error("资源不足: {resource}")]
    ResourceExhausted { resource: String },

    /// 内部错误
    #[error("内部错误: {error}")]
    InternalError { error: String },

    /// 未知错误
    #[error("未知错误: {error}")]
    Unknown { error: String },
}

impl IpcMessage {
    /// 创建新的IPC消息
    pub fn new(
        message_type: IpcMessageType,
        payload: serde_json::Value,
        source: String,
    ) -> Self {
        Self {
            version: IPC_PROTOCOL_VERSION,
            message_id: Uuid::new_v4().to_string(),
            message_type,
            payload,
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            source,
            target: None,
            response_required: false,
            priority: MessagePriority::Normal,
            headers: HashMap::new(),
        }
    }

    /// 创建需要响应的消息
    pub fn new_request(
        message_type: IpcMessageType,
        payload: serde_json::Value,
        source: String,
        target: String,
    ) -> Self {
        let mut message = Self::new(message_type, payload, source);
        message.target = Some(target);
        message.response_required = true;
        message
    }

    /// 创建响应消息
    pub fn create_response(
        &self,
        status: ResponseStatus,
        data: serde_json::Value,
        source: String,
    ) -> IpcResponse {
        IpcResponse {
            request_id: self.message_id.clone(),
            status,
            data,
            error: None,
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            source,
        }
    }

    /// 创建错误响应
    pub fn create_error_response(
        &self,
        error: String,
        source: String,
    ) -> IpcResponse {
        IpcResponse {
            request_id: self.message_id.clone(),
            status: ResponseStatus::Error,
            data: serde_json::Value::Null,
            error: Some(error),
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            source,
        }
    }

    /// 设置消息优先级
    pub fn with_priority(mut self, priority: MessagePriority) -> Self {
        self.priority = priority;
        self
    }

    /// 添加头部信息
    pub fn with_header(mut self, key: String, value: String) -> Self {
        self.headers.insert(key, value);
        self
    }

    /// 验证消息格式
    pub fn validate(&self) -> Result<(), IpcError> {
        // 检查协议版本
        if self.version != IPC_PROTOCOL_VERSION {
            return Err(IpcError::UnsupportedVersion {
                version: self.version,
            });
        }

        // 检查消息ID
        if self.message_id.is_empty() {
            return Err(IpcError::InvalidMessageFormat {
                message: "消息ID不能为空".to_string(),
            });
        }

        // 检查消息来源
        if self.source.is_empty() {
            return Err(IpcError::InvalidMessageFormat {
                message: "消息来源不能为空".to_string(),
            });
        }

        // 检查时间戳合理性 (不能是未来时间)
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        if self.timestamp > now + 300 {  // 允许5分钟的时钟偏差
            return Err(IpcError::InvalidMessageFormat {
                message: "消息时间戳不能是未来时间".to_string(),
            });
        }

        Ok(())
    }

    /// 检查消息是否过期
    pub fn is_expired(&self, ttl_seconds: u64) -> bool {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        now > self.timestamp + ttl_seconds
    }
    
    /// 创建ping消息
    pub fn ping(source: String) -> Self {
        Self::new(
            IpcMessageType::Ping,
            serde_json::json!({"ping": true}),
            source,
        )
    }
    
    /// 创建请求消息 (使用RequestResponse类型)
    pub fn request(source: String, payload: serde_json::Value) -> Self {
        let mut msg = Self::new(
            IpcMessageType::BrowserRequest, // 使用现有的请求类型
            payload,
            source,
        );
        msg.response_required = true;
        msg
    }
}

impl Default for MessagePriority {
    fn default() -> Self {
        MessagePriority::Normal
    }
}

/// 协议版本管理器
pub struct ProtocolVersionManager {
    /// 支持的版本列表
    supported_versions: Vec<u32>,
    /// 当前版本
    current_version: u32,
    /// 版本兼容性矩阵
    compatibility_matrix: HashMap<u32, Vec<u32>>,
}

impl ProtocolVersionManager {
    /// 创建新的版本管理器
    pub fn new() -> Self {
        let mut compatibility_matrix = HashMap::new();
        // 版本1兼容自己
        compatibility_matrix.insert(1, vec![1]);

        Self {
            supported_versions: vec![1],
            current_version: IPC_PROTOCOL_VERSION,
            compatibility_matrix,
        }
    }

    /// 检查版本是否支持
    pub fn is_version_supported(&self, version: u32) -> bool {
        self.supported_versions.contains(&version)
    }

    /// 获取兼容的版本列表
    pub fn get_compatible_versions(&self, version: u32) -> Option<&Vec<u32>> {
        self.compatibility_matrix.get(&version)
    }

    /// 协商版本
    pub fn negotiate_version(&self, client_versions: &[u32]) -> Option<u32> {
        // 找到客户端和服务端都支持的最高版本
        client_versions
            .iter()
            .filter(|&v| self.is_version_supported(*v))
            .max()
            .copied()
    }
}

impl Default for ProtocolVersionManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;

    #[test]
    fn test_ipc_message_creation() {
        let message = IpcMessage::new(
            IpcMessageType::Ping,
            json!({"data": "test"}),
            "test-client".to_string(),
        );

        assert_eq!(message.version, IPC_PROTOCOL_VERSION);
        assert_eq!(message.message_type, IpcMessageType::Ping);
        assert_eq!(message.source, "test-client");
        assert!(!message.message_id.is_empty());
        assert_eq!(message.response_required, false);
        assert_eq!(message.priority, MessagePriority::Normal);
    }

    #[test]
    fn test_ipc_request_message() {
        let message = IpcMessage::new_request(
            IpcMessageType::AppStatus,
            json!({"app_id": "test-app"}),
            "client".to_string(),
            "daemon".to_string(),
        );

        assert_eq!(message.target, Some("daemon".to_string()));
        assert_eq!(message.response_required, true);
    }

    #[test]
    fn test_message_validation() {
        let message = IpcMessage::new(
            IpcMessageType::Ping,
            json!({}),
            "test".to_string(),
        );

        assert!(message.validate().is_ok());
    }

    #[test]
    fn test_invalid_version() {
        let mut message = IpcMessage::new(
            IpcMessageType::Ping,
            json!({}),
            "test".to_string(),
        );
        message.version = 999;

        assert!(matches!(
            message.validate(),
            Err(IpcError::UnsupportedVersion { version: 999 })
        ));
    }

    #[test]
    fn test_empty_source() {
        let mut message = IpcMessage::new(
            IpcMessageType::Ping,
            json!({}),
            "test".to_string(),
        );
        message.source = String::new();

        assert!(matches!(
            message.validate(),
            Err(IpcError::InvalidMessageFormat { .. })
        ));
    }

    #[test]
    fn test_future_timestamp() {
        let mut message = IpcMessage::new(
            IpcMessageType::Ping,
            json!({}),
            "test".to_string(),
        );
        
        // 设置一个未来的时间戳 (1小时后)
        let future_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs() + 3600;
        message.timestamp = future_time;

        assert!(matches!(
            message.validate(),
            Err(IpcError::InvalidMessageFormat { .. })
        ));
    }

    #[test]
    fn test_message_expiration() {
        let mut message = IpcMessage::new(
            IpcMessageType::Ping,
            json!({}),
            "test".to_string(),
        );
        
        // 设置一个过去的时间戳
        message.timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs() - 3600; // 1小时前

        assert!(message.is_expired(1800)); // 30分钟TTL
        assert!(!message.is_expired(7200)); // 2小时TTL
    }

    #[test]
    fn test_response_creation() {
        let request = IpcMessage::new_request(
            IpcMessageType::AppStatus,
            json!({"app_id": "test"}),
            "client".to_string(),
            "daemon".to_string(),
        );

        let response = request.create_response(
            ResponseStatus::Success,
            json!({"status": "running"}),
            "daemon".to_string(),
        );

        assert_eq!(response.request_id, request.message_id);
        assert_eq!(response.status, ResponseStatus::Success);
        assert_eq!(response.source, "daemon");
        assert!(response.error.is_none());
    }

    #[test]
    fn test_error_response_creation() {
        let request = IpcMessage::new_request(
            IpcMessageType::AppStatus,
            json!({"app_id": "invalid"}),
            "client".to_string(),
            "daemon".to_string(),
        );

        let response = request.create_error_response(
            "应用不存在".to_string(),
            "daemon".to_string(),
        );

        assert_eq!(response.request_id, request.message_id);
        assert_eq!(response.status, ResponseStatus::Error);
        assert_eq!(response.error, Some("应用不存在".to_string()));
    }

    #[test]
    fn test_message_priority() {
        let message = IpcMessage::new(
            IpcMessageType::Ping,
            json!({}),
            "test".to_string(),
        ).with_priority(MessagePriority::High);

        assert_eq!(message.priority, MessagePriority::High);
    }

    #[test]
    fn test_message_headers() {
        let message = IpcMessage::new(
            IpcMessageType::Ping,
            json!({}),
            "test".to_string(),
        )
        .with_header("user-agent".to_string(), "test-client/1.0".to_string())
        .with_header("session-id".to_string(), "abc123".to_string());

        assert_eq!(message.headers.len(), 2);
        assert_eq!(message.headers.get("user-agent"), Some(&"test-client/1.0".to_string()));
        assert_eq!(message.headers.get("session-id"), Some(&"abc123".to_string()));
    }

    #[test]
    fn test_message_priority_ordering() {
        assert!(MessagePriority::Critical > MessagePriority::High);
        assert!(MessagePriority::High > MessagePriority::Normal);
        assert!(MessagePriority::Normal > MessagePriority::Low);
    }

    #[test]
    fn test_protocol_version_manager() {
        let manager = ProtocolVersionManager::new();

        assert!(manager.is_version_supported(1));
        assert!(!manager.is_version_supported(2));

        let compatible = manager.get_compatible_versions(1);
        assert!(compatible.is_some());
        assert_eq!(compatible.unwrap(), &vec![1]);

        let negotiated = manager.negotiate_version(&[1, 2, 3]);
        assert_eq!(negotiated, Some(1));

        let negotiated = manager.negotiate_version(&[2, 3]);
        assert_eq!(negotiated, None);
    }

    #[test]
    fn test_message_serialization() {
        let message = IpcMessage::new(
            IpcMessageType::Ping,
            json!({"data": "test"}),
            "test-client".to_string(),
        );

        // 测试序列化
        let serialized = serde_json::to_string(&message).unwrap();
        assert!(!serialized.is_empty());

        // 测试反序列化
        let deserialized: IpcMessage = serde_json::from_str(&serialized).unwrap();
        assert_eq!(message, deserialized);
    }

    #[test]
    fn test_response_serialization() {
        let response = IpcResponse {
            request_id: "test-id".to_string(),
            status: ResponseStatus::Success,
            data: json!({"result": "ok"}),
            error: None,
            timestamp: 1234567890,
            source: "daemon".to_string(),
        };

        // 测试序列化
        let serialized = serde_json::to_string(&response).unwrap();
        assert!(!serialized.is_empty());

        // 测试反序列化
        let deserialized: IpcResponse = serde_json::from_str(&serialized).unwrap();
        assert_eq!(response, deserialized);
    }

    #[test]
    fn test_custom_message_type() {
        let custom_type = IpcMessageType::Custom("MyCustomMessage".to_string());
        let message = IpcMessage::new(
            custom_type.clone(),
            json!({"custom_data": "value"}),
            "test".to_string(),
        );

        assert_eq!(message.message_type, custom_type);
    }
} 