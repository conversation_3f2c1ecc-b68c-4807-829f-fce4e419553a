//! IPC 通信引擎模块
//! 
//! 提供跨平台的进程间通信能力，支持：
//! - TCP Socket 通信 (跨平台)
//! - Unix Domain Socket (macOS/Linux)
//! - Named Pipe (Windows)
//! - 消息序列化和反序列化
//! - 连接池和负载均衡

pub mod protocol;
pub mod transport;
pub mod server;
pub mod client;
pub mod codec;
pub mod pool;

// 重新导出主要类型
pub use protocol::{IpcMessage, IpcMessageType, IpcResponse, IpcError};
pub use transport::{IpcTransport, IpcConnection, TransportConfig, TransportFactory};
pub use server::{IpcServer, ServerConfig};
pub use client::{IpcClient, ClientConfig};
pub use codec::{CodecType, Codec, JsonCodec, MessagePackCodec, BinaryCodec, CodecFactory, CodecManager};
pub use pool::{ConnectionPool, PoolConfig};

/// IPC 模块错误类型
pub type IpcResult<T> = Result<T, IpcError>;

// 最终集成测试模块 - 使用模拟连接，避免网络相关问题
#[cfg(test)]
pub mod integration_tests_final;

#[cfg(test)]
mod tests {
    use super::*;

    /// 测试模块基本导入
    #[test]
    fn test_module_imports() {
        // 确保所有主要类型都能正确导入
        let _: Option<IpcMessage> = None;
        let _: Option<IpcMessageType> = None;
        let _: Option<IpcResponse> = None;
        let _: Option<IpcError> = None;
    }
}
