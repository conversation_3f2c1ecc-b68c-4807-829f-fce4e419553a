//! IPC 传输层模块
//! 
//! 提供跨平台的传输层实现，支持：
//! - TCP Socket (跨平台)
//! - Unix Domain Socket (macOS/Linux)
//! - Named Pipe (Windows)

use async_trait::async_trait;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tokio::net::{TcpListener, TcpStream};
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use uuid::Uuid;
use std::time::Duration;
use crate::ipc::{IpcError, IpcResult};

#[cfg(unix)]
use tokio::net::{UnixListener, UnixStream};

#[cfg(windows)]
use tokio::net::windows::named_pipe::{NamedPipeServer, ClientOptions};

/// 传输配置
#[derive(Debug, Clone)]
pub struct TransportConfig {
    /// 传输类型
    pub transport_type: TransportType,
    /// 绑定地址
    pub address: String,
    /// 端口 (TCP模式)
    pub port: Option<u16>,
    /// 连接超时 (毫秒)
    pub timeout_ms: u64,
    /// 最大消息大小 (字节)
    pub max_message_size: usize,
    /// 缓冲区大小
    pub buffer_size: usize,
}

/// 传输类型
#[derive(Debug, Clone, PartialEq, serde::Serialize, serde::Deserialize)]
pub enum TransportType {
    /// TCP Socket
    Tcp,
    /// Unix Domain Socket
    UnixSocket,
    /// Windows Named Pipe
    NamedPipe,
}

/// IPC 传输层接口
#[async_trait]
pub trait IpcTransport: Send + Sync {
    /// 绑定到指定地址
    async fn bind(&mut self, config: &TransportConfig) -> IpcResult<()>;
    
    /// 接受新连接
    async fn accept(&mut self) -> IpcResult<Box<dyn IpcConnection>>;
    
    /// 连接到服务器
    async fn connect(config: &TransportConfig) -> IpcResult<Box<dyn IpcConnection>>
    where
        Self: Sized;
    
    /// 关闭传输
    async fn shutdown(&mut self) -> IpcResult<()>;
    
    /// 获取本地地址
    fn local_addr(&self) -> Option<String>;
}

/// IPC 连接接口
#[async_trait]
pub trait IpcConnection: Send + Sync {
    /// 发送数据
    async fn send(&mut self, data: &[u8]) -> IpcResult<()>;
    
    /// 接收数据
    async fn recv(&mut self) -> IpcResult<Vec<u8>>;
    
    /// 关闭连接
    async fn close(&mut self) -> IpcResult<()>;
    
    /// 获取连接ID
    fn connection_id(&self) -> &str;
    
    /// 检查连接是否活跃
    fn is_alive(&self) -> bool;
    
    /// 获取远程地址
    fn remote_addr(&self) -> Option<String>;

    /// 获取对等进程ID
    fn peer_pid(&self) -> Option<u32>;
}

/// TCP 传输实现
pub struct TcpTransport {
    listener: Option<TcpListener>,
    local_addr: Option<String>,
    config: Option<TransportConfig>,
}

impl TcpTransport {
    /// 创建新的TCP传输
    pub fn new() -> Self {
        Self {
            listener: None,
            local_addr: None,
            config: None,
        }
    }
}

#[async_trait]
impl IpcTransport for TcpTransport {
    async fn bind(&mut self, config: &TransportConfig) -> IpcResult<()> {
        let port = config.port.ok_or_else(|| IpcError::InvalidMessageFormat {
            message: "TCP传输需要指定端口".to_string(),
        })?;
        
        let addr = format!("{}:{}", config.address, port);
        let listener = TcpListener::bind(&addr).await.map_err(|e| {
            IpcError::NetworkError {
                error: format!("绑定TCP地址失败: {} - {}", addr, e),
            }
        })?;
        
        // 获取实际绑定的地址（当端口为0时系统会分配端口）
        let actual_addr = listener.local_addr().map_err(|e| {
            IpcError::NetworkError {
                error: format!("获取本地地址失败: {}", e),
            }
        })?;
        
        self.local_addr = Some(actual_addr.to_string());
        self.listener = Some(listener);
        self.config = Some(config.clone());
        
        Ok(())
    }
    
    async fn accept(&mut self) -> IpcResult<Box<dyn IpcConnection>> {
        let listener = self.listener.as_ref().ok_or_else(|| IpcError::ConnectionError {
            error: "TCP传输未绑定".to_string(),
        })?;
        
        let (stream, remote_addr) = listener.accept().await.map_err(|e| {
            IpcError::NetworkError {
                error: format!("接受TCP连接失败: {}", e),
            }
        })?;
        
        let connection = TcpConnection::new(stream, remote_addr.to_string());
        Ok(Box::new(connection))
    }
    
    async fn connect(config: &TransportConfig) -> IpcResult<Box<dyn IpcConnection>> {
        let port = config.port.ok_or_else(|| IpcError::InvalidMessageFormat {
            message: "TCP传输需要指定端口".to_string(),
        })?;
        
        let addr = format!("{}:{}", config.address, port);
        
        let stream = tokio::time::timeout(
            Duration::from_millis(config.timeout_ms),
            TcpStream::connect(&addr)
        ).await
        .map_err(|_| IpcError::TimeoutError {
            operation: format!("连接TCP服务器: {}", addr),
        })?
        .map_err(|e| IpcError::NetworkError {
            error: format!("连接TCP服务器失败: {} - {}", addr, e),
        })?;
        
        let connection = TcpConnection::new(stream, addr);
        Ok(Box::new(connection))
    }
    
    async fn shutdown(&mut self) -> IpcResult<()> {
        self.listener = None;
        self.local_addr = None;
        self.config = None;
        Ok(())
    }
    
    fn local_addr(&self) -> Option<String> {
        self.local_addr.clone()
    }
}

/// TCP 连接实现
pub struct TcpConnection {
    stream: TcpStream,
    connection_id: String,
    remote_addr: String,
    is_alive: bool,
}

impl TcpConnection {
    /// 创建新的TCP连接
    pub fn new(stream: TcpStream, remote_addr: String) -> Self {
        Self {
            stream,
            connection_id: Uuid::new_v4().to_string(),
            remote_addr,
            is_alive: true,
        }
    }
}

#[async_trait]
impl IpcConnection for TcpConnection {
    async fn send(&mut self, data: &[u8]) -> IpcResult<()> {
        if !self.is_alive {
            return Err(IpcError::ConnectionError {
                error: "连接已关闭".to_string(),
            });
        }
        
        // 发送数据长度前缀 (4字节大端序)
        let len = data.len() as u32;
        self.stream.write_all(&len.to_be_bytes()).await.map_err(|e| {
            self.is_alive = false;
            IpcError::NetworkError {
                error: format!("发送数据长度失败: {}", e),
            }
        })?;
        
        // 发送数据
        self.stream.write_all(data).await.map_err(|e| {
            self.is_alive = false;
            IpcError::NetworkError {
                error: format!("发送数据失败: {}", e),
            }
        })?;
        
        self.stream.flush().await.map_err(|e| {
            self.is_alive = false;
            IpcError::NetworkError {
                error: format!("刷新数据失败: {}", e),
            }
        })?;
        
        Ok(())
    }
    
    async fn recv(&mut self) -> IpcResult<Vec<u8>> {
        if !self.is_alive {
            return Err(IpcError::ConnectionError {
                error: "连接已关闭".to_string(),
            });
        }
        
        // 读取数据长度前缀 (4字节大端序)
        let mut len_buf = [0u8; 4];
        self.stream.read_exact(&mut len_buf).await.map_err(|e| {
            self.is_alive = false;
            IpcError::NetworkError {
                error: format!("读取数据长度失败: {}", e),
            }
        })?;
        
        let len = u32::from_be_bytes(len_buf) as usize;
        
        // 检查消息大小限制
        if len > 10 * 1024 * 1024 {  // 10MB 限制
            return Err(IpcError::InvalidMessageFormat {
                message: format!("消息大小超出限制: {} bytes", len),
            });
        }
        
        // 读取数据
        let mut data = vec![0u8; len];
        self.stream.read_exact(&mut data).await.map_err(|e| {
            self.is_alive = false;
            IpcError::NetworkError {
                error: format!("读取数据失败: {}", e),
            }
        })?;
        
        Ok(data)
    }
    
    async fn close(&mut self) -> IpcResult<()> {
        self.is_alive = false;
        self.stream.shutdown().await.map_err(|e| {
            IpcError::NetworkError {
                error: format!("关闭TCP连接失败: {}", e),
            }
        })?;
        Ok(())
    }
    
    fn connection_id(&self) -> &str {
        &self.connection_id
    }
    
    fn is_alive(&self) -> bool {
        self.is_alive
    }
    
    fn remote_addr(&self) -> Option<String> {
        Some(self.remote_addr.clone())
    }

    fn peer_pid(&self) -> Option<u32> {
        // TCP连接无法直接获取对等进程ID
        None
    }
}

/// Unix Socket 传输实现 (仅Unix系统)
#[cfg(unix)]
pub struct UnixSocketTransport {
    listener: Option<UnixListener>,
    socket_path: Option<String>,
    config: Option<TransportConfig>,
}

#[cfg(unix)]
impl UnixSocketTransport {
    /// 创建新的Unix Socket传输
    pub fn new() -> Self {
        Self {
            listener: None,
            socket_path: None,
            config: None,
        }
    }
}

#[cfg(unix)]
#[async_trait]
impl IpcTransport for UnixSocketTransport {
    async fn bind(&mut self, config: &TransportConfig) -> IpcResult<()> {
        let socket_path = &config.address;
        
        // 如果socket文件已存在，先删除
        if std::path::Path::new(socket_path).exists() {
            std::fs::remove_file(socket_path).map_err(|e| {
                IpcError::NetworkError {
                    error: format!("删除现有socket文件失败: {} - {}", socket_path, e),
                }
            })?;
        }
        
        let listener = UnixListener::bind(socket_path).map_err(|e| {
            IpcError::NetworkError {
                error: format!("绑定Unix Socket失败: {} - {}", socket_path, e),
            }
        })?;
        
        self.socket_path = Some(socket_path.to_string());
        self.listener = Some(listener);
        self.config = Some(config.clone());
        
        Ok(())
    }
    
    async fn accept(&mut self) -> IpcResult<Box<dyn IpcConnection>> {
        let listener = self.listener.as_ref().ok_or_else(|| IpcError::ConnectionError {
            error: "Unix Socket传输未绑定".to_string(),
        })?;
        
        let (stream, _) = listener.accept().await.map_err(|e| {
            IpcError::NetworkError {
                error: format!("接受Unix Socket连接失败: {}", e),
            }
        })?;
        
        let connection = UnixSocketConnection::new(stream);
        Ok(Box::new(connection))
    }
    
    async fn connect(config: &TransportConfig) -> IpcResult<Box<dyn IpcConnection>> {
        let socket_path = &config.address;
        
        let stream = tokio::time::timeout(
            Duration::from_millis(config.timeout_ms),
            UnixStream::connect(socket_path)
        ).await
        .map_err(|_| IpcError::TimeoutError {
            operation: format!("连接Unix Socket: {}", socket_path),
        })?
        .map_err(|e| IpcError::NetworkError {
            error: format!("连接Unix Socket失败: {} - {}", socket_path, e),
        })?;
        
        let connection = UnixSocketConnection::new(stream);
        Ok(Box::new(connection))
    }
    
    async fn shutdown(&mut self) -> IpcResult<()> {
        self.listener = None;
        
        // 清理socket文件
        if let Some(socket_path) = &self.socket_path {
            if std::path::Path::new(socket_path).exists() {
                let _ = std::fs::remove_file(socket_path);
            }
        }
        
        self.socket_path = None;
        self.config = None;
        Ok(())
    }
    
    fn local_addr(&self) -> Option<String> {
        self.socket_path.clone()
    }
}

/// Unix Socket 连接实现
#[cfg(unix)]
pub struct UnixSocketConnection {
    stream: UnixStream,
    connection_id: String,
    is_alive: bool,
}

#[cfg(unix)]
impl UnixSocketConnection {
    /// 创建新的Unix Socket连接
    pub fn new(stream: UnixStream) -> Self {
        Self {
            stream,
            connection_id: Uuid::new_v4().to_string(),
            is_alive: true,
        }
    }
}

#[cfg(unix)]
#[async_trait]
impl IpcConnection for UnixSocketConnection {
    async fn send(&mut self, data: &[u8]) -> IpcResult<()> {
        if !self.is_alive {
            return Err(IpcError::ConnectionError {
                error: "连接已关闭".to_string(),
            });
        }
        
        // 发送数据长度前缀 (4字节大端序)
        let len = data.len() as u32;
        self.stream.write_all(&len.to_be_bytes()).await.map_err(|e| {
            self.is_alive = false;
            IpcError::NetworkError {
                error: format!("发送数据长度失败: {}", e),
            }
        })?;
        
        // 发送数据
        self.stream.write_all(data).await.map_err(|e| {
            self.is_alive = false;
            IpcError::NetworkError {
                error: format!("发送数据失败: {}", e),
            }
        })?;
        
        self.stream.flush().await.map_err(|e| {
            self.is_alive = false;
            IpcError::NetworkError {
                error: format!("刷新数据失败: {}", e),
            }
        })?;
        
        Ok(())
    }
    
    async fn recv(&mut self) -> IpcResult<Vec<u8>> {
        if !self.is_alive {
            return Err(IpcError::ConnectionError {
                error: "连接已关闭".to_string(),
            });
        }
        
        // 读取数据长度前缀 (4字节大端序)
        let mut len_buf = [0u8; 4];
        self.stream.read_exact(&mut len_buf).await.map_err(|e| {
            self.is_alive = false;
            IpcError::NetworkError {
                error: format!("读取数据长度失败: {}", e),
            }
        })?;
        
        let len = u32::from_be_bytes(len_buf) as usize;
        
        // 检查消息大小限制
        if len > 10 * 1024 * 1024 {  // 10MB 限制
            return Err(IpcError::InvalidMessageFormat {
                message: format!("消息大小超出限制: {} bytes", len),
            });
        }
        
        // 读取数据
        let mut data = vec![0u8; len];
        self.stream.read_exact(&mut data).await.map_err(|e| {
            self.is_alive = false;
            IpcError::NetworkError {
                error: format!("读取数据失败: {}", e),
            }
        })?;
        
        Ok(data)
    }
    
    async fn close(&mut self) -> IpcResult<()> {
        self.is_alive = false;
        self.stream.shutdown().await.map_err(|e| {
            IpcError::NetworkError {
                error: format!("关闭Unix Socket连接失败: {}", e),
            }
        })?;
        Ok(())
    }
    
    fn connection_id(&self) -> &str {
        &self.connection_id
    }
    
    fn is_alive(&self) -> bool {
        self.is_alive
    }
    
    fn remote_addr(&self) -> Option<String> {
        None  // Unix Socket没有远程地址概念
    }

    fn peer_pid(&self) -> Option<u32> {
        // Unix Socket可以通过SO_PEERCRED获取对等进程ID
        #[cfg(target_os = "linux")]
        {
            use std::os::unix::io::AsRawFd;
            use std::mem;

            unsafe {
                let mut ucred: libc::ucred = mem::zeroed();
                let mut len = mem::size_of::<libc::ucred>() as libc::socklen_t;

                if libc::getsockopt(
                    self.stream.as_raw_fd(),
                    libc::SOL_SOCKET,
                    libc::SO_PEERCRED,
                    &mut ucred as *mut _ as *mut libc::c_void,
                    &mut len,
                ) == 0 {
                    return Some(ucred.pid as u32);
                }
            }
        }

        None
    }
}

/// 传输工厂
pub struct TransportFactory;

impl TransportFactory {
    /// 创建传输实例
    pub fn create_transport(transport_type: &TransportType) -> IpcResult<Box<dyn IpcTransport>> {
        match transport_type {
            TransportType::Tcp => Ok(Box::new(TcpTransport::new())),
            
            #[cfg(unix)]
            TransportType::UnixSocket => Ok(Box::new(UnixSocketTransport::new())),
            
            #[cfg(not(unix))]
            TransportType::UnixSocket => Err(IpcError::UnsupportedVersion {
                version: 0,  // 使用0表示不支持的传输类型
            }),
            
            TransportType::NamedPipe => {
                #[cfg(windows)]
                {
                    // TODO: 实现Windows Named Pipe
                    Err(IpcError::InternalError {
                        error: "Named Pipe传输尚未实现".to_string(),
                    })
                }
                #[cfg(not(windows))]
                {
                    Err(IpcError::UnsupportedVersion {
                        version: 0,  // 使用0表示不支持的传输类型
                    })
                }
            }
        }
    }
    
    /// 自动选择最佳传输类型
    pub fn auto_select_transport() -> TransportType {
        #[cfg(unix)]
        {
            TransportType::UnixSocket
        }
        #[cfg(windows)]
        {
            TransportType::Tcp  // 暂时使用TCP，后续实现Named Pipe
        }
        #[cfg(not(any(unix, windows)))]
        {
            TransportType::Tcp
        }
    }
}

impl Default for TransportConfig {
    fn default() -> Self {
        Self {
            transport_type: TransportFactory::auto_select_transport(),
            address: "127.0.0.1".to_string(),
            port: Some(8080),
            timeout_ms: 30000,  // 30秒
            max_message_size: 10 * 1024 * 1024,  // 10MB
            buffer_size: 8192,  // 8KB
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::{sleep, Duration};

    #[test]
    fn test_transport_config_default() {
        let config = TransportConfig::default();
        assert_eq!(config.address, "127.0.0.1");
        assert_eq!(config.timeout_ms, 30000);
        assert_eq!(config.max_message_size, 10 * 1024 * 1024);
    }

    #[test]
    fn test_transport_factory_auto_select() {
        let transport_type = TransportFactory::auto_select_transport();
        
        #[cfg(unix)]
        assert_eq!(transport_type, TransportType::UnixSocket);
        
        #[cfg(windows)]
        assert_eq!(transport_type, TransportType::Tcp);
    }

    #[test]
    fn test_transport_factory_create_tcp() {
        let transport = TransportFactory::create_transport(&TransportType::Tcp);
        assert!(transport.is_ok());
    }

    #[cfg(unix)]
    #[test]
    fn test_transport_factory_create_unix_socket() {
        let transport = TransportFactory::create_transport(&TransportType::UnixSocket);
        assert!(transport.is_ok());
    }

    #[cfg(not(unix))]
    #[test]
    fn test_transport_factory_create_unix_socket_unsupported() {
        let transport = TransportFactory::create_transport(&TransportType::UnixSocket);
        assert!(transport.is_err());
    }

    #[tokio::test]
    async fn test_tcp_transport_bind_and_shutdown() {
        let mut transport = TcpTransport::new();
        
        let config = TransportConfig {
            transport_type: TransportType::Tcp,
            address: "127.0.0.1".to_string(),
            port: Some(0),  // 让系统分配端口
            ..TransportConfig::default()
        };
        
        // 测试绑定
        let result = transport.bind(&config).await;
        assert!(result.is_ok());
        assert!(transport.local_addr().is_some());
        
        // 测试关闭
        let result = transport.shutdown().await;
        assert!(result.is_ok());
        assert!(transport.local_addr().is_none());
    }

    #[tokio::test]
    async fn test_tcp_connection_send_recv() {
        // 启动服务器
        let mut server_transport = TcpTransport::new();
        let config = TransportConfig {
            transport_type: TransportType::Tcp,
            address: "127.0.0.1".to_string(),
            port: Some(0),  // 让系统分配端口
            ..TransportConfig::default()
        };
        
        server_transport.bind(&config).await.unwrap();
        let server_addr = server_transport.local_addr().unwrap();
        
        // 解析服务器端口
        let port: u16 = server_addr.split(':').nth(1).unwrap().parse().unwrap();
        
        // 启动客户端连接任务
        let client_config = TransportConfig {
            transport_type: TransportType::Tcp,
            address: "127.0.0.1".to_string(),
            port: Some(port),
            ..TransportConfig::default()
        };
        
        let server_handle = tokio::spawn(async move {
            let mut connection = server_transport.accept().await.unwrap();
            
            // 接收数据
            let received_data = connection.recv().await.unwrap();
            assert_eq!(received_data, b"Hello, IPC!");
            
            // 发送响应
            connection.send(b"Hello, Client!").await.unwrap();
            
            let _ = connection.close().await; // 忽略关闭错误
        });
        
        // 等待一下让服务器启动
        sleep(Duration::from_millis(100)).await;
        
        // 连接客户端
        let mut client_connection = TcpTransport::connect(&client_config).await.unwrap();
        
        // 发送数据
        client_connection.send(b"Hello, IPC!").await.unwrap();
        
        // 接收响应
        let response = client_connection.recv().await.unwrap();
        assert_eq!(response, b"Hello, Client!");
        
        let _ = client_connection.close().await; // 忽略关闭错误
        
        // 等待服务器任务完成
        server_handle.await.unwrap();
    }

    #[cfg(unix)]
    #[tokio::test]
    async fn test_unix_socket_transport_bind_and_shutdown() {
        let mut transport = UnixSocketTransport::new();
        
        let socket_path = "/tmp/test_ipc_socket";
        let config = TransportConfig {
            transport_type: TransportType::UnixSocket,
            address: socket_path.to_string(),
            port: None,
            ..TransportConfig::default()
        };
        
        // 测试绑定
        let result = transport.bind(&config).await;
        assert!(result.is_ok());
        assert_eq!(transport.local_addr(), Some(socket_path.to_string()));
        
        // 测试关闭
        let result = transport.shutdown().await;
        assert!(result.is_ok());
        assert!(transport.local_addr().is_none());
        
        // 确认socket文件已删除
        assert!(!std::path::Path::new(socket_path).exists());
    }

    #[cfg(unix)]
    #[tokio::test]
    async fn test_unix_socket_connection_send_recv() {
        let socket_path = "/tmp/test_ipc_socket_comm";
        
        // 启动服务器
        let mut server_transport = UnixSocketTransport::new();
        let config = TransportConfig {
            transport_type: TransportType::UnixSocket,
            address: socket_path.to_string(),
            port: None,
            ..TransportConfig::default()
        };
        
        server_transport.bind(&config).await.unwrap();
        
        let server_handle = tokio::spawn(async move {
            let mut connection = server_transport.accept().await.unwrap();
            
            // 接收数据
            let received_data = connection.recv().await.unwrap();
            assert_eq!(received_data, b"Unix Socket Test");
            
            // 发送响应
            connection.send(b"Unix Socket Response").await.unwrap();
            
            let _ = connection.close().await; // 忽略关闭错误
            server_transport.shutdown().await.unwrap();
        });
        
        // 等待一下让服务器启动
        sleep(Duration::from_millis(100)).await;
        
        // 连接客户端
        let mut client_connection = UnixSocketTransport::connect(&config).await.unwrap();
        
        // 发送数据
        client_connection.send(b"Unix Socket Test").await.unwrap();
        
        // 接收响应
        let response = client_connection.recv().await.unwrap();
        assert_eq!(response, b"Unix Socket Response");
        
        let _ = client_connection.close().await; // 忽略关闭错误
        
        // 等待服务器任务完成
        server_handle.await.unwrap();
    }

    #[tokio::test]
    async fn test_connection_id_uniqueness() {
        let stream1 = TcpStream::connect("127.0.0.1:22").await;
        let stream2 = TcpStream::connect("127.0.0.1:22").await;
        
        // 即使连接失败，我们也可以测试连接ID的唯一性
        if let (Ok(s1), Ok(s2)) = (stream1, stream2) {
            let conn1 = TcpConnection::new(s1, "test1".to_string());
            let conn2 = TcpConnection::new(s2, "test2".to_string());
            
            assert_ne!(conn1.connection_id(), conn2.connection_id());
        }
    }

    #[tokio::test]
    async fn test_message_size_limit() {
        // 简化测试：只测试正常消息大小限制检查
        let mut server_transport = TcpTransport::new();
        let config = TransportConfig {
            transport_type: TransportType::Tcp,
            address: "127.0.0.1".to_string(),
            port: Some(0),
            ..TransportConfig::default()
        };
        
        server_transport.bind(&config).await.unwrap();
        let server_addr = server_transport.local_addr().unwrap();
        let port: u16 = server_addr.split(':').nth(1).unwrap().parse().unwrap();
        
        let client_config = TransportConfig {
            transport_type: TransportType::Tcp,
            address: "127.0.0.1".to_string(),
            port: Some(port),
            ..TransportConfig::default()
        };
        
        let server_handle = tokio::spawn(async move {
            let mut connection = server_transport.accept().await.unwrap();
            
            // 接收正常大小的消息
            let received_data = connection.recv().await.unwrap();
            assert_eq!(received_data, b"Normal message");
            
            let _ = connection.close().await;
        });
        
        sleep(Duration::from_millis(100)).await;
        
        let mut client_connection = TcpTransport::connect(&client_config).await.unwrap();
        
        // 发送正常大小的消息
        client_connection.send(b"Normal message").await.unwrap();
        
        let _ = client_connection.close().await;
        server_handle.await.unwrap();
    }
} 