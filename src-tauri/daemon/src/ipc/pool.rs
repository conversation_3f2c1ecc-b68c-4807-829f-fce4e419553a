//! IPC 连接池模块
//! 
//! 提供高效的连接池管理和负载均衡功能

use crate::ipc::{IpcError, IpcResult, IpcConnection, IpcMessage, IpcResponse};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{RwLock, Mutex};
use tokio::time::{Duration, Instant};
use uuid::Uuid;

/// 连接池配置
#[derive(Debug, Clone)]
pub struct PoolConfig {
    /// 最小连接数
    pub min_connections: usize,
    /// 最大连接数
    pub max_connections: usize,
    /// 连接超时时间 (毫秒)
    pub connection_timeout_ms: u64,
    /// 空闲连接超时时间 (毫秒)
    pub idle_timeout_ms: u64,
    /// 连接健康检查间隔 (毫秒)
    pub health_check_interval_ms: u64,
    /// 负载均衡策略
    pub load_balance_strategy: LoadBalanceStrategy,
    /// 是否启用连接回收
    pub enable_connection_recycling: bool,
    /// 最大重试次数
    pub max_retry_attempts: u32,
}

/// 负载均衡策略
#[derive(Debug, Clone, PartialEq)]
pub enum LoadBalanceStrategy {
    /// 轮询
    RoundRobin,
    /// 最少连接
    LeastConnections,
    /// 随机
    Random,
    /// 加权轮询
    WeightedRoundRobin,
}

/// 连接状态
#[derive(Debug, Clone, PartialEq)]
pub enum ConnectionStatus {
    /// 活跃状态
    Active,
    /// 空闲状态
    Idle,
    /// 不健康状态
    Unhealthy,
    /// 已关闭
    Closed,
}

/// 连接池中的连接信息
#[derive(Debug, Clone)]
pub struct PooledConnection {
    /// 连接ID
    pub id: String,
    /// 连接创建时间
    pub created_at: Instant,
    /// 最后使用时间
    pub last_used: Instant,
    /// 连接状态
    pub status: ConnectionStatus,
    /// 使用次数
    pub usage_count: u64,
    /// 权重 (用于加权负载均衡)
    pub weight: u32,
    /// 当前活跃请求数
    pub active_requests: u32,
}

/// 连接池统计信息
#[derive(Debug, Clone)]
pub struct PoolStats {
    /// 总连接数
    pub total_connections: usize,
    /// 活跃连接数
    pub active_connections: usize,
    /// 空闲连接数
    pub idle_connections: usize,
    /// 不健康连接数
    pub unhealthy_connections: usize,
    /// 总请求数
    pub total_requests: u64,
    /// 成功请求数
    pub successful_requests: u64,
    /// 失败请求数
    pub failed_requests: u64,
    /// 平均响应时间 (毫秒)
    pub avg_response_time_ms: f64,
    /// 连接池创建时间
    pub created_at: Instant,
}

/// 连接池
pub struct ConnectionPool {
    config: PoolConfig,
    connections: Arc<RwLock<HashMap<String, PooledConnection>>>,
    // 实际连接对象的存储 (简化实现)
    connection_objects: Arc<RwLock<HashMap<String, Arc<Mutex<Box<dyn IpcConnection>>>>>>,
    stats: Arc<RwLock<PoolStats>>,
    load_balancer: Arc<Mutex<LoadBalancer>>,
}

/// 负载均衡器
struct LoadBalancer {
    strategy: LoadBalanceStrategy,
    round_robin_index: usize,
    connection_weights: HashMap<String, u32>,
}

impl ConnectionPool {
    /// 创建新的连接池
    pub fn new(config: PoolConfig) -> Self {
        let load_balancer = LoadBalancer {
            strategy: config.load_balance_strategy.clone(),
            round_robin_index: 0,
            connection_weights: HashMap::new(),
        };

        Self {
            config,
            connections: Arc::new(RwLock::new(HashMap::new())),
            connection_objects: Arc::new(RwLock::new(HashMap::new())),
            stats: Arc::new(RwLock::new(PoolStats {
                total_connections: 0,
                active_connections: 0,
                idle_connections: 0,
                unhealthy_connections: 0,
                total_requests: 0,
                successful_requests: 0,
                failed_requests: 0,
                avg_response_time_ms: 0.0,
                created_at: Instant::now(),
            })),
            load_balancer: Arc::new(Mutex::new(load_balancer)),
        }
    }

    /// 获取连接
    pub async fn get_connection(&self) -> IpcResult<String> {
        let mut stats = self.stats.write().await;
        stats.total_requests += 1;
        drop(stats);

        // 选择连接
        let connection_id = self.select_connection().await?;
        
        // 更新连接状态
        let mut connections = self.connections.write().await;
        if let Some(conn) = connections.get_mut(&connection_id) {
            conn.status = ConnectionStatus::Active;
            conn.last_used = Instant::now();
            conn.usage_count += 1;
            conn.active_requests += 1;
        }
        
        Ok(connection_id)
    }

    /// 归还连接
    pub async fn return_connection(&self, connection_id: &str) -> IpcResult<()> {
        let mut connections = self.connections.write().await;
        if let Some(conn) = connections.get_mut(connection_id) {
            conn.status = ConnectionStatus::Idle;
            conn.active_requests = conn.active_requests.saturating_sub(1);
        }
        
        Ok(())
    }

    /// 发送请求 (通过连接池)
    pub async fn send_request(&self, request: IpcMessage) -> IpcResult<IpcResponse> {
        let start_time = Instant::now();
        let mut retry_count = 0;
        
        while retry_count < self.config.max_retry_attempts {
            match self.try_send_request(&request).await {
                Ok(response) => {
                    // 更新统计信息
                    let elapsed = start_time.elapsed().as_millis() as f64;
                    let mut stats = self.stats.write().await;
                    stats.successful_requests += 1;
                    stats.avg_response_time_ms = 
                        (stats.avg_response_time_ms * (stats.successful_requests - 1) as f64 + elapsed) 
                        / stats.successful_requests as f64;
                    
                    return Ok(response);
                }
                Err(e) => {
                    retry_count += 1;
                    if retry_count >= self.config.max_retry_attempts {
                        let mut stats = self.stats.write().await;
                        stats.failed_requests += 1;
                        return Err(e);
                    }
                    
                    // 等待一段时间后重试
                    tokio::time::sleep(Duration::from_millis(100 * retry_count as u64)).await;
                }
            }
        }
        
        Err(IpcError::InternalError {
            error: "超过最大重试次数".to_string(),
        })
    }

    /// 尝试发送请求
    async fn try_send_request(&self, request: &IpcMessage) -> IpcResult<IpcResponse> {
        let connection_id = self.get_connection().await?;
        
        // 模拟请求处理 (简化实现)
        tokio::time::sleep(Duration::from_millis(10)).await;
        
        // 归还连接
        self.return_connection(&connection_id).await?;
        
        // 返回模拟响应
        Ok(IpcResponse::success(
            request.message_id.clone(),
            serde_json::json!({"status": "success", "connection_id": connection_id}),
        ))
    }

    /// 添加连接到池中
    pub async fn add_connection(&self, connection: Box<dyn IpcConnection>) -> IpcResult<String> {
        let connection_id = Uuid::new_v4().to_string();
        
        let pooled_conn = PooledConnection {
            id: connection_id.clone(),
            created_at: Instant::now(),
            last_used: Instant::now(),
            status: ConnectionStatus::Idle,
            usage_count: 0,
            weight: 1,
            active_requests: 0,
        };
        
        // 添加到连接池
        self.connections.write().await.insert(connection_id.clone(), pooled_conn);
        self.connection_objects.write().await.insert(connection_id.clone(), Arc::new(Mutex::new(connection)));
        
        // 更新统计信息
        let mut stats = self.stats.write().await;
        stats.total_connections += 1;
        stats.idle_connections += 1;
        
        Ok(connection_id)
    }

    /// 移除连接
    pub async fn remove_connection(&self, connection_id: &str) -> IpcResult<()> {
        let mut connections = self.connections.write().await;
        let mut connection_objects = self.connection_objects.write().await;
        
        if let Some(conn) = connections.remove(connection_id) {
            // 关闭连接
            if let Some(conn_obj) = connection_objects.remove(connection_id) {
                if let Ok(mut conn_obj) = conn_obj.try_lock() {
                    let _ = conn_obj.close().await;
                }
            }
            
            // 更新统计信息 (使用saturating_sub防止溢出)
            let mut stats = self.stats.write().await;
            stats.total_connections = stats.total_connections.saturating_sub(1);
            match conn.status {
                ConnectionStatus::Active => stats.active_connections = stats.active_connections.saturating_sub(1),
                ConnectionStatus::Idle => stats.idle_connections = stats.idle_connections.saturating_sub(1),
                ConnectionStatus::Unhealthy => stats.unhealthy_connections = stats.unhealthy_connections.saturating_sub(1),
                _ => {}
            }
        }
        
        Ok(())
    }

    /// 选择连接 (负载均衡)
    async fn select_connection(&self) -> IpcResult<String> {
        let connections = self.connections.read().await;
        
        if connections.is_empty() {
            return Err(IpcError::ConnectionError {
                error: "连接池中没有可用连接".to_string(),
            });
        }
        
        // 过滤出可用连接
        let available_connections: Vec<_> = connections
            .values()
            .filter(|conn| conn.status == ConnectionStatus::Idle || conn.status == ConnectionStatus::Active)
            .collect();
        
        if available_connections.is_empty() {
            return Err(IpcError::ConnectionError {
                error: "没有可用的健康连接".to_string(),
            });
        }
        
        // 根据负载均衡策略选择连接
        let mut balancer = self.load_balancer.lock().await;
        let selected_conn = match balancer.strategy {
            LoadBalanceStrategy::RoundRobin => {
                let index = balancer.round_robin_index % available_connections.len();
                balancer.round_robin_index = (balancer.round_robin_index + 1) % available_connections.len();
                available_connections[index]
            }
            LoadBalanceStrategy::LeastConnections => {
                available_connections
                    .iter()
                    .min_by_key(|conn| conn.active_requests)
                    .unwrap()
            }
            LoadBalanceStrategy::Random => {
                use rand::Rng;
                let index = rand::thread_rng().gen_range(0..available_connections.len());
                available_connections[index]
            }
            LoadBalanceStrategy::WeightedRoundRobin => {
                // 简化的加权轮询实现
                available_connections
                    .iter()
                    .max_by_key(|conn| conn.weight)
                    .unwrap()
            }
        };
        
        Ok(selected_conn.id.clone())
    }

    /// 健康检查
    pub async fn health_check(&self) -> IpcResult<()> {
        let mut unhealthy_connections = Vec::new();
        
        {
            let mut connections = self.connections.write().await;
            let now = Instant::now();
            
            for (id, conn) in connections.iter_mut() {
                // 检查空闲超时
                if conn.status == ConnectionStatus::Idle 
                    && now.duration_since(conn.last_used).as_millis() > self.config.idle_timeout_ms as u128 {
                    conn.status = ConnectionStatus::Unhealthy;
                    unhealthy_connections.push(id.clone());
                }
            }
        }
        
        // 移除不健康的连接
        for conn_id in unhealthy_connections {
            let _ = self.remove_connection(&conn_id).await;
        }
        
        Ok(())
    }

    /// 启动后台任务
    pub async fn start_background_tasks(&self) -> IpcResult<()> {
        // 启动健康检查任务
        let pool = self.clone();
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(
                Duration::from_millis(pool.config.health_check_interval_ms)
            );
            
            loop {
                interval.tick().await;
                if let Err(e) = pool.health_check().await {
                    eprintln!("健康检查失败: {}", e);
                }
            }
        });
        
        Ok(())
    }

    /// 获取统计信息
    pub async fn get_stats(&self) -> PoolStats {
        let stats = self.stats.read().await;
        stats.clone()
    }

    /// 获取连接数
    pub async fn connection_count(&self) -> usize {
        self.connections.read().await.len()
    }

    /// 关闭连接池
    pub async fn shutdown(&self) -> IpcResult<()> {
        let connection_ids: Vec<String> = {
            let connections = self.connections.read().await;
            connections.keys().cloned().collect()
        };
        
        for conn_id in connection_ids {
            let _ = self.remove_connection(&conn_id).await;
        }
        
        Ok(())
    }
}

// 为了支持clone，我们需要实现Clone trait
impl Clone for ConnectionPool {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            connections: Arc::clone(&self.connections),
            connection_objects: Arc::clone(&self.connection_objects),
            stats: Arc::clone(&self.stats),
            load_balancer: Arc::clone(&self.load_balancer),
        }
    }
}

impl Default for PoolConfig {
    fn default() -> Self {
        Self {
            min_connections: 1,
            max_connections: 10,
            connection_timeout_ms: 5000,
            idle_timeout_ms: 30000,
            health_check_interval_ms: 10000,
            load_balance_strategy: LoadBalanceStrategy::RoundRobin,
            enable_connection_recycling: true,
            max_retry_attempts: 3,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::ipc::transport::{TcpTransport, TransportConfig, IpcTransport};
    use async_trait::async_trait;

    // 模拟连接实现，用于测试
    struct MockConnection {
        id: String,
    }

    impl MockConnection {
        fn new() -> Self {
            Self {
                id: uuid::Uuid::new_v4().to_string(),
            }
        }
    }

    #[async_trait]
    impl IpcConnection for MockConnection {
        async fn send(&mut self, _data: &[u8]) -> IpcResult<()> {
            Ok(())
        }

        async fn recv(&mut self) -> IpcResult<Vec<u8>> {
            Ok(b"mock response".to_vec())
        }

        async fn close(&mut self) -> IpcResult<()> {
            Ok(())
        }

        fn connection_id(&self) -> &str {
            &self.id
        }

        fn is_alive(&self) -> bool {
            true
        }

        fn remote_addr(&self) -> Option<String> {
            Some("127.0.0.1:8080".to_string())
        }

        fn peer_pid(&self) -> Option<u32> {
            Some(12345) // 模拟PID
        }
    }

    #[tokio::test]
    async fn test_pool_config_default() {
        let config = PoolConfig::default();
        
        assert_eq!(config.min_connections, 1);
        assert_eq!(config.max_connections, 10);
        assert_eq!(config.connection_timeout_ms, 5000);
        assert_eq!(config.idle_timeout_ms, 30000);
        assert_eq!(config.health_check_interval_ms, 10000);
        assert_eq!(config.load_balance_strategy, LoadBalanceStrategy::RoundRobin);
        assert!(config.enable_connection_recycling);
        assert_eq!(config.max_retry_attempts, 3);
    }

    #[tokio::test]
    async fn test_connection_pool_creation() {
        let config = PoolConfig::default();
        let pool = ConnectionPool::new(config);
        
        assert_eq!(pool.connection_count().await, 0);
        
        let stats = pool.get_stats().await;
        assert_eq!(stats.total_connections, 0);
        assert_eq!(stats.active_connections, 0);
        assert_eq!(stats.idle_connections, 0);
    }

    #[tokio::test]
    async fn test_load_balance_strategies() {
        let strategies = vec![
            LoadBalanceStrategy::RoundRobin,
            LoadBalanceStrategy::LeastConnections,
            LoadBalanceStrategy::Random,
            LoadBalanceStrategy::WeightedRoundRobin,
        ];
        
        for strategy in strategies {
            let mut config = PoolConfig::default();
            config.load_balance_strategy = strategy.clone();
            
            let pool = ConnectionPool::new(config);
            assert_eq!(pool.config.load_balance_strategy, strategy);
        }
    }

    #[tokio::test]
    async fn test_connection_lifecycle() {
        let config = PoolConfig::default();
        let pool = ConnectionPool::new(config);
        
        // 创建模拟连接 (不实际连接网络)
        let connection = Box::new(MockConnection::new()) as Box<dyn IpcConnection>;
        
        // 添加连接
        let conn_id = pool.add_connection(connection).await.unwrap();
        assert_eq!(pool.connection_count().await, 1);
        
        // 获取连接
        let retrieved_id = pool.get_connection().await.unwrap();
        assert_eq!(retrieved_id, conn_id);
        
        // 归还连接
        pool.return_connection(&conn_id).await.unwrap();
        
        // 移除连接
        pool.remove_connection(&conn_id).await.unwrap();
        assert_eq!(pool.connection_count().await, 0);
    }

    #[tokio::test]
    async fn test_pool_stats() {
        let config = PoolConfig::default();
        let pool = ConnectionPool::new(config);
        
        let stats = pool.get_stats().await;
        assert_eq!(stats.total_connections, 0);
        assert_eq!(stats.total_requests, 0);
        assert_eq!(stats.successful_requests, 0);
        assert_eq!(stats.failed_requests, 0);
        assert_eq!(stats.avg_response_time_ms, 0.0);
    }

    #[tokio::test]
    async fn test_connection_status() {
        let statuses = vec![
            ConnectionStatus::Active,
            ConnectionStatus::Idle,
            ConnectionStatus::Unhealthy,
            ConnectionStatus::Closed,
        ];
        
        for status in statuses {
            let conn = PooledConnection {
                id: "test".to_string(),
                created_at: Instant::now(),
                last_used: Instant::now(),
                status: status.clone(),
                usage_count: 0,
                weight: 1,
                active_requests: 0,
            };
            
            assert_eq!(conn.status, status);
        }
    }

    #[tokio::test]
    async fn test_pool_shutdown() {
        let config = PoolConfig::default();
        let pool = ConnectionPool::new(config);
        
        // 添加一些连接
        for _ in 0..3 {
            let connection = Box::new(MockConnection::new()) as Box<dyn IpcConnection>;
            pool.add_connection(connection).await.unwrap();
        }
        
        assert_eq!(pool.connection_count().await, 3);
        
        // 关闭连接池
        pool.shutdown().await.unwrap();
        assert_eq!(pool.connection_count().await, 0);
    }

    #[tokio::test]
    async fn test_send_request() {
        let config = PoolConfig::default();
        let pool = ConnectionPool::new(config);
        
        // 添加连接
        let connection = Box::new(MockConnection::new()) as Box<dyn IpcConnection>;
        pool.add_connection(connection).await.unwrap();
        
        // 发送请求
        let request = IpcMessage::ping("test-client".to_string());
        let response = pool.send_request(request).await.unwrap();
        
        assert!(response.is_success());
        
        // 检查统计信息
        let stats = pool.get_stats().await;
        assert_eq!(stats.total_requests, 1);
        assert_eq!(stats.successful_requests, 1);
    }

    #[tokio::test]
    async fn test_health_check() {
        let mut config = PoolConfig::default();
        config.idle_timeout_ms = 100; // 100ms超时
        
        let pool = ConnectionPool::new(config);
        
        // 添加连接
        let connection = Box::new(MockConnection::new()) as Box<dyn IpcConnection>;
        let conn_id = pool.add_connection(connection).await.unwrap();
        
        // 等待超时
        tokio::time::sleep(Duration::from_millis(200)).await;
        
        // 执行健康检查
        pool.health_check().await.unwrap();
        
        // 验证连接被移除
        assert_eq!(pool.connection_count().await, 0);
    }

    #[tokio::test]
    async fn test_round_robin_load_balancing() {
        let config = PoolConfig::default();
        let pool = ConnectionPool::new(config);
        
        // 添加多个连接
        let mut conn_ids = Vec::new();
        for _ in 0..3 {
            let connection = Box::new(MockConnection::new()) as Box<dyn IpcConnection>;
            let conn_id = pool.add_connection(connection).await.unwrap();
            conn_ids.push(conn_id);
        }
        
        // 测试轮询选择
        let mut selected_connections = Vec::new();
        for _ in 0..6 {
            let conn_id = pool.get_connection().await.unwrap();
            selected_connections.push(conn_id.clone());
            pool.return_connection(&conn_id).await.unwrap();
        }
        
        // 验证轮询行为 (应该重复模式)
        assert_eq!(selected_connections.len(), 6);
        assert_eq!(selected_connections[0], selected_connections[3]);
        assert_eq!(selected_connections[1], selected_connections[4]);
        assert_eq!(selected_connections[2], selected_connections[5]);
    }
} 