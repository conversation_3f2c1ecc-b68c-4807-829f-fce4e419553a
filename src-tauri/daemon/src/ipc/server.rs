//! IPC 服务器模块
//! 
//! 提供高性能的IPC服务器实现，支持：
//! - 多客户端连接管理
//! - 异步消息处理
//! - 事件驱动架构
//! - 优雅关闭

use crate::ipc::{
    IpcError, IpcResult, IpcMessage, IpcResponse, IpcMessageType,
    IpcTransport, IpcConnection, TransportConfig, TransportFactory
};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{RwLock, mpsc, oneshot};
use tokio::time::{Duration, timeout};
use async_trait::async_trait;
use uuid::Uuid;
use tracing::{info, warn, error, debug};

/// 服务器配置
#[derive(Debug, Clone)]
pub struct ServerConfig {
    /// 绑定地址
    pub bind_address: String,
    /// 绑定端口 (TCP模式)
    pub port: Option<u16>,
    /// 最大连接数
    pub max_connections: u32,
    /// 连接超时 (毫秒)
    pub connection_timeout_ms: u64,
    /// 消息处理超时 (毫秒)
    pub message_timeout_ms: u64,
    /// 心跳间隔 (毫秒)
    pub heartbeat_interval_ms: u64,
    /// 传输配置
    pub transport_config: TransportConfig,
}

/// 连接信息
#[derive(Debug, Clone)]
pub struct ConnectionInfo {
    /// 连接ID
    pub connection_id: String,
    /// 远程地址
    pub remote_addr: Option<String>,
    /// 连接时间
    pub connected_at: std::time::SystemTime,
    /// 最后活跃时间
    pub last_activity: std::time::SystemTime,
    /// 消息统计
    pub message_count: u64,
}

/// 服务器事件
#[derive(Debug, Clone)]
pub enum ServerEvent {
    /// 客户端连接
    ClientConnected {
        connection_id: String,
        remote_addr: Option<String>,
    },
    /// 客户端断开
    ClientDisconnected {
        connection_id: String,
        reason: String,
    },
    /// 消息接收
    MessageReceived {
        connection_id: String,
        message: IpcMessage,
    },
    /// 消息发送
    MessageSent {
        connection_id: String,
        message: IpcResponse,
    },
    /// 服务器错误
    ServerError {
        error: String,
    },
}

/// 消息处理器trait
#[async_trait]
pub trait MessageHandler: Send + Sync {
    /// 处理消息
    async fn handle_message(
        &self,
        connection_id: &str,
        message: IpcMessage,
    ) -> IpcResult<Option<IpcResponse>>;
    
    /// 处理连接事件
    async fn handle_connection_event(&self, event: ServerEvent) -> IpcResult<()>;
}

/// 默认消息处理器
pub struct DefaultMessageHandler;

#[async_trait]
impl MessageHandler for DefaultMessageHandler {
    async fn handle_message(
        &self,
        connection_id: &str,
        message: IpcMessage,
    ) -> IpcResult<Option<IpcResponse>> {
        debug!("处理来自连接 {} 的消息: {:?}", connection_id, message.message_type);
        
        match message.message_type {
            IpcMessageType::Ping => {
                // 响应ping消息
                Ok(Some(IpcResponse::success(
                    message.message_id,
                    serde_json::json!({"pong": true}),
                )))
            }
            IpcMessageType::BrowserRequest => {
                // 默认请求处理
                Ok(Some(IpcResponse::success(
                    message.message_id,
                    serde_json::json!({"status": "received"}),
                )))
            }
            _ => {
                // 其他消息类型不需要响应
                Ok(None)
            }
        }
    }
    
    async fn handle_connection_event(&self, event: ServerEvent) -> IpcResult<()> {
        match event {
            ServerEvent::ClientConnected { connection_id, remote_addr } => {
                info!("客户端连接: {} from {:?}", connection_id, remote_addr);
            }
            ServerEvent::ClientDisconnected { connection_id, reason } => {
                info!("客户端断开: {} - {}", connection_id, reason);
            }
            ServerEvent::ServerError { error } => {
                error!("服务器错误: {}", error);
            }
            _ => {}
        }
        Ok(())
    }
}

/// IPC 服务器
pub struct IpcServer {
    config: ServerConfig,
    transport: Option<Box<dyn IpcTransport>>,
    connections: Arc<RwLock<HashMap<String, ConnectionInfo>>>,
    message_handler: Arc<dyn MessageHandler>,
    event_sender: Option<mpsc::UnboundedSender<ServerEvent>>,
    shutdown_sender: Option<oneshot::Sender<()>>,
    is_running: Arc<RwLock<bool>>,
}

impl IpcServer {
    /// 创建新的IPC服务器
    pub fn new(config: ServerConfig) -> Self {
        Self {
            config,
            transport: None,
            connections: Arc::new(RwLock::new(HashMap::new())),
            message_handler: Arc::new(DefaultMessageHandler),
            event_sender: None,
            shutdown_sender: None,
            is_running: Arc::new(RwLock::new(false)),
        }
    }
    
    /// 设置消息处理器
    pub fn with_message_handler(mut self, handler: Arc<dyn MessageHandler>) -> Self {
        self.message_handler = handler;
        self
    }
    
    /// 启动服务器
    pub async fn start(&mut self) -> IpcResult<()> {
        if *self.is_running.read().await {
            return Err(IpcError::InternalError {
                error: "服务器已经在运行".to_string(),
            });
        }
        
        // 创建传输层
        let mut transport = TransportFactory::create_transport(&self.config.transport_config.transport_type)?;
        transport.bind(&self.config.transport_config).await?;
        
        self.transport = Some(transport);
        
        // 创建事件通道
        let (event_sender, mut event_receiver) = mpsc::unbounded_channel();
        self.event_sender = Some(event_sender);
        
        // 创建关闭通道
        let (shutdown_sender, shutdown_receiver) = oneshot::channel();
        self.shutdown_sender = Some(shutdown_sender);
        
        // 标记为运行状态
        *self.is_running.write().await = true;
        
        info!("IPC服务器启动，监听地址: {:?}", 
              self.transport.as_ref().unwrap().local_addr());
        
        // 启动事件处理任务
        let message_handler = self.message_handler.clone();
        let event_handle = tokio::spawn(async move {
            while let Some(event) = event_receiver.recv().await {
                if let Err(e) = message_handler.handle_connection_event(event).await {
                    error!("处理事件失败: {}", e);
                }
            }
        });
        
        // 启动主循环
        let transport = self.transport.take().unwrap();
        let connections = self.connections.clone();
        let message_handler = self.message_handler.clone();
        let event_sender = self.event_sender.as_ref().unwrap().clone();
        let is_running = self.is_running.clone();
        let config = self.config.clone();
        
        let server_handle = tokio::spawn(async move {
            Self::run_server_loop(
                transport,
                connections,
                message_handler,
                event_sender,
                is_running,
                config,
                shutdown_receiver,
            ).await
        });
        
        // 等待服务器任务完成
        tokio::select! {
            result = server_handle => {
                match result {
                    Ok(Ok(_)) => info!("服务器正常关闭"),
                    Ok(Err(e)) => error!("服务器运行错误: {}", e),
                    Err(e) => error!("服务器任务错误: {}", e),
                }
            }
        }
        
        // 等待事件处理完成
        event_handle.abort();
        
        *self.is_running.write().await = false;
        Ok(())
    }
    
    /// 关闭服务器
    pub async fn shutdown(&mut self) -> IpcResult<()> {
        if !*self.is_running.read().await {
            return Ok(());
        }
        
        info!("正在关闭IPC服务器...");
        
        // 发送关闭信号
        if let Some(sender) = self.shutdown_sender.take() {
            let _ = sender.send(());
        }
        
        // 等待服务器停止
        while *self.is_running.read().await {
            tokio::time::sleep(Duration::from_millis(10)).await;
        }
        
        info!("IPC服务器已关闭");
        Ok(())
    }
    
    /// 获取连接信息
    pub async fn get_connections(&self) -> Vec<ConnectionInfo> {
        self.connections.read().await.values().cloned().collect()
    }
    
    /// 获取连接数量
    pub async fn connection_count(&self) -> usize {
        self.connections.read().await.len()
    }
    
    /// 检查服务器是否运行
    pub async fn is_running(&self) -> bool {
        *self.is_running.read().await
    }
    
    /// 服务器主循环
    async fn run_server_loop(
        mut transport: Box<dyn IpcTransport>,
        connections: Arc<RwLock<HashMap<String, ConnectionInfo>>>,
        message_handler: Arc<dyn MessageHandler>,
        event_sender: mpsc::UnboundedSender<ServerEvent>,
        is_running: Arc<RwLock<bool>>,
        config: ServerConfig,
        mut shutdown_receiver: oneshot::Receiver<()>,
    ) -> IpcResult<()> {
        loop {
            tokio::select! {
                // 处理新连接
                connection_result = transport.accept() => {
                    match connection_result {
                        Ok(connection) => {
                            let connection_id = connection.connection_id().to_string();
                            let remote_addr = connection.remote_addr();
                            
                            // 检查连接数限制
                            if connections.read().await.len() >= config.max_connections as usize {
                                warn!("连接数达到上限，拒绝新连接: {}", connection_id);
                                continue;
                            }
                            
                            // 添加连接信息
                            let now = std::time::SystemTime::now();
                            let conn_info = ConnectionInfo {
                                connection_id: connection_id.clone(),
                                remote_addr: remote_addr.clone(),
                                connected_at: now,
                                last_activity: now,
                                message_count: 0,
                            };
                            
                            connections.write().await.insert(connection_id.clone(), conn_info);
                            
                            // 发送连接事件
                            let _ = event_sender.send(ServerEvent::ClientConnected {
                                connection_id: connection_id.clone(),
                                remote_addr,
                            });
                            
                            // 启动连接处理任务
                            let connections_clone = connections.clone();
                            let message_handler_clone = message_handler.clone();
                            let event_sender_clone = event_sender.clone();
                            let config_clone = config.clone();
                            
                            tokio::spawn(async move {
                                if let Err(e) = Self::handle_connection(
                                    connection,
                                    connections_clone,
                                    message_handler_clone,
                                    event_sender_clone,
                                    config_clone,
                                ).await {
                                    error!("处理连接失败 {}: {}", connection_id, e);
                                }
                            });
                        }
                        Err(e) => {
                            error!("接受连接失败: {}", e);
                            let _ = event_sender.send(ServerEvent::ServerError {
                                error: format!("接受连接失败: {}", e),
                            });
                        }
                    }
                }
                
                // 处理关闭信号
                _ = &mut shutdown_receiver => {
                    info!("收到关闭信号，正在停止服务器...");
                    break;
                }
            }
        }
        
        // 关闭传输层
        transport.shutdown().await?;
        
        // 标记为停止状态
        *is_running.write().await = false;
        
        Ok(())
    }
    
    /// 处理单个连接
    async fn handle_connection(
        mut connection: Box<dyn IpcConnection>,
        connections: Arc<RwLock<HashMap<String, ConnectionInfo>>>,
        message_handler: Arc<dyn MessageHandler>,
        event_sender: mpsc::UnboundedSender<ServerEvent>,
        config: ServerConfig,
    ) -> IpcResult<()> {
        let connection_id = connection.connection_id().to_string();
        
        loop {
            // 设置接收超时
            let recv_result = timeout(
                Duration::from_millis(config.connection_timeout_ms),
                connection.recv()
            ).await;
            
            match recv_result {
                Ok(Ok(data)) => {
                    // 更新连接活跃时间
                    if let Some(conn_info) = connections.write().await.get_mut(&connection_id) {
                        conn_info.last_activity = std::time::SystemTime::now();
                        conn_info.message_count += 1;
                    }
                    
                    // 解析消息
                    match serde_json::from_slice::<IpcMessage>(&data) {
                        Ok(message) => {
                            // 发送消息接收事件
                            let _ = event_sender.send(ServerEvent::MessageReceived {
                                connection_id: connection_id.clone(),
                                message: message.clone(),
                            });
                            
                            // 处理消息
                            let response_result = timeout(
                                Duration::from_millis(config.message_timeout_ms),
                                message_handler.handle_message(&connection_id, message)
                            ).await;
                            
                            match response_result {
                                Ok(Ok(Some(response))) => {
                                    // 发送响应
                                    let response_data = serde_json::to_vec(&response).map_err(|e| {
                                        IpcError::SerializationError {
                                            error: format!("序列化响应失败: {}", e),
                                        }
                                    })?;
                                    
                                    if let Err(e) = connection.send(&response_data).await {
                                        error!("发送响应失败: {}", e);
                                        break;
                                    }
                                    
                                    // 发送消息发送事件
                                    let _ = event_sender.send(ServerEvent::MessageSent {
                                        connection_id: connection_id.clone(),
                                        message: response,
                                    });
                                }
                                Ok(Ok(None)) => {
                                    // 不需要响应
                                }
                                Ok(Err(e)) => {
                                    error!("处理消息失败: {}", e);
                                    // 可以选择发送错误响应
                                }
                                Err(_) => {
                                    error!("消息处理超时");
                                    // 可以选择发送超时响应
                                }
                            }
                        }
                        Err(e) => {
                            error!("解析消息失败: {}", e);
                            // 可以选择发送错误响应
                        }
                    }
                }
                Ok(Err(e)) => {
                    debug!("连接接收失败: {}", e);
                    break;
                }
                Err(_) => {
                    debug!("连接接收超时: {}", connection_id);
                    // 检查连接是否仍然活跃
                    if !connection.is_alive() {
                        break;
                    }
                }
            }
        }
        
        // 清理连接
        connections.write().await.remove(&connection_id);
        
        // 关闭连接
        let _ = connection.close().await;
        
        // 发送断开事件
        let _ = event_sender.send(ServerEvent::ClientDisconnected {
            connection_id,
            reason: "连接关闭".to_string(),
        });
        
        Ok(())
    }
}

impl Default for ServerConfig {
    fn default() -> Self {
        Self {
            bind_address: "127.0.0.1".to_string(),
            port: Some(8080),
            max_connections: 100,
            connection_timeout_ms: 30000,  // 30秒
            message_timeout_ms: 5000,      // 5秒
            heartbeat_interval_ms: 30000,  // 30秒
            transport_config: TransportConfig::default(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::{sleep, Duration};
    use crate::ipc::transport::TransportType;

    #[tokio::test]
    async fn test_server_creation() {
        let config = ServerConfig::default();
        let server = IpcServer::new(config);
        
        assert!(!server.is_running().await);
        assert_eq!(server.connection_count().await, 0);
    }

    #[tokio::test]
    async fn test_server_config_default() {
        let config = ServerConfig::default();
        
        assert_eq!(config.bind_address, "127.0.0.1");
        assert_eq!(config.port, Some(8080));
        assert_eq!(config.max_connections, 100);
        assert_eq!(config.connection_timeout_ms, 30000);
        assert_eq!(config.message_timeout_ms, 5000);
    }

    #[tokio::test]
    async fn test_connection_info() {
        let now = std::time::SystemTime::now();
        let conn_info = ConnectionInfo {
            connection_id: "test-conn".to_string(),
            remote_addr: Some("127.0.0.1:12345".to_string()),
            connected_at: now,
            last_activity: now,
            message_count: 0,
        };
        
        assert_eq!(conn_info.connection_id, "test-conn");
        assert_eq!(conn_info.remote_addr, Some("127.0.0.1:12345".to_string()));
        assert_eq!(conn_info.message_count, 0);
    }

    #[tokio::test]
    async fn test_default_message_handler() {
        let handler = DefaultMessageHandler;
        
        // 测试ping消息
        let ping_msg = IpcMessage::ping("test-source".to_string());
        let response = handler.handle_message("test-conn", ping_msg.clone()).await.unwrap();
        
        assert!(response.is_some());
        let resp = response.unwrap();
        assert_eq!(resp.request_id, ping_msg.message_id);
        assert!(resp.is_success());
        
        // 测试请求消息
        let request_msg = IpcMessage::request(
            "test-source".to_string(),
            serde_json::json!({"test": "data"}),
        );
        let response = handler.handle_message("test-conn", request_msg.clone()).await.unwrap();
        
        assert!(response.is_some());
        let resp = response.unwrap();
        assert_eq!(resp.request_id, request_msg.message_id);
        assert!(resp.is_success());
    }

    #[tokio::test]
    async fn test_server_event_handling() {
        let handler = DefaultMessageHandler;
        
        // 测试连接事件
        let event = ServerEvent::ClientConnected {
            connection_id: "test-conn".to_string(),
            remote_addr: Some("127.0.0.1:12345".to_string()),
        };
        
        let result = handler.handle_connection_event(event).await;
        assert!(result.is_ok());
        
        // 测试断开事件
        let event = ServerEvent::ClientDisconnected {
            connection_id: "test-conn".to_string(),
            reason: "测试断开".to_string(),
        };
        
        let result = handler.handle_connection_event(event).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_server_startup_shutdown() {
        let mut config = ServerConfig::default();
        config.transport_config.port = Some(0); // 让系统分配端口
        
        let mut server = IpcServer::new(config);
        
        // 在后台启动服务器
        let server_handle = tokio::spawn(async move {
            server.start().await
        });
        
        // 等待一下让服务器启动
        sleep(Duration::from_millis(100)).await;
        
        // 停止服务器
        server_handle.abort();
        
        // 等待服务器停止
        let _ = server_handle.await;
    }

    #[tokio::test]
    async fn test_server_with_custom_handler() {
        struct CustomHandler;
        
        #[async_trait]
        impl MessageHandler for CustomHandler {
            async fn handle_message(
                &self,
                _connection_id: &str,
                message: IpcMessage,
            ) -> IpcResult<Option<IpcResponse>> {
                Ok(Some(IpcResponse::success(
                    message.message_id,
                    serde_json::json!({"custom": "response"}),
                )))
            }
            
            async fn handle_connection_event(&self, _event: ServerEvent) -> IpcResult<()> {
                Ok(())
            }
        }
        
        let config = ServerConfig::default();
        let server = IpcServer::new(config).with_message_handler(Arc::new(CustomHandler));
        
        assert!(!server.is_running().await);
    }
} 