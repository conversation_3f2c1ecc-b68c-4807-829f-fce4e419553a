//! 守护进程错误类型定义

use thiserror::Error;

/// 守护进程错误类型
#[derive(Error, Debug)]
pub enum DaemonError {
    /// 配置相关错误
    #[error("配置加载失败: {0}")]
    ConfigLoadError(String),
    
    #[error("配置解析失败: {0}")]
    ConfigParseError(String),
    
    #[error("配置验证失败: {0}")]
    ConfigValidationError(String),
    
    #[error("配置序列化失败: {0}")]
    ConfigSerializeError(String),
    
    #[error("配置保存失败: {0}")]
    ConfigSaveError(String),
    
    /// 服务相关错误
    #[error("服务启动失败: {0}")]
    ServiceStartError(String),
    
    #[error("服务停止失败: {0}")]
    ServiceStopError(String),
    
    #[error("服务安装失败: {0}")]
    ServiceInstallError(String),
    
    #[error("服务卸载失败: {0}")]
    ServiceUninstallError(String),
    
    /// IPC 相关错误
    #[error("IPC 服务器启动失败: {0}")]
    IpcServerStartError(String),
    
    #[error("IPC 连接失败: {0}")]
    IpcConnectionError(String),
    
    #[error("IPC 消息发送失败: {0}")]
    IpcSendError(String),
    
    #[error("IPC 消息接收失败: {0}")]
    IpcReceiveError(String),
    
    /// Native Messaging 相关错误
    #[error("Native Messaging Host 启动失败: {0}")]
    NativeMessagingStartError(String),
    
    #[error("浏览器注册失败: {0}")]
    BrowserRegistrationError(String),
    
    #[error("消息处理失败: {0}")]
    MessageHandlingError(String),
    
    /// 应用管理相关错误
    #[error("应用启动失败: {0}")]
    AppStartError(String),
    
    #[error("应用停止失败: {0}")]
    AppStopError(String),
    
    #[error("应用健康检查失败: {0}")]
    AppHealthCheckError(String),
    
    /// 安全相关错误
    #[error("安全验证失败: {0}")]
    SecurityValidationError(String),
    
    #[error("加密失败: {0}")]
    EncryptionError(String),
    
    #[error("解密失败: {0}")]
    DecryptionError(String),
    
    /// 监控相关错误
    #[error("监控系统启动失败: {0}")]
    MonitoringStartError(String),
    
    #[error("指标收集失败: {0}")]
    MetricsCollectionError(String),
    
    /// 系统相关错误
    #[error("文件系统错误: {0}")]
    FileSystemError(String),
    
    #[error("网络错误: {0}")]
    NetworkError(String),
    
    #[error("权限错误: {0}")]
    PermissionError(String),
    
    /// 通用错误
    #[error("内部错误: {0}")]
    InternalError(String),
    
    #[error("未知错误: {0}")]
    UnknownError(String),
}

/// 错误结果类型别名
pub type DaemonResult<T> = Result<T, DaemonError>;

impl From<std::io::Error> for DaemonError {
    fn from(error: std::io::Error) -> Self {
        DaemonError::FileSystemError(error.to_string())
    }
}

impl From<serde_json::Error> for DaemonError {
    fn from(error: serde_json::Error) -> Self {
        DaemonError::ConfigParseError(error.to_string())
    }
}

impl From<toml::de::Error> for DaemonError {
    fn from(error: toml::de::Error) -> Self {
        DaemonError::ConfigParseError(error.to_string())
    }
}

impl From<toml::ser::Error> for DaemonError {
    fn from(error: toml::ser::Error) -> Self {
        DaemonError::ConfigSerializeError(error.to_string())
    }
}

impl From<tokio::time::error::Elapsed> for DaemonError {
    fn from(error: tokio::time::error::Elapsed) -> Self {
        DaemonError::InternalError(format!("操作超时: {}", error))
    }
}

impl From<tokio::sync::mpsc::error::SendError<crate::config::ConfigChangeEvent>> for DaemonError {
    fn from(error: tokio::sync::mpsc::error::SendError<crate::config::ConfigChangeEvent>) -> Self {
        DaemonError::InternalError(format!("发送配置变化事件失败: {}", error))
    }
}

/// 错误上下文扩展
pub trait ErrorContext<T> {
    /// 添加错误上下文
    fn with_context<F>(self, f: F) -> Result<T, DaemonError>
    where
        F: FnOnce() -> String;

    /// 添加错误上下文（懒加载）
    fn with_context_lazy<F>(self, f: F) -> Result<T, DaemonError>
    where
        F: FnOnce() -> String;
}

impl<T, E> ErrorContext<T> for Result<T, E>
where
    E: Into<DaemonError>,
{
    fn with_context<F>(self, f: F) -> Result<T, DaemonError>
    where
        F: FnOnce() -> String,
    {
        self.map_err(|e| {
            let original_error = e.into();
            let context = f();
            DaemonError::InternalError(format!("{}: {}", context, original_error))
        })
    }

    fn with_context_lazy<F>(self, f: F) -> Result<T, DaemonError>
    where
        F: FnOnce() -> String,
    {
        self.map_err(|e| {
            let original_error = e.into();
            let context = f();
            DaemonError::InternalError(format!("{}: {}", context, original_error))
        })
    }
}

/// 错误报告器
pub struct ErrorReporter {
    /// 是否启用详细错误信息
    verbose: bool,
}

impl ErrorReporter {
    /// 创建新的错误报告器
    pub fn new(verbose: bool) -> Self {
        Self { verbose }
    }

    /// 报告错误
    pub fn report(&self, error: &DaemonError) -> String {
        if self.verbose {
            self.format_verbose_error(error)
        } else {
            self.format_simple_error(error)
        }
    }

    /// 格式化详细错误信息
    fn format_verbose_error(&self, error: &DaemonError) -> String {
        match error {
            DaemonError::ConfigLoadError(msg) => {
                format!("配置加载失败: {}\n建议: 检查配置文件路径和权限", msg)
            }
            DaemonError::ConfigParseError(msg) => {
                format!("配置解析失败: {}\n建议: 检查配置文件语法", msg)
            }
            DaemonError::ConfigValidationError(msg) => {
                format!("配置验证失败: {}\n建议: 检查配置项的值是否正确", msg)
            }
            DaemonError::ServiceStartError(msg) => {
                format!("服务启动失败: {}\n建议: 检查端口占用和权限", msg)
            }
            DaemonError::IpcServerStartError(msg) => {
                format!("IPC服务器启动失败: {}\n建议: 检查网络配置和防火墙设置", msg)
            }
            DaemonError::PermissionError(msg) => {
                format!("权限错误: {}\n建议: 以管理员权限运行或检查文件权限", msg)
            }
            _ => format!("错误: {}", error),
        }
    }

    /// 格式化简单错误信息
    fn format_simple_error(&self, error: &DaemonError) -> String {
        error.to_string()
    }
}

/// 错误恢复策略
#[derive(Debug, Clone)]
pub enum RecoveryStrategy {
    /// 重试操作
    Retry { max_attempts: u32, delay_ms: u64 },
    /// 使用默认值
    UseDefault,
    /// 跳过操作
    Skip,
    /// 停止服务
    Stop,
}

/// 错误恢复管理器
pub struct ErrorRecoveryManager {
    /// 错误计数器
    error_counts: std::collections::HashMap<String, u32>,
    /// 最大错误次数
    max_errors: u32,
}

impl ErrorRecoveryManager {
    /// 创建新的错误恢复管理器
    pub fn new(max_errors: u32) -> Self {
        Self {
            error_counts: std::collections::HashMap::new(),
            max_errors,
        }
    }

    /// 记录错误
    pub fn record_error(&mut self, error_type: &str) -> bool {
        let count = self.error_counts.entry(error_type.to_string()).or_insert(0);
        *count += 1;
        *count <= self.max_errors
    }

    /// 重置错误计数
    pub fn reset_error_count(&mut self, error_type: &str) {
        self.error_counts.remove(error_type);
    }

    /// 获取恢复策略
    pub fn get_recovery_strategy(&self, error: &DaemonError) -> RecoveryStrategy {
        match error {
            DaemonError::ConfigLoadError(_) => RecoveryStrategy::UseDefault,
            DaemonError::IpcConnectionError(_) => RecoveryStrategy::Retry {
                max_attempts: 3,
                delay_ms: 1000,
            },
            DaemonError::AppHealthCheckError(_) => RecoveryStrategy::Retry {
                max_attempts: 5,
                delay_ms: 5000,
            },
            DaemonError::PermissionError(_) => RecoveryStrategy::Stop,
            _ => RecoveryStrategy::Skip,
        }
    }
}
