//! Linux systemd 服务管理器
//! 
//! 提供 Linux systemd 系统服务的完整生命周期管理

use std::path::{Path, PathBuf};
use std::process::Stdio;
use async_trait::async_trait;
use tracing::{info, warn, error, debug};
use chrono::{DateTime, Utc};

use crate::error::DaemonError;
use super::super::{
    PlatformServiceManager, ServiceStatus, ServiceConfig, ServiceInstallConfig, 
    ServiceInfo, ServiceError
};
use super::service_manager::SystemdServiceManager;

/// Linux systemd 服务管理器
/// 
/// 负责 Linux 系统下的 systemd 服务管理
pub struct LinuxServiceManager {
    /// 服务名称
    service_name: String,
    /// systemd 单元文件路径
    service_file_path: PathBuf,
    /// systemd 服务管理器
    systemd_manager: SystemdServiceManager,
    /// systemctl 命令路径
    systemctl_path: PathBuf,
}

impl LinuxServiceManager {
    /// 创建新的 Linux 服务管理器
    /// 
    /// # 参数
    /// - `service_name`: 服务名称，将用作 systemd 单元名称
    /// 
    /// # 示例
    /// ```rust
    /// let manager = LinuxServiceManager::new("secure-password-daemon".to_string());
    /// ```
    pub fn new(service_name: String) -> Self {
        let service_file_path = Self::get_systemd_service_path(&service_name);
        let systemd_manager = SystemdServiceManager::new();
        let systemctl_path = Self::find_systemctl_path();

        Self {
            service_name,
            service_file_path,
            systemd_manager,
            systemctl_path,
        }
    }

    /// 获取 systemd 服务文件路径
    /// 
    /// 系统级服务文件存储在 `/etc/systemd/system/` 目录
    fn get_systemd_service_path(service_name: &str) -> PathBuf {
        PathBuf::from("/etc/systemd/system").join(format!("{}.service", service_name))
    }

    /// 获取用户级 systemd 服务文件路径
    /// 
    /// 用户级服务文件存储在 `~/.config/systemd/user/` 目录
    fn get_user_systemd_service_path(service_name: &str) -> Result<PathBuf, DaemonError> {
        let home_dir = std::env::var("HOME")
            .map_err(|_| DaemonError::InternalError("无法获取用户主目录".to_string()))?;
        
        Ok(PathBuf::from(home_dir)
            .join(".config")
            .join("systemd")
            .join("user")
            .join(format!("{}.service", service_name)))
    }

    /// 查找 systemctl 命令路径
    fn find_systemctl_path() -> PathBuf {
        // 常见的 systemctl 路径
        let possible_paths = vec![
            "/usr/bin/systemctl",
            "/bin/systemctl",
            "/usr/local/bin/systemctl",
        ];

        for path in possible_paths {
            if Path::new(path).exists() {
                return PathBuf::from(path);
            }
        }

        // 默认路径
        PathBuf::from("systemctl")
    }

    /// 执行 systemctl 命令
    /// 
    /// # 参数
    /// - `args`: systemctl 命令参数
    /// 
    /// # 返回
    /// - `Ok(String)`: 命令输出
    /// - `Err(DaemonError)`: 命令执行失败
    async fn execute_systemctl(&self, args: &[&str]) -> Result<String, DaemonError> {
        debug!("执行 systemctl 命令: {:?}", args);

        let output = tokio::process::Command::new(&self.systemctl_path)
            .args(args)
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .output()
            .await
            .map_err(|e| DaemonError::InternalError(format!("systemctl 命令执行失败: {}", e)))?;

        if output.status.success() {
            let stdout = String::from_utf8_lossy(&output.stdout).to_string();
            debug!("systemctl 命令输出: {}", stdout);
            Ok(stdout)
        } else {
            let stderr = String::from_utf8_lossy(&output.stderr).to_string();
            error!("systemctl 命令失败: {}", stderr);
            Err(DaemonError::InternalError(format!("systemctl 命令失败: {}", stderr)))
        }
    }

    /// 执行带超时的 systemctl 命令
    async fn execute_systemctl_with_timeout(
        &self, 
        args: &[&str], 
        timeout_secs: u64
    ) -> Result<String, DaemonError> {
        let future = self.execute_systemctl(args);
        let timeout = tokio::time::timeout(
            std::time::Duration::from_secs(timeout_secs), 
            future
        );

        timeout.await
            .map_err(|_| DaemonError::InternalError(format!("systemctl 命令超时 ({} 秒)", timeout_secs)))?
    }

    /// 重新加载 systemd 配置
    async fn daemon_reload(&self) -> Result<(), DaemonError> {
        info!("重新加载 systemd 配置");
        self.execute_systemctl(&["daemon-reload"]).await?;
        Ok(())
    }

    /// 启用服务 (开机自启)
    async fn enable_service(&self) -> Result<(), DaemonError> {
        info!("启用服务自动启动: {}", self.service_name);
        self.execute_systemctl(&["enable", &self.service_name]).await?;
        Ok(())
    }

    /// 禁用服务 (关闭开机自启)
    async fn disable_service(&self) -> Result<(), DaemonError> {
        info!("禁用服务自动启动: {}", self.service_name);
        self.execute_systemctl(&["disable", &self.service_name]).await?;
        Ok(())
    }

    /// 启动服务
    async fn start_service_via_systemctl(&self) -> Result<(), DaemonError> {
        info!("启动服务: {}", self.service_name);
        self.execute_systemctl_with_timeout(&["start", &self.service_name], 30).await?;
        Ok(())
    }

    /// 停止服务
    async fn stop_service_via_systemctl(&self) -> Result<(), DaemonError> {
        info!("停止服务: {}", self.service_name);
        self.execute_systemctl_with_timeout(&["stop", &self.service_name], 30).await?;
        Ok(())
    }

    /// 重启服务
    async fn restart_service_via_systemctl(&self) -> Result<(), DaemonError> {
        info!("重启服务: {}", self.service_name);
        self.execute_systemctl_with_timeout(&["restart", &self.service_name], 60).await?;
        Ok(())
    }

    /// 获取服务状态信息
    async fn get_service_status_info(&self) -> Result<String, DaemonError> {
        self.execute_systemctl(&["status", &self.service_name, "--no-pager"]).await
    }

    /// 解析服务状态
    fn parse_service_status(&self, status_output: &str) -> ServiceStatus {
        if status_output.contains("Active: active (running)") {
            ServiceStatus::Running
        } else if status_output.contains("Active: inactive (dead)") {
            ServiceStatus::Stopped
        } else if status_output.contains("Active: activating") {
            ServiceStatus::Starting
        } else if status_output.contains("Active: deactivating") {
            ServiceStatus::Stopping
        } else if status_output.contains("Active: failed") {
            ServiceStatus::Error("服务运行失败".to_string())
        } else if status_output.contains("could not be found") || status_output.contains("not loaded") {
            ServiceStatus::NotInstalled
        } else {
            ServiceStatus::Unknown
        }
    }

    /// 从状态输出中提取进程 ID
    fn extract_process_id(&self, status_output: &str) -> Option<u32> {
        // 查找 "Main PID: 1234" 模式
        for line in status_output.lines() {
            if line.contains("Main PID:") {
                let parts: Vec<&str> = line.split_whitespace().collect();
                for (i, part) in parts.iter().enumerate() {
                    if part == &"PID:" && i + 1 < parts.len() {
                        if let Ok(pid) = parts[i + 1].parse::<u32>() {
                            return Some(pid);
                        }
                    }
                }
            }
        }
        None
    }

    /// 等待服务状态变化
    /// 
    /// # 参数
    /// - `expected_status`: 期望的服务状态
    /// - `timeout_secs`: 超时时间 (秒)
    async fn wait_for_status_change(
        &self, 
        expected_status: ServiceStatus,
        timeout_secs: u64
    ) -> Result<(), DaemonError> {
        let start_time = std::time::Instant::now();
        let timeout = std::time::Duration::from_secs(timeout_secs);

        while start_time.elapsed() < timeout {
            let current_status = self.get_service_status().await?;
            if std::mem::discriminant(&current_status) == std::mem::discriminant(&expected_status) {
                return Ok(());
            }

            tokio::time::sleep(std::time::Duration::from_millis(200)).await;
        }

        Err(DaemonError::InternalError(format!(
            "等待服务状态变化超时: 期望 {:?}, 超时 {} 秒", 
            expected_status, 
            timeout_secs
        )))
    }

    /// 检查 systemd 是否可用
    pub fn is_systemd_available(&self) -> bool {
        // 检查 systemctl 命令是否存在
        if !self.systemctl_path.exists() && !which::which("systemctl").is_ok() {
            return false;
        }

        // 检查 systemd 是否运行
        std::process::Command::new(&self.systemctl_path)
            .args(&["--version"])
            .output()
            .map(|output| output.status.success())
            .unwrap_or(false)
    }

    /// 获取服务的 journal 日志
    async fn get_journal_logs(&self, lines: usize) -> Result<Vec<String>, DaemonError> {
        let output = tokio::process::Command::new("journalctl")
            .args(&[
                "-u", &self.service_name,
                "-n", &lines.to_string(),
                "--no-pager",
                "--output=short-iso",
            ])
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .output()
            .await
            .map_err(|e| DaemonError::InternalError(format!("获取 journal 日志失败: {}", e)))?;

        if output.status.success() {
            let log_content = String::from_utf8_lossy(&output.stdout);
            let log_lines: Vec<String> = log_content
                .lines()
                .map(|line| line.to_string())
                .collect();

            Ok(log_lines)
        } else {
            let stderr = String::from_utf8_lossy(&output.stderr);
            warn!("获取 journal 日志失败: {}", stderr);
            Ok(vec![])
        }
    }
}

#[async_trait]
impl PlatformServiceManager for LinuxServiceManager {
    async fn install_service(&self, config: &ServiceInstallConfig) -> Result<(), DaemonError> {
        info!("安装 Linux systemd 服务: {}", self.service_name);

        // 检查权限
        if !self.check_permissions()? {
            return Err(DaemonError::PermissionError(
                "需要 root 权限或适当的 systemd 权限安装服务".to_string()
            ));
        }

        // 检查 systemd 是否可用
        if !self.is_systemd_available() {
            return Err(DaemonError::InternalError(
                "systemd 不可用或未运行".to_string()
            ));
        }

        // 检查服务是否已存在
        if self.is_service_installed().await? && !config.force_overwrite {
            return Err(ServiceError::ServiceAlreadyExists.into());
        }

        // 创建 systemd 服务文件
        let service_content = self.systemd_manager.create_service_file(&config.service_config)?;
        
        // 写入服务文件
        tokio::fs::write(&self.service_file_path, service_content).await
            .map_err(|e| DaemonError::FileSystemError(format!("写入服务文件失败: {}", e)))?;

        info!("systemd 服务文件已创建: {:?}", self.service_file_path);

        // 设置文件权限
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;
            let mut perms = tokio::fs::metadata(&self.service_file_path).await
                .map_err(|e| DaemonError::FileSystemError(format!("获取文件权限失败: {}", e)))?
                .permissions();
            perms.set_mode(0o644); // rw-r--r--
            tokio::fs::set_permissions(&self.service_file_path, perms).await
                .map_err(|e| DaemonError::FileSystemError(format!("设置文件权限失败: {}", e)))?;
        }

        // 重新加载 systemd 配置
        self.daemon_reload().await?;

        // 如果配置了自动启动，启用服务
        if config.service_config.auto_start {
            self.enable_service().await?;
        }

        // 如果配置了立即启动，启动服务
        if config.start_immediately {
            self.start_service().await?;
        }

        info!("Linux systemd 服务安装完成: {}", self.service_name);
        Ok(())
    }

    async fn uninstall_service(&self) -> Result<(), DaemonError> {
        info!("卸载 Linux systemd 服务: {}", self.service_name);

        // 检查权限
        if !self.check_permissions()? {
            return Err(DaemonError::PermissionError(
                "需要 root 权限或适当的 systemd 权限卸载服务".to_string()
            ));
        }

        // 停止服务 (如果正在运行)
        let status = self.get_service_status().await?;
        if status == ServiceStatus::Running {
            self.stop_service().await?;
        }

        // 禁用服务 (如果已启用)
        if let Err(e) = self.disable_service().await {
            warn!("禁用服务失败 (继续卸载): {}", e);
        }

        // 删除服务文件
        if self.service_file_path.exists() {
            tokio::fs::remove_file(&self.service_file_path).await
                .map_err(|e| DaemonError::FileSystemError(format!("删除服务文件失败: {}", e)))?;
            info!("服务文件已删除: {:?}", self.service_file_path);
        }

        // 重新加载 systemd 配置
        self.daemon_reload().await?;

        info!("Linux systemd 服务卸载完成: {}", self.service_name);
        Ok(())
    }

    async fn start_service(&self) -> Result<(), DaemonError> {
        info!("启动 Linux systemd 服务: {}", self.service_name);

        // 检查服务是否已安装
        if !self.is_service_installed().await? {
            return Err(ServiceError::ServiceNotInstalled.into());
        }

        // 启动服务
        self.start_service_via_systemctl().await?;

        // 等待服务启动
        self.wait_for_status_change(ServiceStatus::Running, 15).await?;

        info!("Linux systemd 服务启动完成: {}", self.service_name);
        Ok(())
    }

    async fn stop_service(&self) -> Result<(), DaemonError> {
        info!("停止 Linux systemd 服务: {}", self.service_name);

        // 检查服务是否正在运行
        let status = self.get_service_status().await?;
        if status != ServiceStatus::Running {
            warn!("服务未运行: {}", self.service_name);
            return Ok(());
        }

        // 停止服务
        self.stop_service_via_systemctl().await?;

        // 等待服务停止
        self.wait_for_status_change(ServiceStatus::Stopped, 15).await?;

        info!("Linux systemd 服务停止完成: {}", self.service_name);
        Ok(())
    }

    async fn restart_service(&self) -> Result<(), DaemonError> {
        info!("重启 Linux systemd 服务: {}", self.service_name);

        // 重启服务
        self.restart_service_via_systemctl().await?;

        // 等待服务启动
        self.wait_for_status_change(ServiceStatus::Running, 30).await?;

        info!("Linux systemd 服务重启完成: {}", self.service_name);
        Ok(())
    }

    async fn get_service_status(&self) -> Result<ServiceStatus, DaemonError> {
        match self.get_service_status_info().await {
            Ok(status_output) => Ok(self.parse_service_status(&status_output)),
            Err(_) => {
                // 如果无法获取状态，检查服务文件是否存在
                if self.is_service_installed().await? {
                    Ok(ServiceStatus::Stopped)
                } else {
                    Ok(ServiceStatus::NotInstalled)
                }
            }
        }
    }

    async fn get_service_info(&self) -> Result<ServiceInfo, DaemonError> {
        let status = self.get_service_status().await?;
        
        // 读取服务文件获取配置信息
        let service_content = if self.service_file_path.exists() {
            tokio::fs::read_to_string(&self.service_file_path).await
                .map_err(|e| DaemonError::FileSystemError(format!("读取服务文件失败: {}", e)))?
        } else {
            return Err(ServiceError::ServiceNotInstalled.into());
        };

        // 解析服务文件获取信息
        let (executable_path, auto_start) = self.systemd_manager.parse_service_file(&service_content)?;

        // 获取进程 ID (如果运行中)
        let process_id = if status == ServiceStatus::Running {
            self.get_service_status_info().await.ok()
                .and_then(|output| self.extract_process_id(&output))
        } else {
            None
        };

        Ok(ServiceInfo {
            name: self.service_name.clone(),
            display_name: self.service_name.clone(), // systemd 通常使用相同的名称
            status,
            process_id,
            start_time: None, // 可以通过 journalctl 获取，但这里简化处理
            auto_start,
            executable_path,
        })
    }

    async fn enable_auto_start(&self) -> Result<(), DaemonError> {
        info!("启用 Linux systemd 服务自动启动: {}", self.service_name);
        self.enable_service().await
    }

    async fn disable_auto_start(&self) -> Result<(), DaemonError> {
        info!("禁用 Linux systemd 服务自动启动: {}", self.service_name);
        self.disable_service().await
    }

    async fn is_service_installed(&self) -> Result<bool, DaemonError> {
        Ok(self.service_file_path.exists())
    }

    fn check_permissions(&self) -> Result<bool, DaemonError> {
        // 检查是否为 root 用户或具有适当的 systemd 权限
        let euid = unsafe { libc::geteuid() };
        
        // root 用户总是有权限
        if euid == 0 {
            return Ok(true);
        }

        // 检查是否可以访问 systemd 系统目录
        let systemd_dir = Path::new("/etc/systemd/system");
        match std::fs::metadata(systemd_dir) {
            Ok(_) => {
                // 尝试创建一个临时文件来测试写权限
                let test_file = systemd_dir.join(".test_write_permission");
                match std::fs::write(&test_file, "") {
                    Ok(_) => {
                        // 清理测试文件
                        let _ = std::fs::remove_file(&test_file);
                        Ok(true)
                    }
                    Err(_) => Ok(false),
                }
            }
            Err(_) => Ok(false),
        }
    }

    async fn get_service_logs(&self, lines: usize) -> Result<Vec<String>, DaemonError> {
        info!("获取 Linux systemd 服务日志: {} (最近 {} 行)", self.service_name, lines);
        self.get_journal_logs(lines).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;

    #[tokio::test]
    async fn test_linux_service_manager_creation() {
        let manager = LinuxServiceManager::new("test-service".to_string());
        assert_eq!(manager.service_name, "test-service");
        assert!(manager.service_file_path.to_string_lossy().contains("test-service.service"));
    }

    #[test]
    fn test_systemd_service_path() {
        let path = LinuxServiceManager::get_systemd_service_path("test-service");
        assert_eq!(path, PathBuf::from("/etc/systemd/system/test-service.service"));
    }

    #[tokio::test]
    async fn test_service_status_not_installed() {
        let manager = LinuxServiceManager::new("nonexistent-service".to_string());
        let status = manager.get_service_status().await.unwrap();
        assert_eq!(status, ServiceStatus::NotInstalled);
    }

    #[tokio::test]
    async fn test_is_service_installed_false() {
        let manager = LinuxServiceManager::new("nonexistent-service".to_string());
        let installed = manager.is_service_installed().await.unwrap();
        assert!(!installed);
    }

    #[test]
    fn test_parse_service_status_running() {
        let manager = LinuxServiceManager::new("test-service".to_string());
        let status_output = "● test-service.service - Test Service\n   Active: active (running) since Mon 2023-01-01 10:00:00 UTC; 1h ago";
        let status = manager.parse_service_status(status_output);
        assert_eq!(status, ServiceStatus::Running);
    }

    #[test]
    fn test_parse_service_status_stopped() {
        let manager = LinuxServiceManager::new("test-service".to_string());
        let status_output = "● test-service.service - Test Service\n   Active: inactive (dead)";
        let status = manager.parse_service_status(status_output);
        assert_eq!(status, ServiceStatus::Stopped);
    }

    #[test]
    fn test_extract_process_id() {
        let manager = LinuxServiceManager::new("test-service".to_string());
        let status_output = "● test-service.service - Test Service\n   Active: active (running)\n   Main PID: 1234 (test-daemon)";
        let pid = manager.extract_process_id(status_output);
        assert_eq!(pid, Some(1234));
    }

    #[test]
    fn test_check_permissions() {
        let manager = LinuxServiceManager::new("test-service".to_string());
        let has_permissions = manager.check_permissions();
        // 测试环境中的权限检查结果可能不同
        assert!(has_permissions.is_ok());
    }

    #[test]
    fn test_find_systemctl_path() {
        let path = LinuxServiceManager::find_systemctl_path();
        // 在大多数 Linux 系统中应该能找到 systemctl
        assert!(!path.as_os_str().is_empty());
    }
}