//! systemd 服务文件管理器
//! 
//! 负责创建和解析 systemd 服务单元文件

use std::collections::HashMap;
use std::path::PathBuf;
use crate::error::DaemonError;
use super::super::{ServiceConfig, ServiceError};

/// systemd 服务文件管理器
pub struct SystemdServiceManager;

impl SystemdServiceManager {
    /// 创建新的 systemd 服务管理器
    pub fn new() -> Self {
        Self
    }

    /// 创建 systemd 服务文件内容
    /// 
    /// # 参数
    /// - `config`: 服务配置
    /// 
    /// # 返回
    /// - `Ok(String)`: 服务文件内容
    /// - `Err(DaemonError)`: 创建失败
    pub fn create_service_file(&self, config: &ServiceConfig) -> Result<String, DaemonError> {
        let mut service_content = String::new();

        // [Unit] 部分
        service_content.push_str("[Unit]\n");
        service_content.push_str(&format!("Description={}\n", config.description));
        service_content.push_str("Documentation=man:systemd.service(5)\n");
        
        // 依赖关系
        if !config.dependencies.is_empty() {
            let deps = config.dependencies.join(" ");
            service_content.push_str(&format!("Requires={}\n", deps));
            service_content.push_str(&format!("After={}\n", deps));
        } else {
            service_content.push_str("After=network.target\n");
        }
        
        service_content.push_str("StartLimitIntervalSec=0\n");
        service_content.push_str("\n");

        // [Service] 部分
        service_content.push_str("[Service]\n");
        service_content.push_str("Type=notify\n");
        service_content.push_str("NotifyAccess=main\n");
        
        // 可执行文件和参数
        let mut exec_start = config.executable_path.to_string_lossy().to_string();
        if !config.arguments.is_empty() {
            exec_start.push(' ');
            exec_start.push_str(&config.arguments.join(" "));
        }
        service_content.push_str(&format!("ExecStart={}\n", exec_start));

        // 工作目录
        if let Some(ref working_dir) = config.working_directory {
            service_content.push_str(&format!("WorkingDirectory={}\n", working_dir.display()));
        }

        // 运行用户和组
        if let Some(ref user) = config.run_as_user {
            service_content.push_str(&format!("User={}\n", user));
        }
        if let Some(ref group) = config.run_as_group {
            service_content.push_str(&format!("Group={}\n", group));
        }

        // 环境变量
        if !config.environment.is_empty() {
            for (key, value) in &config.environment {
                service_content.push_str(&format!("Environment=\"{}={}\"\n", key, value));
            }
        }

        // 重启策略
        service_content.push_str("Restart=on-failure\n");
        service_content.push_str("RestartSec=5s\n");
        service_content.push_str("StartLimitBurst=3\n");

        // 安全设置
        service_content.push_str("NoNewPrivileges=true\n");
        service_content.push_str("ProtectSystem=strict\n");
        service_content.push_str("ProtectHome=true\n");
        service_content.push_str("PrivateTmp=true\n");
        service_content.push_str("PrivateDevices=true\n");
        service_content.push_str("ProtectKernelTunables=true\n");
        service_content.push_str("ProtectKernelModules=true\n");
        service_content.push_str("ProtectControlGroups=true\n");

        // 资源限制
        service_content.push_str("LimitNOFILE=65536\n");
        service_content.push_str("LimitNPROC=4096\n");

        service_content.push_str("\n");

        // [Install] 部分
        service_content.push_str("[Install]\n");
        if config.auto_start {
            service_content.push_str("WantedBy=multi-user.target\n");
        }

        Ok(service_content)
    }

    /// 解析 systemd 服务文件
    /// 
    /// # 参数
    /// - `content`: 服务文件内容
    /// 
    /// # 返回
    /// - `Ok((PathBuf, bool))`: (可执行文件路径, 是否自动启动)
    pub fn parse_service_file(&self, content: &str) -> Result<(PathBuf, bool), DaemonError> {
        let mut executable_path = PathBuf::new();
        let mut auto_start = false;

        for line in content.lines() {
            let line = line.trim();
            
            if line.starts_with("ExecStart=") {
                let exec_start = line.strip_prefix("ExecStart=").unwrap_or("");
                // 解析可执行文件路径 (第一个参数)
                if let Some(first_arg) = exec_start.split_whitespace().next() {
                    executable_path = PathBuf::from(first_arg);
                }
            } else if line.starts_with("WantedBy=") {
                auto_start = true;
            }
        }

        if executable_path.as_os_str().is_empty() {
            return Err(ServiceError::InvalidConfiguration(
                "未找到 ExecStart 配置".to_string()
            ).into());
        }

        Ok((executable_path, auto_start))
    }

    /// 验证服务文件配置
    /// 
    /// # 参数
    /// - `config`: 服务配置
    /// 
    /// # 返回
    /// - `Ok(())`: 配置有效
    /// - `Err(DaemonError)`: 配置无效
    pub fn validate_service_config(&self, config: &ServiceConfig) -> Result<(), DaemonError> {
        // 检查服务名称
        if config.service_name.is_empty() {
            return Err(ServiceError::InvalidConfiguration("服务名称不能为空".to_string()).into());
        }

        // 检查可执行文件
        if !config.executable_path.exists() {
            return Err(ServiceError::InvalidConfiguration(
                format!("可执行文件不存在: {}", config.executable_path.display())
            ).into());
        }

        // 检查可执行权限
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;
            let metadata = std::fs::metadata(&config.executable_path)
                .map_err(|e| ServiceError::InvalidConfiguration(
                    format!("无法读取可执行文件权限: {}", e)
                ))?;
            let permissions = metadata.permissions();
            if permissions.mode() & 0o111 == 0 {
                return Err(ServiceError::InvalidConfiguration(
                    "可执行文件没有执行权限".to_string()
                ).into());
            }
        }

        // 检查工作目录
        if let Some(ref working_dir) = config.working_directory {
            if !working_dir.exists() {
                return Err(ServiceError::InvalidConfiguration(
                    format!("工作目录不存在: {}", working_dir.display())
                ).into());
            }
        }

        // 检查用户名
        if let Some(ref user) = config.run_as_user {
            if user.is_empty() {
                return Err(ServiceError::InvalidConfiguration("用户名不能为空".to_string()).into());
            }
        }

        // 检查组名
        if let Some(ref group) = config.run_as_group {
            if group.is_empty() {
                return Err(ServiceError::InvalidConfiguration("组名不能为空".to_string()).into());
            }
        }

        Ok(())
    }

    /// 创建并验证服务文件
    /// 
    /// # 参数
    /// - `config`: 服务配置
    /// 
    /// # 返回
    /// - `Ok(String)`: 有效的服务文件内容
    pub fn create_and_validate_service_file(&self, config: &ServiceConfig) -> Result<String, DaemonError> {
        // 验证配置
        self.validate_service_config(config)?;

        // 创建服务文件
        let service_content = self.create_service_file(config)?;

        // 验证生成的服务文件
        let (parsed_path, _) = self.parse_service_file(&service_content)?;
        if parsed_path != config.executable_path {
            return Err(ServiceError::InvalidConfiguration(
                "生成的服务文件与配置不匹配".to_string()
            ).into());
        }

        Ok(service_content)
    }

    /// 获取服务文件模板
    /// 
    /// # 返回
    /// 标准的 systemd 服务文件模板
    pub fn get_service_template() -> &'static str {
        r#"[Unit]
Description={{DESCRIPTION}}
Documentation=man:systemd.service(5)
After=network.target
StartLimitIntervalSec=0

[Service]
Type=notify
NotifyAccess=main
ExecStart={{EXECUTABLE_PATH}} {{ARGUMENTS}}
WorkingDirectory={{WORKING_DIRECTORY}}
User={{USER}}
Group={{GROUP}}
Restart=on-failure
RestartSec=5s
StartLimitBurst=3

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
PrivateTmp=true
PrivateDevices=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
"#
    }
}

impl Default for SystemdServiceManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;

    fn create_test_service_config() -> ServiceConfig {
        let mut environment = HashMap::new();
        environment.insert("PATH".to_string(), "/usr/bin:/bin".to_string());
        environment.insert("LOG_LEVEL".to_string(), "info".to_string());

        ServiceConfig {
            service_name: "test-daemon".to_string(),
            display_name: "Test Daemon".to_string(),
            description: "Test daemon service for systemd".to_string(),
            executable_path: PathBuf::from("/usr/local/bin/test-daemon"),
            working_directory: Some(PathBuf::from("/var/lib/test-daemon")),
            arguments: vec!["--daemon".to_string(), "--config=/etc/test-daemon.conf".to_string()],
            auto_start: true,
            dependencies: vec!["network.target".to_string()],
            run_as_user: Some("daemon".to_string()),
            run_as_group: Some("daemon".to_string()),
            environment,
        }
    }

    #[test]
    fn test_systemd_manager_creation() {
        let manager = SystemdServiceManager::new();
        assert!(true); // 验证创建成功
    }

    #[test]
    fn test_create_service_file() {
        let manager = SystemdServiceManager::new();
        let config = create_test_service_config();
        
        let result = manager.create_service_file(&config);
        assert!(result.is_ok());
        
        let service_content = result.unwrap();
        assert!(service_content.contains("[Unit]"));
        assert!(service_content.contains("[Service]"));
        assert!(service_content.contains("[Install]"));
        assert!(service_content.contains("Description=Test daemon service for systemd"));
        assert!(service_content.contains("ExecStart=/usr/local/bin/test-daemon"));
        assert!(service_content.contains("User=daemon"));
        assert!(service_content.contains("Group=daemon"));
        assert!(service_content.contains("WantedBy=multi-user.target"));
    }

    #[test]
    fn test_parse_service_file() {
        let manager = SystemdServiceManager::new();
        let service_content = r#"
[Unit]
Description=Test Service

[Service]
ExecStart=/usr/bin/test-service --config /etc/test.conf
User=test

[Install]
WantedBy=multi-user.target
"#;

        let result = manager.parse_service_file(service_content);
        assert!(result.is_ok());
        
        let (executable_path, auto_start) = result.unwrap();
        assert_eq!(executable_path, PathBuf::from("/usr/bin/test-service"));
        assert!(auto_start);
    }

    #[test]
    fn test_parse_service_file_no_install() {
        let manager = SystemdServiceManager::new();
        let service_content = r#"
[Unit]
Description=Test Service

[Service]
ExecStart=/usr/bin/test-service
"#;

        let result = manager.parse_service_file(service_content);
        assert!(result.is_ok());
        
        let (executable_path, auto_start) = result.unwrap();
        assert_eq!(executable_path, PathBuf::from("/usr/bin/test-service"));
        assert!(!auto_start);
    }

    #[test]
    fn test_validate_service_config_empty_name() {
        let manager = SystemdServiceManager::new();
        let mut config = create_test_service_config();
        config.service_name = String::new();
        
        let result = manager.validate_service_config(&config);
        assert!(result.is_err());
    }

    #[test]
    fn test_validate_service_config_nonexistent_executable() {
        let manager = SystemdServiceManager::new();
        let mut config = create_test_service_config();
        config.executable_path = PathBuf::from("/nonexistent/executable");
        
        let result = manager.validate_service_config(&config);
        assert!(result.is_err());
    }

    #[test]
    fn test_service_file_roundtrip() {
        let manager = SystemdServiceManager::new();
        let config = create_test_service_config();
        
        // 创建服务文件
        let service_content = manager.create_service_file(&config).unwrap();
        
        // 解析服务文件
        let (parsed_path, parsed_auto_start) = manager.parse_service_file(&service_content).unwrap();
        
        // 验证关键字段
        assert_eq!(parsed_path, config.executable_path);
        assert_eq!(parsed_auto_start, config.auto_start);
    }

    #[test]
    fn test_get_service_template() {
        let template = SystemdServiceManager::get_service_template();
        assert!(template.contains("[Unit]"));
        assert!(template.contains("[Service]"));
        assert!(template.contains("[Install]"));
        assert!(template.contains("{{DESCRIPTION}}"));
        assert!(template.contains("{{EXECUTABLE_PATH}}"));
    }
} 