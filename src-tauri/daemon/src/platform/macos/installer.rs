//! macOS 服务安装器
//! 
//! 提供 macOS LaunchDaemon 和 LaunchAgent 的安装、卸载功能

use std::path::{Path, PathBuf};
use std::process::Command;
use tracing::{info, warn, error, debug};

use crate::error::DaemonError;
use super::super::{ServiceConfig, ServiceInstallConfig, ServiceError};
use super::plist_manager::PlistManager;

/// macOS 服务安装器
/// 
/// 负责处理 LaunchDaemon 和 LaunchAgent 的安装和卸载
pub struct MacOSServiceInstaller {
    /// Plist 管理器
    plist_manager: PlistManager,
}

impl MacOSServiceInstaller {
    /// 创建新的 macOS 服务安装器
    pub fn new() -> Self {
        Self {
            plist_manager: PlistManager::new(),
        }
    }

    /// 安装 LaunchDaemon (系统级守护进程)
    /// 
    /// # 参数
    /// - `config`: 服务安装配置
    /// 
    /// # 返回
    /// - `Ok(())`: 安装成功
    /// - `Err(DaemonError)`: 安装失败
    /// 
    /// # 权限要求
    /// - 需要 root 权限或 sudo 权限
    pub async fn install_launch_daemon(&self, config: &ServiceInstallConfig) -> Result<(), DaemonError> {
        info!("开始安装 LaunchDaemon: {}", config.service_config.service_name);

        // 检查权限
        self.check_root_permissions()?;

        // 确保 LaunchDaemons 目录存在
        let launch_daemons_dir = PathBuf::from("/Library/LaunchDaemons");
        self.ensure_directory_exists(&launch_daemons_dir).await?;

        // 生成 plist 文件路径
        let plist_path = launch_daemons_dir.join(format!("{}.plist", config.service_config.service_name));

        // 检查是否已存在
        if plist_path.exists() && !config.force_overwrite {
            return Err(ServiceError::ServiceAlreadyExists.into());
        }

        // 创建 plist 内容
        let plist_content = self.plist_manager.create_and_validate_plist(&config.service_config, true)?;

        // 写入 plist 文件
        tokio::fs::write(&plist_path, plist_content).await
            .map_err(|e| DaemonError::FileSystemError(format!("写入 plist 文件失败: {}", e)))?;

        // 设置适当的文件权限
        self.set_plist_permissions(&plist_path).await?;

        info!("LaunchDaemon plist 文件已创建: {:?}", plist_path);

        // 复制可执行文件到安装目录 (如果需要)
        if let Some(install_dir) = config.install_path.parent() {
            self.install_executable(&config.service_config, install_dir).await?;
        }

        // 加载服务到 launchctl
        self.load_service(&plist_path).await?;

        // 如果配置了立即启动
        if config.start_immediately {
            self.start_service(&config.service_config.service_name).await?;
        }

        info!("LaunchDaemon 安装完成: {}", config.service_config.service_name);
        Ok(())
    }

    /// 安装 LaunchAgent (用户级代理)
    /// 
    /// # 参数
    /// - `config`: 服务安装配置
    /// 
    /// # 返回
    /// - `Ok(())`: 安装成功
    /// - `Err(DaemonError)`: 安装失败
    pub async fn install_launch_agent(&self, config: &ServiceInstallConfig) -> Result<(), DaemonError> {
        info!("开始安装 LaunchAgent: {}", config.service_config.service_name);

        // 获取用户 LaunchAgents 目录
        let launch_agents_dir = self.get_user_launch_agents_dir()?;
        self.ensure_directory_exists(&launch_agents_dir).await?;

        // 生成 plist 文件路径
        let plist_path = launch_agents_dir.join(format!("{}.plist", config.service_config.service_name));

        // 检查是否已存在
        if plist_path.exists() && !config.force_overwrite {
            return Err(ServiceError::ServiceAlreadyExists.into());
        }

        // 创建 plist 内容
        let plist_content = self.plist_manager.create_and_validate_plist(&config.service_config, false)?;

        // 写入 plist 文件
        tokio::fs::write(&plist_path, plist_content).await
            .map_err(|e| DaemonError::FileSystemError(format!("写入 plist 文件失败: {}", e)))?;

        // 设置适当的文件权限
        self.set_plist_permissions(&plist_path).await?;

        info!("LaunchAgent plist 文件已创建: {:?}", plist_path);

        // 复制可执行文件到安装目录 (如果需要)
        if let Some(install_dir) = config.install_path.parent() {
            self.install_executable(&config.service_config, install_dir).await?;
        }

        // 加载服务到 launchctl
        self.load_service(&plist_path).await?;

        // 如果配置了立即启动
        if config.start_immediately {
            self.start_service(&config.service_config.service_name).await?;
        }

        info!("LaunchAgent 安装完成: {}", config.service_config.service_name);
        Ok(())
    }

    /// 卸载 LaunchDaemon
    /// 
    /// # 参数
    /// - `service_name`: 服务名称
    pub async fn uninstall_launch_daemon(&self, service_name: &str) -> Result<(), DaemonError> {
        info!("开始卸载 LaunchDaemon: {}", service_name);

        // 检查权限
        self.check_root_permissions()?;

        let plist_path = PathBuf::from("/Library/LaunchDaemons").join(format!("{}.plist", service_name));

        // 停止服务
        if let Err(e) = self.stop_service(service_name).await {
            warn!("停止服务失败 (继续卸载): {}", e);
        }

        // 卸载服务
        if let Err(e) = self.unload_service(&plist_path).await {
            warn!("卸载服务失败 (继续删除文件): {}", e);
        }

        // 删除 plist 文件
        if plist_path.exists() {
            tokio::fs::remove_file(&plist_path).await
                .map_err(|e| DaemonError::FileSystemError(format!("删除 plist 文件失败: {}", e)))?;
            info!("已删除 plist 文件: {:?}", plist_path);
        }

        info!("LaunchDaemon 卸载完成: {}", service_name);
        Ok(())
    }

    /// 卸载 LaunchAgent
    /// 
    /// # 参数
    /// - `service_name`: 服务名称
    pub async fn uninstall_launch_agent(&self, service_name: &str) -> Result<(), DaemonError> {
        info!("开始卸载 LaunchAgent: {}", service_name);

        let launch_agents_dir = self.get_user_launch_agents_dir()?;
        let plist_path = launch_agents_dir.join(format!("{}.plist", service_name));

        // 停止服务
        if let Err(e) = self.stop_service(service_name).await {
            warn!("停止服务失败 (继续卸载): {}", e);
        }

        // 卸载服务
        if let Err(e) = self.unload_service(&plist_path).await {
            warn!("卸载服务失败 (继续删除文件): {}", e);
        }

        // 删除 plist 文件
        if plist_path.exists() {
            tokio::fs::remove_file(&plist_path).await
                .map_err(|e| DaemonError::FileSystemError(format!("删除 plist 文件失败: {}", e)))?;
            info!("已删除 plist 文件: {:?}", plist_path);
        }

        info!("LaunchAgent 卸载完成: {}", service_name);
        Ok(())
    }

    /// 执行 launchctl 命令
    /// 
    /// # 参数
    /// - `args`: 命令参数
    async fn execute_launchctl(&self, args: &[&str]) -> Result<String, DaemonError> {
        debug!("执行 launchctl 命令: {:?}", args);

        let output = tokio::process::Command::new("launchctl")
            .args(args)
            .output()
            .await
            .map_err(|e| DaemonError::InternalError(format!("执行 launchctl 失败: {}", e)))?;

        if output.status.success() {
            let stdout = String::from_utf8_lossy(&output.stdout).to_string();
            debug!("launchctl 输出: {}", stdout);
            Ok(stdout)
        } else {
            let stderr = String::from_utf8_lossy(&output.stderr).to_string();
            error!("launchctl 错误: {}", stderr);
            Err(DaemonError::InternalError(format!("launchctl 命令失败: {}", stderr)))
        }
    }

    /// 加载服务到 launchctl
    async fn load_service(&self, plist_path: &Path) -> Result<(), DaemonError> {
        info!("加载服务: {:?}", plist_path);
        
        let path_str = plist_path.to_string_lossy();
        self.execute_launchctl(&["load", &path_str]).await?;
        
        Ok(())
    }

    /// 从 launchctl 卸载服务
    async fn unload_service(&self, plist_path: &Path) -> Result<(), DaemonError> {
        info!("卸载服务: {:?}", plist_path);
        
        let path_str = plist_path.to_string_lossy();
        self.execute_launchctl(&["unload", &path_str]).await?;
        
        Ok(())
    }

    /// 启动服务
    async fn start_service(&self, service_name: &str) -> Result<(), DaemonError> {
        info!("启动服务: {}", service_name);
        
        self.execute_launchctl(&["start", service_name]).await?;
        
        Ok(())
    }

    /// 停止服务
    async fn stop_service(&self, service_name: &str) -> Result<(), DaemonError> {
        info!("停止服务: {}", service_name);
        
        self.execute_launchctl(&["stop", service_name]).await?;
        
        Ok(())
    }

    /// 安装可执行文件
    /// 
    /// # 参数
    /// - `config`: 服务配置
    /// - `install_dir`: 安装目录
    async fn install_executable(&self, config: &ServiceConfig, install_dir: &Path) -> Result<(), DaemonError> {
        // 确保安装目录存在
        self.ensure_directory_exists(install_dir).await?;

        let source_path = &config.executable_path;
        let target_path = install_dir.join(source_path.file_name()
            .ok_or_else(|| DaemonError::FileSystemError("无效的可执行文件路径".to_string()))?);

        // 如果源文件和目标文件不同，则复制
        if *source_path != target_path {
            info!("复制可执行文件: {:?} -> {:?}", source_path, target_path);
            
            tokio::fs::copy(source_path, &target_path).await
                .map_err(|e| DaemonError::FileSystemError(format!("复制可执行文件失败: {}", e)))?;

            // 设置可执行权限
            #[cfg(unix)]
            {
                use std::os::unix::fs::PermissionsExt;
                let mut perms = tokio::fs::metadata(&target_path).await
                    .map_err(|e| DaemonError::FileSystemError(format!("获取文件权限失败: {}", e)))?
                    .permissions();
                perms.set_mode(0o755); // rwxr-xr-x
                tokio::fs::set_permissions(&target_path, perms).await
                    .map_err(|e| DaemonError::FileSystemError(format!("设置文件权限失败: {}", e)))?;
            }
        }

        Ok(())
    }

    /// 确保目录存在
    async fn ensure_directory_exists(&self, dir_path: &Path) -> Result<(), DaemonError> {
        if !dir_path.exists() {
            info!("创建目录: {:?}", dir_path);
            tokio::fs::create_dir_all(dir_path).await
                .map_err(|e| DaemonError::FileSystemError(format!("创建目录失败: {}", e)))?;
        }
        Ok(())
    }

    /// 设置 plist 文件权限
    async fn set_plist_permissions(&self, plist_path: &Path) -> Result<(), DaemonError> {
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;
            let mut perms = tokio::fs::metadata(plist_path).await
                .map_err(|e| DaemonError::FileSystemError(format!("获取文件权限失败: {}", e)))?
                .permissions();
            perms.set_mode(0o644); // rw-r--r--
            tokio::fs::set_permissions(plist_path, perms).await
                .map_err(|e| DaemonError::FileSystemError(format!("设置文件权限失败: {}", e)))?;
        }
        Ok(())
    }

    /// 获取用户 LaunchAgents 目录
    fn get_user_launch_agents_dir(&self) -> Result<PathBuf, DaemonError> {
        let home_dir = std::env::var("HOME")
            .map_err(|_| DaemonError::InternalError("无法获取用户主目录".to_string()))?;
        
        Ok(PathBuf::from(home_dir).join("Library").join("LaunchAgents"))
    }

    /// 检查 root 权限
    fn check_root_permissions(&self) -> Result<(), DaemonError> {
        let euid = unsafe { libc::geteuid() };
        if euid != 0 {
            return Err(DaemonError::PermissionError(
                "需要 root 权限安装 LaunchDaemon".to_string()
            ));
        }
        Ok(())
    }

    /// 创建安装脚本
    /// 
    /// 生成可用于自动化安装的 shell 脚本
    /// 
    /// # 参数
    /// - `config`: 服务安装配置
    /// - `script_path`: 脚本输出路径
    pub async fn create_install_script(
        &self, 
        config: &ServiceInstallConfig, 
        script_path: &Path
    ) -> Result<(), DaemonError> {
        let script_content = format!(r#"#!/bin/bash
# macOS LaunchDaemon 安装脚本
# 服务名称: {}

set -e

SERVICE_NAME="{}"
PLIST_PATH="/Library/LaunchDaemons/${{SERVICE_NAME}}.plist"
EXECUTABLE_PATH="{}"
INSTALL_DIR="{}"

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "错误: 需要 root 权限运行此脚本"
    echo "请使用: sudo $0"
    exit 1
fi

echo "开始安装 LaunchDaemon: $SERVICE_NAME"

# 停止现有服务 (如果存在)
if launchctl list | grep -q "$SERVICE_NAME"; then
    echo "停止现有服务..."
    launchctl stop "$SERVICE_NAME" || true
    launchctl unload "$PLIST_PATH" || true
fi

# 创建安装目录
mkdir -p "$(dirname "$INSTALL_DIR")"

# 复制可执行文件
if [ -f "$EXECUTABLE_PATH" ]; then
    echo "复制可执行文件..."
    cp "$EXECUTABLE_PATH" "$INSTALL_DIR"
    chmod 755 "$INSTALL_DIR"
else
    echo "警告: 可执行文件不存在: $EXECUTABLE_PATH"
fi

# 创建 plist 文件 (这里应该包含实际的 plist 内容)
echo "创建 plist 文件..."
cat > "$PLIST_PATH" << 'EOF'
{}
EOF

# 设置文件权限
chmod 644 "$PLIST_PATH"

# 加载并启动服务
echo "加载服务..."
launchctl load "$PLIST_PATH"

if [ "{}" = "true" ]; then
    echo "启动服务..."
    launchctl start "$SERVICE_NAME"
fi

echo "安装完成!"
echo "服务状态:"
launchctl list | grep "$SERVICE_NAME" || echo "服务未运行"
"#,
            config.service_config.display_name,
            config.service_config.service_name,
            config.service_config.executable_path.display(),
            config.install_path.display(),
            self.plist_manager.create_daemon_plist(&config.service_config)?,
            config.start_immediately
        );

        // 写入脚本文件
        tokio::fs::write(script_path, script_content).await
            .map_err(|e| DaemonError::FileSystemError(format!("写入安装脚本失败: {}", e)))?;

        // 设置可执行权限
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;
            let mut perms = tokio::fs::metadata(script_path).await
                .map_err(|e| DaemonError::FileSystemError(format!("获取脚本权限失败: {}", e)))?
                .permissions();
            perms.set_mode(0o755); // rwxr-xr-x
            tokio::fs::set_permissions(script_path, perms).await
                .map_err(|e| DaemonError::FileSystemError(format!("设置脚本权限失败: {}", e)))?;
        }

        info!("安装脚本已创建: {:?}", script_path);
        Ok(())
    }

    /// 创建卸载脚本
    /// 
    /// # 参数
    /// - `service_name`: 服务名称
    /// - `script_path`: 脚本输出路径
    pub async fn create_uninstall_script(
        &self, 
        service_name: &str, 
        script_path: &Path
    ) -> Result<(), DaemonError> {
        let script_content = format!(r#"#!/bin/bash
# macOS LaunchDaemon 卸载脚本
# 服务名称: {}

set -e

SERVICE_NAME="{}"
PLIST_PATH="/Library/LaunchDaemons/${{SERVICE_NAME}}.plist"

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "错误: 需要 root 权限运行此脚本"
    echo "请使用: sudo $0"
    exit 1
fi

echo "开始卸载 LaunchDaemon: $SERVICE_NAME"

# 停止服务
if launchctl list | grep -q "$SERVICE_NAME"; then
    echo "停止服务..."
    launchctl stop "$SERVICE_NAME" || true
fi

# 卸载服务
if [ -f "$PLIST_PATH" ]; then
    echo "卸载服务..."
    launchctl unload "$PLIST_PATH" || true
fi

# 删除 plist 文件
if [ -f "$PLIST_PATH" ]; then
    echo "删除 plist 文件..."
    rm -f "$PLIST_PATH"
fi

echo "卸载完成!"
echo "服务状态:"
launchctl list | grep "$SERVICE_NAME" || echo "服务已卸载"
"#,
            service_name,
            service_name
        );

        // 写入脚本文件
        tokio::fs::write(script_path, script_content).await
            .map_err(|e| DaemonError::FileSystemError(format!("写入卸载脚本失败: {}", e)))?;

        // 设置可执行权限
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;
            let mut perms = tokio::fs::metadata(script_path).await
                .map_err(|e| DaemonError::FileSystemError(format!("获取脚本权限失败: {}", e)))?
                .permissions();
            perms.set_mode(0o755); // rwxr-xr-x
            tokio::fs::set_permissions(script_path, perms).await
                .map_err(|e| DaemonError::FileSystemError(format!("设置脚本权限失败: {}", e)))?;
        }

        info!("卸载脚本已创建: {:?}", script_path);
        Ok(())
    }
}

impl Default for MacOSServiceInstaller {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;
    use tempfile::TempDir;

    fn create_test_service_config() -> ServiceConfig {
        ServiceConfig {
            service_name: "com.test.daemon".to_string(),
            display_name: "Test Daemon".to_string(),
            description: "Test daemon service".to_string(),
            executable_path: PathBuf::from("/usr/local/bin/test-daemon"),
            working_directory: Some(PathBuf::from("/var/lib/test-daemon")),
            arguments: vec!["--daemon".to_string()],
            auto_start: true,
            dependencies: vec![],
            run_as_user: Some("daemon".to_string()),
            run_as_group: Some("daemon".to_string()),
            environment: HashMap::new(),
        }
    }

    fn create_test_install_config() -> ServiceInstallConfig {
        ServiceInstallConfig {
            service_config: create_test_service_config(),
            install_path: PathBuf::from("/usr/local/bin/test-daemon"),
            start_immediately: true,
            force_overwrite: false,
        }
    }

    #[test]
    fn test_installer_creation() {
        let installer = MacOSServiceInstaller::new();
        assert!(true); // 验证创建成功
    }

    #[tokio::test]
    async fn test_create_install_script() {
        let installer = MacOSServiceInstaller::new();
        let config = create_test_install_config();
        
        let temp_dir = TempDir::new().unwrap();
        let script_path = temp_dir.path().join("install.sh");
        
        let result = installer.create_install_script(&config, &script_path).await;
        assert!(result.is_ok());
        
        // 验证脚本文件存在
        assert!(script_path.exists());
        
        // 验证脚本内容
        let script_content = tokio::fs::read_to_string(&script_path).await.unwrap();
        assert!(script_content.contains("com.test.daemon"));
        assert!(script_content.contains("launchctl load"));
    }

    #[tokio::test]
    async fn test_create_uninstall_script() {
        let installer = MacOSServiceInstaller::new();
        
        let temp_dir = TempDir::new().unwrap();
        let script_path = temp_dir.path().join("uninstall.sh");
        
        let result = installer.create_uninstall_script("com.test.daemon", &script_path).await;
        assert!(result.is_ok());
        
        // 验证脚本文件存在
        assert!(script_path.exists());
        
        // 验证脚本内容
        let script_content = tokio::fs::read_to_string(&script_path).await.unwrap();
        assert!(script_content.contains("com.test.daemon"));
        assert!(script_content.contains("launchctl unload"));
    }

    #[test]
    fn test_get_user_launch_agents_dir() {
        let installer = MacOSServiceInstaller::new();
        
        // 在测试环境中设置 HOME 环境变量
        std::env::set_var("HOME", "/Users/<USER>");
        
        let result = installer.get_user_launch_agents_dir();
        assert!(result.is_ok());
        
        let dir = result.unwrap();
        assert_eq!(dir, PathBuf::from("/Users/<USER>/Library/LaunchAgents"));
    }

    #[test]
    fn test_check_root_permissions() {
        let installer = MacOSServiceInstaller::new();
        
        // 在测试环境中通常不是 root 用户
        let result = installer.check_root_permissions();
        // 根据实际运行环境，这个测试可能成功或失败
        assert!(result.is_ok() || result.is_err());
    }
} 