//! macOS plist 文件管理器
//! 
//! 负责创建和管理 LaunchDaemon 的 plist 配置文件

use std::collections::HashMap;
use std::path::PathBuf;
use serde::{Serialize, Deserialize};
use crate::error::DaemonError;
use super::super::{ServiceConfig, ServiceError};

/// LaunchDaemon plist 数据结构
/// 
/// 参考 Apple 官方文档: https://developer.apple.com/library/archive/documentation/MacOSX/Conceptual/BPSystemStartup/Chapters/CreatingLaunchdJobs.html
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LaunchDaemonPlist {
    /// 服务标识符 (必需)
    #[serde(rename = "Label")]
    pub label: String,

    /// 可执行程序路径 (二选一: Program 或 ProgramArguments)
    #[serde(rename = "Program", skip_serializing_if = "Option::is_none")]
    pub program: Option<String>,

    /// 可执行程序及参数 (二选一: Program 或 ProgramArguments)
    #[serde(rename = "ProgramArguments", skip_serializing_if = "Option::is_none")]
    pub program_arguments: Option<Vec<String>>,

    /// 启动时自动运行
    #[serde(rename = "RunAtLoad", skip_serializing_if = "Option::is_none")]
    pub run_at_load: Option<bool>,

    /// 工作目录
    #[serde(rename = "WorkingDirectory", skip_serializing_if = "Option::is_none")]
    pub working_directory: Option<String>,

    /// 环境变量
    #[serde(rename = "EnvironmentVariables", skip_serializing_if = "Option::is_none")]
    pub environment_variables: Option<HashMap<String, String>>,

    /// 运行用户
    #[serde(rename = "UserName", skip_serializing_if = "Option::is_none")]
    pub user_name: Option<String>,

    /// 运行组
    #[serde(rename = "GroupName", skip_serializing_if = "Option::is_none")]
    pub group_name: Option<String>,

    /// 标准输出重定向
    #[serde(rename = "StandardOutPath", skip_serializing_if = "Option::is_none")]
    pub standard_out_path: Option<String>,

    /// 标准错误重定向
    #[serde(rename = "StandardErrorPath", skip_serializing_if = "Option::is_none")]
    pub standard_error_path: Option<String>,

    /// 保持运行 (进程退出后自动重启)
    #[serde(rename = "KeepAlive", skip_serializing_if = "Option::is_none")]
    pub keep_alive: Option<bool>,

    /// 保持运行条件
    #[serde(rename = "KeepAliveConditions", skip_serializing_if = "Option::is_none")]
    pub keep_alive_conditions: Option<KeepAliveConditions>,

    /// 启动间隔 (秒)
    #[serde(rename = "StartInterval", skip_serializing_if = "Option::is_none")]
    pub start_interval: Option<u64>,

    /// 启动日历 (定时任务)
    #[serde(rename = "StartCalendarInterval", skip_serializing_if = "Option::is_none")]
    pub start_calendar_interval: Option<Vec<CalendarInterval>>,

    /// 监控文件路径
    #[serde(rename = "WatchPaths", skip_serializing_if = "Option::is_none")]
    pub watch_paths: Option<Vec<String>>,

    /// 监控队列目录
    #[serde(rename = "QueueDirectories", skip_serializing_if = "Option::is_none")]
    pub queue_directories: Option<Vec<String>>,

    /// 软资源限制
    #[serde(rename = "SoftResourceLimits", skip_serializing_if = "Option::is_none")]
    pub soft_resource_limits: Option<HashMap<String, u64>>,

    /// 硬资源限制
    #[serde(rename = "HardResourceLimits", skip_serializing_if = "Option::is_none")]
    pub hard_resource_limits: Option<HashMap<String, u64>>,

    /// 进程类型
    #[serde(rename = "ProcessType", skip_serializing_if = "Option::is_none")]
    pub process_type: Option<String>,

    /// 网络状态
    #[serde(rename = "NetworkState", skip_serializing_if = "Option::is_none")]
    pub network_state: Option<String>,

    /// 禁用进程控制组
    #[serde(rename = "LaunchdJoinProcessGroup", skip_serializing_if = "Option::is_none")]
    pub launchd_join_process_group: Option<bool>,

    /// 退出超时时间
    #[serde(rename = "ExitTimeOut", skip_serializing_if = "Option::is_none")]
    pub exit_timeout: Option<u64>,

    /// 成功退出代码
    #[serde(rename = "SuccessfulExitCodes", skip_serializing_if = "Option::is_none")]
    pub successful_exit_codes: Option<Vec<i32>>,
}

/// 保持运行条件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KeepAliveConditions {
    /// 文件系统路径存在时保持运行
    #[serde(rename = "PathState", skip_serializing_if = "Option::is_none")]
    pub path_state: Option<HashMap<String, bool>>,

    /// 其他条件可以根据需要添加
    #[serde(rename = "NetworkState", skip_serializing_if = "Option::is_none")]
    pub network_state: Option<String>,
}

/// 日历间隔配置 (用于定时任务)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CalendarInterval {
    /// 分钟 (0-59)
    #[serde(rename = "Minute", skip_serializing_if = "Option::is_none")]
    pub minute: Option<u8>,

    /// 小时 (0-23)
    #[serde(rename = "Hour", skip_serializing_if = "Option::is_none")]
    pub hour: Option<u8>,

    /// 日期 (1-31)
    #[serde(rename = "Day", skip_serializing_if = "Option::is_none")]
    pub day: Option<u8>,

    /// 月份 (1-12)
    #[serde(rename = "Month", skip_serializing_if = "Option::is_none")]
    pub month: Option<u8>,

    /// 星期 (0-7, 0和7都表示星期日)
    #[serde(rename = "Weekday", skip_serializing_if = "Option::is_none")]
    pub weekday: Option<u8>,
}

/// plist 文件管理器
pub struct PlistManager;

impl PlistManager {
    /// 创建新的 plist 管理器
    pub fn new() -> Self {
        Self
    }

    /// 创建 LaunchDaemon plist 配置
    /// 
    /// # 参数
    /// - `config`: 服务配置
    /// 
    /// # 返回
    /// - `Ok(String)`: plist XML 内容
    /// - `Err(DaemonError)`: 创建失败
    pub fn create_daemon_plist(&self, config: &ServiceConfig) -> Result<String, DaemonError> {
        let plist = LaunchDaemonPlist {
            label: config.service_name.clone(),
            program: None, // 使用 program_arguments 替代
            program_arguments: Some({
                let mut args = vec![config.executable_path.to_string_lossy().to_string()];
                args.extend(config.arguments.clone());
                args
            }),
            run_at_load: Some(config.auto_start),
            working_directory: config.working_directory
                .as_ref()
                .map(|p| p.to_string_lossy().to_string()),
            environment_variables: if config.environment.is_empty() {
                None
            } else {
                Some(config.environment.clone())
            },
            user_name: config.run_as_user.clone(),
            group_name: config.run_as_group.clone(),
            standard_out_path: Some(format!("/var/log/{}.out.log", config.service_name)),
            standard_error_path: Some(format!("/var/log/{}.err.log", config.service_name)),
            keep_alive: Some(true), // 守护进程通常需要保持运行
            keep_alive_conditions: None,
            start_interval: None,
            start_calendar_interval: None,
            watch_paths: None,
            queue_directories: None,
            soft_resource_limits: None,
            hard_resource_limits: None,
            process_type: Some("Interactive".to_string()), // 或 "Background"
            network_state: None,
            launchd_join_process_group: Some(true),
            exit_timeout: Some(30), // 30秒退出超时
            successful_exit_codes: Some(vec![0]), // 只有退出代码0被认为是成功
        };

        self.serialize_to_plist_xml(&plist)
    }

    /// 创建 LaunchAgent plist 配置 (用户级服务)
    /// 
    /// # 参数
    /// - `config`: 服务配置
    /// 
    /// # 返回
    /// - `Ok(String)`: plist XML 内容
    pub fn create_agent_plist(&self, config: &ServiceConfig) -> Result<String, DaemonError> {
        let plist = LaunchDaemonPlist {
            label: config.service_name.clone(),
            program: None,
            program_arguments: Some({
                let mut args = vec![config.executable_path.to_string_lossy().to_string()];
                args.extend(config.arguments.clone());
                args
            }),
            run_at_load: Some(config.auto_start),
            working_directory: config.working_directory
                .as_ref()
                .map(|p| p.to_string_lossy().to_string()),
            environment_variables: if config.environment.is_empty() {
                None
            } else {
                Some(config.environment.clone())
            },
            user_name: None, // LaunchAgent 在用户上下文中运行
            group_name: None,
            standard_out_path: Some(format!("/tmp/{}.out.log", config.service_name)),
            standard_error_path: Some(format!("/tmp/{}.err.log", config.service_name)),
            keep_alive: Some(true),
            keep_alive_conditions: None,
            start_interval: None,
            start_calendar_interval: None,
            watch_paths: None,
            queue_directories: None,
            soft_resource_limits: None,
            hard_resource_limits: None,
            process_type: Some("Interactive".to_string()),
            network_state: None,
            launchd_join_process_group: Some(true),
            exit_timeout: Some(30),
            successful_exit_codes: Some(vec![0]),
        };

        self.serialize_to_plist_xml(&plist)
    }

    /// 将 plist 结构序列化为 XML 格式
    /// 
    /// # 参数
    /// - `plist`: plist 数据结构
    /// 
    /// # 返回
    /// - `Ok(String)`: XML 格式的 plist 内容
    fn serialize_to_plist_xml(&self, plist: &LaunchDaemonPlist) -> Result<String, DaemonError> {
        // 使用 plist crate 进行序列化
        let mut xml_data = Vec::new();
        plist::to_writer_xml(&mut xml_data, plist)
            .map_err(|e| ServiceError::SerializationError(format!("plist 序列化失败: {}", e)))?;

        String::from_utf8(xml_data)
            .map_err(|e| ServiceError::SerializationError(format!("XML 转换失败: {}", e)).into())
    }

    /// 从 XML 内容解析 plist 结构
    /// 
    /// # 参数
    /// - `xml_content`: XML 格式的 plist 内容
    /// 
    /// # 返回
    /// - `Ok(LaunchDaemonPlist)`: 解析后的 plist 结构
    pub fn parse_from_xml(&self, xml_content: &str) -> Result<LaunchDaemonPlist, DaemonError> {
        plist::from_bytes(xml_content.as_bytes())
            .map_err(|e| ServiceError::SerializationError(format!("plist 解析失败: {}", e)).into())
    }

    /// 验证 plist 配置的有效性
    /// 
    /// # 参数
    /// - `plist`: 要验证的 plist 配置
    /// 
    /// # 返回
    /// - `Ok(())`: 配置有效
    /// - `Err(DaemonError)`: 配置无效
    pub fn validate_plist(&self, plist: &LaunchDaemonPlist) -> Result<(), DaemonError> {
        // 检查必需字段
        if plist.label.is_empty() {
            return Err(ServiceError::InvalidConfiguration("Label 不能为空".to_string()).into());
        }

        // 检查可执行程序配置
        if plist.program.is_none() && plist.program_arguments.is_none() {
            return Err(ServiceError::InvalidConfiguration(
                "必须指定 Program 或 ProgramArguments".to_string()
            ).into());
        }

        if let Some(ref args) = plist.program_arguments {
            if args.is_empty() {
                return Err(ServiceError::InvalidConfiguration(
                    "ProgramArguments 不能为空".to_string()
                ).into());
            }

            // 检查可执行文件是否存在
            let executable_path = PathBuf::from(&args[0]);
            if !executable_path.exists() {
                return Err(ServiceError::InvalidConfiguration(
                    format!("可执行文件不存在: {}", args[0])
                ).into());
            }
        }

        // 检查工作目录
        if let Some(ref working_dir) = plist.working_directory {
            let working_path = PathBuf::from(working_dir);
            if !working_path.exists() {
                return Err(ServiceError::InvalidConfiguration(
                    format!("工作目录不存在: {}", working_dir)
                ).into());
            }
        }

        // 检查用户名和组名 (如果指定)
        if let Some(ref user) = plist.user_name {
            if user.is_empty() {
                return Err(ServiceError::InvalidConfiguration("UserName 不能为空".to_string()).into());
            }
        }

        if let Some(ref group) = plist.group_name {
            if group.is_empty() {
                return Err(ServiceError::InvalidConfiguration("GroupName 不能为空".to_string()).into());
            }
        }

        Ok(())
    }

    /// 从服务配置创建并验证 plist
    /// 
    /// # 参数
    /// - `config`: 服务配置
    /// - `is_daemon`: 是否为 LaunchDaemon (否则为 LaunchAgent)
    /// 
    /// # 返回
    /// - `Ok(String)`: 有效的 plist XML 内容
    pub fn create_and_validate_plist(
        &self, 
        config: &ServiceConfig, 
        is_daemon: bool
    ) -> Result<String, DaemonError> {
        // 创建 plist
        let xml_content = if is_daemon {
            self.create_daemon_plist(config)?
        } else {
            self.create_agent_plist(config)?
        };

        // 解析并验证
        let plist = self.parse_from_xml(&xml_content)?;
        self.validate_plist(&plist)?;

        Ok(xml_content)
    }
}

impl Default for PlistManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;

    fn create_test_service_config() -> ServiceConfig {
        let mut environment = HashMap::new();
        environment.insert("PATH".to_string(), "/usr/bin:/bin".to_string());
        environment.insert("LOG_LEVEL".to_string(), "info".to_string());

        ServiceConfig {
            service_name: "com.test.daemon".to_string(),
            display_name: "Test Daemon".to_string(),
            description: "Test daemon service".to_string(),
            executable_path: PathBuf::from("/usr/local/bin/test-daemon"),
            working_directory: Some(PathBuf::from("/var/lib/test-daemon")),
            arguments: vec!["--daemon".to_string(), "--config=/etc/test-daemon.conf".to_string()],
            auto_start: true,
            dependencies: vec![],
            run_as_user: Some("daemon".to_string()),
            run_as_group: Some("daemon".to_string()),
            environment,
        }
    }

    #[test]
    fn test_plist_manager_creation() {
        let manager = PlistManager::new();
        assert!(true); // 简单验证创建成功
    }

    #[test]
    fn test_create_daemon_plist() {
        let manager = PlistManager::new();
        let config = create_test_service_config();
        
        let result = manager.create_daemon_plist(&config);
        assert!(result.is_ok());
        
        let xml_content = result.unwrap();
        assert!(xml_content.contains("com.test.daemon"));
        assert!(xml_content.contains("/usr/local/bin/test-daemon"));
        assert!(xml_content.contains("RunAtLoad"));
    }

    #[test]
    fn test_create_agent_plist() {
        let manager = PlistManager::new();
        let config = create_test_service_config();
        
        let result = manager.create_agent_plist(&config);
        assert!(result.is_ok());
        
        let xml_content = result.unwrap();
        assert!(xml_content.contains("com.test.daemon"));
        assert!(xml_content.contains("/usr/local/bin/test-daemon"));
    }

    #[test]
    fn test_plist_validation_empty_label() {
        let manager = PlistManager::new();
        let mut plist = LaunchDaemonPlist {
            label: "".to_string(), // 空标签应该导致验证失败
            program: None,
            program_arguments: Some(vec!["/bin/echo".to_string()]),
            run_at_load: Some(true),
            working_directory: None,
            environment_variables: None,
            user_name: None,
            group_name: None,
            standard_out_path: None,
            standard_error_path: None,
            keep_alive: None,
            keep_alive_conditions: None,
            start_interval: None,
            start_calendar_interval: None,
            watch_paths: None,
            queue_directories: None,
            soft_resource_limits: None,
            hard_resource_limits: None,
            process_type: None,
            network_state: None,
            launchd_join_process_group: None,
            exit_timeout: None,
            successful_exit_codes: None,
        };

        let result = manager.validate_plist(&plist);
        assert!(result.is_err());
    }

    #[test]
    fn test_plist_validation_no_program() {
        let manager = PlistManager::new();
        let plist = LaunchDaemonPlist {
            label: "com.test.service".to_string(),
            program: None,
            program_arguments: None, // 没有指定程序
            run_at_load: Some(true),
            working_directory: None,
            environment_variables: None,
            user_name: None,
            group_name: None,
            standard_out_path: None,
            standard_error_path: None,
            keep_alive: None,
            keep_alive_conditions: None,
            start_interval: None,
            start_calendar_interval: None,
            watch_paths: None,
            queue_directories: None,
            soft_resource_limits: None,
            hard_resource_limits: None,
            process_type: None,
            network_state: None,
            launchd_join_process_group: None,
            exit_timeout: None,
            successful_exit_codes: None,
        };

        let result = manager.validate_plist(&plist);
        assert!(result.is_err());
    }

    #[test]
    fn test_plist_roundtrip() {
        let manager = PlistManager::new();
        let config = create_test_service_config();
        
        // 创建 plist
        let xml_content = manager.create_daemon_plist(&config).unwrap();
        
        // 解析回来
        let parsed_plist = manager.parse_from_xml(&xml_content).unwrap();
        
        // 验证关键字段
        assert_eq!(parsed_plist.label, config.service_name);
        assert!(parsed_plist.program_arguments.is_some());
        
        let args = parsed_plist.program_arguments.unwrap();
        assert_eq!(args[0], config.executable_path.to_string_lossy());
    }
} 