//! macOS LaunchDaemon 服务管理器
//! 
//! 提供 macOS 系统 LaunchDaemon 服务的完整生命周期管理

use std::path::{Path, PathBuf};
use std::process::{Command, Stdio};
use async_trait::async_trait;
use tracing::{info, warn, error, debug};
use chrono::{DateTime, Utc};

use crate::error::DaemonError;
use super::super::{
    PlatformServiceManager, ServiceStatus, ServiceConfig, ServiceInstallConfig, 
    ServiceInfo, ServiceError
};
use super::plist_manager::PlistManager;

/// macOS LaunchDaemon 服务管理器
/// 
/// 负责 macOS 系统下的 LaunchDaemon 服务管理
pub struct MacOSServiceManager {
    /// 服务名称
    service_name: String,
    /// LaunchDaemon plist 文件路径
    plist_path: PathBuf,
    /// Plist 管理器
    plist_manager: PlistManager,
}

impl MacOSServiceManager {
    /// 创建新的 macOS 服务管理器
    /// 
    /// # 参数
    /// - `service_name`: 服务名称，将用作 LaunchDaemon 标识符
    /// 
    /// # 示例
    /// ```rust
    /// let manager = MacOSServiceManager::new("com.securepassword.daemon".to_string());
    /// ```
    pub fn new(service_name: String) -> Self {
        let plist_path = Self::get_launchd_plist_path(&service_name);
        let plist_manager = PlistManager::new();

        Self {
            service_name,
            plist_path,
            plist_manager,
        }
    }

    /// 获取 LaunchDaemon plist 文件路径
    /// 
    /// LaunchDaemon plist 文件存储在 `/Library/LaunchDaemons/` 目录
    fn get_launchd_plist_path(service_name: &str) -> PathBuf {
        PathBuf::from("/Library/LaunchDaemons").join(format!("{}.plist", service_name))
    }

    /// 获取 LaunchAgent plist 文件路径 (用户级服务)
    /// 
    /// LaunchAgent plist 文件存储在 `~/Library/LaunchAgents/` 目录
    fn get_launch_agent_plist_path(service_name: &str) -> Result<PathBuf, DaemonError> {
        let home_dir = std::env::var("HOME")
            .map_err(|_| DaemonError::InternalError("无法获取用户主目录".to_string()))?;
        
        Ok(PathBuf::from(home_dir)
            .join("Library")
            .join("LaunchAgents")
            .join(format!("{}.plist", service_name)))
    }

    /// 执行 launchctl 命令
    /// 
    /// # 参数
    /// - `args`: launchctl 命令参数
    /// 
    /// # 返回
    /// - `Ok(String)`: 命令输出
    /// - `Err(DaemonError)`: 命令执行失败
    async fn execute_launchctl(&self, args: &[&str]) -> Result<String, DaemonError> {
        debug!("执行 launchctl 命令: {:?}", args);

        let output = tokio::process::Command::new("launchctl")
            .args(args)
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .output()
            .await
            .map_err(|e| DaemonError::InternalError(format!("launchctl 命令执行失败: {}", e)))?;

        if output.status.success() {
            let stdout = String::from_utf8_lossy(&output.stdout).to_string();
            debug!("launchctl 命令输出: {}", stdout);
            Ok(stdout)
        } else {
            let stderr = String::from_utf8_lossy(&output.stderr).to_string();
            error!("launchctl 命令失败: {}", stderr);
            Err(DaemonError::InternalError(format!("launchctl 命令失败: {}", stderr)))
        }
    }

    /// 检查服务是否被 launchctl 加载
    async fn is_service_loaded(&self) -> Result<bool, DaemonError> {
        let output = self.execute_launchctl(&["list"]).await?;
        Ok(output.contains(&self.service_name))
    }

    /// 检查服务是否正在运行
    async fn is_service_running(&self) -> Result<bool, DaemonError> {
        let output = self.execute_launchctl(&["list", &self.service_name]).await;
        
        match output {
            Ok(content) => {
                // 检查输出中是否包含 PID 信息
                Ok(content.contains("\"PID\"") && !content.contains("\"PID\" = 0"))
            }
            Err(_) => Ok(false), // 服务未加载或不存在
        }
    }

    /// 加载服务到 launchctl
    async fn load_service(&self) -> Result<(), DaemonError> {
        info!("加载服务到 launchctl: {}", self.service_name);
        
        let plist_path_str = self.plist_path.to_string_lossy();
        self.execute_launchctl(&["load", &plist_path_str]).await?;
        
        Ok(())
    }

    /// 从 launchctl 卸载服务
    async fn unload_service(&self) -> Result<(), DaemonError> {
        info!("从 launchctl 卸载服务: {}", self.service_name);
        
        let plist_path_str = self.plist_path.to_string_lossy();
        self.execute_launchctl(&["unload", &plist_path_str]).await?;
        
        Ok(())
    }

    /// 启动服务 (通过 launchctl)
    async fn start_service_via_launchctl(&self) -> Result<(), DaemonError> {
        info!("通过 launchctl 启动服务: {}", self.service_name);
        
        self.execute_launchctl(&["start", &self.service_name]).await?;
        
        Ok(())
    }

    /// 停止服务 (通过 launchctl)
    async fn stop_service_via_launchctl(&self) -> Result<(), DaemonError> {
        info!("通过 launchctl 停止服务: {}", self.service_name);
        
        self.execute_launchctl(&["stop", &self.service_name]).await?;
        
        Ok(())
    }

    /// 获取服务详细信息
    async fn get_service_details(&self) -> Result<serde_json::Value, DaemonError> {
        let output = self.execute_launchctl(&["list", &self.service_name]).await?;
        
        // 解析 launchctl list 输出 (通常是类似 plist 的格式)
        // 这里简化处理，实际实现可能需要更复杂的解析
        serde_json::from_str(&output)
            .map_err(|e| DaemonError::InternalError(format!("解析服务信息失败: {}", e)))
    }

    /// 等待服务状态变化
    /// 
    /// # 参数
    /// - `expected_status`: 期望的服务状态
    /// - `timeout_secs`: 超时时间 (秒)
    async fn wait_for_status_change(
        &self, 
        expected_status: ServiceStatus,
        timeout_secs: u64
    ) -> Result<(), DaemonError> {
        let start_time = std::time::Instant::now();
        let timeout = std::time::Duration::from_secs(timeout_secs);

        while start_time.elapsed() < timeout {
            let current_status = self.get_service_status().await?;
            if current_status == expected_status {
                return Ok(());
            }

            tokio::time::sleep(std::time::Duration::from_millis(100)).await;
        }

        Err(DaemonError::InternalError(format!(
            "等待服务状态变化超时: 期望 {:?}, 超时 {} 秒", 
            expected_status, 
            timeout_secs
        )))
    }
}

#[async_trait]
impl PlatformServiceManager for MacOSServiceManager {
    async fn install_service(&self, config: &ServiceInstallConfig) -> Result<(), DaemonError> {
        info!("安装 macOS LaunchDaemon 服务: {}", self.service_name);

        // 检查权限
        if !self.check_permissions()? {
            return Err(DaemonError::PermissionError(
                "需要管理员权限安装 LaunchDaemon 服务".to_string()
            ));
        }

        // 检查服务是否已存在
        if self.is_service_installed().await? && !config.force_overwrite {
            return Err(ServiceError::ServiceAlreadyExists.into());
        }

        // 创建 plist 文件
        let plist_data = self.plist_manager.create_daemon_plist(&config.service_config)?;
        
        // 写入 plist 文件
        tokio::fs::write(&self.plist_path, plist_data).await
            .map_err(|e| DaemonError::FileSystemError(format!("写入 plist 文件失败: {}", e)))?;

        info!("plist 文件已创建: {:?}", self.plist_path);

        // 设置文件权限
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;
            let mut perms = tokio::fs::metadata(&self.plist_path).await
                .map_err(|e| DaemonError::FileSystemError(format!("获取文件权限失败: {}", e)))?
                .permissions();
            perms.set_mode(0o644); // rw-r--r--
            tokio::fs::set_permissions(&self.plist_path, perms).await
                .map_err(|e| DaemonError::FileSystemError(format!("设置文件权限失败: {}", e)))?;
        }

        // 加载服务到 launchctl
        self.load_service().await?;

        // 如果配置了立即启动，则启动服务
        if config.start_immediately {
            self.start_service().await?;
        }

        info!("macOS LaunchDaemon 服务安装完成: {}", self.service_name);
        Ok(())
    }

    async fn uninstall_service(&self) -> Result<(), DaemonError> {
        info!("卸载 macOS LaunchDaemon 服务: {}", self.service_name);

        // 检查权限
        if !self.check_permissions()? {
            return Err(DaemonError::PermissionError(
                "需要管理员权限卸载 LaunchDaemon 服务".to_string()
            ));
        }

        // 停止服务 (如果正在运行)
        if self.is_service_running().await? {
            self.stop_service().await?;
        }

        // 从 launchctl 卸载服务
        if self.is_service_loaded().await? {
            self.unload_service().await?;
        }

        // 删除 plist 文件
        if self.plist_path.exists() {
            tokio::fs::remove_file(&self.plist_path).await
                .map_err(|e| DaemonError::FileSystemError(format!("删除 plist 文件失败: {}", e)))?;
            info!("plist 文件已删除: {:?}", self.plist_path);
        }

        info!("macOS LaunchDaemon 服务卸载完成: {}", self.service_name);
        Ok(())
    }

    async fn start_service(&self) -> Result<(), DaemonError> {
        info!("启动 macOS LaunchDaemon 服务: {}", self.service_name);

        // 检查服务是否已安装
        if !self.is_service_installed().await? {
            return Err(ServiceError::ServiceNotInstalled.into());
        }

        // 如果服务未加载，先加载
        if !self.is_service_loaded().await? {
            self.load_service().await?;
        }

        // 启动服务
        self.start_service_via_launchctl().await?;

        // 等待服务启动
        self.wait_for_status_change(ServiceStatus::Running, 10).await?;

        info!("macOS LaunchDaemon 服务启动完成: {}", self.service_name);
        Ok(())
    }

    async fn stop_service(&self) -> Result<(), DaemonError> {
        info!("停止 macOS LaunchDaemon 服务: {}", self.service_name);

        // 检查服务是否正在运行
        if !self.is_service_running().await? {
            warn!("服务未运行: {}", self.service_name);
            return Ok(());
        }

        // 停止服务
        self.stop_service_via_launchctl().await?;

        // 等待服务停止
        self.wait_for_status_change(ServiceStatus::Stopped, 10).await?;

        info!("macOS LaunchDaemon 服务停止完成: {}", self.service_name);
        Ok(())
    }

    async fn restart_service(&self) -> Result<(), DaemonError> {
        info!("重启 macOS LaunchDaemon 服务: {}", self.service_name);

        // 先停止服务
        self.stop_service().await?;

        // 等待一段时间确保服务完全停止
        tokio::time::sleep(std::time::Duration::from_millis(500)).await;

        // 再启动服务
        self.start_service().await?;

        info!("macOS LaunchDaemon 服务重启完成: {}", self.service_name);
        Ok(())
    }

    async fn get_service_status(&self) -> Result<ServiceStatus, DaemonError> {
        // 检查服务是否已安装
        if !self.is_service_installed().await? {
            return Ok(ServiceStatus::NotInstalled);
        }

        // 检查服务是否已加载
        if !self.is_service_loaded().await? {
            return Ok(ServiceStatus::Stopped);
        }

        // 检查服务是否正在运行
        if self.is_service_running().await? {
            Ok(ServiceStatus::Running)
        } else {
            Ok(ServiceStatus::Stopped)
        }
    }

    async fn get_service_info(&self) -> Result<ServiceInfo, DaemonError> {
        let status = self.get_service_status().await?;
        
        // 读取 plist 文件获取配置信息
        let plist_content = if self.plist_path.exists() {
            tokio::fs::read_to_string(&self.plist_path).await
                .map_err(|e| DaemonError::FileSystemError(format!("读取 plist 文件失败: {}", e)))?
        } else {
            return Err(ServiceError::ServiceNotInstalled.into());
        };

        let plist_data: serde_json::Value = serde_json::from_str(&plist_content)
            .map_err(|e| DaemonError::InternalError(format!("解析 plist 文件失败: {}", e)))?;

        // 获取进程 ID (如果运行中)
        let process_id = if status == ServiceStatus::Running {
            if let Ok(details) = self.get_service_details().await {
                details.get("PID")
                    .and_then(|pid| pid.as_u64())
                    .map(|pid| pid as u32)
            } else {
                None
            }
        } else {
            None
        };

        Ok(ServiceInfo {
            name: self.service_name.clone(),
            display_name: plist_data.get("Label")
                .and_then(|v| v.as_str())
                .unwrap_or(&self.service_name)
                .to_string(),
            status,
            process_id,
            start_time: None, // LaunchDaemon 通常不提供启动时间信息
            auto_start: plist_data.get("RunAtLoad")
                .and_then(|v| v.as_bool())
                .unwrap_or(false),
            executable_path: plist_data.get("Program")
                .and_then(|v| v.as_str())
                .map(PathBuf::from)
                .unwrap_or_default(),
        })
    }

    async fn enable_auto_start(&self) -> Result<(), DaemonError> {
        info!("启用 macOS LaunchDaemon 自动启动: {}", self.service_name);

        // LaunchDaemon 通过 plist 文件中的 RunAtLoad 属性控制自动启动
        // 如果需要修改，需要重新生成和加载 plist 文件
        // 这里假设在安装时已经正确配置了 RunAtLoad 属性

        // 检查服务是否已加载
        if !self.is_service_loaded().await? {
            self.load_service().await?;
        }

        Ok(())
    }

    async fn disable_auto_start(&self) -> Result<(), DaemonError> {
        info!("禁用 macOS LaunchDaemon 自动启动: {}", self.service_name);

        // 从 launchctl 卸载服务即可禁用自动启动
        if self.is_service_loaded().await? {
            self.unload_service().await?;
        }

        Ok(())
    }

    async fn is_service_installed(&self) -> Result<bool, DaemonError> {
        Ok(self.plist_path.exists())
    }

    fn check_permissions(&self) -> Result<bool, DaemonError> {
        // 检查是否为 root 用户或具有 sudo 权限
        let euid = unsafe { libc::geteuid() };
        Ok(euid == 0)
    }

    async fn get_service_logs(&self, lines: usize) -> Result<Vec<String>, DaemonError> {
        info!("获取 macOS LaunchDaemon 服务日志: {} (最近 {} 行)", self.service_name, lines);

        // macOS 系统日志通常通过 log 命令或 Console.app 查看
        // 这里使用 log 命令获取相关日志
        let output = tokio::process::Command::new("log")
            .args(&[
                "show",
                "--predicate", &format!("subsystem CONTAINS \"{}\"", self.service_name),
                "--info",
                "--last", &format!("{}m", lines * 2), // 估算时间范围
            ])
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .output()
            .await
            .map_err(|e| DaemonError::InternalError(format!("获取系统日志失败: {}", e)))?;

        if output.status.success() {
            let log_content = String::from_utf8_lossy(&output.stdout);
            let log_lines: Vec<String> = log_content
                .lines()
                .rev() // 反转以获取最新的日志
                .take(lines)
                .map(|line| line.to_string())
                .collect::<Vec<_>>()
                .into_iter()
                .rev() // 再次反转以恢复正确顺序
                .collect();

            Ok(log_lines)
        } else {
            let stderr = String::from_utf8_lossy(&output.stderr);
            warn!("获取系统日志失败: {}", stderr);
            Ok(vec![])
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_macos_service_manager_creation() {
        let manager = MacOSServiceManager::new("com.test.daemon".to_string());
        assert_eq!(manager.service_name, "com.test.daemon");
        assert!(manager.plist_path.to_string_lossy().contains("com.test.daemon.plist"));
    }

    #[tokio::test]
    async fn test_launchd_plist_path() {
        let path = MacOSServiceManager::get_launchd_plist_path("com.test.service");
        assert_eq!(path, PathBuf::from("/Library/LaunchDaemons/com.test.service.plist"));
    }

    #[tokio::test]
    async fn test_service_status_not_installed() {
        let manager = MacOSServiceManager::new("com.nonexistent.service".to_string());
        let status = manager.get_service_status().await.unwrap();
        assert_eq!(status, ServiceStatus::NotInstalled);
    }

    #[tokio::test]
    async fn test_is_service_installed_false() {
        let manager = MacOSServiceManager::new("com.nonexistent.service".to_string());
        let installed = manager.is_service_installed().await.unwrap();
        assert!(!installed);
    }

    #[tokio::test]
    async fn test_check_permissions() {
        let manager = MacOSServiceManager::new("com.test.service".to_string());
        let has_permissions = manager.check_permissions();
        // 测试环境中通常不是 root 用户
        assert!(has_permissions.is_ok());
    }
} 