//! 平台特定实现模块

use std::path::PathBuf;
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use crate::error::DaemonError;

#[cfg(windows)]
pub mod windows;

#[cfg(target_os = "macos")]
pub mod macos;

#[cfg(target_os = "linux")]
pub mod linux;

// 重新导出平台特定模块
#[cfg(windows)]
pub use windows::*;

#[cfg(target_os = "macos")]
pub use macos::*;

#[cfg(target_os = "linux")]
pub use linux::*;

/// 服务状态枚举
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ServiceStatus {
    /// 服务未安装
    NotInstalled,
    /// 服务已停止
    Stopped,
    /// 服务正在启动
    Starting,
    /// 服务正在运行
    Running,
    /// 服务正在停止
    Stopping,
    /// 服务异常状态
    Error(String),
    /// 未知状态
    Unknown,
}

/// 服务配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceConfig {
    /// 服务名称
    pub service_name: String,
    /// 服务显示名称
    pub display_name: String,
    /// 服务描述
    pub description: String,
    /// 可执行文件路径
    pub executable_path: PathBuf,
    /// 工作目录
    pub working_directory: Option<PathBuf>,
    /// 启动参数
    pub arguments: Vec<String>,
    /// 是否自动启动
    pub auto_start: bool,
    /// 服务依赖
    pub dependencies: Vec<String>,
    /// 运行用户 (Unix系统)
    pub run_as_user: Option<String>,
    /// 运行组 (Unix系统)
    pub run_as_group: Option<String>,
    /// 环境变量
    pub environment: std::collections::HashMap<String, String>,
}

/// 服务安装配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceInstallConfig {
    /// 基础服务配置
    pub service_config: ServiceConfig,
    /// 安装路径
    pub install_path: PathBuf,
    /// 是否立即启动
    pub start_immediately: bool,
    /// 是否覆盖已存在的服务
    pub force_overwrite: bool,
}

/// 服务信息结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceInfo {
    /// 服务名称
    pub name: String,
    /// 显示名称
    pub display_name: String,
    /// 服务状态
    pub status: ServiceStatus,
    /// 进程 ID (如果运行中)
    pub process_id: Option<u32>,
    /// 启动时间
    pub start_time: Option<chrono::DateTime<chrono::Utc>>,
    /// 是否自动启动
    pub auto_start: bool,
    /// 可执行文件路径
    pub executable_path: PathBuf,
}

/// 平台系统服务管理器接口
/// 
/// 提供跨平台系统服务管理的统一接口
#[async_trait]
pub trait PlatformServiceManager: Send + Sync {
    /// 安装系统服务
    /// 
    /// 在目标平台上安装守护进程作为系统服务
    /// 
    /// # 参数
    /// - `config`: 服务安装配置
    /// 
    /// # 返回
    /// - `Ok(())`: 安装成功
    /// - `Err(DaemonError)`: 安装失败
    /// 
    /// # 平台支持
    /// - Windows: Windows Service
    /// - macOS: LaunchDaemon
    /// - Linux: systemd service
    /// 
    /// # 权限要求
    /// - Windows: 管理员权限
    /// - macOS: sudo 权限  
    /// - Linux: root 权限或 systemd 用户权限
    async fn install_service(&self, config: &ServiceInstallConfig) -> Result<(), DaemonError>;

    /// 卸载系统服务
    /// 
    /// 从系统中完全移除守护进程服务
    /// 
    /// # 返回
    /// - `Ok(())`: 卸载成功
    /// - `Err(DaemonError)`: 卸载失败
    async fn uninstall_service(&self) -> Result<(), DaemonError>;

    /// 启动系统服务
    /// 
    /// 启动已安装的系统服务
    async fn start_service(&self) -> Result<(), DaemonError>;

    /// 停止系统服务
    /// 
    /// 停止运行中的系统服务
    async fn stop_service(&self) -> Result<(), DaemonError>;

    /// 重启系统服务
    /// 
    /// 重新启动系统服务
    async fn restart_service(&self) -> Result<(), DaemonError>;

    /// 获取服务状态
    /// 
    /// 查询系统服务的当前状态
    /// 
    /// # 返回状态
    /// - `ServiceStatus::Running`: 服务正在运行
    /// - `ServiceStatus::Stopped`: 服务已停止
    /// - `ServiceStatus::Starting`: 服务正在启动
    /// - `ServiceStatus::Stopping`: 服务正在停止
    /// - `ServiceStatus::Error(String)`: 服务异常
    async fn get_service_status(&self) -> Result<ServiceStatus, DaemonError>;

    /// 获取服务详细信息
    /// 
    /// 获取服务的详细配置和运行时信息
    async fn get_service_info(&self) -> Result<ServiceInfo, DaemonError>;

    /// 配置自动启动
    /// 
    /// 设置服务开机自动启动
    async fn enable_auto_start(&self) -> Result<(), DaemonError>;

    /// 禁用自动启动
    /// 
    /// 禁用服务开机自动启动
    async fn disable_auto_start(&self) -> Result<(), DaemonError>;

    /// 检查服务是否已安装
    /// 
    /// 检查指定服务是否已经安装在系统中
    async fn is_service_installed(&self) -> Result<bool, DaemonError>;

    /// 检查当前用户权限
    /// 
    /// 检查当前用户是否有足够权限管理系统服务
    fn check_permissions(&self) -> Result<bool, DaemonError>;

    /// 获取服务日志
    /// 
    /// 获取系统服务的日志信息
    /// 
    /// # 参数
    /// - `lines`: 获取最近的行数
    async fn get_service_logs(&self, lines: usize) -> Result<Vec<String>, DaemonError>;
}

/// 服务管理器工厂
/// 
/// 根据当前平台创建相应的服务管理器实例
pub struct ServiceManagerFactory;

impl ServiceManagerFactory {
    /// 创建平台特定的服务管理器
    /// 
    /// # 参数
    /// - `service_name`: 服务名称
    /// 
    /// # 返回
    /// 平台特定的服务管理器实现
    pub fn create(service_name: String) -> Box<dyn PlatformServiceManager> {
        #[cfg(windows)]
        {
            Box::new(windows::WindowsServiceManager::new(service_name))
        }

        #[cfg(target_os = "macos")]
        {
            Box::new(macos::MacOSServiceManager::new(service_name))
        }

        #[cfg(target_os = "linux")]
        {
            Box::new(linux::LinuxServiceManager::new(service_name))
        }

        #[cfg(not(any(windows, target_os = "macos", target_os = "linux")))]
        {
            compile_error!("Unsupported platform for system service management")
        }
    }

    /// 检测当前平台
    pub fn detect_platform() -> String {
        #[cfg(windows)]
        return "windows".to_string();

        #[cfg(target_os = "macos")]
        return "macos".to_string();

        #[cfg(target_os = "linux")]
        return "linux".to_string();

        #[cfg(not(any(windows, target_os = "macos", target_os = "linux")))]
        return "unknown".to_string();
    }

    /// 检查平台服务支持
    pub fn is_service_supported() -> bool {
        cfg!(any(windows, target_os = "macos", target_os = "linux"))
    }
}

/// 服务操作错误类型
#[derive(Debug, thiserror::Error)]
pub enum ServiceError {
    #[error("服务未安装")]
    ServiceNotInstalled,

    #[error("服务已存在")]
    ServiceAlreadyExists,

    #[error("权限不足")]
    InsufficientPermissions,

    #[error("平台不支持")]
    PlatformNotSupported,

    #[error("服务配置无效: {0}")]
    InvalidConfiguration(String),

    #[error("系统操作失败: {0}")]
    SystemOperationFailed(String),

    #[error("IO 操作失败: {0}")]
    IoError(#[from] std::io::Error),

    #[error("序列化失败: {0}")]
    SerializationError(String),
}

/// 将 ServiceError 转换为 DaemonError
impl From<ServiceError> for DaemonError {
    fn from(error: ServiceError) -> Self {
        match error {
            ServiceError::ServiceNotInstalled => DaemonError::ServiceStartError("服务未安装".to_string()),
            ServiceError::ServiceAlreadyExists => DaemonError::ServiceInstallError("服务已存在".to_string()),
            ServiceError::InsufficientPermissions => DaemonError::PermissionError("权限不足".to_string()),
            ServiceError::PlatformNotSupported => DaemonError::InternalError("平台不支持".to_string()),
            ServiceError::InvalidConfiguration(msg) => DaemonError::ConfigValidationError(msg),
            ServiceError::SystemOperationFailed(msg) => DaemonError::InternalError(msg),
            ServiceError::IoError(err) => DaemonError::FileSystemError(err.to_string()),
            ServiceError::SerializationError(msg) => DaemonError::ConfigSerializeError(msg),
        }
    }
}
