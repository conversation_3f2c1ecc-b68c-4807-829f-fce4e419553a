//! # Native Messaging 代理模块
//! 
//! 这个模块实现了 Native Messaging Host，负责处理浏览器扩展的请求
//! 并将其代理到守护进程的主服务。
//! 
//! ## 主要组件
//! 
//! - `host`: Native Messaging Host 主实现
//! - `proxy`: 请求代理和转发机制
//! - `browser`: 浏览器适配层
//! - `protocol`: 协议实现和消息格式
//! - `handlers`: 消息处理器
//! - `registry`: 浏览器注册管理
//! 
//! ## 使用示例
//! 
//! ```rust
//! use native_messaging::{NativeMessagingHost, HostConfig};
//! 
//! #[tokio::main]
//! async fn main() -> Result<(), Box<dyn std::error::Error>> {
//!     let config = HostConfig::default();
//!     let mut host = NativeMessagingHost::new(config).await?;
//!     host.start().await?;
//!     Ok(())
//! }
//! ```

pub mod host;
pub mod proxy;
pub mod browser;
pub mod protocol;
pub mod handlers;
pub mod registry;
pub mod error;

// 重新导出主要类型
pub use host::{NativeMessagingHost, HostConfig};
pub use proxy::{RequestProxy, ProxyConfig};
pub use browser::{BrowserType, BrowserAdapter, BrowserRegistry};
pub use protocol::{NativeMessage, MessageType, ProtocolVersion};
pub use handlers::{MessageHandler, HandlerRegistry};
pub use registry::{RegistryManager, HostRegistration};
pub use error::{NativeMessagingError, Result};

/// Native Messaging 模块版本
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

/// 默认的 Native Messaging Host 名称
pub const DEFAULT_HOST_NAME: &str = "com.securepassword.host";

/// 支持的协议版本
pub const SUPPORTED_PROTOCOL_VERSIONS: &[u32] = &[1, 2];

/// 最大消息大小 (1MB)
pub const MAX_MESSAGE_SIZE: usize = 1024 * 1024;

/// 默认请求超时时间 (30秒)
pub const DEFAULT_REQUEST_TIMEOUT: std::time::Duration = std::time::Duration::from_secs(30);
