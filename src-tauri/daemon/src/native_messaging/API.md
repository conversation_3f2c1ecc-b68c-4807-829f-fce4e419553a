# Native Messaging API 文档

## 概述

本文档描述了Native Messaging模块的完整API接口，包括所有公共类型、方法和配置选项。

## 核心类型

### NativeMessage

Native Messaging协议的核心消息类型。

```rust
pub struct NativeMessage {
    pub version: u32,
    pub message_type: String,
    pub request_id: String,
    pub payload: serde_json::Value,
    pub timestamp: u64,
    pub source: String,
    pub target: Option<String>,
    pub signature: Option<String>,
    pub extensions: HashMap<String, serde_json::Value>,
}
```

#### 方法

##### 构造方法

```rust
impl NativeMessage {
    // 创建请求消息
    pub fn new_request(
        request_id: String,
        payload: serde_json::Value,
        source: String,
    ) -> Self;
    
    // 创建响应消息
    pub fn new_response(
        request_id: String,
        payload: serde_json::Value,
        source: String,
    ) -> Self;
    
    // 创建错误消息
    pub fn new_error(
        request_id: String,
        error_message: String,
        source: String,
    ) -> Self;
    
    // 创建Ping消息
    pub fn new_ping(
        request_id: String,
        source: String,
    ) -> Self;
    
    // 创建Pong消息
    pub fn new_pong(
        request_id: String,
        source: String,
    ) -> Self;
}
```

##### 实用方法

```rust
impl NativeMessage {
    // 获取消息类型
    pub fn get_message_type(&self) -> MessageType;
    
    // 验证消息格式
    pub fn validate(&self) -> Result<()>;
    
    // 检查消息大小
    pub fn check_size_limit(&self, max_size: usize) -> Result<()>;
    
    // 添加扩展字段
    pub fn add_extension(&mut self, key: String, value: serde_json::Value);
    
    // 获取扩展字段
    pub fn get_extension(&self, key: &str) -> Option<&serde_json::Value>;
    
    // 设置签名
    pub fn set_signature(&mut self, signature: String);
    
    // 验证签名
    pub fn verify_signature(&self, public_key: &str) -> Result<bool>;
}
```

### MessageType

消息类型枚举。

```rust
#[derive(Debug, Clone, PartialEq)]
pub enum MessageType {
    Request,
    Response,
    Error,
    Ping,
    Pong,
    Heartbeat,
    Custom(String),
}
```

#### 方法

```rust
impl MessageType {
    // 从字符串转换
    pub fn from_str(s: &str) -> Result<Self>;
    
    // 转换为字符串
    pub fn to_string(&self) -> String;
    
    // 检查是否为请求类型
    pub fn is_request(&self) -> bool;
    
    // 检查是否为响应类型
    pub fn is_response(&self) -> bool;
    
    // 检查是否为错误类型
    pub fn is_error(&self) -> bool;
}
```

### ProtocolVersion

协议版本枚举。

```rust
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum ProtocolVersion {
    V1,
    V2,
}
```

#### 方法

```rust
impl ProtocolVersion {
    // 从数字转换
    pub fn from_u32(version: u32) -> Result<Self>;
    
    // 转换为数字
    pub fn to_u32(&self) -> u32;
    
    // 获取支持的版本列表
    pub fn supported_versions() -> Vec<Self>;
    
    // 检查版本兼容性
    pub fn is_compatible(&self, other: &Self) -> bool;
}
```

## 主机服务

### NativeMessagingHost

Native Messaging主机服务的核心类型。

```rust
pub struct NativeMessagingHost {
    // 私有字段
}
```

#### 方法

```rust
impl NativeMessagingHost {
    // 创建新的主机实例
    pub async fn new(
        config: HostConfig,
        proxy: RequestProxy,
    ) -> Result<Self>;
    
    // 启动主机服务
    pub async fn run(&self) -> Result<()>;
    
    // 停止主机服务
    pub async fn shutdown(&self) -> Result<()>;
    
    // 获取统计信息
    pub async fn get_stats(&self) -> HostStats;
    
    // 获取配置信息
    pub fn get_config(&self) -> &HostConfig;
    
    // 处理单个消息
    pub async fn handle_message(&self, message: NativeMessage) -> Result<NativeMessage>;
    
    // 验证扩展权限
    pub fn validate_extension(&self, extension_id: &str) -> Result<()>;
    
    // 启动心跳服务
    pub async fn start_heartbeat(&self) -> Result<()>;
    
    // 停止心跳服务
    pub async fn stop_heartbeat(&self) -> Result<()>;
}
```

### HostConfig

主机配置结构。

```rust
#[derive(Debug, Clone)]
pub struct HostConfig {
    pub host_name: String,
    pub allowed_extensions: Vec<String>,
    pub max_message_size: usize,
    pub enable_logging: bool,
    pub heartbeat_interval: Duration,
    pub request_timeout: Duration,
    pub max_concurrent_requests: usize,
    pub ipc_config: IpcClientConfig,
}
```

#### 方法

```rust
impl HostConfig {
    // 创建默认配置
    pub fn default() -> Self;
    
    // 从文件加载配置
    pub fn from_file(path: &str) -> Result<Self>;
    
    // 保存配置到文件
    pub fn save_to_file(&self, path: &str) -> Result<()>;
    
    // 验证配置
    pub fn validate(&self) -> Result<()>;
    
    // 合并配置
    pub fn merge(&mut self, other: &Self);
}
```

### HostStats

主机统计信息。

```rust
#[derive(Debug, Clone, Default)]
pub struct HostStats {
    pub total_requests: u64,
    pub successful_requests: u64,
    pub failed_requests: u64,
    pub avg_response_time_ms: f64,
    pub active_connections: u32,
    pub uptime_seconds: u64,
    pub last_heartbeat: Option<u64>,
}
```

#### 方法

```rust
impl HostStats {
    // 重置统计信息
    pub fn reset(&mut self);
    
    // 计算成功率
    pub fn success_rate(&self) -> f64;
    
    // 计算失败率
    pub fn failure_rate(&self) -> f64;
    
    // 转换为JSON
    pub fn to_json(&self) -> serde_json::Value;
}
```

## 请求代理

### RequestProxy

请求代理器，负责将Native Message转换为IPC消息。

```rust
pub struct RequestProxy {
    // 私有字段
}
```

#### 方法

```rust
impl RequestProxy {
    // 创建新的代理实例
    pub async fn new(
        config: ProxyConfig,
        ipc_client: IpcClient,
    ) -> Result<Self>;
    
    // 代理单个请求
    pub async fn proxy_request(&self, request: NativeMessage) -> Result<NativeMessage>;
    
    // 代理批量请求
    pub async fn proxy_batch_requests(&self, requests: Vec<NativeMessage>) -> Result<Vec<NativeMessage>>;
    
    // 获取统计信息
    pub async fn get_stats(&self) -> ProxyStats;
    
    // 获取配置信息
    pub fn get_config(&self) -> &ProxyConfig;
    
    // 清理缓存
    pub async fn clear_cache(&self) -> Result<()>;
    
    // 刷新缓存
    pub async fn refresh_cache(&self) -> Result<()>;
}
```

### ProxyConfig

代理配置结构。

```rust
#[derive(Debug, Clone)]
pub struct ProxyConfig {
    pub timeout: Duration,
    pub max_retries: u32,
    pub retry_delay: Duration,
    pub enable_batch_processing: bool,
    pub batch_size: usize,
    pub batch_timeout: Duration,
    pub enable_caching: bool,
    pub cache_ttl: Duration,
}
```

#### 方法

```rust
impl ProxyConfig {
    // 创建默认配置
    pub fn default() -> Self;
    
    // 验证配置
    pub fn validate(&self) -> Result<()>;
    
    // 优化配置
    pub fn optimize(&mut self);
}
```

### ProxyStats

代理统计信息。

```rust
#[derive(Debug, Clone, Default)]
pub struct ProxyStats {
    pub total_requests: u64,
    pub successful_requests: u64,
    pub failed_requests: u64,
    pub retry_count: u64,
    pub avg_response_time_ms: f64,
    pub cache_hits: u64,
    pub cache_misses: u64,
    pub batch_processed: u64,
}
```

## 浏览器适配

### BrowserAdapter

浏览器适配器trait。

```rust
pub trait BrowserAdapter: Send + Sync {
    // 获取浏览器类型
    fn get_browser_type(&self) -> BrowserType;
    
    // 处理消息
    async fn handle_message(&self, message: &NativeMessage) -> Result<NativeMessage>;
    
    // 验证扩展
    fn validate_extension(&self, extension_id: &str) -> Result<()>;
    
    // 获取浏览器版本
    async fn get_browser_version(&self) -> Result<String>;
    
    // 检查浏览器是否安装
    async fn is_browser_installed(&self) -> bool;
    
    // 获取适配器统计信息
    fn get_stats(&self) -> BrowserAdapterStats;
}
```

### BrowserType

浏览器类型枚举。

```rust
#[derive(Debug, Clone, PartialEq)]
pub enum BrowserType {
    Chrome,
    Firefox,
    Edge,
    Safari,
    Custom(String),
}
```

#### 方法

```rust
impl BrowserType {
    // 从字符串转换
    pub fn from_str(s: &str) -> Result<Self>;
    
    // 获取主机名称
    pub fn get_host_name(&self) -> &str;
    
    // 获取显示名称
    pub fn get_display_name(&self) -> &str;
    
    // 检查是否支持
    pub fn is_supported(&self) -> bool;
}
```

### ChromeAdapter

Chrome浏览器适配器。

```rust
pub struct ChromeAdapter {
    // 私有字段
}
```

#### 方法

```rust
impl ChromeAdapter {
    // 创建新的Chrome适配器
    pub fn new() -> Self;
    
    // 获取Chrome安装路径
    pub fn get_chrome_path() -> Result<PathBuf>;
    
    // 检查Chrome扩展
    pub fn check_extension(&self, extension_id: &str) -> Result<bool>;
}
```

### FirefoxAdapter

Firefox浏览器适配器。

```rust
pub struct FirefoxAdapter {
    // 私有字段
}
```

#### 方法

```rust
impl FirefoxAdapter {
    // 创建新的Firefox适配器
    pub fn new() -> Self;
    
    // 获取Firefox安装路径
    pub fn get_firefox_path() -> Result<PathBuf>;
    
    // 检查Firefox扩展
    pub fn check_extension(&self, extension_id: &str) -> Result<bool>;
}
```

## 消息处理

### MessageHandler

消息处理器trait。

```rust
pub trait MessageHandler: Send + Sync {
    // 处理消息
    async fn handle_message(&self, message: &NativeMessage) -> Result<NativeMessage>;
    
    // 获取处理器名称
    fn get_name(&self) -> &str;
    
    // 获取支持的消息类型
    fn get_supported_types(&self) -> Vec<MessageType>;
    
    // 检查是否支持消息类型
    fn supports_type(&self, message_type: &MessageType) -> bool;
}
```

### HandlerRegistry

消息处理器注册表。

```rust
pub struct HandlerRegistry {
    // 私有字段
}
```

#### 方法

```rust
impl HandlerRegistry {
    // 创建新的注册表
    pub fn new() -> Self;
    
    // 注册处理器
    pub fn register(&mut self, name: String, handler: Box<dyn MessageHandler>);
    
    // 取消注册处理器
    pub fn unregister(&mut self, name: &str) -> Option<Box<dyn MessageHandler>>;
    
    // 获取处理器
    pub fn get(&self, name: &str) -> Option<&dyn MessageHandler>;
    
    // 获取所有处理器名称
    pub fn get_handler_names(&self) -> Vec<String>;
    
    // 查找支持消息类型的处理器
    pub fn find_handler(&self, message_type: &MessageType) -> Option<&dyn MessageHandler>;
}
```

## 浏览器注册

### RegistryManager

浏览器注册管理器。

```rust
pub struct RegistryManager {
    // 私有字段
}
```

#### 方法

```rust
impl RegistryManager {
    // 创建新的注册管理器
    pub fn new() -> Self;
    
    // 注册Chrome主机
    pub async fn register_chrome_host(&self, registration: &HostRegistration) -> Result<()>;
    
    // 注册Firefox主机
    pub async fn register_firefox_host(&self, registration: &HostRegistration) -> Result<()>;
    
    // 注册Edge主机
    pub async fn register_edge_host(&self, registration: &HostRegistration) -> Result<()>;
    
    // 取消注册Chrome主机
    pub async fn unregister_chrome_host(&self, host_name: &str) -> Result<()>;
    
    // 取消注册Firefox主机
    pub async fn unregister_firefox_host(&self, host_name: &str) -> Result<()>;
    
    // 取消注册Edge主机
    pub async fn unregister_edge_host(&self, host_name: &str) -> Result<()>;
    
    // 检查主机是否已注册
    pub async fn is_host_registered(&self, browser: &BrowserType, host_name: &str) -> bool;
    
    // 获取已注册的主机列表
    pub async fn get_registered_hosts(&self, browser: &BrowserType) -> Result<Vec<String>>;
}
```

### HostRegistration

主机注册信息。

```rust
#[derive(Debug, Clone)]
pub struct HostRegistration {
    pub name: String,
    pub description: String,
    pub path: String,
    pub allowed_extensions: Vec<String>,
    pub allowed_origins: Vec<String>,
}
```

#### 方法

```rust
impl HostRegistration {
    // 创建新的注册信息
    pub fn new(
        name: String,
        description: String,
        path: String,
    ) -> Self;
    
    // 添加允许的扩展
    pub fn add_extension(&mut self, extension_id: String);
    
    // 添加允许的来源
    pub fn add_origin(&mut self, origin: String);
    
    // 验证注册信息
    pub fn validate(&self) -> Result<()>;
    
    // 转换为JSON
    pub fn to_json(&self) -> serde_json::Value;
    
    // 从JSON创建
    pub fn from_json(json: &serde_json::Value) -> Result<Self>;
}
```

## 协议编解码

### ProtocolCodec

协议编解码器。

```rust
pub struct ProtocolCodec {
    // 私有字段
}
```

#### 方法

```rust
impl ProtocolCodec {
    // 创建新的编解码器
    pub fn new(version: ProtocolVersion) -> Self;
    
    // 编码消息
    pub fn encode(&self, message: &NativeMessage) -> Result<Vec<u8>>;
    
    // 解码消息
    pub fn decode(&self, data: &[u8]) -> Result<NativeMessage>;
    
    // 异步编码到写入器
    pub async fn encode_async<W>(&self, message: &NativeMessage, writer: &mut W) -> Result<()>
    where
        W: tokio::io::AsyncWrite + Unpin;
    
    // 异步从读取器解码
    pub async fn decode_async<R>(&self, reader: &mut R) -> Result<NativeMessage>
    where
        R: tokio::io::AsyncRead + Unpin;
    
    // 同步编码到写入器
    pub fn encode_sync<W>(&self, message: &NativeMessage, writer: &mut W) -> Result<()>
    where
        W: std::io::Write;
    
    // 同步从读取器解码
    pub fn decode_sync<R>(&self, reader: &mut R) -> Result<NativeMessage>
    where
        R: std::io::Read;
}
```

### ProtocolNegotiator

协议版本协商器。

```rust
pub struct ProtocolNegotiator {
    // 私有字段
}
```

#### 方法

```rust
impl ProtocolNegotiator {
    // 创建新的协商器
    pub fn new() -> Self;
    
    // 协商协议版本
    pub fn negotiate(&self, client_versions: &[ProtocolVersion]) -> Option<ProtocolVersion>;
    
    // 获取支持的版本
    pub fn get_supported_versions(&self) -> Vec<ProtocolVersion>;
    
    // 添加支持的版本
    pub fn add_supported_version(&mut self, version: ProtocolVersion);
    
    // 移除支持的版本
    pub fn remove_supported_version(&mut self, version: &ProtocolVersion);
    
    // 检查版本是否支持
    pub fn is_version_supported(&self, version: &ProtocolVersion) -> bool;
}
```

## 错误处理

### NativeMessagingError

Native Messaging错误类型。

```rust
#[derive(Error, Debug)]
pub enum NativeMessagingError {
    #[error("IO错误: {0}")]
    IoError(#[from] std::io::Error),
    
    #[error("序列化错误: {0}")]
    SerializationError(String),
    
    #[error("协议错误: {0}")]
    ProtocolError(String),
    
    #[error("消息格式错误: {0}")]
    MessageFormatError(String),
    
    #[error("不支持的消息类型: {0}")]
    UnsupportedMessageType(String),
    
    #[error("扩展未授权: {0}")]
    UnauthorizedExtension(String),
    
    #[error("消息过大: {0} bytes")]
    MessageTooLarge(usize),
    
    #[error("连接错误: {0}")]
    ConnectionError(String),
    
    #[error("超时错误: {0}")]
    TimeoutError(String),
    
    #[error("IPC错误: {0}")]
    IpcError(#[from] IpcError),
    
    #[error("浏览器注册错误: {0}")]
    BrowserRegistrationError(String),
    
    #[error("配置错误: {0}")]
    ConfigError(String),
    
    #[error("内部错误: {0}")]
    InternalError(String),
}
```

#### 方法

```rust
impl NativeMessagingError {
    // 检查是否为可重试错误
    pub fn is_retryable(&self) -> bool;
    
    // 获取错误代码
    pub fn error_code(&self) -> &str;
    
    // 转换为JSON
    pub fn to_json(&self) -> serde_json::Value;
    
    // 从JSON创建
    pub fn from_json(json: &serde_json::Value) -> Result<Self>;
}
```

## 常量

### 协议常量

```rust
// 协议版本
pub const PROTOCOL_VERSION_1: u32 = 1;
pub const PROTOCOL_VERSION_2: u32 = 2;
pub const CURRENT_PROTOCOL_VERSION: u32 = PROTOCOL_VERSION_2;

// 消息大小限制
pub const DEFAULT_MAX_MESSAGE_SIZE: usize = 1024 * 1024; // 1MB
pub const MAX_MESSAGE_SIZE_LIMIT: usize = 16 * 1024 * 1024; // 16MB

// 超时设置
pub const DEFAULT_REQUEST_TIMEOUT: Duration = Duration::from_secs(30);
pub const DEFAULT_HEARTBEAT_INTERVAL: Duration = Duration::from_secs(60);

// 浏览器主机名称
pub const CHROME_HOST_NAME: &str = "chrome_host";
pub const FIREFOX_HOST_NAME: &str = "firefox_host";
pub const EDGE_HOST_NAME: &str = "edge_host";
```

### 消息类型常量

```rust
// 消息类型字符串
pub const MESSAGE_TYPE_REQUEST: &str = "request";
pub const MESSAGE_TYPE_RESPONSE: &str = "response";
pub const MESSAGE_TYPE_ERROR: &str = "error";
pub const MESSAGE_TYPE_PING: &str = "ping";
pub const MESSAGE_TYPE_PONG: &str = "pong";
pub const MESSAGE_TYPE_HEARTBEAT: &str = "heartbeat";
```

## 辅助函数

### 实用函数

```rust
// 生成唯一请求ID
pub fn generate_request_id() -> String;

// 获取当前时间戳
pub fn get_current_timestamp() -> u64;

// 验证扩展ID格式
pub fn validate_extension_id(extension_id: &str) -> Result<()>;

// 验证主机名称格式
pub fn validate_host_name(host_name: &str) -> Result<()>;

// 计算消息大小
pub fn calculate_message_size(message: &NativeMessage) -> usize;

// 检查消息是否过期
pub fn is_message_expired(message: &NativeMessage, ttl_seconds: u64) -> bool;

// 创建错误响应
pub fn create_error_response(
    request_id: String,
    error: &NativeMessagingError,
    source: String,
) -> NativeMessage;

// 创建成功响应
pub fn create_success_response(
    request_id: String,
    data: serde_json::Value,
    source: String,
) -> NativeMessage;
```

### 配置辅助函数

```rust
// 加载默认主机配置
pub fn load_default_host_config() -> HostConfig;

// 加载默认代理配置
pub fn load_default_proxy_config() -> ProxyConfig;

// 验证配置文件
pub fn validate_config_file(path: &str) -> Result<()>;

// 合并配置
pub fn merge_configs<T: Clone>(base: &T, override_config: &T) -> T;
```

## 平台特定API

### Windows

```rust
#[cfg(target_os = "windows")]
pub mod windows {
    // Windows注册表操作
    pub fn register_chrome_host_registry(registration: &HostRegistration) -> Result<()>;
    pub fn unregister_chrome_host_registry(host_name: &str) -> Result<()>;
    
    // Windows服务管理
    pub fn install_windows_service(config: &ServiceConfig) -> Result<()>;
    pub fn uninstall_windows_service(service_name: &str) -> Result<()>;
}
```

### macOS

```rust
#[cfg(target_os = "macos")]
pub mod macos {
    // macOS LaunchDaemon管理
    pub fn install_launch_daemon(config: &LaunchDaemonConfig) -> Result<()>;
    pub fn uninstall_launch_daemon(service_name: &str) -> Result<()>;
    
    // macOS权限管理
    pub fn check_permissions() -> Result<()>;
    pub fn request_permissions() -> Result<()>;
}
```

### Linux

```rust
#[cfg(target_os = "linux")]
pub mod linux {
    // Linux systemd服务管理
    pub fn install_systemd_service(config: &SystemdConfig) -> Result<()>;
    pub fn uninstall_systemd_service(service_name: &str) -> Result<()>;
    
    // Linux配置文件管理
    pub fn create_config_files(config: &HostConfig) -> Result<()>;
    pub fn remove_config_files(host_name: &str) -> Result<()>;
}
```

## 示例代码

### 完整的使用示例

```rust
use secure_password_daemon::native_messaging::*;
use secure_password_daemon::ipc::{IpcClient, ClientConfig};

#[tokio::main]
async fn main() -> Result<()> {
    // 1. 创建IPC客户端
    let ipc_config = ClientConfig::default();
    let ipc_client = IpcClient::new(ipc_config);
    
    // 2. 创建请求代理
    let proxy_config = ProxyConfig {
        enable_caching: true,
        enable_batch_processing: true,
        timeout: Duration::from_secs(30),
        ..Default::default()
    };
    let proxy = RequestProxy::new(proxy_config, ipc_client).await?;
    
    // 3. 创建主机配置
    let host_config = HostConfig {
        host_name: "com.example.secure_password".to_string(),
        allowed_extensions: vec!["extension_id".to_string()],
        max_message_size: 1024 * 1024,
        enable_logging: true,
        ..Default::default()
    };
    
    // 4. 创建Native Messaging主机
    let host = NativeMessagingHost::new(host_config, proxy).await?;
    
    // 5. 注册浏览器主机
    let registration = HostRegistration {
        name: "com.example.secure_password".to_string(),
        description: "Secure Password Manager".to_string(),
        path: "/path/to/host".to_string(),
        allowed_extensions: vec!["extension_id".to_string()],
        ..Default::default()
    };
    
    let registry = RegistryManager::new();
    registry.register_chrome_host(&registration).await?;
    registry.register_firefox_host(&registration).await?;
    
    // 6. 启动主机服务
    host.run().await?;
    
    Ok(())
}
```

这个API文档提供了Native Messaging模块的完整接口说明，包括所有公共类型、方法和配置选项。开发者可以根据这个文档来使用和扩展Native Messaging功能。 