//! # 请求代理器实现
//! 
//! 负责将浏览器扩展的请求转发到守护进程的IPC服务，
//! 并处理响应的转换和错误处理。

use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{debug, error, info, warn};
use serde::{Deserialize, Serialize};

use crate::ipc::{IpcClient, ClientConfig as IpcClientConfig};
use crate::ipc::protocol::{IpcMessage, IpcResponse, IpcMessageType, MessagePriority};
use crate::native_messaging::{Result, NativeMessagingError};
use crate::native_messaging::protocol::{NativeMessage, MessageType};

/// 代理配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProxyConfig {
    /// 请求超时时间
    pub timeout: Duration,
    /// 最大重试次数
    pub max_retries: u32,
    /// 重试延迟
    pub retry_delay: Duration,
    /// 是否启用批量处理
    pub enable_batch_processing: bool,
    /// 批量大小
    pub batch_size: usize,
    /// 批量超时时间
    pub batch_timeout: Duration,
    /// 是否启用缓存
    pub enable_caching: bool,
    /// 缓存TTL
    pub cache_ttl: Duration,
}

impl Default for ProxyConfig {
    fn default() -> Self {
        Self {
            timeout: Duration::from_secs(30),
            max_retries: 3,
            retry_delay: Duration::from_millis(500),
            enable_batch_processing: false,
            batch_size: 10,
            batch_timeout: Duration::from_millis(100),
            enable_caching: false,
            cache_ttl: Duration::from_secs(300),
        }
    }
}

/// 代理统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProxyStats {
    /// 总请求数
    pub total_requests: u64,
    /// 成功请求数
    pub successful_requests: u64,
    /// 失败请求数
    pub failed_requests: u64,
    /// 重试次数
    pub retry_count: u64,
    /// 平均响应时间 (毫秒)
    pub avg_response_time_ms: f64,
    /// 缓存命中次数
    pub cache_hits: u64,
    /// 缓存未命中次数
    pub cache_misses: u64,
    /// 批量处理次数
    pub batch_processed: u64,
}

impl Default for ProxyStats {
    fn default() -> Self {
        Self {
            total_requests: 0,
            successful_requests: 0,
            failed_requests: 0,
            retry_count: 0,
            avg_response_time_ms: 0.0,
            cache_hits: 0,
            cache_misses: 0,
            batch_processed: 0,
        }
    }
}

/// 缓存条目
#[derive(Debug, Clone)]
struct CacheEntry {
    /// 响应数据
    response: NativeMessage,
    /// 过期时间
    expires_at: Instant,
}

/// 批量请求项
#[derive(Debug)]
struct BatchItem {
    /// 原始请求
    request: NativeMessage,
    /// 响应发送器
    response_sender: tokio::sync::oneshot::Sender<Result<NativeMessage>>,
    /// 添加时间
    added_at: Instant,
}

/// 请求代理器
pub struct RequestProxy {
    /// 配置
    config: ProxyConfig,
    /// IPC 客户端
    ipc_client: IpcClient,
    /// 统计信息
    stats: Arc<RwLock<ProxyStats>>,
    /// 响应缓存
    cache: Arc<RwLock<HashMap<String, CacheEntry>>>,
    /// 批量处理队列
    batch_queue: Arc<RwLock<Vec<BatchItem>>>,
}

impl RequestProxy {
    /// 创建新的请求代理器
    pub async fn new(config: ProxyConfig, ipc_client: IpcClient) -> Result<Self> {
        let proxy = Self {
            config: config.clone(),
            ipc_client,
            stats: Arc::new(RwLock::new(ProxyStats::default())),
            cache: Arc::new(RwLock::new(HashMap::new())),
            batch_queue: Arc::new(RwLock::new(Vec::new())),
        };
        
        // 启动后台任务
        if config.enable_batch_processing {
            proxy.start_batch_processor().await?;
        }
        
        if config.enable_caching {
            proxy.start_cache_cleaner().await?;
        }
        
        Ok(proxy)
    }
    
    /// 代理单个请求
    pub async fn proxy_request(&self, request: NativeMessage) -> Result<NativeMessage> {
        let start_time = Instant::now();
        
        // 更新统计信息
        self.increment_total_requests().await;
        
        debug!("代理请求: {} (类型: {})", request.request_id, request.message_type);
        
        // 检查缓存
        if self.config.enable_caching {
            if let Some(cached_response) = self.check_cache(&request).await? {
                debug!("缓存命中: {}", request.request_id);
                self.increment_cache_hits().await;
                self.increment_successful_requests().await;
                self.update_response_time_stats(start_time.elapsed()).await;
                return Ok(cached_response);
            } else {
                self.increment_cache_misses().await;
            }
        }
        
        // 执行请求代理
        let result = if self.config.enable_batch_processing && self.is_batchable(&request) {
            self.proxy_request_batch(request.clone()).await
        } else {
            self.proxy_request_direct(request.clone()).await
        };
        
        // 更新统计信息和缓存
        match &result {
            Ok(response) => {
                self.increment_successful_requests().await;
                
                // 缓存响应
                if self.config.enable_caching && self.is_cacheable(response) {
                    self.cache_response(&request, response.clone()).await?;
                }
            }
            Err(_) => {
                self.increment_failed_requests().await;
            }
        }
        
        self.update_response_time_stats(start_time.elapsed()).await;
        result
    }
    
    /// 直接代理请求
    async fn proxy_request_direct(&self, request: NativeMessage) -> Result<NativeMessage> {
        let mut retry_count = 0;
        
        loop {
            match self.execute_proxy_request(&request).await {
                Ok(response) => return Ok(response),
                Err(e) => {
                    retry_count += 1;
                    
                    if retry_count <= self.config.max_retries {
                        warn!("请求失败，重试 {}/{}: {} (错误: {})", 
                              retry_count, self.config.max_retries, request.request_id, e);
                        
                        self.increment_retry_count().await;
                        tokio::time::sleep(self.config.retry_delay).await;
                    } else {
                        error!("请求最终失败: {} (错误: {})", request.request_id, e);
                        return Err(e);
                    }
                }
            }
        }
    }
    
    /// 批量代理请求
    async fn proxy_request_batch(&self, request: NativeMessage) -> Result<NativeMessage> {
        let (response_sender, response_receiver) = tokio::sync::oneshot::channel();
        
        // 添加到批量队列
        {
            let mut queue = self.batch_queue.write().await;
            queue.push(BatchItem {
                request,
                response_sender,
                added_at: Instant::now(),
            });
        }
        
        // 等待响应
        response_receiver.await.map_err(|_| {
            NativeMessagingError::ProxyError("批量请求响应接收失败".to_string())
        })?
    }
    
    /// 执行单个代理请求
    async fn execute_proxy_request(&self, request: &NativeMessage) -> Result<NativeMessage> {
        // 转换为IPC消息
        let ipc_message = self.convert_to_ipc_message(request)?;
        
        // 发送IPC请求
        let ipc_response = tokio::time::timeout(
            self.config.timeout,
            self.ipc_client.request(ipc_message)
        ).await.map_err(|_| {
            NativeMessagingError::TimeoutError(format!("IPC请求超时: {}", request.request_id))
        })??;
        
        // 转换IPC响应为Native Message
        self.convert_from_ipc_response(&ipc_response, request)
    }
    
    /// 转换Native Message为IPC消息
    fn convert_to_ipc_message(&self, request: &NativeMessage) -> Result<IpcMessage> {
        // 确定IPC消息类型
        let ipc_message_type = match request.get_message_type() {
            MessageType::Request => {
                // 根据payload中的action确定具体的IPC消息类型
                if let Some(action) = request.payload.get("action").and_then(|v| v.as_str()) {
                    match action {
                        "get_credentials" => IpcMessageType::BrowserRequest,
                        "save_credential" => IpcMessageType::BrowserRequest,
                        "delete_credential" => IpcMessageType::BrowserRequest,
                        "generate_password" => IpcMessageType::BrowserRequest,
                        "get_settings" => IpcMessageType::BrowserRequest,
                        "update_settings" => IpcMessageType::BrowserRequest,
                        "health_check" => IpcMessageType::AppHealthCheck,
                        _ => IpcMessageType::Custom(action.to_string()),
                    }
                } else {
                    return Err(NativeMessagingError::MessageFormatError(
                        "请求消息缺少action字段".to_string()
                    ));
                }
            }
            MessageType::Ping => IpcMessageType::Ping,
            _ => {
                return Err(NativeMessagingError::MessageFormatError(
                    format!("不支持的消息类型: {}", request.message_type)
                ));
            }
        };
        
        Ok(IpcMessage {
            version: 1,
            message_id: request.request_id.clone(),
            message_type: ipc_message_type,
            payload: request.payload.clone(),
            timestamp: request.timestamp,
            source: "native_messaging_host".to_string(),
            target: Some("daemon_main".to_string()),
            response_required: true,
            priority: MessagePriority::Normal,
            headers: {
                let mut headers = std::collections::HashMap::new();
                headers.insert("original_source".to_string(), request.source.clone());
                if let Some(target) = &request.target {
                    headers.insert("original_target".to_string(), target.clone());
                }
                headers
            },
        })
    }
    
    /// 转换IPC响应为Native Message
    fn convert_from_ipc_response(&self, ipc_response: &IpcResponse, original_request: &NativeMessage) -> Result<NativeMessage> {
        let message_type = if ipc_response.is_success() {
            MessageType::Response
        } else {
            MessageType::Error
        };
        
        let payload = if ipc_response.is_success() {
            ipc_response.data.clone()
        } else {
            serde_json::json!({
                "error_code": "request_failed",
                "error_message": ipc_response.error.as_ref().unwrap_or(&"未知错误".to_string())
            })
        };
        
        Ok(NativeMessage {
            version: original_request.version,
            message_type: message_type.to_string(),
            request_id: original_request.request_id.clone(),
            payload,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            source: "host".to_string(),
            target: Some(original_request.source.clone()),
            signature: None,
            extensions: std::collections::HashMap::new(),
        })
    }
    
    /// 检查缓存
    async fn check_cache(&self, request: &NativeMessage) -> Result<Option<NativeMessage>> {
        let cache_key = self.generate_cache_key(request);
        let cache = self.cache.read().await;
        
        if let Some(entry) = cache.get(&cache_key) {
            if Instant::now() < entry.expires_at {
                return Ok(Some(entry.response.clone()));
            }
        }
        
        Ok(None)
    }
    
    /// 缓存响应
    async fn cache_response(&self, request: &NativeMessage, response: NativeMessage) -> Result<()> {
        let cache_key = self.generate_cache_key(request);
        let expires_at = Instant::now() + self.config.cache_ttl;
        
        let mut cache = self.cache.write().await;
        cache.insert(cache_key, CacheEntry {
            response,
            expires_at,
        });
        
        Ok(())
    }
    
    /// 生成缓存键
    fn generate_cache_key(&self, request: &NativeMessage) -> String {
        // 简单的缓存键生成策略，基于请求类型和主要参数
        let mut key_parts = vec![
            request.message_type.clone(),
        ];
        
        if let Some(action) = request.payload.get("action").and_then(|v| v.as_str()) {
            key_parts.push(action.to_string());
        }
        
        if let Some(domain) = request.payload.get("domain").and_then(|v| v.as_str()) {
            key_parts.push(domain.to_string());
        }
        
        key_parts.join(":")
    }
    
    /// 检查请求是否可批量处理
    fn is_batchable(&self, request: &NativeMessage) -> bool {
        // 只有某些类型的请求适合批量处理
        if let Some(action) = request.payload.get("action").and_then(|v| v.as_str()) {
            matches!(action, "get_credentials" | "health_check")
        } else {
            false
        }
    }
    
    /// 检查响应是否可缓存
    fn is_cacheable(&self, response: &NativeMessage) -> bool {
        // 只缓存成功的响应，且不是敏感操作
        response.get_message_type() == MessageType::Response &&
        !response.payload.get("sensitive").and_then(|v| v.as_bool()).unwrap_or(false)
    }
    
    /// 启动批量处理器
    async fn start_batch_processor(&self) -> Result<()> {
        let batch_queue = self.batch_queue.clone();
        let config = self.config.clone();
        let ipc_client = self.ipc_client.clone();
        let stats = self.stats.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(config.batch_timeout);
            
            loop {
                interval.tick().await;
                
                // 处理批量队列
                let items_to_process = {
                    let mut queue = batch_queue.write().await;
                    if queue.is_empty() {
                        continue;
                    }
                    
                    let now = Instant::now();
                    let mut items = Vec::new();
                    let mut remaining = Vec::new();
                    
                    for item in queue.drain(..) {
                        if items.len() < config.batch_size || 
                           now.duration_since(item.added_at) > config.batch_timeout {
                            items.push(item);
                        } else {
                            remaining.push(item);
                        }
                    }
                    
                    queue.extend(remaining);
                    items
                };
                
                if !items_to_process.is_empty() {
                    debug!("处理批量请求: {} 个", items_to_process.len());
                    
                    // 并发处理批量请求
                    let futures: Vec<_> = items_to_process.into_iter().map(|item| {
                        let ipc_client = ipc_client.clone();
                        async move {
                            let result = Self::execute_proxy_request_static(&ipc_client, &item.request).await;
                            let _ = item.response_sender.send(result);
                        }
                    }).collect();
                    
                    futures::future::join_all(futures).await;
                    
                    // 更新统计信息
                    stats.write().await.batch_processed += 1;
                }
            }
        });
        
        Ok(())
    }
    
    /// 静态转换方法 - 将 Native Message 转换为 IPC Message
    fn convert_native_to_ipc_message_static(request: &NativeMessage) -> Result<IpcMessage> {
        // 确定IPC消息类型
        let ipc_message_type = match request.get_message_type() {
            MessageType::Request => {
                // 根据payload中的action确定具体的IPC消息类型
                if let Some(action) = request.payload.get("action").and_then(|v| v.as_str()) {
                    match action {
                        "get_passwords" | "save_password" | "delete_password" => IpcMessageType::BrowserRequest,
                        "generate_password" => IpcMessageType::Custom("generate_password".to_string()),
                        "check_breach" => IpcMessageType::SecurityEvent,
                        _ => IpcMessageType::Custom(action.to_string()),
                    }
                } else {
                    IpcMessageType::BrowserRequest
                }
            },
            MessageType::Response => IpcMessageType::Success,
            MessageType::Error => IpcMessageType::Error,
            MessageType::Ping => IpcMessageType::Heartbeat,
            MessageType::Pong => IpcMessageType::Heartbeat,
            MessageType::Notification => IpcMessageType::Notification,
            MessageType::Custom(msg_type) => IpcMessageType::Custom(msg_type),
        };

        Ok(IpcMessage {
            version: 1,
            message_id: request.request_id.clone(),
            message_type: ipc_message_type,
            payload: request.payload.clone(),
            timestamp: request.timestamp,
            source: "native_messaging_host".to_string(),
            target: Some("daemon_main".to_string()),
            response_required: true,
            priority: MessagePriority::Normal,
            headers: std::collections::HashMap::new(),
        })
    }

    /// 静态方法执行代理请求 (用于批量处理)
    async fn execute_proxy_request_static(ipc_client: &IpcClient, request: &NativeMessage) -> Result<NativeMessage> {
        // 复用主要的转换逻辑 - 将 Native Message 转换为 IPC Message
        let ipc_message = Self::convert_native_to_ipc_message_static(request)?;
        
        let ipc_response = ipc_client.request(ipc_message).await?;
        
        Ok(NativeMessage {
            version: request.version,
            message_type: if ipc_response.is_success() { "response" } else { "error" }.to_string(),
            request_id: request.request_id.clone(),
            payload: ipc_response.data,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            source: "host".to_string(),
            target: Some(request.source.clone()),
            signature: None,
            extensions: std::collections::HashMap::new(),
        })
    }
    
    /// 启动缓存清理器
    async fn start_cache_cleaner(&self) -> Result<()> {
        let cache = self.cache.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(60));
            
            loop {
                interval.tick().await;
                
                // 清理过期的缓存条目
                let now = Instant::now();
                let mut cache_guard = cache.write().await;
                cache_guard.retain(|_, entry| now < entry.expires_at);
            }
        });
        
        Ok(())
    }
    
    /// 获取统计信息
    pub async fn get_stats(&self) -> ProxyStats {
        self.stats.read().await.clone()
    }
    
    /// 批量代理请求
    pub async fn proxy_batch_requests(&self, requests: Vec<NativeMessage>) -> Result<Vec<NativeMessage>> {
        let futures: Vec<_> = requests.into_iter().map(|request| {
            self.proxy_request(request)
        }).collect();
        
        let results = futures::future::join_all(futures).await;
        
        let mut responses = Vec::new();
        for result in results {
            match result {
                Ok(response) => responses.push(response),
                Err(e) => {
                    error!("批量请求中的单个请求失败: {}", e);
                    // 可以选择继续处理其他请求或者直接返回错误
                    return Err(e);
                }
            }
        }
        
        Ok(responses)
    }
    
    // 统计信息更新方法
    async fn increment_total_requests(&self) {
        self.stats.write().await.total_requests += 1;
    }
    
    async fn increment_successful_requests(&self) {
        self.stats.write().await.successful_requests += 1;
    }
    
    async fn increment_failed_requests(&self) {
        self.stats.write().await.failed_requests += 1;
    }
    
    async fn increment_retry_count(&self) {
        self.stats.write().await.retry_count += 1;
    }
    
    async fn increment_cache_hits(&self) {
        self.stats.write().await.cache_hits += 1;
    }
    
    async fn increment_cache_misses(&self) {
        self.stats.write().await.cache_misses += 1;
    }
    
    async fn update_response_time_stats(&self, response_time: Duration) {
        let mut stats = self.stats.write().await;
        let response_time_ms = response_time.as_millis() as f64;
        
        // 简单的移动平均
        if stats.avg_response_time_ms == 0.0 {
            stats.avg_response_time_ms = response_time_ms;
        } else {
            stats.avg_response_time_ms = (stats.avg_response_time_ms * 0.9) + (response_time_ms * 0.1);
        }
    }
}

impl Clone for RequestProxy {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            ipc_client: self.ipc_client.clone(),
            stats: self.stats.clone(),
            cache: self.cache.clone(),
            batch_queue: self.batch_queue.clone(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::ipc::{IpcClient, ClientConfig};

    async fn create_test_proxy() -> RequestProxy {
        let config = ProxyConfig::default();
        let ipc_config = ClientConfig::default();
        let ipc_client = IpcClient::new(ipc_config);
        
        RequestProxy::new(config, ipc_client).await.unwrap()
    }

    #[tokio::test]
    async fn test_proxy_creation() {
        let proxy = create_test_proxy().await;
        let stats = proxy.get_stats().await;
        
        assert_eq!(stats.total_requests, 0);
        assert_eq!(stats.successful_requests, 0);
        assert_eq!(stats.failed_requests, 0);
    }

    #[test]
    fn test_proxy_config_default() {
        let config = ProxyConfig::default();
        
        assert_eq!(config.timeout, Duration::from_secs(30));
        assert_eq!(config.max_retries, 3);
        assert_eq!(config.retry_delay, Duration::from_millis(500));
        assert!(!config.enable_batch_processing);
        assert!(!config.enable_caching);
    }

    #[test]
    fn test_cache_key_generation() {
        // 直接测试缓存键生成逻辑，不需要创建IpcClient
        let request = NativeMessage::new_request(
            "test-id".to_string(),
            serde_json::json!({
                "action": "get_credentials",
                "domain": "example.com"
            }),
            "test-source".to_string(),
        );
        
        // 模拟缓存键生成逻辑
        let mut key_parts = vec![request.message_type.clone()];
        if let Some(action) = request.payload.get("action").and_then(|v| v.as_str()) {
            key_parts.push(action.to_string());
        }
        if let Some(domain) = request.payload.get("domain").and_then(|v| v.as_str()) {
            key_parts.push(domain.to_string());
        }
        let cache_key = key_parts.join(":");
        
        assert_eq!(cache_key, "request:get_credentials:example.com");
    }

    #[tokio::test]
    async fn test_ipc_message_conversion() {
        let proxy = create_test_proxy().await;
        
        let request = NativeMessage::new_request(
            "test-id".to_string(),
            serde_json::json!({
                "action": "get_credentials",
                "domain": "example.com"
            }),
            "test-extension".to_string(),
        );
        
        let ipc_message = proxy.convert_to_ipc_message(&request).unwrap();
        
        assert_eq!(ipc_message.message_id, "test-id");
        assert!(matches!(ipc_message.message_type, IpcMessageType::BrowserRequest));
        assert_eq!(ipc_message.source, "native_messaging_host");
    }

    #[test]
    fn test_batchable_check() {
        // 直接测试批量处理逻辑，不需要创建IpcClient
        let batchable_request = NativeMessage::new_request(
            "test-id".to_string(),
            serde_json::json!({"action": "get_credentials"}),
            "test-source".to_string(),
        );
        
        let non_batchable_request = NativeMessage::new_request(
            "test-id".to_string(),
            serde_json::json!({"action": "save_credential"}),
            "test-source".to_string(),
        );
        
        // 检查批量处理逻辑
        let is_batchable_1 = batchable_request.payload.get("action")
            .and_then(|v| v.as_str())
            .map(|action| matches!(action, "get_credentials" | "health_check"))
            .unwrap_or(false);
        
        let is_batchable_2 = non_batchable_request.payload.get("action")
            .and_then(|v| v.as_str())
            .map(|action| matches!(action, "get_credentials" | "health_check"))
            .unwrap_or(false);
        
        assert!(is_batchable_1);
        assert!(!is_batchable_2);
    }

    #[test]
    fn test_cacheable_check() {
        let response = NativeMessage::new_response(
            "test-id".to_string(),
            serde_json::json!({"data": "test"}),
            "host".to_string(),
        );
        
        let sensitive_response = NativeMessage::new_response(
            "test-id".to_string(),
            serde_json::json!({"data": "test", "sensitive": true}),
            "host".to_string(),
        );
        
        // 检查可缓存逻辑
        let is_cacheable_1 = response.get_message_type() == MessageType::Response &&
            !response.payload.get("sensitive").and_then(|v| v.as_bool()).unwrap_or(false);
        
        let is_cacheable_2 = sensitive_response.get_message_type() == MessageType::Response &&
            !sensitive_response.payload.get("sensitive").and_then(|v| v.as_bool()).unwrap_or(false);
        
        assert!(is_cacheable_1);
        assert!(!is_cacheable_2);
    }
} 