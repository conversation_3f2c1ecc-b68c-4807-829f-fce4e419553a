//! 消息处理器模块
//!
//! 提供处理不同类型Native Messaging消息的框架

use crate::native_messaging::{
    error::{NativeMessagingError, Result},
    protocol::NativeMessage,
};
use async_trait::async_trait;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, error, info, warn};

/// 消息处理器接口
#[async_trait]
pub trait MessageHandler: Send + Sync {
    /// 处理消息
    async fn handle_message(&self, message: NativeMessage) -> Result<NativeMessage>;
    
    /// 获取处理器名称
    fn name(&self) -> &str;
    
    /// 获取支持的消息类型
    fn supported_message_types(&self) -> Vec<String>;
}

/// 处理器注册表
pub struct HandlerRegistry {
    handlers: Arc<RwLock<HashMap<String, Arc<dyn MessageHandler>>>>,
}

impl HandlerRegistry {
    /// 创建新的处理器注册表
    pub fn new() -> Self {
        Self {
            handlers: Arc::new(RwLock::new(HashMap::new())),
        }
    }
    
    /// 注册处理器
    pub async fn register_handler(&self, message_type: String, handler: Arc<dyn MessageHandler>) {
        let type_name = message_type.clone();
        let mut handlers = self.handlers.write().await;
        handlers.insert(message_type, handler);
        info!("注册消息处理器: {}", type_name);
    }
    
    /// 获取处理器
    pub async fn get_handler(&self, message_type: &str) -> Option<Arc<dyn MessageHandler>> {
        let handlers = self.handlers.read().await;
        handlers.get(message_type).cloned()
    }
    
    /// 处理消息
    pub async fn handle_message(&self, message: NativeMessage) -> Result<NativeMessage> {
        let message_type = &message.message_type;
        
        if let Some(handler) = self.get_handler(message_type).await {
            debug!("使用处理器处理消息: {}", message_type);
            handler.handle_message(message).await
        } else {
            warn!("未找到消息类型的处理器: {}", message_type);
            Err(NativeMessagingError::ProtocolError(
                format!("不支持的消息类型: {}", message_type)
            ))
        }
    }
    
    /// 获取所有注册的消息类型
    pub async fn get_registered_types(&self) -> Vec<String> {
        let handlers = self.handlers.read().await;
        handlers.keys().cloned().collect()
    }
}

impl Default for HandlerRegistry {
    fn default() -> Self {
        Self::new()
    }
}

/// 默认消息处理器
pub struct DefaultMessageHandler {
    name: String,
}

impl DefaultMessageHandler {
    pub fn new(name: String) -> Self {
        Self { name }
    }
}

#[async_trait]
impl MessageHandler for DefaultMessageHandler {
    async fn handle_message(&self, message: NativeMessage) -> Result<NativeMessage> {
        debug!("默认处理器处理消息: {:?}", message);
        
        // 创建默认响应
        let response = NativeMessage::new_response(
            message.request_id,
            serde_json::json!({
                "status": "handled",
                "handler": self.name,
                "message_type": message.message_type
            }),
            "daemon".to_string(),
        );
        
        Ok(response)
    }
    
    fn name(&self) -> &str {
        &self.name
    }
    
    fn supported_message_types(&self) -> Vec<String> {
        vec!["default".to_string()]
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_handler_registry() {
        let registry = HandlerRegistry::new();
        
        // 注册处理器
        let handler = Arc::new(DefaultMessageHandler::new("test".to_string()));
        registry.register_handler("test".to_string(), handler.clone()).await;
        
        // 检查注册的类型
        let types = registry.get_registered_types().await;
        assert!(types.contains(&"test".to_string()));
        
        // 获取处理器
        let retrieved = registry.get_handler("test").await;
        assert!(retrieved.is_some());
    }
    
    #[tokio::test]
    async fn test_default_handler() {
        let handler = DefaultMessageHandler::new("test".to_string());
        
        let message = NativeMessage::new_request(
            "test-request".to_string(),
            serde_json::json!({"test": "data"}),
            "test-source".to_string(),
        );
        
        let response = handler.handle_message(message).await.unwrap();
        assert_eq!(response.get_message_type(), crate::native_messaging::protocol::MessageType::Response);
        assert_eq!(response.source, "daemon");
    }
} 