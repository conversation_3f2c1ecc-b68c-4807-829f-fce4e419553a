# Native Messaging 模块文档

## 概述

Native Messaging模块是Secure Password密码管理器的关键组件，负责处理浏览器扩展与守护进程之间的通信。该模块实现了Chrome和Firefox的Native Messaging协议，提供了安全、高效的跨进程通信机制。

## 架构设计

### 核心组件

```
Native Messaging Module
├── Protocol Layer (协议层)
│   ├── NativeMessage - 消息格式定义
│   ├── ProtocolCodec - 编解码器
│   └── ProtocolNegotiator - 版本协商
├── Host Layer (主机层)
│   ├── NativeMessagingHost - 主机服务
│   ├── HostConfig - 主机配置
│   └── HostStats - 统计信息
├── Proxy Layer (代理层)
│   ├── RequestProxy - 请求代理器
│   ├── ProxyConfig - 代理配置
│   └── ProxyStats - 代理统计
├── Browser Layer (浏览器层)
│   ├── BrowserAdapter - 浏览器适配器
│   ├── ChromeAdapter - Chrome适配器
│   ├── FirefoxAdapter - Firefox适配器
│   └── BrowserRegistry - 浏览器注册表
├── Handler Layer (处理层)
│   ├── MessageHandler - 消息处理接口
│   ├── HandlerRegistry - 处理器注册表
│   └── DefaultMessageHandler - 默认处理器
└── Registry Layer (注册层)
    ├── HostRegistration - 主机注册信息
    └── RegistryManager - 注册管理器
```

### 数据流

```
浏览器扩展 → Native Messaging Host → Request Proxy → IPC Client → 守护进程核心
     ↓                ↓                    ↓             ↓
  标准I/O         Native Message      IPC Message   业务逻辑处理
```

## 核心功能

### 1. 协议支持

- **Native Messaging Protocol**: 支持Chrome和Firefox的Native Messaging标准
- **版本协商**: 自动协商最佳协议版本
- **消息编解码**: 高效的JSON消息序列化/反序列化
- **错误处理**: 完整的错误处理和恢复机制

### 2. 多浏览器支持

- **Chrome**: 完整支持Chrome Native Messaging
- **Firefox**: 完整支持Firefox Native Messaging
- **Edge**: 基于Chrome内核的支持
- **Safari**: 预留接口支持

### 3. 性能优化

- **缓存机制**: 智能响应缓存，减少重复请求
- **批量处理**: 支持批量请求处理，提高吞吐量
- **异步处理**: 全异步架构，避免阻塞
- **连接复用**: 高效的连接池管理

### 4. 安全特性

- **扩展验证**: 验证扩展来源和权限
- **消息签名**: 可选的消息完整性验证
- **访问控制**: 基于扩展ID的访问控制
- **审计日志**: 完整的操作审计

## 使用指南

### 基本使用

```rust
use crate::native_messaging::{
    NativeMessagingHost, HostConfig, RequestProxy, ProxyConfig
};
use crate::ipc::{IpcClient, ClientConfig};

// 创建主机配置
let host_config = HostConfig {
    host_name: "com.example.secure_password".to_string(),
    allowed_extensions: vec!["extension_id".to_string()],
    max_message_size: 1024 * 1024, // 1MB
    enable_logging: true,
    ..Default::default()
};

// 创建IPC客户端
let ipc_config = ClientConfig::default();
let ipc_client = IpcClient::new(ipc_config);

// 创建请求代理
let proxy_config = ProxyConfig {
    enable_caching: true,
    enable_batch_processing: true,
    timeout: Duration::from_secs(30),
    ..Default::default()
};
let proxy = RequestProxy::new(proxy_config, ipc_client).await?;

// 创建Native Messaging主机
let host = NativeMessagingHost::new(host_config, proxy).await?;

// 启动主机服务
host.run().await?;
```

### 浏览器注册

```rust
use crate::native_messaging::{RegistryManager, HostRegistration};

// 创建主机注册信息
let registration = HostRegistration {
    name: "com.example.secure_password".to_string(),
    description: "Secure Password Manager".to_string(),
    path: "/path/to/native_host".to_string(),
    allowed_extensions: vec!["extension_id".to_string()],
    ..Default::default()
};

// 注册到Chrome
let registry = RegistryManager::new();
registry.register_chrome_host(&registration).await?;

// 注册到Firefox
registry.register_firefox_host(&registration).await?;
```

### 消息处理

```rust
use crate::native_messaging::{MessageHandler, NativeMessage};

struct CustomMessageHandler;

impl MessageHandler for CustomMessageHandler {
    async fn handle_message(&self, message: &NativeMessage) -> Result<NativeMessage> {
        match message.get_message_type() {
            MessageType::Request => {
                // 处理请求消息
                let response_payload = serde_json::json!({
                    "status": "success",
                    "data": "processed"
                });
                
                Ok(NativeMessage::new_response(
                    message.request_id.clone(),
                    response_payload,
                    "host".to_string(),
                ))
            }
            MessageType::Ping => {
                // 处理Ping消息
                Ok(NativeMessage::new_pong(
                    message.request_id.clone(),
                    "host".to_string(),
                ))
            }
            _ => {
                Err(NativeMessagingError::UnsupportedMessageType(
                    message.message_type.clone()
                ))
            }
        }
    }
}
```

## 配置选项

### HostConfig

```rust
pub struct HostConfig {
    /// 主机名称
    pub host_name: String,
    /// 允许的扩展ID列表
    pub allowed_extensions: Vec<String>,
    /// 最大消息大小 (字节)
    pub max_message_size: usize,
    /// 是否启用日志记录
    pub enable_logging: bool,
    /// 心跳间隔
    pub heartbeat_interval: Duration,
    /// 请求超时时间
    pub request_timeout: Duration,
    /// 最大并发请求数
    pub max_concurrent_requests: usize,
    /// IPC客户端配置
    pub ipc_config: IpcClientConfig,
}
```

### ProxyConfig

```rust
pub struct ProxyConfig {
    /// 请求超时时间
    pub timeout: Duration,
    /// 最大重试次数
    pub max_retries: u32,
    /// 重试延迟
    pub retry_delay: Duration,
    /// 是否启用批量处理
    pub enable_batch_processing: bool,
    /// 批量大小
    pub batch_size: usize,
    /// 批量超时时间
    pub batch_timeout: Duration,
    /// 是否启用缓存
    pub enable_caching: bool,
    /// 缓存TTL
    pub cache_ttl: Duration,
}
```

## 错误处理

### 错误类型

```rust
pub enum NativeMessagingError {
    /// IO错误
    IoError(std::io::Error),
    /// 序列化错误
    SerializationError(String),
    /// 协议错误
    ProtocolError(String),
    /// 消息格式错误
    MessageFormatError(String),
    /// 不支持的消息类型
    UnsupportedMessageType(String),
    /// 扩展未授权
    UnauthorizedExtension(String),
    /// 消息过大
    MessageTooLarge(usize),
    /// 连接错误
    ConnectionError(String),
    /// 超时错误
    TimeoutError(String),
    /// IPC错误
    IpcError(IpcError),
    /// 浏览器注册错误
    BrowserRegistrationError(String),
    /// 配置错误
    ConfigError(String),
    /// 内部错误
    InternalError(String),
}
```

### 错误处理最佳实践

```rust
// 使用Result类型处理错误
async fn handle_request(message: NativeMessage) -> Result<NativeMessage> {
    // 验证消息
    message.validate()?;
    
    // 处理请求
    match process_request(&message).await {
        Ok(response) => Ok(response),
        Err(e) => {
            error!("请求处理失败: {}", e);
            Ok(NativeMessage::new_error(
                message.request_id,
                format!("处理失败: {}", e),
                "host".to_string(),
            ))
        }
    }
}

// 记录错误日志
fn log_error(error: &NativeMessagingError) {
    match error {
        NativeMessagingError::UnauthorizedExtension(ext_id) => {
            warn!("未授权的扩展尝试连接: {}", ext_id);
        }
        NativeMessagingError::MessageTooLarge(size) => {
            warn!("消息过大: {} bytes", size);
        }
        _ => {
            error!("Native Messaging错误: {}", error);
        }
    }
}
```

## 性能优化

### 缓存策略

```rust
// 启用智能缓存
let proxy_config = ProxyConfig {
    enable_caching: true,
    cache_ttl: Duration::from_secs(300), // 5分钟
    ..Default::default()
};

// 缓存键生成策略
fn generate_cache_key(message: &NativeMessage) -> String {
    let mut key_parts = vec![message.message_type.clone()];
    
    if let Some(action) = message.payload.get("action") {
        key_parts.push(action.to_string());
    }
    
    if let Some(domain) = message.payload.get("domain") {
        key_parts.push(domain.to_string());
    }
    
    key_parts.join(":")
}
```

### 批量处理

```rust
// 启用批量处理
let proxy_config = ProxyConfig {
    enable_batch_processing: true,
    batch_size: 10,
    batch_timeout: Duration::from_millis(100),
    ..Default::default()
};

// 批量处理逻辑
async fn process_batch(requests: Vec<NativeMessage>) -> Vec<Result<NativeMessage>> {
    let futures: Vec<_> = requests.into_iter()
        .map(|req| process_single_request(req))
        .collect();
    
    futures::future::join_all(futures).await
}
```

## 监控和统计

### 统计信息

```rust
// 获取主机统计信息
let host_stats = host.get_stats().await;
println!("总请求数: {}", host_stats.total_requests);
println!("成功请求数: {}", host_stats.successful_requests);
println!("失败请求数: {}", host_stats.failed_requests);
println!("平均响应时间: {:.2}ms", host_stats.avg_response_time_ms);

// 获取代理统计信息
let proxy_stats = proxy.get_stats().await;
println!("缓存命中率: {:.2}%", 
    proxy_stats.cache_hits as f64 / 
    (proxy_stats.cache_hits + proxy_stats.cache_misses) as f64 * 100.0);
```

### 健康检查

```rust
// 实现健康检查
async fn health_check() -> Result<()> {
    // 检查IPC连接
    let ping_msg = IpcMessage::ping("health_check".to_string());
    let response = ipc_client.request(ping_msg).await?;
    
    if !response.is_success() {
        return Err(NativeMessagingError::ConnectionError(
            "IPC连接异常".to_string()
        ));
    }
    
    Ok(())
}
```

## 部署指南

### 系统要求

- **操作系统**: Windows 10+, macOS 10.14+, Linux (Ubuntu 18.04+)
- **浏览器**: Chrome 88+, Firefox 78+, Edge 88+
- **权限**: 需要注册表写入权限 (Windows) 或配置文件写入权限 (macOS/Linux)

### 安装步骤

1. **编译Native Messaging Host**
   ```bash
   cargo build --release -p secure-password-daemon
   ```

2. **注册浏览器主机**
   ```bash
   # Chrome
   ./target/release/secure-password-daemon --register-chrome
   
   # Firefox
   ./target/release/secure-password-daemon --register-firefox
   ```

3. **配置扩展权限**
   ```json
   {
     "name": "com.example.secure_password",
     "description": "Secure Password Manager",
     "path": "/path/to/secure-password-daemon",
     "type": "stdio",
     "allowed_extensions": ["your_extension_id"]
   }
   ```

### 故障排除

#### 常见问题

1. **扩展无法连接到Native Host**
   - 检查主机是否正确注册
   - 验证扩展ID是否在允许列表中
   - 检查主机程序路径是否正确

2. **消息传输失败**
   - 检查消息大小是否超过限制
   - 验证消息格式是否正确
   - 检查网络连接状态

3. **性能问题**
   - 启用缓存机制
   - 调整批量处理参数
   - 优化消息处理逻辑

#### 调试技巧

```rust
// 启用详细日志
let host_config = HostConfig {
    enable_logging: true,
    log_level: LogLevel::Debug,
    ..Default::default()
};

// 使用调试模式
#[cfg(debug_assertions)]
fn debug_message(message: &NativeMessage) {
    println!("Debug: {:?}", message);
}
```

## 安全考虑

### 访问控制

- **扩展验证**: 严格验证扩展来源
- **权限检查**: 基于扩展ID的细粒度权限控制
- **消息验证**: 验证消息完整性和格式

### 数据保护

- **敏感数据处理**: 敏感数据不进入缓存
- **内存清理**: 及时清理敏感数据
- **日志安全**: 避免在日志中记录敏感信息

## 测试

### 单元测试

```bash
# 运行所有Native Messaging测试
cargo test -p secure-password-daemon native_messaging

# 运行特定模块测试
cargo test -p secure-password-daemon native_messaging::protocol
cargo test -p secure-password-daemon native_messaging::host
cargo test -p secure-password-daemon native_messaging::proxy
```

### 集成测试

```bash
# 运行集成测试
cargo test -p secure-password-daemon --test native_messaging_integration
```

### 性能测试

```bash
# 运行性能基准测试
cargo bench -p secure-password-daemon native_messaging
```

## 扩展开发

### 自定义消息处理器

```rust
use crate::native_messaging::MessageHandler;

pub struct CustomHandler {
    // 自定义字段
}

impl MessageHandler for CustomHandler {
    async fn handle_message(&self, message: &NativeMessage) -> Result<NativeMessage> {
        // 自定义处理逻辑
        todo!()
    }
}

// 注册自定义处理器
let mut registry = HandlerRegistry::new();
registry.register("custom", Box::new(CustomHandler::new()));
```

### 自定义浏览器适配器

```rust
use crate::native_messaging::BrowserAdapter;

pub struct CustomBrowserAdapter;

impl BrowserAdapter for CustomBrowserAdapter {
    fn get_browser_type(&self) -> BrowserType {
        BrowserType::Custom("MyBrowser".to_string())
    }
    
    async fn handle_message(&self, message: &NativeMessage) -> Result<NativeMessage> {
        // 自定义浏览器处理逻辑
        todo!()
    }
}
```

## 版本历史

- **v1.0.0**: 初始版本，支持基本的Native Messaging功能
- **v1.1.0**: 添加批量处理和缓存支持
- **v1.2.0**: 增强安全特性和错误处理
- **v1.3.0**: 支持多浏览器适配和性能优化

## 许可证

本项目采用MIT许可证，详情请参阅LICENSE文件。

## 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 联系方式

- 项目主页: https://github.com/example/secure-password
- 问题报告: https://github.com/example/secure-password/issues
- 邮箱: <EMAIL> 