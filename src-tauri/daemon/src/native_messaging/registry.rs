//! 浏览器注册管理模块
//!
//! 负责管理Native Messaging主机的注册和配置

use crate::native_messaging::{
    error::{NativeMessagingError, Result},
    browser::BrowserType,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use tokio::fs;
use tracing::{debug, error, info, warn};

/// 主机注册信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HostRegistration {
    /// 主机名称
    pub name: String,
    /// 主机描述
    pub description: String,
    /// 可执行文件路径
    pub path: String,
    /// 通信类型 (通常是 "stdio")
    pub r#type: String,
    /// 允许的来源列表
    pub allowed_origins: Vec<String>,
    /// 允许的扩展列表 (Firefox)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub allowed_extensions: Option<Vec<String>>,
}

impl HostRegistration {
    /// 创建新的主机注册信息
    pub fn new(
        name: String,
        description: String,
        path: String,
        allowed_origins: Vec<String>,
    ) -> Self {
        Self {
            name,
            description,
            path,
            r#type: "stdio".to_string(),
            allowed_origins,
            allowed_extensions: None,
        }
    }
    
    /// 设置允许的扩展列表（Firefox）
    pub fn with_allowed_extensions(mut self, extensions: Vec<String>) -> Self {
        self.allowed_extensions = Some(extensions);
        self
    }
}

/// 注册管理器
pub struct RegistryManager {
    daemon_path: String,
    registrations: HashMap<BrowserType, HostRegistration>,
}

impl RegistryManager {
    /// 创建新的注册管理器
    pub fn new(daemon_path: String) -> Self {
        Self {
            daemon_path,
            registrations: HashMap::new(),
        }
    }
    
    /// 为所有浏览器创建注册配置
    pub fn create_registrations(&mut self, allowed_extensions: Vec<String>) -> Result<()> {
        // Chrome
        let chrome_origins = allowed_extensions
            .iter()
            .map(|ext| format!("chrome-extension://{}/", ext))
            .collect();
        
        let chrome_registration = HostRegistration::new(
            "com.securepassword.chrome".to_string(),
            "Secure Password Chrome Native Messaging Host".to_string(),
            self.daemon_path.clone(),
            chrome_origins,
        );
        
        self.registrations.insert(BrowserType::Chrome, chrome_registration);
        
        // Firefox
        let firefox_registration = HostRegistration::new(
            "com.securepassword.firefox".to_string(),
            "Secure Password Firefox Native Messaging Host".to_string(),
            self.daemon_path.clone(),
            vec![], // Firefox 不使用 origins
        ).with_allowed_extensions(allowed_extensions.clone());
        
        self.registrations.insert(BrowserType::Firefox, firefox_registration);
        
        // Edge
        let edge_origins = allowed_extensions
            .iter()
            .map(|ext| format!("chrome-extension://{}/", ext))
            .collect();
        
        let edge_registration = HostRegistration::new(
            "com.securepassword.edge".to_string(),
            "Secure Password Edge Native Messaging Host".to_string(),
            self.daemon_path.clone(),
            edge_origins,
        );
        
        self.registrations.insert(BrowserType::Edge, edge_registration);
        
        // Safari (macOS only)
        #[cfg(target_os = "macos")]
        {
            let safari_origins = allowed_extensions
                .iter()
                .map(|ext| format!("safari-extension://{}/", ext))
                .collect();
            
            let safari_registration = HostRegistration::new(
                "com.securepassword.safari".to_string(),
                "Secure Password Safari Native Messaging Host".to_string(),
                self.daemon_path.clone(),
                safari_origins,
            );
            
            self.registrations.insert(BrowserType::Safari, safari_registration);
        }
        
        info!("已创建 {} 个浏览器的注册配置", self.registrations.len());
        Ok(())
    }
    
    /// 注册所有浏览器
    pub async fn register_all(&self) -> Result<()> {
        let mut success_count = 0;
        let mut error_count = 0;
        
        for (browser_type, registration) in &self.registrations {
            match self.register_browser(browser_type, registration).await {
                Ok(()) => {
                    info!("成功注册 {} 浏览器", browser_type.name());
                    success_count += 1;
                }
                Err(e) => {
                    error!("注册 {} 浏览器失败: {}", browser_type.name(), e);
                    error_count += 1;
                }
            }
        }
        
        info!("浏览器注册完成: {} 成功, {} 失败", success_count, error_count);
        
        if error_count > 0 {
            warn!("部分浏览器注册失败，但不影响整体功能");
        }
        
        Ok(())
    }
    
    /// 取消注册所有浏览器
    pub async fn unregister_all(&self) -> Result<()> {
        let mut success_count = 0;
        let mut error_count = 0;
        
        for browser_type in self.registrations.keys() {
            match self.unregister_browser(browser_type).await {
                Ok(()) => {
                    info!("成功取消注册 {} 浏览器", browser_type.name());
                    success_count += 1;
                }
                Err(e) => {
                    error!("取消注册 {} 浏览器失败: {}", browser_type.name(), e);
                    error_count += 1;
                }
            }
        }
        
        info!("浏览器取消注册完成: {} 成功, {} 失败", success_count, error_count);
        Ok(())
    }
    
    /// 注册单个浏览器
    async fn register_browser(&self, browser_type: &BrowserType, registration: &HostRegistration) -> Result<()> {
        match browser_type {
            #[cfg(target_os = "windows")]
            _ => self.register_windows(browser_type, registration).await,
            
            #[cfg(target_os = "macos")]
            _ => self.register_macos(browser_type, registration).await,
            
            #[cfg(target_os = "linux")]
            _ => self.register_linux(browser_type, registration).await,
        }
    }
    
    /// 取消注册单个浏览器
    async fn unregister_browser(&self, browser_type: &BrowserType) -> Result<()> {
        match browser_type {
            #[cfg(target_os = "windows")]
            _ => self.unregister_windows(browser_type).await,
            
            #[cfg(target_os = "macos")]
            _ => self.unregister_macos(browser_type).await,
            
            #[cfg(target_os = "linux")]
            _ => self.unregister_linux(browser_type).await,
        }
    }
    
    /// Windows 注册
    #[cfg(target_os = "windows")]
    async fn register_windows(&self, browser_type: &BrowserType, registration: &HostRegistration) -> Result<()> {
        use winreg::enums::*;
        use winreg::RegKey;
        
        let registry_path = browser_type.registry_path();
        let config_json = serde_json::to_string_pretty(registration)?;
        
        // 写入注册表
        let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
        let key = hklm.create_subkey(&registry_path)
            .map_err(|e| NativeMessagingError::IoError(e.into()))?;
        
        key.set_value("", &config_json)
            .map_err(|e| NativeMessagingError::IoError(e.into()))?;
        
        debug!("Windows注册表写入成功: {}", registry_path);
        Ok(())
    }
    
    /// Windows 取消注册
    #[cfg(target_os = "windows")]
    async fn unregister_windows(&self, browser_type: &BrowserType) -> Result<()> {
        use winreg::enums::*;
        use winreg::RegKey;
        
        let registry_path = browser_type.registry_path();
        let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
        
        let _ = hklm.delete_subkey(&registry_path);
        debug!("Windows注册表删除: {}", registry_path);
        Ok(())
    }
    
    /// macOS 注册
    #[cfg(target_os = "macos")]
    async fn register_macos(&self, browser_type: &BrowserType, registration: &HostRegistration) -> Result<()> {
        let config_path = browser_type.config_path();
        let config_json = serde_json::to_string_pretty(registration)?;
        
        // 创建目录
        if let Some(parent) = config_path.parent() {
            fs::create_dir_all(parent).await?;
        }
        
        // 写入配置文件
        fs::write(&config_path, config_json).await?;
        
        debug!("macOS配置文件写入成功: {:?}", config_path);
        Ok(())
    }
    
    /// macOS 取消注册
    #[cfg(target_os = "macos")]
    async fn unregister_macos(&self, browser_type: &BrowserType) -> Result<()> {
        let config_path = browser_type.config_path();
        let _ = fs::remove_file(&config_path).await;
        debug!("macOS配置文件删除: {:?}", config_path);
        Ok(())
    }
    
    /// Linux 注册
    #[cfg(target_os = "linux")]
    async fn register_linux(&self, browser_type: &BrowserType, registration: &HostRegistration) -> Result<()> {
        let config_path = browser_type.config_path();
        let config_json = serde_json::to_string_pretty(registration)?;
        
        // 创建目录
        if let Some(parent) = config_path.parent() {
            fs::create_dir_all(parent).await?;
        }
        
        // 写入配置文件
        fs::write(&config_path, config_json).await?;
        
        debug!("Linux配置文件写入成功: {:?}", config_path);
        Ok(())
    }
    
    /// Linux 取消注册
    #[cfg(target_os = "linux")]
    async fn unregister_linux(&self, browser_type: &BrowserType) -> Result<()> {
        let config_path = browser_type.config_path();
        let _ = fs::remove_file(&config_path).await;
        debug!("Linux配置文件删除: {:?}", config_path);
        Ok(())
    }
    
    /// 检查浏览器是否已注册
    pub async fn is_registered(&self, browser_type: &BrowserType) -> bool {
        match browser_type {
            #[cfg(target_os = "windows")]
            _ => self.check_windows_registration(browser_type).await,
            
            #[cfg(target_os = "macos")]
            _ => self.check_macos_registration(browser_type).await,
            
            #[cfg(target_os = "linux")]
            _ => self.check_linux_registration(browser_type).await,
        }
    }
    
    /// 检查Windows注册状态
    #[cfg(target_os = "windows")]
    async fn check_windows_registration(&self, browser_type: &BrowserType) -> bool {
        use winreg::enums::*;
        use winreg::RegKey;
        
        let registry_path = browser_type.registry_path();
        let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
        
        hklm.open_subkey(&registry_path).is_ok()
    }
    
    /// 检查macOS注册状态
    #[cfg(target_os = "macos")]
    async fn check_macos_registration(&self, browser_type: &BrowserType) -> bool {
        let config_path = browser_type.config_path();
        fs::metadata(&config_path).await.is_ok()
    }
    
    /// 检查Linux注册状态
    #[cfg(target_os = "linux")]
    async fn check_linux_registration(&self, browser_type: &BrowserType) -> bool {
        let config_path = browser_type.config_path();
        fs::metadata(&config_path).await.is_ok()
    }
    
    /// 获取注册配置
    pub fn get_registration(&self, browser_type: &BrowserType) -> Option<&HostRegistration> {
        self.registrations.get(browser_type)
    }
    
    /// 获取所有注册配置
    pub fn get_all_registrations(&self) -> &HashMap<BrowserType, HostRegistration> {
        &self.registrations
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_host_registration_creation() {
        let registration = HostRegistration::new(
            "test.host".to_string(),
            "Test Host".to_string(),
            "/path/to/daemon".to_string(),
            vec!["chrome-extension://test/".to_string()],
        );
        
        assert_eq!(registration.name, "test.host");
        assert_eq!(registration.description, "Test Host");
        assert_eq!(registration.path, "/path/to/daemon");
        assert_eq!(registration.r#type, "stdio");
        assert_eq!(registration.allowed_origins.len(), 1);
        assert!(registration.allowed_extensions.is_none());
    }
    
    #[test]
    fn test_host_registration_with_extensions() {
        let registration = HostRegistration::new(
            "test.host".to_string(),
            "Test Host".to_string(),
            "/path/to/daemon".to_string(),
            vec![],
        ).with_allowed_extensions(vec!["<EMAIL>".to_string()]);
        
        assert!(registration.allowed_extensions.is_some());
        assert_eq!(registration.allowed_extensions.unwrap().len(), 1);
    }
    
    #[tokio::test]
    async fn test_registry_manager_creation() {
        let mut manager = RegistryManager::new("/test/daemon".to_string());
        
        let extensions = vec!["test-extension".to_string()];
        manager.create_registrations(extensions).unwrap();
        
        assert!(manager.get_registration(&BrowserType::Chrome).is_some());
        assert!(manager.get_registration(&BrowserType::Firefox).is_some());
        assert!(manager.get_registration(&BrowserType::Edge).is_some());
        
        #[cfg(target_os = "macos")]
        assert!(manager.get_registration(&BrowserType::Safari).is_some());
    }
    
    #[test]
    fn test_registration_serialization() {
        let registration = HostRegistration::new(
            "test.host".to_string(),
            "Test Host".to_string(),
            "/path/to/daemon".to_string(),
            vec!["chrome-extension://test/".to_string()],
        );
        
        let json = serde_json::to_string(&registration).unwrap();
        let deserialized: HostRegistration = serde_json::from_str(&json).unwrap();
        
        assert_eq!(registration.name, deserialized.name);
        assert_eq!(registration.description, deserialized.description);
        assert_eq!(registration.path, deserialized.path);
    }
} 