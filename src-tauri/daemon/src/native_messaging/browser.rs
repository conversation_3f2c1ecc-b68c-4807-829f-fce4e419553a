//! 浏览器适配层模块
//!
//! 提供跨浏览器的Native Messaging支持，包括Chrome、Firefox、Edge、Safari

use crate::native_messaging::{
    error::{NativeMessagingError, Result},
    protocol::{NativeMessage, MessageType},
};
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::{Arc, RwLock};
use tokio::sync::Mutex;
use tracing::{debug, error, info, warn};

/// 支持的浏览器类型
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum BrowserType {
    Chrome,
    Firefox,
    Edge,
    Safari,
}

impl BrowserType {
    /// 获取浏览器名称
    pub fn name(&self) -> &'static str {
        match self {
            BrowserType::Chrome => "Chrome",
            BrowserType::Firefox => "Firefox",
            BrowserType::Edge => "Edge",
            BrowserType::Safari => "Safari",
        }
    }

    /// 获取Native Messaging主机名
    pub fn host_name(&self) -> String {
        match self {
            BrowserType::Chrome => "com.securepassword.chrome",
            BrowserType::Firefox => "com.securepassword.firefox",
            BrowserType::Edge => "com.securepassword.edge",
            BrowserType::Safari => "com.securepassword.safari",
        }.to_string()
    }

    /// 获取注册表路径（Windows）
    #[cfg(target_os = "windows")]
    pub fn registry_path(&self) -> String {
        match self {
            BrowserType::Chrome => r"SOFTWARE\Google\Chrome\NativeMessagingHosts\com.securepassword.chrome",
            BrowserType::Firefox => r"SOFTWARE\Mozilla\NativeMessagingHosts\com.securepassword.firefox",
            BrowserType::Edge => r"SOFTWARE\Microsoft\Edge\NativeMessagingHosts\com.securepassword.edge",
            BrowserType::Safari => r"SOFTWARE\Apple\Safari\NativeMessagingHosts\com.securepassword.safari",
        }.to_string()
    }

    /// 获取配置文件路径（macOS/Linux）
    #[cfg(not(target_os = "windows"))]
    pub fn config_path(&self) -> PathBuf {
        let home = std::env::var("HOME").unwrap_or_else(|_| "/tmp".to_string());
        match self {
            BrowserType::Chrome => {
                #[cfg(target_os = "macos")]
                return PathBuf::from(format!("{}/Library/Application Support/Google/Chrome/NativeMessagingHosts/com.securepassword.chrome.json", home));
                #[cfg(target_os = "linux")]
                return PathBuf::from(format!("{}/.config/google-chrome/NativeMessagingHosts/com.securepassword.chrome.json", home));
            }
            BrowserType::Firefox => {
                #[cfg(target_os = "macos")]
                return PathBuf::from(format!("{}/Library/Application Support/Mozilla/NativeMessagingHosts/com.securepassword.firefox.json", home));
                #[cfg(target_os = "linux")]
                return PathBuf::from(format!("{}/.mozilla/native-messaging-hosts/com.securepassword.firefox.json", home));
            }
            BrowserType::Edge => {
                #[cfg(target_os = "macos")]
                return PathBuf::from(format!("{}/Library/Application Support/Microsoft Edge/NativeMessagingHosts/com.securepassword.edge.json", home));
                #[cfg(target_os = "linux")]
                return PathBuf::from(format!("{}/.config/microsoft-edge/NativeMessagingHosts/com.securepassword.edge.json", home));
            }
            BrowserType::Safari => {
                #[cfg(target_os = "macos")]
                return PathBuf::from(format!("{}/Library/Safari/NativeMessagingHosts/com.securepassword.safari.json", home));
                #[cfg(target_os = "linux")]
                return PathBuf::from("/tmp/safari-not-supported.json");
            }
        }
    }
}

/// 浏览器适配器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BrowserAdapterConfig {
    /// 浏览器类型
    pub browser_type: BrowserType,
    /// 允许的扩展ID列表
    pub allowed_extensions: Vec<String>,
    /// 连接超时时间（秒）
    pub connection_timeout: u64,
    /// 消息超时时间（秒）
    pub message_timeout: u64,
    /// 最大重试次数
    pub max_retries: u32,
    /// 是否启用调试模式
    pub debug_mode: bool,
}

impl Default for BrowserAdapterConfig {
    fn default() -> Self {
        Self {
            browser_type: BrowserType::Chrome,
            allowed_extensions: vec![],
            connection_timeout: 30,
            message_timeout: 10,
            max_retries: 3,
            debug_mode: false,
        }
    }
}

/// 浏览器适配器统计信息
#[derive(Debug, Clone, Default)]
pub struct BrowserAdapterStats {
    /// 总消息数
    pub total_messages: u64,
    /// 成功消息数
    pub successful_messages: u64,
    /// 失败消息数
    pub failed_messages: u64,
    /// 平均响应时间（毫秒）
    pub avg_response_time: u64,
    /// 最后活动时间
    pub last_activity: Option<std::time::SystemTime>,
}

impl BrowserAdapterStats {
    /// 记录消息处理
    pub fn record_message(&mut self, success: bool, response_time: std::time::Duration) {
        self.total_messages += 1;
        if success {
            self.successful_messages += 1;
        } else {
            self.failed_messages += 1;
        }
        
        // 更新平均响应时间
        let new_time = response_time.as_millis() as u64;
        if self.total_messages == 1 {
            self.avg_response_time = new_time;
        } else {
            self.avg_response_time = (self.avg_response_time * (self.total_messages - 1) + new_time) / self.total_messages;
        }
        
        self.last_activity = Some(std::time::SystemTime::now());
    }

    /// 获取成功率
    pub fn success_rate(&self) -> f64 {
        if self.total_messages == 0 {
            0.0
        } else {
            self.successful_messages as f64 / self.total_messages as f64
        }
    }
}

/// 浏览器适配器接口
#[async_trait]
pub trait BrowserAdapter: Send + Sync {
    /// 获取浏览器类型
    fn browser_type(&self) -> BrowserType;

    /// 获取配置
    fn config(&self) -> &BrowserAdapterConfig;

    /// 获取统计信息
    async fn stats(&self) -> BrowserAdapterStats;

    /// 处理来自浏览器的消息
    async fn handle_message(&self, message: NativeMessage) -> Result<NativeMessage>;

    /// 验证扩展权限
    fn validate_extension(&self, extension_id: &str) -> Result<()>;

    /// 注册Native Messaging主机
    async fn register_host(&self, daemon_path: &str) -> Result<()>;

    /// 取消注册Native Messaging主机
    async fn unregister_host(&self) -> Result<()>;

    /// 检查浏览器是否已安装
    async fn is_browser_installed(&self) -> bool;

    /// 获取浏览器版本
    async fn get_browser_version(&self) -> Option<String>;
}

/// Chrome浏览器适配器
pub struct ChromeAdapter {
    config: BrowserAdapterConfig,
    stats: Arc<Mutex<BrowserAdapterStats>>,
}

impl ChromeAdapter {
    pub fn new(config: BrowserAdapterConfig) -> Self {
        let mut chrome_config = config;
        chrome_config.browser_type = BrowserType::Chrome;

        Self {
            config: chrome_config,
            stats: Arc::new(Mutex::new(BrowserAdapterStats::default())),
        }
    }

    /// 转发消息到后端服务进行实际处理
    async fn forward_to_ipc_service(&self, message: &NativeMessage) -> Result<NativeMessage> {
        // 更新统计信息
        {
            let mut stats = self.stats.lock().await;
            stats.total_messages += 1;
            stats.last_activity = Some(std::time::SystemTime::now());
        }

        // 根据消息类型和payload中的action进行实际处理
        if let Some(action) = message.payload.get("action").and_then(|v| v.as_str()) {
            match action {
                "get_passwords" => self.handle_get_passwords(message).await,
                "save_password" => self.handle_save_password(message).await,
                "delete_password" => self.handle_delete_password(message).await,
                "generate_password" => self.handle_generate_password(message).await,
                "check_breach" => self.handle_check_breach(message).await,
                _ => {
                    warn!("Chrome适配器收到未知操作类型: {}", action);
                    Ok(NativeMessage::new_error(
                        message.request_id.clone(),
                        "unknown_action".to_string(),
                        format!("不支持的操作类型: {}", action),
                        "daemon".to_string(),
                    ))
                }
            }
        } else {
            // 如果没有action字段，返回通用响应
            debug!("Chrome适配器处理无action的消息: {:?}", message.message_type);
            Ok(NativeMessage::new_response(
                message.request_id.clone(),
                serde_json::json!({
                    "status": "processed",
                    "browser": "chrome",
                    "message_type": message.message_type
                }),
                "daemon".to_string(),
            ))
        }
    }

    /// 处理获取密码请求
    async fn handle_get_passwords(&self, message: &NativeMessage) -> Result<NativeMessage> {
        debug!("Chrome适配器处理获取密码请求");
        // TODO: 实际应该调用密码管理服务
        Ok(NativeMessage::new_response(
            message.request_id.clone(),
            serde_json::json!({
                "passwords": [],
                "count": 0,
                "browser": "chrome"
            }),
            "daemon".to_string(),
        ))
    }

    /// 处理保存密码请求
    async fn handle_save_password(&self, message: &NativeMessage) -> Result<NativeMessage> {
        debug!("Chrome适配器处理保存密码请求");
        // TODO: 实际应该调用密码管理服务
        Ok(NativeMessage::new_response(
            message.request_id.clone(),
            serde_json::json!({
                "status": "saved",
                "browser": "chrome"
            }),
            "daemon".to_string(),
        ))
    }

    /// 处理删除密码请求
    async fn handle_delete_password(&self, message: &NativeMessage) -> Result<NativeMessage> {
        debug!("Chrome适配器处理删除密码请求");
        // TODO: 实际应该调用密码管理服务
        Ok(NativeMessage::new_response(
            message.request_id.clone(),
            serde_json::json!({
                "status": "deleted",
                "browser": "chrome"
            }),
            "daemon".to_string(),
        ))
    }

    /// 处理生成密码请求
    async fn handle_generate_password(&self, message: &NativeMessage) -> Result<NativeMessage> {
        debug!("Chrome适配器处理生成密码请求");
        // TODO: 实际应该调用密码生成服务
        Ok(NativeMessage::new_response(
            message.request_id.clone(),
            serde_json::json!({
                "password": "GeneratedPassword123!",
                "strength": "strong",
                "browser": "chrome"
            }),
            "daemon".to_string(),
        ))
    }

    /// 处理密码泄露检查请求
    async fn handle_check_breach(&self, message: &NativeMessage) -> Result<NativeMessage> {
        debug!("Chrome适配器处理密码泄露检查请求");
        // TODO: 实际应该调用泄露检查服务
        Ok(NativeMessage::new_response(
            message.request_id.clone(),
            serde_json::json!({
                "is_breached": false,
                "breach_count": 0,
                "browser": "chrome"
            }),
            "daemon".to_string(),
        ))
    }
}

#[async_trait]
impl BrowserAdapter for ChromeAdapter {
    fn browser_type(&self) -> BrowserType {
        BrowserType::Chrome
    }

    fn config(&self) -> &BrowserAdapterConfig {
        &self.config
    }

    async fn stats(&self) -> BrowserAdapterStats {
        self.stats.lock().await.clone()
    }

    async fn handle_message(&self, message: NativeMessage) -> Result<NativeMessage> {
        let start_time = std::time::Instant::now();
        
        // 验证扩展权限
        self.validate_extension(&message.source)?;

        // 处理消息
        let response = match message.get_message_type() {
            MessageType::Ping => {
                debug!("Chrome适配器处理心跳消息");
                NativeMessage::new_pong(
                    message.request_id,
                    "daemon".to_string(),
                )
            }
            _ => {
                debug!("Chrome适配器处理消息: {:?}", message.message_type);
                // 转发到IPC服务进行实际处理
                self.forward_to_ipc_service(&message).await.unwrap_or_else(|e| {
                    error!("转发到IPC服务失败: {}", e);
                    NativeMessage::new_error(
                        message.request_id.clone(),
                        "ipc_forward_error".to_string(),
                        format!("无法转发请求到IPC服务: {}", e),
                        "daemon".to_string(),
                    )
                })
            }
        };

        // 记录统计信息
        let mut stats = self.stats.lock().await;
        stats.record_message(true, start_time.elapsed());

        Ok(response)
    }

    fn validate_extension(&self, extension_id: &str) -> Result<()> {
        if self.config.allowed_extensions.is_empty() {
            return Ok(()); // 如果没有限制，则允许所有扩展
        }

        if self.config.allowed_extensions.contains(&extension_id.to_string()) {
            Ok(())
        } else {
            Err(NativeMessagingError::SecurityError(
                format!("Chrome扩展 {} 未在允许列表中", extension_id)
            ))
        }
    }

    async fn register_host(&self, daemon_path: &str) -> Result<()> {
        let host_config = serde_json::json!({
            "name": self.browser_type().host_name(),
            "description": "Secure Password Chrome Native Messaging Host",
            "path": daemon_path,
            "type": "stdio",
            "allowed_origins": self.config.allowed_extensions.iter()
                .map(|id| format!("chrome-extension://{}/", id))
                .collect::<Vec<_>>()
        });

        #[cfg(target_os = "windows")]
        {
            // Windows注册表注册
            use winreg::enums::*;
            use winreg::RegKey;
            
            let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
            let key = hklm.create_subkey(self.browser_type().registry_path())?;
            key.set_value("", &daemon_path)?;
            info!("Chrome Native Messaging主机已注册到Windows注册表");
        }

        #[cfg(not(target_os = "windows"))]
        {
            // macOS/Linux文件系统注册
            let config_path = self.browser_type().config_path();
            if let Some(parent) = config_path.parent() {
                tokio::fs::create_dir_all(parent).await?;
            }
            tokio::fs::write(&config_path, serde_json::to_string_pretty(&host_config)?).await?;
            info!("Chrome Native Messaging主机已注册到: {:?}", config_path);
        }

        Ok(())
    }

    async fn unregister_host(&self) -> Result<()> {
        #[cfg(target_os = "windows")]
        {
            use winreg::enums::*;
            use winreg::RegKey;
            
            let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
            let _ = hklm.delete_subkey(self.browser_type().registry_path());
            info!("Chrome Native Messaging主机已从Windows注册表移除");
        }

        #[cfg(not(target_os = "windows"))]
        {
            let config_path = self.browser_type().config_path();
            let _ = tokio::fs::remove_file(&config_path).await;
            info!("Chrome Native Messaging主机配置文件已移除: {:?}", config_path);
        }

        Ok(())
    }

    async fn is_browser_installed(&self) -> bool {
        #[cfg(target_os = "windows")]
        {
            use winreg::enums::*;
            use winreg::RegKey;
            
            let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
            hklm.open_subkey(r"SOFTWARE\Google\Chrome").is_ok()
        }

        #[cfg(target_os = "macos")]
        {
            tokio::fs::metadata("/Applications/Google Chrome.app").await.is_ok()
        }

        #[cfg(target_os = "linux")]
        {
            tokio::process::Command::new("which")
                .arg("google-chrome")
                .output()
                .await
                .map(|output| output.status.success())
                .unwrap_or(false)
        }
    }

    async fn get_browser_version(&self) -> Option<String> {
        #[cfg(target_os = "windows")]
        {
            use winreg::enums::*;
            use winreg::RegKey;
            
            let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
            if let Ok(chrome_key) = hklm.open_subkey(r"SOFTWARE\Google\Chrome") {
                if let Ok(version) = chrome_key.get_value::<String, _>("Version") {
                    return Some(version);
                }
            }
        }

        #[cfg(target_os = "macos")]
        {
            if let Ok(output) = tokio::process::Command::new("/Applications/Google Chrome.app/Contents/MacOS/Google Chrome")
                .arg("--version")
                .output()
                .await
            {
                if let Ok(version_str) = String::from_utf8(output.stdout) {
                    return Some(version_str.trim().to_string());
                }
            }
        }

        #[cfg(target_os = "linux")]
        {
            if let Ok(output) = tokio::process::Command::new("google-chrome")
                .arg("--version")
                .output()
                .await
            {
                if let Ok(version_str) = String::from_utf8(output.stdout) {
                    return Some(version_str.trim().to_string());
                }
            }
        }

        None
    }
}

/// Firefox浏览器适配器
pub struct FirefoxAdapter {
    config: BrowserAdapterConfig,
    stats: Arc<Mutex<BrowserAdapterStats>>,
}

impl FirefoxAdapter {
    pub fn new(config: BrowserAdapterConfig) -> Self {
        let mut firefox_config = config;
        firefox_config.browser_type = BrowserType::Firefox;
        
        Self {
            config: firefox_config,
            stats: Arc::new(Mutex::new(BrowserAdapterStats::default())),
        }
    }
}

#[async_trait]
impl BrowserAdapter for FirefoxAdapter {
    fn browser_type(&self) -> BrowserType {
        BrowserType::Firefox
    }

    fn config(&self) -> &BrowserAdapterConfig {
        &self.config
    }

    async fn stats(&self) -> BrowserAdapterStats {
        self.stats.lock().await.clone()
    }

    async fn handle_message(&self, message: NativeMessage) -> Result<NativeMessage> {
        let start_time = std::time::Instant::now();
        
        self.validate_extension(&message.source)?;

        let response = match message.get_message_type() {
            MessageType::Ping => {
                debug!("Firefox适配器处理心跳消息");
                NativeMessage::new_pong(
                    message.request_id,
                    "daemon".to_string(),
                )
            }
            _ => {
                debug!("Firefox适配器处理消息: {:?}", message.message_type);
                NativeMessage::new_response(
                    message.request_id,
                    serde_json::json!({"status": "processed", "browser": "firefox"}),
                    "daemon".to_string(),
                )
            }
        };

        let mut stats = self.stats.lock().await;
        stats.record_message(true, start_time.elapsed());

        Ok(response)
    }

    fn validate_extension(&self, extension_id: &str) -> Result<()> {
        if self.config.allowed_extensions.is_empty() {
            return Ok(());
        }

        if self.config.allowed_extensions.contains(&extension_id.to_string()) {
            Ok(())
        } else {
            Err(NativeMessagingError::SecurityError(
                format!("Firefox扩展 {} 未在允许列表中", extension_id)
            ))
        }
    }

    async fn register_host(&self, daemon_path: &str) -> Result<()> {
        let host_config = serde_json::json!({
            "name": self.browser_type().host_name(),
            "description": "Secure Password Firefox Native Messaging Host",
            "path": daemon_path,
            "type": "stdio",
            "allowed_extensions": self.config.allowed_extensions
        });

        #[cfg(not(target_os = "windows"))]
        {
            let config_path = self.browser_type().config_path();
            if let Some(parent) = config_path.parent() {
                tokio::fs::create_dir_all(parent).await?;
            }
            tokio::fs::write(&config_path, serde_json::to_string_pretty(&host_config)?).await?;
            info!("Firefox Native Messaging主机已注册到: {:?}", config_path);
        }

        Ok(())
    }

    async fn unregister_host(&self) -> Result<()> {
        #[cfg(not(target_os = "windows"))]
        {
            let config_path = self.browser_type().config_path();
            let _ = tokio::fs::remove_file(&config_path).await;
            info!("Firefox Native Messaging主机配置文件已移除: {:?}", config_path);
        }

        Ok(())
    }

    async fn is_browser_installed(&self) -> bool {
        #[cfg(target_os = "windows")]
        {
            use winreg::enums::*;
            use winreg::RegKey;
            
            let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
            hklm.open_subkey(r"SOFTWARE\Mozilla\Firefox").is_ok()
        }

        #[cfg(target_os = "macos")]
        {
            tokio::fs::metadata("/Applications/Firefox.app").await.is_ok()
        }

        #[cfg(target_os = "linux")]
        {
            tokio::process::Command::new("which")
                .arg("firefox")
                .output()
                .await
                .map(|output| output.status.success())
                .unwrap_or(false)
        }
    }

    async fn get_browser_version(&self) -> Option<String> {
        #[cfg(target_os = "linux")]
        {
            if let Ok(output) = tokio::process::Command::new("firefox")
                .arg("--version")
                .output()
                .await
            {
                if let Ok(version_str) = String::from_utf8(output.stdout) {
                    return Some(version_str.trim().to_string());
                }
            }
        }

        #[cfg(target_os = "macos")]
        {
            if let Ok(output) = tokio::process::Command::new("/Applications/Firefox.app/Contents/MacOS/firefox")
                .arg("--version")
                .output()
                .await
            {
                if let Ok(version_str) = String::from_utf8(output.stdout) {
                    return Some(version_str.trim().to_string());
                }
            }
        }

        None
    }
}

/// 浏览器注册管理器
pub struct BrowserRegistry {
    adapters: HashMap<BrowserType, Box<dyn BrowserAdapter>>,
    configs: HashMap<BrowserType, BrowserAdapterConfig>,
}

impl BrowserRegistry {
    /// 创建新的浏览器注册管理器
    pub fn new() -> Self {
        Self {
            adapters: HashMap::new(),
            configs: HashMap::new(),
        }
    }

    /// 注册浏览器适配器
    pub fn register_adapter(&mut self, adapter: Box<dyn BrowserAdapter>) {
        let browser_type = adapter.browser_type();
        let config = adapter.config().clone();
        self.configs.insert(browser_type.clone(), config);
        self.adapters.insert(browser_type, adapter);
    }

    /// 获取浏览器适配器
    pub fn get_adapter(&self, browser_type: &BrowserType) -> Option<&Box<dyn BrowserAdapter>> {
        self.adapters.get(browser_type)
    }

    /// 获取所有支持的浏览器类型
    pub fn supported_browsers(&self) -> Vec<BrowserType> {
        self.adapters.keys().cloned().collect()
    }

    /// 注册所有Native Messaging主机
    pub async fn register_all_hosts(&self, daemon_path: &str) -> Result<()> {
        for adapter in self.adapters.values() {
            if let Err(e) = adapter.register_host(daemon_path).await {
                warn!("注册{}主机失败: {}", adapter.browser_type().name(), e);
            }
        }
        Ok(())
    }

    /// 取消注册所有Native Messaging主机
    pub async fn unregister_all_hosts(&self) -> Result<()> {
        for adapter in self.adapters.values() {
            if let Err(e) = adapter.unregister_host().await {
                warn!("取消注册{}主机失败: {}", adapter.browser_type().name(), e);
            }
        }
        Ok(())
    }

    /// 处理来自浏览器的消息
    pub async fn handle_browser_message(&self, browser_type: &BrowserType, message: NativeMessage) -> Result<NativeMessage> {
        if let Some(adapter) = self.get_adapter(browser_type) {
            adapter.handle_message(message).await
        } else {
            Err(NativeMessagingError::ProtocolError(
                format!("不支持的浏览器类型: {:?}", browser_type)
            ))
        }
    }

    /// 获取所有浏览器的统计信息
    pub async fn get_all_stats(&self) -> HashMap<BrowserType, BrowserAdapterStats> {
        let mut stats = HashMap::new();
        for (browser_type, adapter) in &self.adapters {
            stats.insert(browser_type.clone(), adapter.stats().await);
        }
        stats
    }
}

impl Default for BrowserRegistry {
    fn default() -> Self {
        let mut registry = Self::new();
        
        // 注册默认的浏览器适配器
        registry.register_adapter(Box::new(ChromeAdapter::new(BrowserAdapterConfig {
            browser_type: BrowserType::Chrome,
            ..Default::default()
        })));
        
        registry.register_adapter(Box::new(FirefoxAdapter::new(BrowserAdapterConfig {
            browser_type: BrowserType::Firefox,
            ..Default::default()
        })));
        
        registry
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_browser_type_name() {
        assert_eq!(BrowserType::Chrome.name(), "Chrome");
        assert_eq!(BrowserType::Firefox.name(), "Firefox");
        assert_eq!(BrowserType::Edge.name(), "Edge");
        assert_eq!(BrowserType::Safari.name(), "Safari");
    }

    #[test]
    fn test_browser_type_host_name() {
        assert_eq!(BrowserType::Chrome.host_name(), "com.securepassword.chrome");
        assert_eq!(BrowserType::Firefox.host_name(), "com.securepassword.firefox");
    }

    #[test]
    fn test_browser_adapter_config_default() {
        let config = BrowserAdapterConfig::default();
        assert_eq!(config.browser_type, BrowserType::Chrome);
        assert_eq!(config.connection_timeout, 30);
        assert_eq!(config.max_retries, 3);
    }

    #[test]
    fn test_browser_adapter_stats() {
        let mut stats = BrowserAdapterStats::default();
        assert_eq!(stats.total_messages, 0);
        assert_eq!(stats.success_rate(), 0.0);
        
        stats.record_message(true, std::time::Duration::from_millis(100));
        assert_eq!(stats.total_messages, 1);
        assert_eq!(stats.successful_messages, 1);
        assert_eq!(stats.success_rate(), 1.0);
        
        stats.record_message(false, std::time::Duration::from_millis(200));
        assert_eq!(stats.total_messages, 2);
        assert_eq!(stats.successful_messages, 1);
        assert_eq!(stats.success_rate(), 0.5);
    }

    #[tokio::test]
    async fn test_chrome_adapter_creation() {
        let config = BrowserAdapterConfig::default();
        let adapter = ChromeAdapter::new(config);
        assert_eq!(adapter.browser_type(), BrowserType::Chrome);
    }

    #[tokio::test]
    async fn test_firefox_adapter_creation() {
        let config = BrowserAdapterConfig::default();
        let adapter = FirefoxAdapter::new(config);
        assert_eq!(adapter.browser_type(), BrowserType::Firefox);
    }

    #[tokio::test]
    async fn test_browser_registry_creation() {
        let registry = BrowserRegistry::default();
        let supported = registry.supported_browsers();
        assert!(supported.contains(&BrowserType::Chrome));
        assert!(supported.contains(&BrowserType::Firefox));
    }

    #[tokio::test]
    async fn test_message_handling() {
        let mut config = BrowserAdapterConfig::default();
        // 确保允许测试扩展
        config.allowed_extensions = vec!["test-extension".to_string()];
        let adapter = ChromeAdapter::new(config);

        let message = NativeMessage::new_request(
            "test-request".to_string(),
            serde_json::json!({"action": "get_passwords"}),
            "test-extension".to_string(),
        );

        let response = adapter.handle_message(message).await.unwrap();
        assert_eq!(response.get_message_type(), MessageType::Response);
        assert_eq!(response.source, "daemon");
    }
} 