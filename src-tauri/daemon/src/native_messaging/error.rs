//! # Native Messaging 错误类型
//! 
//! 定义了 Native Messaging 模块中使用的所有错误类型。

use std::fmt;
use serde::{Deserialize, Serialize};

/// Native Messaging 模块的结果类型
pub type Result<T> = std::result::Result<T, NativeMessagingError>;

/// Native Messaging 错误类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum NativeMessagingError {
    /// IO 错误
    IoError(String),
    
    /// 序列化/反序列化错误
    SerializationError(String),
    
    /// 协议错误
    ProtocolError(String),
    
    /// 消息格式错误
    MessageFormatError(String),
    
    /// 消息大小超限
    MessageTooLarge(usize),
    
    /// 连接错误
    ConnectionError(String),
    
    /// 超时错误
    TimeoutError(String),
    
    /// 浏览器不支持
    UnsupportedBrowser(String),
    
    /// 扩展ID无效
    InvalidExtensionId(String),
    
    /// 扩展未授权
    ExtensionNotAuthorized(String),
    
    /// 请求处理错误
    RequestHandlingError(String),
    
    /// 代理错误
    ProxyError(String),
    
    /// 注册表错误
    RegistryError(String),
    
    /// 配置错误
    ConfigError(String),
    
    /// 安全错误
    SecurityError(String),
    
    /// 内部错误
    InternalError(String),
}

impl fmt::Display for NativeMessagingError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            NativeMessagingError::IoError(msg) => write!(f, "IO错误: {}", msg),
            NativeMessagingError::SerializationError(msg) => write!(f, "序列化错误: {}", msg),
            NativeMessagingError::ProtocolError(msg) => write!(f, "协议错误: {}", msg),
            NativeMessagingError::MessageFormatError(msg) => write!(f, "消息格式错误: {}", msg),
            NativeMessagingError::MessageTooLarge(size) => write!(f, "消息过大: {} bytes", size),
            NativeMessagingError::ConnectionError(msg) => write!(f, "连接错误: {}", msg),
            NativeMessagingError::TimeoutError(msg) => write!(f, "超时错误: {}", msg),
            NativeMessagingError::UnsupportedBrowser(browser) => write!(f, "不支持的浏览器: {}", browser),
            NativeMessagingError::InvalidExtensionId(id) => write!(f, "无效的扩展ID: {}", id),
            NativeMessagingError::ExtensionNotAuthorized(id) => write!(f, "扩展未授权: {}", id),
            NativeMessagingError::RequestHandlingError(msg) => write!(f, "请求处理错误: {}", msg),
            NativeMessagingError::ProxyError(msg) => write!(f, "代理错误: {}", msg),
            NativeMessagingError::RegistryError(msg) => write!(f, "注册表错误: {}", msg),
            NativeMessagingError::ConfigError(msg) => write!(f, "配置错误: {}", msg),
            NativeMessagingError::SecurityError(msg) => write!(f, "安全错误: {}", msg),
            NativeMessagingError::InternalError(msg) => write!(f, "内部错误: {}", msg),
        }
    }
}

impl std::error::Error for NativeMessagingError {}

// 从其他错误类型转换
impl From<std::io::Error> for NativeMessagingError {
    fn from(error: std::io::Error) -> Self {
        NativeMessagingError::IoError(error.to_string())
    }
}

impl From<serde_json::Error> for NativeMessagingError {
    fn from(error: serde_json::Error) -> Self {
        NativeMessagingError::SerializationError(error.to_string())
    }
}

impl From<tokio::time::error::Elapsed> for NativeMessagingError {
    fn from(error: tokio::time::error::Elapsed) -> Self {
        NativeMessagingError::TimeoutError(error.to_string())
    }
}

// 转换为IPC错误
impl From<crate::ipc::IpcError> for NativeMessagingError {
    fn from(error: crate::ipc::IpcError) -> Self {
        NativeMessagingError::ProxyError(format!("IPC错误: {}", error))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_display() {
        let error = NativeMessagingError::IoError("test error".to_string());
        assert_eq!(error.to_string(), "IO错误: test error");
    }

    #[test]
    fn test_error_from_io_error() {
        let io_error = std::io::Error::new(std::io::ErrorKind::NotFound, "file not found");
        let nm_error = NativeMessagingError::from(io_error);
        assert!(matches!(nm_error, NativeMessagingError::IoError(_)));
    }

    #[test]
    fn test_error_from_serde_error() {
        let serde_error = serde_json::from_str::<serde_json::Value>("invalid json").unwrap_err();
        let nm_error = NativeMessagingError::from(serde_error);
        assert!(matches!(nm_error, NativeMessagingError::SerializationError(_)));
    }

    #[test]
    fn test_error_serialization() {
        let error = NativeMessagingError::MessageTooLarge(1024);
        let serialized = serde_json::to_string(&error).unwrap();
        let deserialized: NativeMessagingError = serde_json::from_str(&serialized).unwrap();
        assert!(matches!(deserialized, NativeMessagingError::MessageTooLarge(1024)));
    }
} 