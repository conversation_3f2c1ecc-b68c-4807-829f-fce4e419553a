//! # Native Messaging 协议实现
//! 
//! 实现了 Native Messaging 协议的消息格式、编解码器和版本管理。
//! 
//! ## 协议规范
//! 
//! Native Messaging 协议使用 JSON 格式传输消息，每个消息前缀4字节长度信息。
//! 
//! ```text
//! [4 bytes length][JSON message]
//! ```

use std::collections::HashMap;
use std::io::{Read, Write};
use std::time::{SystemTime, UNIX_EPOCH};
use serde::{Deserialize, Serialize};
use tokio::io::{AsyncRead, AsyncReadExt, AsyncWrite, AsyncWriteExt};
use crate::native_messaging::{Result, NativeMessagingError, MAX_MESSAGE_SIZE};

/// 协议版本
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ProtocolVersion {
    V1 = 1,
    V2 = 2,
}

impl ProtocolVersion {
    /// 获取所有支持的版本
    pub fn supported_versions() -> Vec<Self> {
        vec![Self::V1, Self::V2]
    }
    
    /// 从数字转换
    pub fn from_u32(value: u32) -> Option<Self> {
        match value {
            1 => Some(Self::V1),
            2 => Some(Self::V2),
            _ => None,
        }
    }
    
    /// 转换为数字
    pub fn to_u32(self) -> u32 {
        self as u32
    }
}

/// 消息类型
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum MessageType {
    /// 请求消息
    Request,
    /// 响应消息
    Response,
    /// 错误消息
    Error,
    /// 心跳消息
    Ping,
    /// 心跳响应
    Pong,
    /// 通知消息
    Notification,
    /// 自定义消息类型
    Custom(String),
}

impl MessageType {
    /// 从字符串转换
    pub fn from_str(s: &str) -> Self {
        match s {
            "request" => Self::Request,
            "response" => Self::Response,
            "error" => Self::Error,
            "ping" => Self::Ping,
            "pong" => Self::Pong,
            "notification" => Self::Notification,
            _ => Self::Custom(s.to_string()),
        }
    }
    
    /// 转换为字符串
    pub fn to_string(&self) -> String {
        match self {
            Self::Request => "request".to_string(),
            Self::Response => "response".to_string(),
            Self::Error => "error".to_string(),
            Self::Ping => "ping".to_string(),
            Self::Pong => "pong".to_string(),
            Self::Notification => "notification".to_string(),
            Self::Custom(s) => s.clone(),
        }
    }
}

/// Native Messaging 消息格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NativeMessage {
    /// 协议版本
    pub version: u32,
    /// 消息类型
    pub message_type: String,
    /// 请求ID (用于关联请求和响应)
    pub request_id: String,
    /// 消息负载
    pub payload: serde_json::Value,
    /// 时间戳
    pub timestamp: u64,
    /// 消息来源
    pub source: String,
    /// 目标
    #[serde(skip_serializing_if = "Option::is_none")]
    pub target: Option<String>,
    /// 消息签名 (可选)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub signature: Option<String>,
    /// 扩展字段
    #[serde(flatten)]
    pub extensions: HashMap<String, serde_json::Value>,
}

impl NativeMessage {
    /// 创建新的请求消息
    pub fn new_request(
        request_id: String,
        payload: serde_json::Value,
        source: String,
    ) -> Self {
        Self {
            version: ProtocolVersion::V2.to_u32(),
            message_type: MessageType::Request.to_string(),
            request_id,
            payload,
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            source,
            target: None,
            signature: None,
            extensions: HashMap::new(),
        }
    }
    
    /// 创建响应消息
    pub fn new_response(
        request_id: String,
        payload: serde_json::Value,
        source: String,
    ) -> Self {
        Self {
            version: ProtocolVersion::V2.to_u32(),
            message_type: MessageType::Response.to_string(),
            request_id,
            payload,
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            source,
            target: None,
            signature: None,
            extensions: HashMap::new(),
        }
    }
    
    /// 创建错误消息
    pub fn new_error(
        request_id: String,
        error_code: String,
        error_message: String,
        source: String,
    ) -> Self {
        let payload = serde_json::json!({
            "error_code": error_code,
            "error_message": error_message
        });
        
        Self {
            version: ProtocolVersion::V2.to_u32(),
            message_type: MessageType::Error.to_string(),
            request_id,
            payload,
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            source,
            target: None,
            signature: None,
            extensions: HashMap::new(),
        }
    }
    
    /// 创建心跳消息
    pub fn new_ping(source: String) -> Self {
        Self {
            version: ProtocolVersion::V2.to_u32(),
            message_type: MessageType::Ping.to_string(),
            request_id: format!("ping_{}", uuid::Uuid::new_v4()),
            payload: serde_json::json!({}),
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            source,
            target: None,
            signature: None,
            extensions: HashMap::new(),
        }
    }
    
    /// 创建心跳响应
    pub fn new_pong(request_id: String, source: String) -> Self {
        Self {
            version: ProtocolVersion::V2.to_u32(),
            message_type: MessageType::Pong.to_string(),
            request_id,
            payload: serde_json::json!({}),
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            source,
            target: None,
            signature: None,
            extensions: HashMap::new(),
        }
    }
    
    /// 获取消息类型
    pub fn get_message_type(&self) -> MessageType {
        MessageType::from_str(&self.message_type)
    }
    
    /// 设置目标
    pub fn set_target(&mut self, target: String) {
        self.target = Some(target);
    }
    
    /// 添加扩展字段
    pub fn add_extension(&mut self, key: String, value: serde_json::Value) {
        self.extensions.insert(key, value);
    }
    
    /// 获取扩展字段
    pub fn get_extension(&self, key: &str) -> Option<&serde_json::Value> {
        self.extensions.get(key)
    }
    
    /// 验证消息格式
    pub fn validate(&self) -> Result<()> {
        // 验证版本
        if ProtocolVersion::from_u32(self.version).is_none() {
            return Err(NativeMessagingError::ProtocolError(
                format!("不支持的协议版本: {}", self.version)
            ));
        }
        
        // 验证请求ID
        if self.request_id.is_empty() {
            return Err(NativeMessagingError::MessageFormatError(
                "请求ID不能为空".to_string()
            ));
        }
        
        // 验证来源
        if self.source.is_empty() {
            return Err(NativeMessagingError::MessageFormatError(
                "消息来源不能为空".to_string()
            ));
        }
        
        // 验证时间戳
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        let time_diff = (now as i64 - self.timestamp as i64).abs();
        
        // 允许5分钟的时间偏差
        if time_diff > 300 {
            return Err(NativeMessagingError::ProtocolError(
                format!("消息时间戳过期: 偏差{}秒", time_diff)
            ));
        }
        
        Ok(())
    }
}

/// Native Messaging 协议编解码器
#[derive(Clone)]
pub struct ProtocolCodec {
    version: ProtocolVersion,
    max_message_size: usize,
}

impl ProtocolCodec {
    /// 创建新的编解码器
    pub fn new(version: ProtocolVersion) -> Self {
        Self {
            version,
            max_message_size: MAX_MESSAGE_SIZE,
        }
    }
    
    /// 设置最大消息大小
    pub fn set_max_message_size(&mut self, size: usize) {
        self.max_message_size = size;
    }
    
    /// 编码消息到字节数组
    pub fn encode(&self, message: &NativeMessage) -> Result<Vec<u8>> {
        // 序列化消息
        let json_bytes = serde_json::to_vec(message)?;
        
        // 检查消息大小
        if json_bytes.len() > self.max_message_size {
            return Err(NativeMessagingError::MessageTooLarge(json_bytes.len()));
        }
        
        // 创建带长度前缀的消息
        let length = json_bytes.len() as u32;
        let mut result = Vec::with_capacity(4 + json_bytes.len());
        
        // 写入长度 (小端字节序)
        result.extend_from_slice(&length.to_le_bytes());
        
        // 写入消息体
        result.extend_from_slice(&json_bytes);
        
        Ok(result)
    }
    
    /// 从字节数组解码消息
    pub fn decode(&self, data: &[u8]) -> Result<NativeMessage> {
        if data.len() < 4 {
            return Err(NativeMessagingError::MessageFormatError(
                "消息长度不足".to_string()
            ));
        }
        
        // 读取长度前缀
        let length = u32::from_le_bytes([data[0], data[1], data[2], data[3]]) as usize;
        
        // 验证长度
        if length > self.max_message_size {
            return Err(NativeMessagingError::MessageTooLarge(length));
        }
        
        if data.len() < 4 + length {
            return Err(NativeMessagingError::MessageFormatError(
                "消息不完整".to_string()
            ));
        }
        
        // 提取消息体
        let message_data = &data[4..4 + length];
        
        // 反序列化消息
        let message: NativeMessage = serde_json::from_slice(message_data)?;
        
        // 验证消息格式
        message.validate()?;
        
        Ok(message)
    }
    
    /// 从同步读取器读取消息
    pub fn read_message<R: Read>(&self, reader: &mut R) -> Result<NativeMessage> {
        // 读取长度前缀
        let mut length_bytes = [0u8; 4];
        reader.read_exact(&mut length_bytes)?;
        let length = u32::from_le_bytes(length_bytes) as usize;
        
        // 验证长度
        if length > self.max_message_size {
            return Err(NativeMessagingError::MessageTooLarge(length));
        }
        
        // 读取消息体
        let mut message_bytes = vec![0u8; length];
        reader.read_exact(&mut message_bytes)?;
        
        // 反序列化消息
        let message: NativeMessage = serde_json::from_slice(&message_bytes)?;
        
        // 验证消息格式
        message.validate()?;
        
        Ok(message)
    }
    
    /// 向同步写入器写入消息
    pub fn write_message<W: Write>(&self, writer: &mut W, message: &NativeMessage) -> Result<()> {
        let encoded = self.encode(message)?;
        writer.write_all(&encoded)?;
        writer.flush()?;
        Ok(())
    }
    
    /// 从异步读取器读取消息
    pub async fn read_message_async<R: AsyncRead + Unpin>(&self, reader: &mut R) -> Result<NativeMessage> {
        // 读取长度前缀
        let mut length_bytes = [0u8; 4];
        reader.read_exact(&mut length_bytes).await?;
        let length = u32::from_le_bytes(length_bytes) as usize;
        
        // 验证长度
        if length > self.max_message_size {
            return Err(NativeMessagingError::MessageTooLarge(length));
        }
        
        // 读取消息体
        let mut message_bytes = vec![0u8; length];
        reader.read_exact(&mut message_bytes).await?;
        
        // 反序列化消息
        let message: NativeMessage = serde_json::from_slice(&message_bytes)?;
        
        // 验证消息格式
        message.validate()?;
        
        Ok(message)
    }
    
    /// 向异步写入器写入消息
    pub async fn write_message_async<W: AsyncWrite + Unpin>(&self, writer: &mut W, message: &NativeMessage) -> Result<()> {
        let encoded = self.encode(message)?;
        writer.write_all(&encoded).await?;
        writer.flush().await?;
        Ok(())
    }
}

/// 协议版本协商器
pub struct ProtocolNegotiator {
    supported_versions: Vec<ProtocolVersion>,
}

impl ProtocolNegotiator {
    /// 创建新的协商器
    pub fn new() -> Self {
        Self {
            supported_versions: ProtocolVersion::supported_versions(),
        }
    }
    
    /// 协商协议版本
    pub fn negotiate(&self, client_versions: &[u32]) -> Option<ProtocolVersion> {
        // 找到客户端和服务端都支持的最高版本
        client_versions
            .iter()
            .filter_map(|&v| ProtocolVersion::from_u32(v))
            .filter(|v| self.supported_versions.contains(v))
            .max_by_key(|v| v.to_u32())
    }
    
    /// 检查版本是否支持
    pub fn is_supported(&self, version: ProtocolVersion) -> bool {
        self.supported_versions.contains(&version)
    }
    
    /// 获取支持的版本列表
    pub fn get_supported_versions(&self) -> &[ProtocolVersion] {
        &self.supported_versions
    }
}

impl Default for ProtocolNegotiator {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::io::Cursor;

    #[test]
    fn test_protocol_version_conversion() {
        assert_eq!(ProtocolVersion::from_u32(1), Some(ProtocolVersion::V1));
        assert_eq!(ProtocolVersion::from_u32(2), Some(ProtocolVersion::V2));
        assert_eq!(ProtocolVersion::from_u32(3), None);
        
        assert_eq!(ProtocolVersion::V1.to_u32(), 1);
        assert_eq!(ProtocolVersion::V2.to_u32(), 2);
    }

    #[test]
    fn test_message_type_conversion() {
        assert_eq!(MessageType::from_str("request"), MessageType::Request);
        assert_eq!(MessageType::from_str("custom"), MessageType::Custom("custom".to_string()));
        
        assert_eq!(MessageType::Request.to_string(), "request");
        assert_eq!(MessageType::Custom("test".to_string()).to_string(), "test");
    }

    #[test]
    fn test_native_message_creation() {
        let message = NativeMessage::new_request(
            "test-id".to_string(),
            serde_json::json!({"key": "value"}),
            "test-source".to_string(),
        );
        
        assert_eq!(message.request_id, "test-id");
        assert_eq!(message.message_type, "request");
        assert_eq!(message.source, "test-source");
        assert_eq!(message.get_message_type(), MessageType::Request);
    }

    #[test]
    fn test_message_validation() {
        let mut message = NativeMessage::new_request(
            "test-id".to_string(),
            serde_json::json!({}),
            "test-source".to_string(),
        );
        
        // 正常消息应该验证通过
        assert!(message.validate().is_ok());
        
        // 空请求ID应该失败
        message.request_id = "".to_string();
        assert!(message.validate().is_err());
        
        // 恢复请求ID，测试空来源
        message.request_id = "test-id".to_string();
        message.source = "".to_string();
        assert!(message.validate().is_err());
    }

    #[test]
    fn test_protocol_codec_encode_decode() {
        let codec = ProtocolCodec::new(ProtocolVersion::V2);
        let message = NativeMessage::new_request(
            "test-id".to_string(),
            serde_json::json!({"test": "data"}),
            "test-source".to_string(),
        );
        
        // 编码
        let encoded = codec.encode(&message).unwrap();
        assert!(encoded.len() > 4); // 至少包含长度前缀
        
        // 解码
        let decoded = codec.decode(&encoded).unwrap();
        assert_eq!(decoded.request_id, message.request_id);
        assert_eq!(decoded.message_type, message.message_type);
        assert_eq!(decoded.source, message.source);
    }

    #[test]
    fn test_protocol_codec_sync_io() {
        let codec = ProtocolCodec::new(ProtocolVersion::V2);
        let message = NativeMessage::new_request(
            "test-id".to_string(),
            serde_json::json!({"test": "data"}),
            "test-source".to_string(),
        );
        
        // 写入到缓冲区
        let mut buffer = Vec::new();
        codec.write_message(&mut buffer, &message).unwrap();
        
        // 从缓冲区读取
        let mut cursor = Cursor::new(buffer);
        let decoded = codec.read_message(&mut cursor).unwrap();
        
        assert_eq!(decoded.request_id, message.request_id);
        assert_eq!(decoded.message_type, message.message_type);
    }

    #[tokio::test]
    async fn test_protocol_codec_async_io() {
        let codec = ProtocolCodec::new(ProtocolVersion::V2);
        let message = NativeMessage::new_request(
            "test-id".to_string(),
            serde_json::json!({"test": "data"}),
            "test-source".to_string(),
        );
        
        // 写入到缓冲区
        let mut buffer = Vec::new();
        codec.write_message_async(&mut buffer, &message).await.unwrap();
        
        // 从缓冲区读取
        let mut cursor = Cursor::new(buffer);
        let decoded = codec.read_message_async(&mut cursor).await.unwrap();
        
        assert_eq!(decoded.request_id, message.request_id);
        assert_eq!(decoded.message_type, message.message_type);
    }

    #[test]
    fn test_protocol_negotiator() {
        let negotiator = ProtocolNegotiator::new();
        
        // 测试协商
        let client_versions = vec![1, 2, 3];
        let negotiated = negotiator.negotiate(&client_versions);
        assert_eq!(negotiated, Some(ProtocolVersion::V2)); // 应该选择最高支持的版本
        
        // 测试不支持的版本
        let client_versions = vec![3, 4, 5];
        let negotiated = negotiator.negotiate(&client_versions);
        assert_eq!(negotiated, None);
        
        // 测试支持检查
        assert!(negotiator.is_supported(ProtocolVersion::V1));
        assert!(negotiator.is_supported(ProtocolVersion::V2));
    }

    #[test]
    fn test_message_extensions() {
        let mut message = NativeMessage::new_request(
            "test-id".to_string(),
            serde_json::json!({}),
            "test-source".to_string(),
        );
        
        // 添加扩展字段
        message.add_extension("custom_field".to_string(), serde_json::json!("custom_value"));
        
        // 获取扩展字段
        let value = message.get_extension("custom_field");
        assert_eq!(value, Some(&serde_json::json!("custom_value")));
        
        // 不存在的字段
        let value = message.get_extension("non_existent");
        assert_eq!(value, None);
    }

    #[test]
    fn test_message_size_limit() {
        let mut codec = ProtocolCodec::new(ProtocolVersion::V2);
        codec.set_max_message_size(100); // 设置很小的限制
        
        let large_payload = serde_json::json!({
            "data": "x".repeat(200) // 创建大负载
        });
        
        let message = NativeMessage::new_request(
            "test-id".to_string(),
            large_payload,
            "test-source".to_string(),
        );
        
        // 编码应该失败
        let result = codec.encode(&message);
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), NativeMessagingError::MessageTooLarge(_)));
    }
} 