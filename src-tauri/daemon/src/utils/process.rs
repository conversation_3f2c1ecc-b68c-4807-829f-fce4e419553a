//! 进程管理工具

use std::process::{Command, Stdio};
use std::time::Duration;
use tokio::process::Command as TokioCommand;
use tokio::time::timeout;
use tracing::{info, warn, error};

use crate::error::DaemonError;

/// 进程管理器
pub struct ProcessManager;

impl ProcessManager {
    /// 启动进程
    pub async fn start_process(
        program: &str,
        args: &[&str],
        working_dir: Option<&str>,
        timeout_secs: u64,
    ) -> Result<tokio::process::Child, DaemonError> {
        info!("启动进程: {} {:?}", program, args);
        
        let mut cmd = TokioCommand::new(program);
        cmd.args(args)
           .stdout(Stdio::piped())
           .stderr(Stdio::piped())
           .stdin(Stdio::null());
        
        if let Some(dir) = working_dir {
            cmd.current_dir(dir);
        }
        
        let child = cmd.spawn()
            .map_err(|e| DaemonError::AppStartError(format!("进程启动失败: {}", e)))?;
        
        info!("进程启动成功，PID: {:?}", child.id());
        Ok(child)
    }
    
    /// 停止进程
    pub async fn stop_process(
        mut child: tokio::process::Child,
        timeout_secs: u64,
    ) -> Result<(), DaemonError> {
        if let Some(pid) = child.id() {
            info!("停止进程，PID: {}", pid);
            
            // 尝试优雅关闭
            if let Err(e) = child.kill().await {
                warn!("无法终止进程: {}", e);
            }
            
            // 等待进程退出
            match timeout(Duration::from_secs(timeout_secs), child.wait()).await {
                Ok(Ok(status)) => {
                    info!("进程已退出，状态: {}", status);
                    Ok(())
                }
                Ok(Err(e)) => {
                    error!("等待进程退出失败: {}", e);
                    Err(DaemonError::AppStopError(format!("等待进程退出失败: {}", e)))
                }
                Err(_) => {
                    error!("进程停止超时");
                    Err(DaemonError::AppStopError("进程停止超时".to_string()))
                }
            }
        } else {
            warn!("进程已经退出");
            Ok(())
        }
    }
    
    /// 检查进程是否运行
    pub fn is_process_running(pid: u32) -> bool {
        #[cfg(unix)]
        {
            use nix::sys::signal::{kill, Signal};
            use nix::unistd::Pid;
            
            match kill(Pid::from_raw(pid as i32), Signal::SIGCONT) {
                Ok(()) => true,
                Err(_) => false,
            }
        }
        
        #[cfg(windows)]
        {
            use std::ptr;
            use winapi::um::processthreadsapi::OpenProcess;
            use winapi::um::winnt::PROCESS_QUERY_INFORMATION;
            use winapi::um::handleapi::CloseHandle;
            
            unsafe {
                let handle = OpenProcess(PROCESS_QUERY_INFORMATION, 0, pid);
                if handle != ptr::null_mut() {
                    CloseHandle(handle);
                    true
                } else {
                    false
                }
            }
        }
        
        #[cfg(not(any(unix, windows)))]
        {
            // 其他平台的实现
            false
        }
    }
    
    /// 获取进程内存使用情况
    pub fn get_process_memory_usage(pid: u32) -> Result<u64, DaemonError> {
        #[cfg(unix)]
        {
            use std::fs;
            
            let status_path = format!("/proc/{}/status", pid);
            let content = fs::read_to_string(status_path)
                .map_err(|e| DaemonError::InternalError(format!("读取进程状态失败: {}", e)))?;
            
            for line in content.lines() {
                if line.starts_with("VmRSS:") {
                    let parts: Vec<&str> = line.split_whitespace().collect();
                    if parts.len() >= 2 {
                        let memory_kb: u64 = parts[1].parse()
                            .map_err(|e| DaemonError::InternalError(format!("解析内存使用量失败: {}", e)))?;
                        return Ok(memory_kb * 1024); // 转换为字节
                    }
                }
            }
            
            Err(DaemonError::InternalError("未找到内存使用信息".to_string()))
        }
        
        #[cfg(windows)]
        {
            use std::mem;
            use std::ptr;
            use winapi::um::processthreadsapi::OpenProcess;
            use winapi::um::psapi::{GetProcessMemoryInfo, PROCESS_MEMORY_COUNTERS};
            use winapi::um::winnt::PROCESS_QUERY_INFORMATION;
            use winapi::um::handleapi::CloseHandle;
            
            unsafe {
                let handle = OpenProcess(PROCESS_QUERY_INFORMATION, 0, pid);
                if handle == ptr::null_mut() {
                    return Err(DaemonError::InternalError("无法打开进程".to_string()));
                }
                
                let mut pmc: PROCESS_MEMORY_COUNTERS = mem::zeroed();
                let result = GetProcessMemoryInfo(
                    handle,
                    &mut pmc,
                    mem::size_of::<PROCESS_MEMORY_COUNTERS>() as u32,
                );
                
                CloseHandle(handle);
                
                if result != 0 {
                    Ok(pmc.WorkingSetSize as u64)
                } else {
                    Err(DaemonError::InternalError("获取进程内存信息失败".to_string()))
                }
            }
        }
        
        #[cfg(not(any(unix, windows)))]
        {
            Err(DaemonError::InternalError("不支持的平台".to_string()))
        }
    }
}

/// 进程健康检查器
pub struct ProcessHealthChecker {
    /// 进程ID
    pid: u32,
    /// 检查间隔
    check_interval: Duration,
    /// 内存限制 (字节)
    memory_limit: Option<u64>,
}

impl ProcessHealthChecker {
    /// 创建新的健康检查器
    pub fn new(pid: u32, check_interval: Duration, memory_limit: Option<u64>) -> Self {
        Self {
            pid,
            check_interval,
            memory_limit,
        }
    }
    
    /// 执行健康检查
    pub async fn check_health(&self) -> Result<HealthStatus, DaemonError> {
        // 检查进程是否运行
        if !ProcessManager::is_process_running(self.pid) {
            return Ok(HealthStatus::Dead);
        }
        
        // 检查内存使用
        if let Some(limit) = self.memory_limit {
            match ProcessManager::get_process_memory_usage(self.pid) {
                Ok(memory_usage) => {
                    if memory_usage > limit {
                        return Ok(HealthStatus::MemoryExceeded {
                            current: memory_usage,
                            limit,
                        });
                    }
                }
                Err(e) => {
                    warn!("获取进程内存使用失败: {}", e);
                }
            }
        }
        
        Ok(HealthStatus::Healthy)
    }
    
    /// 启动持续健康检查
    pub async fn start_monitoring(&self) -> tokio::task::JoinHandle<()> {
        let pid = self.pid;
        let interval = self.check_interval;
        let memory_limit = self.memory_limit;
        
        tokio::spawn(async move {
            let checker = ProcessHealthChecker::new(pid, interval, memory_limit);
            let mut interval_timer = tokio::time::interval(interval);
            
            loop {
                interval_timer.tick().await;
                
                match checker.check_health().await {
                    Ok(HealthStatus::Healthy) => {
                        // 进程健康
                    }
                    Ok(status) => {
                        warn!("进程健康检查异常: {:?}", status);
                    }
                    Err(e) => {
                        error!("健康检查失败: {}", e);
                    }
                }
            }
        })
    }
}

/// 进程健康状态
#[derive(Debug, Clone)]
pub enum HealthStatus {
    /// 健康
    Healthy,
    /// 进程已死亡
    Dead,
    /// 内存使用超限
    MemoryExceeded { current: u64, limit: u64 },
    /// 响应超时
    Timeout,
}

/// 进程重启管理器
pub struct ProcessRestartManager {
    /// 最大重启次数
    max_restarts: u32,
    /// 重启计数
    restart_count: u32,
    /// 重启间隔
    restart_interval: Duration,
}

impl ProcessRestartManager {
    /// 创建新的重启管理器
    pub fn new(max_restarts: u32, restart_interval: Duration) -> Self {
        Self {
            max_restarts,
            restart_count: 0,
            restart_interval,
        }
    }
    
    /// 检查是否可以重启
    pub fn can_restart(&self) -> bool {
        self.restart_count < self.max_restarts
    }
    
    /// 执行重启
    pub async fn restart(&mut self) -> Result<(), DaemonError> {
        if !self.can_restart() {
            return Err(DaemonError::AppStartError(
                format!("已达到最大重启次数: {}", self.max_restarts)
            ));
        }
        
        self.restart_count += 1;
        info!("执行第 {} 次重启", self.restart_count);
        
        // 等待重启间隔
        tokio::time::sleep(self.restart_interval).await;
        
        Ok(())
    }
    
    /// 重置重启计数
    pub fn reset(&mut self) {
        self.restart_count = 0;
    }
}
