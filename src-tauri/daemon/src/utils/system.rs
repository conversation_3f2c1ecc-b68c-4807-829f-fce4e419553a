//! 系统工具函数

use std::time::{SystemTime, UNIX_EPOCH};
use serde::{Deserialize, Serialize};
use tracing::{info, warn, error};

use crate::error::DaemonError;

/// 系统信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemInfo {
    /// 操作系统
    pub os: String,
    /// 操作系统版本
    pub os_version: String,
    /// 架构
    pub arch: String,
    /// 主机名
    pub hostname: String,
    /// 用户名
    pub username: String,
    /// 总内存 (字节)
    pub total_memory: u64,
    /// 可用内存 (字节)
    pub available_memory: u64,
    /// CPU核心数
    pub cpu_cores: u32,
    /// 系统启动时间
    pub boot_time: u64,
}

/// 系统工具
pub struct SystemUtils;

impl SystemUtils {
    /// 获取系统信息
    pub fn get_system_info() -> Result<SystemInfo, DaemonError> {
        Ok(SystemInfo {
            os: std::env::consts::OS.to_string(),
            os_version: Self::get_os_version()?,
            arch: std::env::consts::ARCH.to_string(),
            hostname: Self::get_hostname()?,
            username: Self::get_username()?,
            total_memory: Self::get_total_memory()?,
            available_memory: Self::get_available_memory()?,
            cpu_cores: Self::get_cpu_cores(),
            boot_time: Self::get_boot_time()?,
        })
    }
    
    /// 获取操作系统版本
    fn get_os_version() -> Result<String, DaemonError> {
        #[cfg(unix)]
        {
            use std::process::Command;
            
            let output = Command::new("uname")
                .arg("-r")
                .output()
                .map_err(|e| DaemonError::InternalError(format!("获取OS版本失败: {}", e)))?;
            
            Ok(String::from_utf8_lossy(&output.stdout).trim().to_string())
        }
        
        #[cfg(windows)]
        {
            // Windows版本获取
            Ok("Windows".to_string())
        }
        
        #[cfg(not(any(unix, windows)))]
        {
            Ok("Unknown".to_string())
        }
    }
    
    /// 获取主机名
    fn get_hostname() -> Result<String, DaemonError> {
        hostname::get()
            .map_err(|e| DaemonError::InternalError(format!("获取主机名失败: {}", e)))?
            .to_string_lossy()
            .to_string()
            .pipe(Ok)
    }
    
    /// 获取用户名
    fn get_username() -> Result<String, DaemonError> {
        std::env::var("USER")
            .or_else(|_| std::env::var("USERNAME"))
            .map_err(|_| DaemonError::InternalError("获取用户名失败".to_string()))
    }
    
    /// 获取总内存
    fn get_total_memory() -> Result<u64, DaemonError> {
        #[cfg(unix)]
        {
            use std::fs;
            
            let content = fs::read_to_string("/proc/meminfo")
                .map_err(|e| DaemonError::InternalError(format!("读取内存信息失败: {}", e)))?;
            
            for line in content.lines() {
                if line.starts_with("MemTotal:") {
                    let parts: Vec<&str> = line.split_whitespace().collect();
                    if parts.len() >= 2 {
                        let memory_kb: u64 = parts[1].parse()
                            .map_err(|e| DaemonError::InternalError(format!("解析内存大小失败: {}", e)))?;
                        return Ok(memory_kb * 1024);
                    }
                }
            }
            
            Err(DaemonError::InternalError("未找到内存信息".to_string()))
        }
        
        #[cfg(windows)]
        {
            use std::mem;
            use winapi::um::sysinfoapi::{GetPhysicallyInstalledSystemMemory, MEMORYSTATUSEX, GlobalMemoryStatusEx};
            
            unsafe {
                let mut mem_status: MEMORYSTATUSEX = mem::zeroed();
                mem_status.dwLength = mem::size_of::<MEMORYSTATUSEX>() as u32;
                
                if GlobalMemoryStatusEx(&mut mem_status) != 0 {
                    Ok(mem_status.ullTotalPhys)
                } else {
                    Err(DaemonError::InternalError("获取内存信息失败".to_string()))
                }
            }
        }
        
        #[cfg(not(any(unix, windows)))]
        {
            Ok(0)
        }
    }
    
    /// 获取可用内存
    fn get_available_memory() -> Result<u64, DaemonError> {
        #[cfg(unix)]
        {
            use std::fs;
            
            let content = fs::read_to_string("/proc/meminfo")
                .map_err(|e| DaemonError::InternalError(format!("读取内存信息失败: {}", e)))?;
            
            for line in content.lines() {
                if line.starts_with("MemAvailable:") {
                    let parts: Vec<&str> = line.split_whitespace().collect();
                    if parts.len() >= 2 {
                        let memory_kb: u64 = parts[1].parse()
                            .map_err(|e| DaemonError::InternalError(format!("解析可用内存失败: {}", e)))?;
                        return Ok(memory_kb * 1024);
                    }
                }
            }
            
            Err(DaemonError::InternalError("未找到可用内存信息".to_string()))
        }
        
        #[cfg(windows)]
        {
            use std::mem;
            use winapi::um::sysinfoapi::{MEMORYSTATUSEX, GlobalMemoryStatusEx};
            
            unsafe {
                let mut mem_status: MEMORYSTATUSEX = mem::zeroed();
                mem_status.dwLength = mem::size_of::<MEMORYSTATUSEX>() as u32;
                
                if GlobalMemoryStatusEx(&mut mem_status) != 0 {
                    Ok(mem_status.ullAvailPhys)
                } else {
                    Err(DaemonError::InternalError("获取可用内存信息失败".to_string()))
                }
            }
        }
        
        #[cfg(not(any(unix, windows)))]
        {
            Ok(0)
        }
    }
    
    /// 获取CPU核心数
    fn get_cpu_cores() -> u32 {
        num_cpus::get() as u32
    }
    
    /// 获取系统启动时间
    fn get_boot_time() -> Result<u64, DaemonError> {
        #[cfg(unix)]
        {
            use std::fs;
            
            let content = fs::read_to_string("/proc/stat")
                .map_err(|e| DaemonError::InternalError(format!("读取系统统计失败: {}", e)))?;
            
            for line in content.lines() {
                if line.starts_with("btime ") {
                    let parts: Vec<&str> = line.split_whitespace().collect();
                    if parts.len() >= 2 {
                        let boot_time: u64 = parts[1].parse()
                            .map_err(|e| DaemonError::InternalError(format!("解析启动时间失败: {}", e)))?;
                        return Ok(boot_time);
                    }
                }
            }
            
            Err(DaemonError::InternalError("未找到启动时间信息".to_string()))
        }
        
        #[cfg(windows)]
        {
            // Windows 启动时间获取比较复杂，这里简化处理
            Ok(0)
        }
        
        #[cfg(not(any(unix, windows)))]
        {
            Ok(0)
        }
    }
    
    /// 获取当前时间戳
    pub fn get_timestamp() -> u64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs()
    }
    
    /// 获取当前时间戳（毫秒）
    pub fn get_timestamp_millis() -> u64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_millis() as u64
    }
    
    /// 检查是否以管理员权限运行
    pub fn is_running_as_admin() -> bool {
        #[cfg(unix)]
        {
            unsafe { libc::geteuid() == 0 }
        }
        
        #[cfg(windows)]
        {
            use std::ptr;
            use winapi::um::processthreadsapi::GetCurrentProcess;
            use winapi::um::securitybaseapi::GetTokenInformation;
            use winapi::um::winnt::{TokenElevation, TOKEN_ELEVATION, TOKEN_QUERY};
            use winapi::um::handleapi::CloseHandle;
            
            unsafe {
                let mut token_handle = ptr::null_mut();
                let process_handle = GetCurrentProcess();
                
                if winapi::um::processthreadsapi::OpenProcessToken(
                    process_handle,
                    TOKEN_QUERY,
                    &mut token_handle,
                ) == 0 {
                    return false;
                }
                
                let mut elevation = TOKEN_ELEVATION { TokenIsElevated: 0 };
                let mut return_length = 0;
                
                let result = GetTokenInformation(
                    token_handle,
                    TokenElevation,
                    &mut elevation as *mut _ as *mut _,
                    std::mem::size_of::<TOKEN_ELEVATION>() as u32,
                    &mut return_length,
                );
                
                CloseHandle(token_handle);
                
                result != 0 && elevation.TokenIsElevated != 0
            }
        }
        
        #[cfg(not(any(unix, windows)))]
        {
            false
        }
    }
    
    /// 获取环境变量
    pub fn get_env_var(key: &str) -> Option<String> {
        std::env::var(key).ok()
    }
    
    /// 设置环境变量
    pub fn set_env_var(key: &str, value: &str) {
        std::env::set_var(key, value);
    }
}

/// 系统资源监控器
pub struct SystemResourceMonitor {
    /// 监控间隔
    interval: std::time::Duration,
}

impl SystemResourceMonitor {
    /// 创建新的系统资源监控器
    pub fn new(interval: std::time::Duration) -> Self {
        Self { interval }
    }
    
    /// 启动监控
    pub async fn start_monitoring(&self) -> tokio::task::JoinHandle<()> {
        let interval = self.interval;
        
        tokio::spawn(async move {
            let mut interval_timer = tokio::time::interval(interval);
            
            loop {
                interval_timer.tick().await;
                
                match SystemUtils::get_system_info() {
                    Ok(info) => {
                        let memory_usage_percent = 
                            ((info.total_memory - info.available_memory) as f64 / info.total_memory as f64) * 100.0;
                        
                        info!(
                            total_memory = info.total_memory,
                            available_memory = info.available_memory,
                            memory_usage_percent = memory_usage_percent,
                            cpu_cores = info.cpu_cores,
                            "系统资源状态"
                        );
                        
                        // 检查资源使用警告
                        if memory_usage_percent > 90.0 {
                            warn!("内存使用率过高: {:.1}%", memory_usage_percent);
                        }
                    }
                    Err(e) => {
                        error!("获取系统信息失败: {}", e);
                    }
                }
            }
        })
    }
}

// 添加管道操作符扩展
trait PipeExt<T> {
    fn pipe<F, R>(self, f: F) -> R
    where
        F: FnOnce(T) -> R;
}

impl<T> PipeExt<T> for T {
    fn pipe<F, R>(self, f: F) -> R
    where
        F: FnOnce(T) -> R,
    {
        f(self)
    }
}
