//! 网络工具函数

use std::net::{IpAddr, SocketAddr, TcpListener};
use std::time::Duration;
use tokio::net::TcpStream;
use tokio::time::timeout;
use tracing::{info, warn, error};

use crate::error::DaemonError;

/// 网络工具
pub struct NetworkUtils;

impl NetworkUtils {
    /// 检查端口是否可用
    pub fn is_port_available(port: u16) -> bool {
        match TcpListener::bind(format!("127.0.0.1:{}", port)) {
            Ok(_) => true,
            Err(_) => false,
        }
    }
    
    /// 查找可用端口
    pub fn find_available_port(start_port: u16, end_port: u16) -> Option<u16> {
        for port in start_port..=end_port {
            if Self::is_port_available(port) {
                return Some(port);
            }
        }
        None
    }
    
    /// 测试TCP连接
    pub async fn test_tcp_connection(
        host: &str,
        port: u16,
        timeout_secs: u64,
    ) -> Result<bool, DaemonError> {
        let addr = format!("{}:{}", host, port);
        
        match timeout(Duration::from_secs(timeout_secs), TcpStream::connect(&addr)).await {
            Ok(Ok(_)) => {
                info!("TCP连接测试成功: {}", addr);
                Ok(true)
            }
            Ok(Err(e)) => {
                warn!("TCP连接测试失败: {} - {}", addr, e);
                Ok(false)
            }
            Err(_) => {
                warn!("TCP连接测试超时: {}", addr);
                Ok(false)
            }
        }
    }
    
    /// 解析地址
    pub fn parse_socket_addr(addr_str: &str) -> Result<SocketAddr, DaemonError> {
        addr_str.parse()
            .map_err(|e| DaemonError::NetworkError(format!("无效的地址格式: {} - {}", addr_str, e)))
    }
    
    /// 验证IP地址
    pub fn is_valid_ip(ip_str: &str) -> bool {
        ip_str.parse::<IpAddr>().is_ok()
    }
    
    /// 获取本地IP地址
    pub fn get_local_ip() -> Result<String, DaemonError> {
        use std::net::UdpSocket;
        
        // 连接到一个远程地址来获取本地IP
        let socket = UdpSocket::bind("0.0.0.0:0")
            .map_err(|e| DaemonError::NetworkError(format!("创建UDP套接字失败: {}", e)))?;
        
        socket.connect("*******:80")
            .map_err(|e| DaemonError::NetworkError(format!("连接失败: {}", e)))?;
        
        let local_addr = socket.local_addr()
            .map_err(|e| DaemonError::NetworkError(format!("获取本地地址失败: {}", e)))?;
        
        Ok(local_addr.ip().to_string())
    }
}

/// 网络监控器
pub struct NetworkMonitor {
    /// 监控的地址列表
    addresses: Vec<SocketAddr>,
    /// 检查间隔
    check_interval: Duration,
    /// 连接超时
    connection_timeout: Duration,
}

impl NetworkMonitor {
    /// 创建新的网络监控器
    pub fn new(
        addresses: Vec<SocketAddr>,
        check_interval: Duration,
        connection_timeout: Duration,
    ) -> Self {
        Self {
            addresses,
            check_interval,
            connection_timeout,
        }
    }
    
    /// 启动网络监控
    pub async fn start_monitoring(&self) -> tokio::task::JoinHandle<()> {
        let addresses = self.addresses.clone();
        let check_interval = self.check_interval;
        let connection_timeout = self.connection_timeout;
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(check_interval);
            
            loop {
                interval.tick().await;
                
                for addr in &addresses {
                    match timeout(connection_timeout, TcpStream::connect(addr)).await {
                        Ok(Ok(_)) => {
                            info!("网络连接正常: {}", addr);
                        }
                        Ok(Err(e)) => {
                            warn!("网络连接失败: {} - {}", addr, e);
                        }
                        Err(_) => {
                            warn!("网络连接超时: {}", addr);
                        }
                    }
                }
            }
        })
    }
}

/// 带宽测试器
pub struct BandwidthTester;

impl BandwidthTester {
    /// 测试下载速度
    pub async fn test_download_speed(
        _url: &str,
        duration_secs: u64,
    ) -> Result<f64, DaemonError> {
        use tokio::time::Instant;

        let start_time = Instant::now();
        let total_bytes = 0u64;
        
        // 这里应该实现实际的HTTP下载测试
        // 为了简化，我们模拟一个测试
        tokio::time::sleep(Duration::from_secs(duration_secs)).await;
        
        let elapsed = start_time.elapsed();
        let speed_mbps = (total_bytes as f64 * 8.0) / (elapsed.as_secs_f64() * 1_000_000.0);
        
        info!("下载速度测试完成: {:.2} Mbps", speed_mbps);
        Ok(speed_mbps)
    }
}

/// 连接池管理器
pub struct ConnectionPool<T> {
    /// 连接池
    pool: Vec<T>,
    /// 最大连接数
    max_connections: usize,
    /// 当前活跃连接数
    active_connections: usize,
}

impl<T> ConnectionPool<T> {
    /// 创建新的连接池
    pub fn new(max_connections: usize) -> Self {
        Self {
            pool: Vec::with_capacity(max_connections),
            max_connections,
            active_connections: 0,
        }
    }
    
    /// 获取连接
    pub fn get_connection(&mut self) -> Option<T> {
        if self.active_connections < self.max_connections {
            self.active_connections += 1;
            self.pool.pop()
        } else {
            None
        }
    }
    
    /// 归还连接
    pub fn return_connection(&mut self, connection: T) {
        if self.pool.len() < self.max_connections {
            self.pool.push(connection);
        }
        if self.active_connections > 0 {
            self.active_connections -= 1;
        }
    }
    
    /// 获取连接池状态
    pub fn get_stats(&self) -> ConnectionPoolStats {
        ConnectionPoolStats {
            total_connections: self.pool.len(),
            active_connections: self.active_connections,
            max_connections: self.max_connections,
        }
    }
}

/// 连接池统计信息
#[derive(Debug, Clone)]
pub struct ConnectionPoolStats {
    /// 总连接数
    pub total_connections: usize,
    /// 活跃连接数
    pub active_connections: usize,
    /// 最大连接数
    pub max_connections: usize,
}

/// 网络安全检查器
pub struct NetworkSecurityChecker;

impl NetworkSecurityChecker {
    /// 检查IP地址是否在白名单中
    pub fn is_ip_whitelisted(ip: &str, whitelist: &[String]) -> bool {
        whitelist.iter().any(|allowed_ip| allowed_ip == ip)
    }
    
    /// 检查IP地址是否在黑名单中
    pub fn is_ip_blacklisted(ip: &str, blacklist: &[String]) -> bool {
        blacklist.iter().any(|blocked_ip| blocked_ip == ip)
    }
    
    /// 验证请求频率
    pub fn check_rate_limit(
        _ip: &str,
        requests_per_minute: u32,
        current_requests: u32,
    ) -> bool {
        current_requests <= requests_per_minute
    }
    
    /// 检测可疑连接模式
    pub fn detect_suspicious_pattern(
        connection_count: u32,
        time_window_secs: u64,
        threshold: u32,
    ) -> bool {
        // 简单的连接频率检测
        let connections_per_second = connection_count as f64 / time_window_secs as f64;
        connections_per_second > threshold as f64
    }
}
