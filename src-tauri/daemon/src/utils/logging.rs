//! 日志管理工具

use anyhow::Result;
use tracing_subscriber::{
    fmt,
    layer::SubscriberExt,
    util::SubscriberInitExt,
    EnvFilter,
};

/// 初始化日志系统
pub fn init_logging() -> Result<()> {
    // 从环境变量或默认值设置日志级别
    let env_filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| EnvFilter::new("info"));
    
    // 创建格式化层
    let fmt_layer = fmt::layer()
        .with_target(true)
        .with_thread_ids(true)
        .with_thread_names(true)
        .with_file(true)
        .with_line_number(true);
    
    // 初始化订阅者
    tracing_subscriber::registry()
        .with(env_filter)
        .with(fmt_layer)
        .init();
    
    Ok(())
}

/// 初始化文件日志
pub fn init_file_logging(log_file: &str, level: &str) -> Result<()> {
    use std::fs::OpenOptions;
    use tracing_subscriber::fmt::writer::MakeWriterExt;
    
    // 创建日志文件写入器
    let file = OpenOptions::new()
        .create(true)
        .append(true)
        .open(log_file)?;
    
    let env_filter = EnvFilter::new(level);
    
    // 创建文件格式化层
    let file_layer = fmt::layer()
        .with_writer(file.with_max_level(tracing::Level::TRACE))
        .with_ansi(false)
        .with_target(true)
        .with_thread_ids(true)
        .with_file(true)
        .with_line_number(true);
    
    // 创建控制台格式化层
    let console_layer = fmt::layer()
        .with_writer(std::io::stdout.with_max_level(tracing::Level::INFO))
        .with_target(false)
        .with_thread_ids(false)
        .with_file(false)
        .with_line_number(false);
    
    // 初始化订阅者
    tracing_subscriber::registry()
        .with(env_filter)
        .with(file_layer)
        .with(console_layer)
        .init();
    
    Ok(())
}

/// 初始化 JSON 格式日志
pub fn init_json_logging(log_file: Option<&str>) -> Result<()> {
    let env_filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| EnvFilter::new("info"));

    match log_file {
        Some(file_path) => {
            use std::fs::OpenOptions;
            use tracing_subscriber::fmt::writer::MakeWriterExt;

            let file = OpenOptions::new()
                .create(true)
                .append(true)
                .open(file_path)?;

            let file_layer = fmt::layer()
                .json()
                .with_writer(file.with_max_level(tracing::Level::TRACE))
                .with_current_span(true)
                .with_span_list(true);

            tracing_subscriber::registry()
                .with(env_filter)
                .with(file_layer)
                .init();
        }
        None => {
            let console_layer = fmt::layer()
                .json()
                .with_current_span(true)
                .with_span_list(true);

            tracing_subscriber::registry()
                .with(env_filter)
                .with(console_layer)
                .init();
        }
    }

    Ok(())
}

/// 日志轮转管理器
pub struct LogRotationManager {
    /// 日志文件路径
    log_file_path: String,
    /// 最大文件大小 (字节)
    max_file_size: u64,
    /// 保留的日志文件数量
    max_files: u32,
}

impl LogRotationManager {
    /// 创建新的日志轮转管理器
    pub fn new(log_file_path: String, max_file_size: u64, max_files: u32) -> Self {
        Self {
            log_file_path,
            max_file_size,
            max_files,
        }
    }

    /// 检查是否需要轮转
    pub fn should_rotate(&self) -> Result<bool> {
        use std::fs;

        match fs::metadata(&self.log_file_path) {
            Ok(metadata) => Ok(metadata.len() > self.max_file_size),
            Err(_) => Ok(false), // 文件不存在，不需要轮转
        }
    }

    /// 执行日志轮转
    pub fn rotate(&self) -> Result<()> {
        use std::fs;
        use std::path::Path;

        let log_path = Path::new(&self.log_file_path);

        // 轮转现有文件
        for i in (1..self.max_files).rev() {
            let old_file = format!("{}.{}", self.log_file_path, i);
            let new_file = format!("{}.{}", self.log_file_path, i + 1);

            if Path::new(&old_file).exists() {
                if i == self.max_files - 1 {
                    // 删除最老的文件
                    fs::remove_file(&old_file)?;
                } else {
                    // 重命名文件
                    fs::rename(&old_file, &new_file)?;
                }
            }
        }

        // 重命名当前日志文件
        if log_path.exists() {
            let backup_file = format!("{}.1", self.log_file_path);
            fs::rename(&self.log_file_path, &backup_file)?;
        }

        Ok(())
    }
}

/// 性能监控日志
pub struct PerformanceLogger {
    /// 开始时间
    start_time: std::time::Instant,
    /// 操作名称
    operation_name: String,
}

impl PerformanceLogger {
    /// 开始性能监控
    pub fn start(operation_name: &str) -> Self {
        Self {
            start_time: std::time::Instant::now(),
            operation_name: operation_name.to_string(),
        }
    }

    /// 结束性能监控并记录日志
    pub fn finish(self) {
        let duration = self.start_time.elapsed();
        tracing::info!(
            operation = %self.operation_name,
            duration_ms = duration.as_millis(),
            "操作完成"
        );
    }

    /// 记录中间检查点
    pub fn checkpoint(&self, checkpoint_name: &str) {
        let duration = self.start_time.elapsed();
        tracing::debug!(
            operation = %self.operation_name,
            checkpoint = checkpoint_name,
            duration_ms = duration.as_millis(),
            "检查点"
        );
    }
}

/// 结构化日志宏
#[macro_export]
macro_rules! log_operation {
    ($level:ident, $operation:expr, $($field:ident = $value:expr),*) => {
        tracing::$level!(
            operation = $operation,
            $($field = $value,)*
        );
    };
}

/// 错误日志宏
#[macro_export]
macro_rules! log_error {
    ($error:expr, $context:expr) => {
        tracing::error!(
            error = %$error,
            context = $context,
            "操作失败"
        );
    };
}

/// 审计日志记录器
pub struct AuditLogger;

impl AuditLogger {
    /// 记录用户操作
    pub fn log_user_action(user_id: &str, action: &str, resource: &str, result: &str) {
        tracing::info!(
            event_type = "user_action",
            user_id = user_id,
            action = action,
            resource = resource,
            result = result,
            timestamp = chrono::Utc::now().to_rfc3339(),
            "用户操作审计"
        );
    }

    /// 记录系统事件
    pub fn log_system_event(event_type: &str, details: &str) {
        tracing::info!(
            event_type = "system_event",
            event = event_type,
            details = details,
            timestamp = chrono::Utc::now().to_rfc3339(),
            "系统事件审计"
        );
    }

    /// 记录安全事件
    pub fn log_security_event(event_type: &str, source: &str, details: &str) {
        tracing::warn!(
            event_type = "security_event",
            security_event = event_type,
            source = source,
            details = details,
            timestamp = chrono::Utc::now().to_rfc3339(),
            "安全事件审计"
        );
    }
}
