//! 信号处理工具

use std::sync::Arc;
use tokio::sync::Notify;
use tracing::{info, error};

/// 信号处理器
pub struct SignalHandler {
    shutdown_notify: Arc<Notify>,
}

impl SignalHandler {
    /// 创建新的信号处理器
    pub fn new() -> Self {
        Self {
            shutdown_notify: Arc::new(Notify::new()),
        }
    }
    
    /// 获取关闭通知
    pub fn shutdown_notify(&self) -> Arc<Notify> {
        self.shutdown_notify.clone()
    }
    
    /// 启动信号监听
    pub async fn start_listening(&self) {
        let shutdown_notify = self.shutdown_notify.clone();
        
        tokio::spawn(async move {
            #[cfg(unix)]
            {
                use tokio::signal::unix::{signal, SignalKind};
                
                let mut sigterm = signal(SignalKind::terminate())
                    .expect("无法注册 SIGTERM 处理器");
                let mut sigint = signal(SignalKind::interrupt())
                    .expect("无法注册 SIGINT 处理器");
                let mut sighup = signal(SignalKind::hangup())
                    .expect("无法注册 SIGHUP 处理器");
                
                tokio::select! {
                    _ = sigterm.recv() => {
                        info!("收到 SIGTERM 信号，准备关闭");
                        shutdown_notify.notify_waiters();
                    }
                    _ = sigint.recv() => {
                        info!("收到 SIGINT 信号，准备关闭");
                        shutdown_notify.notify_waiters();
                    }
                    _ = sighup.recv() => {
                        info!("收到 SIGHUP 信号，重新加载配置");
                        // TODO: 实现配置重新加载
                    }
                }
            }
            
            #[cfg(windows)]
            {
                match tokio::signal::ctrl_c().await {
                    Ok(()) => {
                        info!("收到 Ctrl+C 信号，准备关闭");
                        shutdown_notify.notify_waiters();
                    }
                    Err(err) => {
                        error!("无法监听 Ctrl+C 信号: {}", err);
                    }
                }
            }
        });
    }
}

impl Default for SignalHandler {
    fn default() -> Self {
        Self::new()
    }
}
