//! 应用恢复管理器
//! 
//! 负责检测应用故障并执行恢复策略

use std::sync::Arc;
use std::time::Duration;
use tokio::sync::{RwLock, Mutex};
use tracing::{info, warn, error, debug};
use chrono::{DateTime, Utc};

use super::{
    AppManagerError, AppManagerResult, RecoveryStrategy, HealthStatus,
    launcher::AppLauncher, monitor::HealthChecker, AppConfig,
};

/// 恢复尝试记录
#[derive(Debug, Clone)]
pub struct RecoveryAttempt {
    /// 恢复策略
    pub strategy: RecoveryStrategy,
    /// 尝试时间
    pub attempted_at: DateTime<Utc>,
    /// 是否成功
    pub success: bool,
    /// 错误信息
    pub error_message: Option<String>,
    /// 恢复耗时
    pub duration: Duration,
}

/// 恢复统计信息
#[derive(Debug, Clone)]
pub struct RecoveryStatistics {
    /// 总恢复次数
    pub total_attempts: usize,
    /// 成功恢复次数
    pub successful_attempts: usize,
    /// 失败恢复次数
    pub failed_attempts: usize,
    /// 成功率 (%)
    pub success_rate: f64,
    /// 平均恢复时间
    pub average_recovery_time: Duration,
    /// 最后恢复时间
    pub last_recovery_time: Option<DateTime<Utc>>,
    /// 最常用的恢复策略
    pub most_used_strategy: Option<RecoveryStrategy>,
}

/// 恢复管理器
pub struct RecoveryManager {
    /// 应用配置
    config: AppConfig,
    /// 应用启动器
    launcher: Arc<Mutex<AppLauncher>>,
    /// 健康检查器
    health_checker: HealthChecker,
    /// 恢复历史记录
    recovery_history: Arc<RwLock<Vec<RecoveryAttempt>>>,
    /// 当前恢复尝试次数
    current_attempt_count: Arc<RwLock<usize>>,
    /// 上次恢复时间
    last_recovery_time: Arc<RwLock<Option<DateTime<Utc>>>>,
}

impl RecoveryManager {
    /// 创建新的恢复管理器
    pub fn new(config: AppConfig) -> Self {
        let launcher = Arc::new(Mutex::new(AppLauncher::new(config.clone())));
        let health_checker = HealthChecker::new(config.clone());

        Self {
            config,
            launcher,
            health_checker,
            recovery_history: Arc::new(RwLock::new(Vec::new())),
            current_attempt_count: Arc::new(RwLock::new(0)),
            last_recovery_time: Arc::new(RwLock::new(None)),
        }
    }

    /// 自动恢复应用
    pub async fn auto_recover(&self) -> AppManagerResult<()> {
        info!("开始自动恢复流程");

        // 检查是否需要等待恢复间隔
        if let Some(last_time) = *self.last_recovery_time.read().await {
            let elapsed = chrono::Utc::now() - last_time;
            let min_interval = Duration::from_secs(30); // 最小恢复间隔30秒
            
            if elapsed.to_std().unwrap_or(Duration::ZERO) < min_interval {
                return Err(AppManagerError::RecoveryInProgress(
                    "恢复间隔未到，请稍后再试".to_string()
                ));
            }
        }

        // 获取当前尝试次数
        let attempt_count = {
            let mut count = self.current_attempt_count.write().await;
            *count += 1;
            *count
        };

        // 选择恢复策略
        let strategy = self.select_recovery_strategy(attempt_count).await;
        info!("选择恢复策略: {:?} (第{}次尝试)", strategy, attempt_count);

        // 执行恢复
        let start_time = chrono::Utc::now();
        let result = self.execute_recovery_strategy(&strategy).await;
        let duration = chrono::Utc::now() - start_time;

        // 记录恢复尝试
        let attempt = RecoveryAttempt {
            strategy: strategy.clone(),
            attempted_at: start_time,
            success: result.is_ok(),
            error_message: result.as_ref().err().map(|e| e.to_string()),
            duration: duration.to_std().unwrap_or(Duration::ZERO),
        };

        self.record_recovery_attempt(attempt).await;

        // 更新最后恢复时间
        *self.last_recovery_time.write().await = Some(start_time);

        // 如果恢复成功，重置尝试计数
        if result.is_ok() {
            *self.current_attempt_count.write().await = 0;
            info!("应用恢复成功");
        } else {
            error!("应用恢复失败: {:?}", result);
        }

        result
    }

    /// 手动恢复应用
    pub async fn manual_recover(&self, strategy: RecoveryStrategy) -> AppManagerResult<()> {
        info!("开始手动恢复流程，策略: {:?}", strategy);

        let start_time = chrono::Utc::now();
        let result = self.execute_recovery_strategy(&strategy).await;
        let duration = chrono::Utc::now() - start_time;

        // 记录恢复尝试
        let attempt = RecoveryAttempt {
            strategy: strategy.clone(),
            attempted_at: start_time,
            success: result.is_ok(),
            error_message: result.as_ref().err().map(|e| e.to_string()),
            duration: duration.to_std().unwrap_or(Duration::ZERO),
        };

        self.record_recovery_attempt(attempt).await;

        // 更新最后恢复时间
        *self.last_recovery_time.write().await = Some(start_time);

        result
    }

    /// 检查是否需要恢复
    pub async fn should_recover(&self) -> AppManagerResult<bool> {
        let health_status = self.health_checker.check_health().await?;
        
        match health_status.status {
            HealthStatus::Dead => {
                info!("应用已死亡，需要恢复");
                Ok(true)
            }
            HealthStatus::Critical => {
                warn!("应用处于危险状态，建议恢复");
                Ok(true)
            }
            HealthStatus::Warning => {
                debug!("应用有警告，暂不需要恢复");
                Ok(false)
            }
            HealthStatus::Healthy => {
                debug!("应用运行正常，无需恢复");
                Ok(false)
            }
        }
    }

    /// 获取恢复历史记录
    pub async fn get_recovery_history(&self) -> Vec<RecoveryAttempt> {
        let history = self.recovery_history.read().await;
        history.clone()
    }

    /// 获取恢复统计信息
    pub async fn get_recovery_statistics(&self) -> RecoveryStatistics {
        let history = self.recovery_history.read().await;
        
        let total_attempts = history.len();
        let successful_attempts = history.iter().filter(|a| a.success).count();
        let failed_attempts = total_attempts - successful_attempts;
        
        let success_rate = if total_attempts > 0 {
            (successful_attempts as f64 / total_attempts as f64) * 100.0
        } else {
            0.0
        };

        let average_recovery_time = if total_attempts > 0 {
            let total_duration: Duration = history.iter()
                .map(|a| a.duration)
                .sum();
            total_duration / total_attempts as u32
        } else {
            Duration::ZERO
        };

        let last_recovery_time = history.last().map(|a| a.attempted_at);

        // 统计最常用的恢复策略
        let most_used_strategy = if !history.is_empty() {
            let mut strategy_counts = std::collections::HashMap::new();
            for attempt in history.iter() {
                *strategy_counts.entry(attempt.strategy.clone()).or_insert(0) += 1;
            }
            
            strategy_counts.into_iter()
                .max_by_key(|(_, count)| *count)
                .map(|(strategy, _)| strategy)
        } else {
            None
        };

        RecoveryStatistics {
            total_attempts,
            successful_attempts,
            failed_attempts,
            success_rate,
            average_recovery_time,
            last_recovery_time,
            most_used_strategy,
        }
    }

    /// 清理恢复历史记录
    pub async fn clear_recovery_history(&self) {
        let mut history = self.recovery_history.write().await;
        history.clear();
        info!("恢复历史记录已清理");
    }

    /// 选择恢复策略
    async fn select_recovery_strategy(&self, attempt_count: usize) -> RecoveryStrategy {
        // 根据尝试次数循环选择恢复策略
                 let strategies = vec![
             RecoveryStrategy::RestartApp,
             RecoveryStrategy::ClearCache,
             RecoveryStrategy::ResetConfig,
             RecoveryStrategy::ForceKillAndRestart,
             RecoveryStrategy::RollbackVersion,
         ];

        let index = (attempt_count - 1) % strategies.len();
        strategies[index].clone()
    }

    /// 执行恢复策略
    async fn execute_recovery_strategy(&self, strategy: &RecoveryStrategy) -> AppManagerResult<()> {
        match strategy {
            RecoveryStrategy::RestartApp => {
                self.restart_app().await
            }
            RecoveryStrategy::ClearCache => {
                self.clear_cache().await
            }
            RecoveryStrategy::ResetConfig => {
                self.reset_config().await
            }
                         RecoveryStrategy::ForceKillAndRestart => {
                 self.force_kill_restart().await
             }
            RecoveryStrategy::RollbackVersion => {
                self.rollback_version().await
            }
        }
    }

    /// 重启应用
    async fn restart_app(&self) -> AppManagerResult<()> {
        info!("执行应用重启");
        
        // 尝试优雅关闭
        {
            let mut launcher = self.launcher.lock().await;
            if let Err(e) = launcher.shutdown_app_graceful().await {
                warn!("优雅关闭失败: {}, 继续重启流程", e);
            }
        }

        // 等待一段时间确保进程完全关闭
        tokio::time::sleep(Duration::from_secs(2)).await;

        // 启动应用
        {
            let mut launcher = self.launcher.lock().await;
            launcher.launch_app_silent().await?;
        }

        info!("应用重启完成");
        Ok(())
    }

    /// 清理缓存
    async fn clear_cache(&self) -> AppManagerResult<()> {
        info!("执行缓存清理");
        
        // 这里应该实现具体的缓存清理逻辑
        // 例如删除临时文件、清理数据库缓存等
        
        // 模拟缓存清理
        tokio::time::sleep(Duration::from_millis(500)).await;
        
        // 重启应用
        self.restart_app().await?;
        
        info!("缓存清理完成");
        Ok(())
    }

    /// 重置配置
    async fn reset_config(&self) -> AppManagerResult<()> {
        info!("执行配置重置");
        
        // 这里应该实现具体的配置重置逻辑
        // 例如恢复默认配置、清理用户设置等
        
        // 模拟配置重置
        tokio::time::sleep(Duration::from_millis(300)).await;
        
        // 重启应用
        self.restart_app().await?;
        
        info!("配置重置完成");
        Ok(())
    }

    /// 强制终止并重启
    async fn force_kill_restart(&self) -> AppManagerResult<()> {
        info!("执行强制终止并重启");
        
        // 强制终止应用
        {
            let mut launcher = self.launcher.lock().await;
            if let Err(e) = launcher.force_kill_app().await {
                warn!("强制终止失败: {}, 继续重启流程", e);
            }
        }

        // 等待一段时间确保进程完全终止
        tokio::time::sleep(Duration::from_secs(3)).await;

        // 启动应用
        {
            let mut launcher = self.launcher.lock().await;
            launcher.launch_app_silent().await?;
        }

        info!("强制终止并重启完成");
        Ok(())
    }

    /// 版本回滚
    async fn rollback_version(&self) -> AppManagerResult<()> {
        info!("执行版本回滚");
        
        // 这里应该实现具体的版本回滚逻辑
        // 例如恢复到上一个稳定版本
        
        // 模拟版本回滚
        tokio::time::sleep(Duration::from_secs(1)).await;
        
        // 重启应用
        self.restart_app().await?;
        
        info!("版本回滚完成");
        Ok(())
    }

    /// 记录恢复尝试
    async fn record_recovery_attempt(&self, attempt: RecoveryAttempt) {
        let mut history = self.recovery_history.write().await;
        history.push(attempt);
        
        // 保持历史记录在合理范围内 (最多保留100条)
        if history.len() > 100 {
            history.remove(0);
        }
    }
}

impl Drop for RecoveryManager {
    fn drop(&mut self) {
        info!("恢复管理器正在销毁");
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    fn create_test_config() -> AppConfig {
        AppConfig {
            app_path: PathBuf::from("test_app"),
            working_directory: None,
            startup_args: vec![],
            startup_timeout: 30,
            health_check_interval: 10,
            max_restart_attempts: 3,
            restart_delay: 5,
            ipc_port: 8080,
            silent_mode: true,
            memory_limit_mb: Some(100),
            cpu_limit_percent: Some(50.0),
        }
    }

    #[tokio::test]
    async fn test_recovery_manager_creation() {
        let config = create_test_config();
        let manager = RecoveryManager::new(config);
        
        let history = manager.get_recovery_history().await;
        assert!(history.is_empty());
    }

    #[tokio::test]
    async fn test_recovery_statistics() {
        let config = create_test_config();
        let manager = RecoveryManager::new(config);
        
        let stats = manager.get_recovery_statistics().await;
        assert_eq!(stats.total_attempts, 0);
        assert_eq!(stats.successful_attempts, 0);
        assert_eq!(stats.failed_attempts, 0);
        assert_eq!(stats.success_rate, 0.0);
        assert_eq!(stats.average_recovery_time, Duration::ZERO);
        assert!(stats.last_recovery_time.is_none());
        assert!(stats.most_used_strategy.is_none());
    }

    #[tokio::test]
    async fn test_recovery_strategy_selection() {
        let config = create_test_config();
        let manager = RecoveryManager::new(config);
        
        // 测试策略选择的循环性
        let strategy1 = manager.select_recovery_strategy(1).await;
        let strategy2 = manager.select_recovery_strategy(2).await;
        let strategy6 = manager.select_recovery_strategy(6).await; // 应该和第1次相同
        
        assert_eq!(strategy1, RecoveryStrategy::RestartApp);
        assert_eq!(strategy2, RecoveryStrategy::ClearCache);
        assert_eq!(strategy1, strategy6); // 循环
    }

    #[tokio::test]
    async fn test_clear_recovery_history() {
        let config = create_test_config();
        let manager = RecoveryManager::new(config);
        
        // 添加一个模拟的恢复记录
        let attempt = RecoveryAttempt {
            strategy: RecoveryStrategy::RestartApp,
            attempted_at: chrono::Utc::now(),
            success: true,
            error_message: None,
            duration: Duration::from_secs(1),
        };
        
        manager.record_recovery_attempt(attempt).await;
        
        let history_before = manager.get_recovery_history().await;
        assert_eq!(history_before.len(), 1);
        
        manager.clear_recovery_history().await;
        
        let history_after = manager.get_recovery_history().await;
        assert!(history_after.is_empty());
    }

    #[test]
    fn test_recovery_attempt_creation() {
        let attempt = RecoveryAttempt {
            strategy: RecoveryStrategy::RestartApp,
            attempted_at: chrono::Utc::now(),
            success: true,
            error_message: None,
            duration: Duration::from_secs(1),
        };
        
        assert_eq!(attempt.strategy, RecoveryStrategy::RestartApp);
        assert!(attempt.success);
        assert!(attempt.error_message.is_none());
        assert_eq!(attempt.duration, Duration::from_secs(1));
    }
} 