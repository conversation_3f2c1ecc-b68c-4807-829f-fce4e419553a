//! Tauri 应用管理器模块
//! 
//! 负责 Tauri 主应用的完整生命周期管理，包括：
//! - 应用启动器和生命周期管理
//! - 健康监控和故障恢复
//! - 静默运行模式支持
//! - 应用版本管理和更新

pub mod launcher;
pub mod monitor;
pub mod lifecycle;
pub mod recovery;
pub mod updater;
pub mod process;

// 重新导出主要结构
pub use launcher::AppLauncher;
pub use monitor::HealthChecker;
pub use lifecycle::AppLifecycleManager;
pub use recovery::RecoveryManager;
pub use updater::VersionManager;
pub use process::{ProcessMonitor, ResourceTracker};

use std::path::PathBuf;
use std::time::Duration;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

/// 应用配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    /// 应用可执行文件路径
    pub app_path: PathBuf,
    /// 工作目录
    pub working_directory: Option<PathBuf>,
    /// 启动参数
    pub startup_args: Vec<String>,
    /// 启动超时时间 (秒)
    pub startup_timeout: u64,
    /// 健康检查间隔 (秒)
    pub health_check_interval: u64,
    /// 最大重启尝试次数
    pub max_restart_attempts: u32,
    /// 重启间隔 (秒)
    pub restart_delay: u64,
    /// IPC 端口
    pub ipc_port: u16,
    /// 是否启用静默模式
    pub silent_mode: bool,
    /// 内存限制 (MB)
    pub memory_limit_mb: Option<u64>,
    /// CPU 限制 (百分比)
    pub cpu_limit_percent: Option<f64>,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            app_path: PathBuf::from("./secure-password-app"),
            working_directory: None,
            startup_args: vec![],
            startup_timeout: 60,
            health_check_interval: 30,
            max_restart_attempts: 3,
            restart_delay: 10,
            ipc_port: 8080,
            silent_mode: true,
            memory_limit_mb: Some(500),
            cpu_limit_percent: Some(80.0),
        }
    }
}

/// 应用状态
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum AppStatus {
    /// 未启动
    NotStarted,
    /// 启动中
    Starting,
    /// 运行中
    Running,
    /// 暂停
    Paused,
    /// 重启中
    Restarting,
    /// 停止中
    Stopping,
    /// 已停止
    Stopped,
    /// 错误状态
    Error(String),
    /// 崩溃
    Crashed,
}

/// 应用信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppInfo {
    /// 应用状态
    pub status: AppStatus,
    /// 进程 ID
    pub process_id: Option<u32>,
    /// 启动时间
    pub start_time: Option<DateTime<Utc>>,
    /// 运行时长
    pub uptime: Option<Duration>,
    /// 版本信息
    pub version: Option<String>,
    /// 内存使用 (MB)
    pub memory_usage_mb: Option<u64>,
    /// CPU 使用率 (%)
    pub cpu_usage_percent: Option<f64>,
    /// 重启次数
    pub restart_count: u32,
    /// 最后错误
    pub last_error: Option<String>,
}

/// 健康状态
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum HealthStatus {
    /// 健康
    Healthy,
    /// 警告
    Warning,
    /// 严重
    Critical,
    /// 死亡
    Dead,
}

/// 健康检查结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheckResult {
    /// 健康状态
    pub status: HealthStatus,
    /// 检查时间
    pub checked_at: DateTime<Utc>,
    /// 响应时间 (毫秒)
    pub response_time_ms: Option<u64>,
    /// 内存使用
    pub memory_usage: Option<ResourceUsage>,
    /// CPU 使用
    pub cpu_usage: Option<ResourceUsage>,
    /// 详细信息
    pub details: Option<String>,
}

/// 资源使用情况
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceUsage {
    /// 当前值
    pub current: f64,
    /// 限制值
    pub limit: Option<f64>,
    /// 使用率 (%)
    pub percentage: f64,
}

/// 恢复策略
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum RecoveryStrategy {
    /// 重启应用
    RestartApp,
    /// 清理缓存
    ClearCache,
    /// 重置配置
    ResetConfig,
    /// 强制终止并重启
    ForceKillAndRestart,
    /// 回滚到上一版本
    RollbackVersion,
}

/// 应用管理器错误
#[derive(Debug, thiserror::Error)]
pub enum AppManagerError {
    #[error("应用启动失败: {0}")]
    StartupFailed(String),
    
    #[error("应用停止失败: {0}")]
    ShutdownFailed(String),
    
    #[error("启动超时")]
    StartupTimeout,
    
    #[error("健康检查失败: {0}")]
    HealthCheckFailed(String),
    
    #[error("恢复失败: {0}")]
    RecoveryFailed(String),
    
    #[error("版本管理失败: {0}")]
    VersionManagementFailed(String),
    
    #[error("IPC 连接失败: {0}")]
    IpcConnectionFailed(String),
    
    #[error("进程监控失败: {0}")]
    ProcessMonitoringFailed(String),
    
    #[error("配置错误: {0}")]
    ConfigError(String),
    
    #[error("恢复正在进行: {0}")]
    RecoveryInProgress(String),
    
    #[error("IO 错误: {0}")]
    IoError(#[from] std::io::Error),
}

/// 应用管理器结果类型
pub type AppManagerResult<T> = Result<T, AppManagerError>;
