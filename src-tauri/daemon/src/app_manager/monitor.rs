//! 健康监控模块
//! 
//! 负责应用的健康检查、性能监控和状态报告

use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{RwLock, Notify};
use tokio::time::interval;
use tracing::{info, warn, error, debug};
use chrono::Utc;
use sysinfo::{System, Process, Pid};

use super::{
    AppConfig, HealthStatus, HealthCheckResult, ResourceUsage, 
    AppManagerError, AppManagerResult
};

/// 健康检查器
pub struct HealthChecker {
    /// 应用配置
    config: AppConfig,
    /// 系统信息
    system: Arc<RwLock<System>>,
    /// 当前进程 ID
    process_id: Option<u32>,
    /// 上次检查时间
    last_check_time: Option<Instant>,
    /// 检查历史
    check_history: Arc<RwLock<Vec<HealthCheckResult>>>,
    /// 停止信号
    stop_signal: Arc<Notify>,
    /// 运行状态
    is_running: Arc<RwLock<bool>>,
}

impl HealthChecker {
    /// 创建新的健康检查器
    pub fn new(config: AppConfig) -> Self {
        let mut system = System::new_all();
        system.refresh_all();

        Self {
            config,
            system: Arc::new(RwLock::new(system)),
            process_id: None,
            last_check_time: None,
            check_history: Arc::new(RwLock::new(Vec::new())),
            stop_signal: Arc::new(Notify::new()),
            is_running: Arc::new(RwLock::new(false)),
        }
    }

    /// 设置要监控的进程 ID
    pub fn set_process_id(&mut self, pid: u32) {
        self.process_id = Some(pid);
        info!("设置监控进程 ID: {}", pid);
    }

    /// 启动健康监控
    pub async fn start_monitoring(&self) -> AppManagerResult<()> {
        {
            let mut is_running = self.is_running.write().await;
            if *is_running {
                return Err(AppManagerError::HealthCheckFailed("健康监控已在运行".to_string()));
            }
            *is_running = true;
        }

        info!("启动健康监控，检查间隔: {} 秒", self.config.health_check_interval);

        let config = self.config.clone();
        let system = Arc::clone(&self.system);
        let process_id = self.process_id;
        let check_history = Arc::clone(&self.check_history);
        let stop_signal = Arc::clone(&self.stop_signal);
        let is_running = Arc::clone(&self.is_running);

        tokio::spawn(async move {
            let mut interval_timer = interval(Duration::from_secs(config.health_check_interval));

            loop {
                tokio::select! {
                    _ = interval_timer.tick() => {
                        if let Some(pid) = process_id {
                            match Self::perform_health_check(&config, &system, pid).await {
                                Ok(result) => {
                                    debug!("健康检查完成: {:?}", result.status);
                                    
                                    // 保存检查结果
                                    let mut history = check_history.write().await;
                                    history.push(result.clone());
                                    
                                    // 保持历史记录在合理范围内 (最多保留100条)
                                    if history.len() > 100 {
                                        history.remove(0);
                                    }
                                    
                                    // 如果健康状态严重，记录警告
                                    if result.status == HealthStatus::Critical || result.status == HealthStatus::Dead {
                                        warn!("应用健康状态异常: {:?}", result);
                                    }
                                }
                                Err(e) => {
                                    error!("健康检查失败: {}", e);
                                }
                            }
                        } else {
                            debug!("未设置进程 ID，跳过健康检查");
                        }
                    }
                    _ = stop_signal.notified() => {
                        info!("收到停止信号，退出健康监控");
                        break;
                    }
                }
            }

            let mut is_running = is_running.write().await;
            *is_running = false;
        });

        Ok(())
    }

    /// 停止健康监控
    pub async fn stop_monitoring(&self) -> AppManagerResult<()> {
        info!("停止健康监控");
        
        self.stop_signal.notify_waiters();
        
        // 等待监控线程停止
        let mut attempts = 0;
        while attempts < 10 {
            {
                let is_running = self.is_running.read().await;
                if !*is_running {
                    break;
                }
            }
            tokio::time::sleep(Duration::from_millis(100)).await;
            attempts += 1;
        }

        Ok(())
    }

    /// 执行单次健康检查
    pub async fn check_health(&self) -> AppManagerResult<HealthCheckResult> {
        if let Some(pid) = self.process_id {
            Self::perform_health_check(&self.config, &self.system, pid).await
        } else {
            Err(AppManagerError::HealthCheckFailed("未设置进程 ID".to_string()))
        }
    }

    /// 获取检查历史
    pub async fn get_check_history(&self) -> Vec<HealthCheckResult> {
        let history = self.check_history.read().await;
        history.clone()
    }

    /// 获取最近的健康状态
    pub async fn get_latest_health_status(&self) -> Option<HealthStatus> {
        let history = self.check_history.read().await;
        history.last().map(|result| result.status.clone())
    }

    /// 检查是否正在监控
    pub async fn is_monitoring(&self) -> bool {
        let is_running = self.is_running.read().await;
        *is_running
    }

    /// 执行健康检查的核心逻辑
    async fn perform_health_check(
        config: &AppConfig,
        system: &Arc<RwLock<System>>,
        pid: u32,
    ) -> AppManagerResult<HealthCheckResult> {
        let start_time = Instant::now();
        let checked_at = Utc::now();

        // 刷新系统信息
        {
            let mut sys = system.write().await;
            sys.refresh_processes();
        }

        // 检查进程是否存在
        let process_exists = {
            let sys = system.read().await;
            sys.process(sysinfo::Pid::from_u32(pid)).is_some()
        };

        if !process_exists {
            return Ok(HealthCheckResult {
                status: HealthStatus::Dead,
                checked_at,
                response_time_ms: Some(start_time.elapsed().as_millis() as u64),
                memory_usage: None,
                cpu_usage: None,
                details: Some("进程不存在".to_string()),
            });
        }

        // 获取进程信息
        let (memory_usage, cpu_usage) = {
            let sys = system.read().await;
            if let Some(process) = sys.process(sysinfo::Pid::from_u32(pid)) {
                let memory_mb = process.memory() / 1024 / 1024; // 转换为 MB
                let cpu_percent = process.cpu_usage();

                let memory_usage = ResourceUsage {
                    current: memory_mb as f64,
                    limit: config.memory_limit_mb.map(|l| l as f64),
                    percentage: if let Some(limit) = config.memory_limit_mb {
                        (memory_mb as f64 / limit as f64) * 100.0
                    } else {
                        0.0
                    },
                };

                let cpu_usage = ResourceUsage {
                    current: cpu_percent as f64,
                    limit: config.cpu_limit_percent,
                    percentage: cpu_percent as f64,
                };

                (Some(memory_usage), Some(cpu_usage))
            } else {
                (None, None)
            }
        };

        // 检查 IPC 连接
        let ipc_response_time = Self::check_ipc_connectivity(config.ipc_port).await;

        // 确定健康状态
        let status = Self::determine_health_status(config, &memory_usage, &cpu_usage, &ipc_response_time);

        let response_time_ms = start_time.elapsed().as_millis() as u64;

        let details = Self::generate_health_details(&memory_usage, &cpu_usage, &ipc_response_time);
        
        Ok(HealthCheckResult {
            status,
            checked_at,
            response_time_ms: Some(response_time_ms),
            memory_usage,
            cpu_usage,
            details,
        })
    }

    /// 检查 IPC 连接性
    async fn check_ipc_connectivity(port: u16) -> Option<Duration> {
        let start_time = Instant::now();
        
        match tokio::time::timeout(
            Duration::from_secs(5),
            tokio::net::TcpStream::connect(format!("127.0.0.1:{}", port))
        ).await {
            Ok(Ok(_)) => {
                debug!("IPC 连接检查成功");
                Some(start_time.elapsed())
            }
            Ok(Err(e)) => {
                debug!("IPC 连接失败: {}", e);
                None
            }
            Err(_) => {
                debug!("IPC 连接超时");
                None
            }
        }
    }

    /// 确定健康状态
    fn determine_health_status(
        config: &AppConfig,
        memory_usage: &Option<ResourceUsage>,
        cpu_usage: &Option<ResourceUsage>,
        ipc_response_time: &Option<Duration>,
    ) -> HealthStatus {
        // 检查 IPC 连接
        if ipc_response_time.is_none() {
            return HealthStatus::Critical;
        }

        // 检查 IPC 响应时间
        if let Some(response_time) = ipc_response_time {
            if response_time.as_secs() > 5 {
                return HealthStatus::Warning;
            }
        }

        // 检查内存使用
        if let Some(memory) = memory_usage {
            if let Some(limit) = memory.limit {
                if memory.current > limit {
                    return HealthStatus::Critical;
                }
                if memory.percentage > 90.0 {
                    return HealthStatus::Warning;
                }
            }
        }

        // 检查 CPU 使用
        if let Some(cpu) = cpu_usage {
            if let Some(limit) = cpu.limit {
                if cpu.current > limit {
                    return HealthStatus::Warning;
                }
            }
            if cpu.current > 95.0 {
                return HealthStatus::Critical;
            }
        }

        HealthStatus::Healthy
    }

    /// 生成健康详情
    fn generate_health_details(
        memory_usage: &Option<ResourceUsage>,
        cpu_usage: &Option<ResourceUsage>,
        ipc_response_time: &Option<Duration>,
    ) -> Option<String> {
        let mut details = Vec::new();

        if let Some(memory) = memory_usage {
            details.push(format!("内存: {:.1}MB ({:.1}%)", memory.current, memory.percentage));
        }

        if let Some(cpu) = cpu_usage {
            details.push(format!("CPU: {:.1}%", cpu.current));
        }

        if let Some(response_time) = ipc_response_time {
            details.push(format!("IPC响应: {}ms", response_time.as_millis()));
        } else {
            details.push("IPC: 无响应".to_string());
        }

        if details.is_empty() {
            None
        } else {
            Some(details.join(", "))
        }
    }

    /// 获取健康统计信息
    pub async fn get_health_statistics(&self) -> HealthStatistics {
        let history = self.check_history.read().await;
        
        let total_checks = history.len();
        let healthy_count = history.iter().filter(|r| r.status == HealthStatus::Healthy).count();
        let warning_count = history.iter().filter(|r| r.status == HealthStatus::Warning).count();
        let critical_count = history.iter().filter(|r| r.status == HealthStatus::Critical).count();
        let dead_count = history.iter().filter(|r| r.status == HealthStatus::Dead).count();

        let avg_response_time = if !history.is_empty() {
            let total_time: u64 = history.iter()
                .filter_map(|r| r.response_time_ms)
                .sum();
            total_time as f64 / history.len() as f64
        } else {
            0.0
        };

        let uptime_percentage = if total_checks > 0 {
            ((healthy_count + warning_count) as f64 / total_checks as f64) * 100.0
        } else {
            0.0
        };

        HealthStatistics {
            total_checks,
            healthy_count,
            warning_count,
            critical_count,
            dead_count,
            avg_response_time_ms: avg_response_time,
            uptime_percentage,
            last_check_time: history.last().map(|r| r.checked_at),
        }
    }
}

/// 健康统计信息
#[derive(Debug, Clone)]
pub struct HealthStatistics {
    /// 总检查次数
    pub total_checks: usize,
    /// 健康次数
    pub healthy_count: usize,
    /// 警告次数
    pub warning_count: usize,
    /// 严重次数
    pub critical_count: usize,
    /// 死亡次数
    pub dead_count: usize,
    /// 平均响应时间 (毫秒)
    pub avg_response_time_ms: f64,
    /// 可用性百分比
    pub uptime_percentage: f64,
    /// 最后检查时间
    pub last_check_time: Option<chrono::DateTime<Utc>>,
}

impl Drop for HealthChecker {
    fn drop(&mut self) {
        // 发送停止信号
        self.stop_signal.notify_waiters();
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    fn create_test_config() -> AppConfig {
        AppConfig {
            app_path: PathBuf::from("test-app"),
            working_directory: None,
            startup_args: vec![],
            startup_timeout: 30,
            health_check_interval: 5,
            max_restart_attempts: 3,
            restart_delay: 10,
            ipc_port: 18080,
            silent_mode: true,
            memory_limit_mb: Some(100),
            cpu_limit_percent: Some(80.0),
        }
    }

    #[tokio::test]
    async fn test_health_checker_creation() {
        let config = create_test_config();
        let checker = HealthChecker::new(config.clone());
        
        assert_eq!(checker.config.health_check_interval, config.health_check_interval);
        assert!(checker.process_id.is_none());
        assert!(!checker.is_monitoring().await);
    }

    #[tokio::test]
    async fn test_set_process_id() {
        let config = create_test_config();
        let mut checker = HealthChecker::new(config);
        
        checker.set_process_id(12345);
        assert_eq!(checker.process_id, Some(12345));
    }

    #[tokio::test]
    async fn test_determine_health_status_healthy() {
        let config = create_test_config();
        
        let memory_usage = Some(ResourceUsage {
            current: 50.0,
            limit: Some(100.0),
            percentage: 50.0,
        });
        
        let cpu_usage = Some(ResourceUsage {
            current: 30.0,
            limit: Some(80.0),
            percentage: 30.0,
        });
        
        let ipc_response_time = Some(Duration::from_millis(100));
        
        let status = HealthChecker::determine_health_status(
            &config,
            &memory_usage,
            &cpu_usage,
            &ipc_response_time,
        );
        
        assert_eq!(status, HealthStatus::Healthy);
    }

    #[tokio::test]
    async fn test_determine_health_status_critical() {
        let config = create_test_config();
        
        let memory_usage = Some(ResourceUsage {
            current: 150.0,  // 超过限制
            limit: Some(100.0),
            percentage: 150.0,
        });
        
        let cpu_usage = Some(ResourceUsage {
            current: 30.0,
            limit: Some(80.0),
            percentage: 30.0,
        });
        
        let ipc_response_time = Some(Duration::from_millis(100));
        
        let status = HealthChecker::determine_health_status(
            &config,
            &memory_usage,
            &cpu_usage,
            &ipc_response_time,
        );
        
        assert_eq!(status, HealthStatus::Critical);
    }

    #[tokio::test]
    async fn test_determine_health_status_no_ipc() {
        let config = create_test_config();
        
        let memory_usage = Some(ResourceUsage {
            current: 50.0,
            limit: Some(100.0),
            percentage: 50.0,
        });
        
        let cpu_usage = Some(ResourceUsage {
            current: 30.0,
            limit: Some(80.0),
            percentage: 30.0,
        });
        
        let ipc_response_time = None;  // 无 IPC 连接
        
        let status = HealthChecker::determine_health_status(
            &config,
            &memory_usage,
            &cpu_usage,
            &ipc_response_time,
        );
        
        assert_eq!(status, HealthStatus::Critical);
    }

    #[tokio::test]
    async fn test_generate_health_details() {
        let memory_usage = Some(ResourceUsage {
            current: 75.5,
            limit: Some(100.0),
            percentage: 75.5,
        });
        
        let cpu_usage = Some(ResourceUsage {
            current: 45.2,
            limit: Some(80.0),
            percentage: 45.2,
        });
        
        let ipc_response_time = Some(Duration::from_millis(150));
        
        let details = HealthChecker::generate_health_details(
            &memory_usage,
            &cpu_usage,
            &ipc_response_time,
        );
        
        assert!(details.is_some());
        let details_str = details.unwrap();
        assert!(details_str.contains("内存: 75.5MB"));
        assert!(details_str.contains("CPU: 45.2%"));
        assert!(details_str.contains("IPC响应: 150ms"));
    }

    #[tokio::test]
    async fn test_health_statistics() {
        let config = create_test_config();
        let checker = HealthChecker::new(config);
        
        // 添加一些模拟的检查结果
        {
            let mut history = checker.check_history.write().await;
            history.push(HealthCheckResult {
                status: HealthStatus::Healthy,
                checked_at: Utc::now(),
                response_time_ms: Some(100),
                memory_usage: None,
                cpu_usage: None,
                details: None,
            });
            history.push(HealthCheckResult {
                status: HealthStatus::Warning,
                checked_at: Utc::now(),
                response_time_ms: Some(200),
                memory_usage: None,
                cpu_usage: None,
                details: None,
            });
        }
        
        let stats = checker.get_health_statistics().await;
        
        assert_eq!(stats.total_checks, 2);
        assert_eq!(stats.healthy_count, 1);
        assert_eq!(stats.warning_count, 1);
        assert_eq!(stats.critical_count, 0);
        assert_eq!(stats.dead_count, 0);
        assert_eq!(stats.avg_response_time_ms, 150.0);
        assert_eq!(stats.uptime_percentage, 100.0);
    }
} 