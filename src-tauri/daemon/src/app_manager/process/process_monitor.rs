//! 进程监控器
//! 
//! 负责监控指定进程的状态和资源使用情况

use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{info, warn, error, debug};

use super::{ProcessInfo, ProcessStatus, ResourceLimits, ProcessManagerError, ProcessManagerResult};

/// 进程监控器
pub struct ProcessMonitor {
    /// 监控的进程 ID
    pid: u32,
    /// 资源限制
    resource_limits: ResourceLimits,
    /// 监控历史
    monitoring_history: Arc<RwLock<Vec<ProcessInfo>>>,
    /// 是否正在监控
    is_monitoring: Arc<RwLock<bool>>,
}

impl ProcessMonitor {
    /// 创建新的进程监控器
    pub fn new(pid: u32, resource_limits: ResourceLimits) -> Self {
        Self {
            pid,
            resource_limits,
            monitoring_history: Arc::new(RwLock::new(Vec::new())),
            is_monitoring: Arc::new(RwLock::new(false)),
        }
    }

    /// 启动监控
    pub async fn start_monitoring(&self, interval: Duration) -> ProcessManagerResult<()> {
        {
            let mut is_monitoring = self.is_monitoring.write().await;
            if *is_monitoring {
                return Err(ProcessManagerError::SystemError("监控已在运行".to_string()));
            }
            *is_monitoring = true;
        }

        info!("启动进程监控，PID: {}, 间隔: {:?}", self.pid, interval);

        let pid = self.pid;
        let resource_limits = self.resource_limits.clone();
        let monitoring_history = Arc::clone(&self.monitoring_history);
        let is_monitoring = Arc::clone(&self.is_monitoring);

        tokio::spawn(async move {
            let mut interval_timer = tokio::time::interval(interval);

            loop {
                interval_timer.tick().await;

                // 检查是否应该停止监控
                {
                    let is_monitoring = is_monitoring.read().await;
                    if !*is_monitoring {
                        break;
                    }
                }

                // 收集进程信息
                match Self::collect_process_info(pid).await {
                    Ok(process_info) => {
                        debug!("进程信息收集完成: PID {}", pid);

                        // 检查资源限制
                        Self::check_resource_limits(&process_info, &resource_limits);

                        // 保存到历史记录
                        {
                            let mut history = monitoring_history.write().await;
                            history.push(process_info);

                            // 保持历史记录在合理范围内 (最多保留200条)
                            if history.len() > 200 {
                                history.remove(0);
                            }
                        }
                    }
                    Err(e) => {
                        error!("收集进程信息失败: {}", e);
                        // 进程可能已经退出
                        if matches!(e, ProcessManagerError::ProcessNotFound(_)) {
                            warn!("进程 {} 不存在，停止监控", pid);
                            break;
                        }
                    }
                }
            }

            let mut is_monitoring = is_monitoring.write().await;
            *is_monitoring = false;
            info!("进程监控已停止，PID: {}", pid);
        });

        Ok(())
    }

    /// 停止监控
    pub async fn stop_monitoring(&self) -> ProcessManagerResult<()> {
        let mut is_monitoring = self.is_monitoring.write().await;
        *is_monitoring = false;
        info!("请求停止进程监控，PID: {}", self.pid);
        Ok(())
    }

    /// 获取当前进程信息
    pub async fn get_current_process_info(&self) -> ProcessManagerResult<ProcessInfo> {
        Self::collect_process_info(self.pid).await
    }

    /// 获取监控历史
    pub async fn get_monitoring_history(&self) -> Vec<ProcessInfo> {
        let history = self.monitoring_history.read().await;
        history.clone()
    }

    /// 检查是否正在监控
    pub async fn is_monitoring(&self) -> bool {
        let is_monitoring = self.is_monitoring.read().await;
        *is_monitoring
    }

    /// 收集进程信息
    async fn collect_process_info(pid: u32) -> ProcessManagerResult<ProcessInfo> {
        // TODO: 使用 sysinfo 或其他系统库收集真实的进程信息
        // 这里提供一个模拟实现

        // 检查进程是否存在 (简单的检查)
        #[cfg(unix)]
        {
            use std::process::Command;
            let output = Command::new("ps")
                .args(&["-p", &pid.to_string()])
                .output()
                .map_err(|e| ProcessManagerError::SystemError(format!("执行ps命令失败: {}", e)))?;

            if !output.status.success() {
                return Err(ProcessManagerError::ProcessNotFound(pid));
            }
        }

        #[cfg(windows)]
        {
            use std::process::Command;
            let output = Command::new("tasklist")
                .args(&["/FI", &format!("PID eq {}", pid)])
                .output()
                .map_err(|e| ProcessManagerError::SystemError(format!("执行tasklist命令失败: {}", e)))?;

            if !output.status.success() {
                return Err(ProcessManagerError::ProcessNotFound(pid));
            }

            let output_str = String::from_utf8_lossy(&output.stdout);
            if !output_str.contains(&pid.to_string()) {
                return Err(ProcessManagerError::ProcessNotFound(pid));
            }
        }

        // 模拟进程信息
        Ok(ProcessInfo {
            pid,
            name: format!("process_{}", pid),
            start_time: chrono::Utc::now() - chrono::Duration::minutes(30), // 假设30分钟前启动
            uptime: Duration::from_secs(1800), // 30分钟
            parent_pid: Some(1),
            status: ProcessStatus::Running,
            memory_mb: 50.0 + (pid as f64 % 100.0), // 模拟内存使用
            cpu_percent: (pid as f64 % 20.0), // 模拟CPU使用
            thread_count: 5,
            fd_count: Some(20),
        })
    }

    /// 检查资源限制
    fn check_resource_limits(process_info: &ProcessInfo, limits: &ResourceLimits) {
        // 检查内存限制
        if let Some(max_memory) = limits.max_memory_mb {
            if process_info.memory_mb > max_memory {
                warn!(
                    "进程 {} 内存使用超出限制: {:.2} MB > {:.2} MB",
                    process_info.pid, process_info.memory_mb, max_memory
                );
            }
        }

        // 检查CPU限制
        if let Some(max_cpu) = limits.max_cpu_percent {
            if process_info.cpu_percent > max_cpu {
                warn!(
                    "进程 {} CPU使用超出限制: {:.2}% > {:.2}%",
                    process_info.pid, process_info.cpu_percent, max_cpu
                );
            }
        }

        // 检查线程数限制
        if let Some(max_threads) = limits.max_threads {
            if process_info.thread_count > max_threads {
                warn!(
                    "进程 {} 线程数超出限制: {} > {}",
                    process_info.pid, process_info.thread_count, max_threads
                );
            }
        }

        // 检查文件描述符限制
        if let (Some(fd_count), Some(max_fd)) = (process_info.fd_count, limits.max_file_descriptors) {
            if fd_count > max_fd {
                warn!(
                    "进程 {} 文件描述符数超出限制: {} > {}",
                    process_info.pid, fd_count, max_fd
                );
            }
        }
    }

    /// 获取监控统计信息
    pub async fn get_monitoring_statistics(&self) -> MonitoringStatistics {
        let history = self.monitoring_history.read().await;
        
        if history.is_empty() {
            return MonitoringStatistics::default();
        }

        let total_records = history.len();
        let avg_memory = history.iter().map(|p| p.memory_mb).sum::<f64>() / total_records as f64;
        let avg_cpu = history.iter().map(|p| p.cpu_percent).sum::<f64>() / total_records as f64;
        let max_memory = history.iter().map(|p| p.memory_mb).fold(0.0, f64::max);
        let max_cpu = history.iter().map(|p| p.cpu_percent).fold(0.0, f64::max);

        MonitoringStatistics {
            total_records,
            avg_memory_mb: avg_memory,
            avg_cpu_percent: avg_cpu,
            max_memory_mb: max_memory,
            max_cpu_percent: max_cpu,
            current_status: history.last().map(|p| p.status.clone()),
        }
    }
}

/// 监控统计信息
#[derive(Debug, Clone)]
pub struct MonitoringStatistics {
    /// 总记录数
    pub total_records: usize,
    /// 平均内存使用 (MB)
    pub avg_memory_mb: f64,
    /// 平均CPU使用率 (%)
    pub avg_cpu_percent: f64,
    /// 最大内存使用 (MB)
    pub max_memory_mb: f64,
    /// 最大CPU使用率 (%)
    pub max_cpu_percent: f64,
    /// 当前状态
    pub current_status: Option<ProcessStatus>,
}

impl Default for MonitoringStatistics {
    fn default() -> Self {
        Self {
            total_records: 0,
            avg_memory_mb: 0.0,
            avg_cpu_percent: 0.0,
            max_memory_mb: 0.0,
            max_cpu_percent: 0.0,
            current_status: None,
        }
    }
}

impl Drop for ProcessMonitor {
    fn drop(&mut self) {
        info!("进程监控器正在销毁，PID: {}", self.pid);
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_process_monitor_creation() {
        let limits = ResourceLimits::default();
        let monitor = ProcessMonitor::new(1234, limits);

        assert_eq!(monitor.pid, 1234);
        assert!(!monitor.is_monitoring().await);
    }

    #[tokio::test]
    async fn test_monitoring_statistics() {
        let limits = ResourceLimits::default();
        let monitor = ProcessMonitor::new(1234, limits);

        let stats = monitor.get_monitoring_statistics().await;
        assert_eq!(stats.total_records, 0);
        assert_eq!(stats.avg_memory_mb, 0.0);
        assert!(stats.current_status.is_none());
    }

    #[tokio::test]
    async fn test_stop_monitoring() {
        let limits = ResourceLimits::default();
        let monitor = ProcessMonitor::new(1234, limits);

        let result = monitor.stop_monitoring().await;
        assert!(result.is_ok());
    }
} 