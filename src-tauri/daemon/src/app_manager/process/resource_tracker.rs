//! 资源跟踪器
//! 
//! 负责跟踪和分析进程资源使用情况

use std::collections::VecDeque;
use std::time::{Duration, Instant};
use tracing::{info, warn, debug};
use serde::{Deserialize, Serialize};

use super::{ProcessInfo, ResourceLimits, ProcessManagerError, ProcessManagerResult};

/// 资源使用趋势
#[derive(Debug, Clone)]
pub struct ResourceTrend {
    /// 时间戳
    pub timestamp: Instant,
    /// 内存使用 (MB)
    pub memory_mb: f64,
    /// CPU使用率 (%)
    pub cpu_percent: f64,
    /// 线程数
    pub thread_count: u32,
}

/// 资源跟踪器
pub struct ResourceTracker {
    /// 进程 ID
    pid: u32,
    /// 资源限制
    limits: ResourceLimits,
    /// 资源使用历史 (环形缓冲区)
    resource_history: VecDeque<ResourceTrend>,
    /// 最大历史记录数
    max_history_size: usize,
    /// 警告阈值 (百分比)
    warning_threshold: f64,
}

impl ResourceTracker {
    /// 创建新的资源跟踪器
    pub fn new(pid: u32, limits: ResourceLimits) -> Self {
        Self {
            pid,
            limits,
            resource_history: VecDeque::new(),
            max_history_size: 1000, // 保留最近1000条记录
            warning_threshold: 0.8, // 80%阈值
        }
    }

    /// 添加资源使用记录
    pub fn track_resource_usage(&mut self, process_info: &ProcessInfo) {
        let trend = ResourceTrend {
            timestamp: Instant::now(),
            memory_mb: process_info.memory_mb,
            cpu_percent: process_info.cpu_percent,
            thread_count: process_info.thread_count,
        };

        // 添加到历史记录
        self.resource_history.push_back(trend.clone());

        // 保持历史记录在限制范围内
        if self.resource_history.len() > self.max_history_size {
            self.resource_history.pop_front();
        }

        // 检查资源使用警告
        self.check_resource_warnings(&trend);

        debug!("资源使用记录已添加: PID {}, 内存: {:.2} MB, CPU: {:.2}%", 
               self.pid, trend.memory_mb, trend.cpu_percent);
    }

    /// 获取资源使用统计
    pub fn get_resource_statistics(&self) -> ResourceStatistics {
        if self.resource_history.is_empty() {
            return ResourceStatistics::default();
        }

        let total_records = self.resource_history.len();
        
        // 计算平均值
        let avg_memory = self.resource_history.iter()
            .map(|r| r.memory_mb)
            .sum::<f64>() / total_records as f64;
        
        let avg_cpu = self.resource_history.iter()
            .map(|r| r.cpu_percent)
            .sum::<f64>() / total_records as f64;
        
        let avg_threads = self.resource_history.iter()
            .map(|r| r.thread_count as f64)
            .sum::<f64>() / total_records as f64;

        // 计算最大值
        let max_memory = self.resource_history.iter()
            .map(|r| r.memory_mb)
            .fold(0.0, f64::max);
        
        let max_cpu = self.resource_history.iter()
            .map(|r| r.cpu_percent)
            .fold(0.0, f64::max);
        
        let max_threads = self.resource_history.iter()
            .map(|r| r.thread_count)
            .max()
            .unwrap_or(0);

        // 计算最小值
        let min_memory = self.resource_history.iter()
            .map(|r| r.memory_mb)
            .fold(f64::INFINITY, f64::min);
        
        let min_cpu = self.resource_history.iter()
            .map(|r| r.cpu_percent)
            .fold(f64::INFINITY, f64::min);

        // 获取当前值
        let current = self.resource_history.back();

        ResourceStatistics {
            total_records,
            avg_memory_mb: avg_memory,
            avg_cpu_percent: avg_cpu,
            avg_thread_count: avg_threads,
            max_memory_mb: max_memory,
            max_cpu_percent: max_cpu,
            max_thread_count: max_threads,
            min_memory_mb: if min_memory == f64::INFINITY { 0.0 } else { min_memory },
            min_cpu_percent: if min_cpu == f64::INFINITY { 0.0 } else { min_cpu },
            current_memory_mb: current.map(|r| r.memory_mb),
            current_cpu_percent: current.map(|r| r.cpu_percent),
            current_thread_count: current.map(|r| r.thread_count),
        }
    }

    /// 获取资源使用趋势
    pub fn get_resource_trend(&self, duration: Duration) -> Vec<ResourceTrend> {
        let cutoff_time = Instant::now() - duration;
        
        self.resource_history.iter()
            .filter(|trend| trend.timestamp >= cutoff_time)
            .cloned()
            .collect()
    }

    /// 检查是否超出资源限制
    pub fn check_resource_limits(&self) -> Vec<ResourceViolation> {
        let mut violations = Vec::new();
        
        if let Some(current) = self.resource_history.back() {
            // 检查内存限制
            if let Some(max_memory) = self.limits.max_memory_mb {
                if current.memory_mb > max_memory {
                    violations.push(ResourceViolation {
                        resource_type: ResourceType::Memory,
                        current_value: current.memory_mb,
                        limit_value: max_memory,
                        severity: ViolationSeverity::Critical,
                    });
                } else if current.memory_mb > max_memory * self.warning_threshold {
                    violations.push(ResourceViolation {
                        resource_type: ResourceType::Memory,
                        current_value: current.memory_mb,
                        limit_value: max_memory,
                        severity: ViolationSeverity::Warning,
                    });
                }
            }

            // 检查CPU限制
            if let Some(max_cpu) = self.limits.max_cpu_percent {
                if current.cpu_percent > max_cpu {
                    violations.push(ResourceViolation {
                        resource_type: ResourceType::Cpu,
                        current_value: current.cpu_percent,
                        limit_value: max_cpu,
                        severity: ViolationSeverity::Critical,
                    });
                } else if current.cpu_percent > max_cpu * self.warning_threshold {
                    violations.push(ResourceViolation {
                        resource_type: ResourceType::Cpu,
                        current_value: current.cpu_percent,
                        limit_value: max_cpu,
                        severity: ViolationSeverity::Warning,
                    });
                }
            }

            // 检查线程数限制
            if let Some(max_threads) = self.limits.max_threads {
                if current.thread_count > max_threads {
                    violations.push(ResourceViolation {
                        resource_type: ResourceType::Threads,
                        current_value: current.thread_count as f64,
                        limit_value: max_threads as f64,
                        severity: ViolationSeverity::Critical,
                    });
                } else if current.thread_count as f64 > max_threads as f64 * self.warning_threshold {
                    violations.push(ResourceViolation {
                        resource_type: ResourceType::Threads,
                        current_value: current.thread_count as f64,
                        limit_value: max_threads as f64,
                        severity: ViolationSeverity::Warning,
                    });
                }
            }
        }

        violations
    }

    /// 预测资源使用趋势
    pub fn predict_resource_trend(&self, future_duration: Duration) -> Option<ResourcePrediction> {
        if self.resource_history.len() < 10 {
            return None; // 需要至少10个数据点进行预测
        }

        // 简单的线性趋势预测
        let recent_trends: Vec<_> = self.resource_history.iter()
            .rev()
            .take(10)
            .collect();

        if recent_trends.len() < 2 {
            return None;
        }

        // 计算内存趋势
        let memory_slope = self.calculate_slope(&recent_trends, |t| t.memory_mb);
        let cpu_slope = self.calculate_slope(&recent_trends, |t| t.cpu_percent);

        let current = recent_trends[0];
        let future_seconds = future_duration.as_secs_f64();

        let predicted_memory = current.memory_mb + memory_slope * future_seconds;
        let predicted_cpu = current.cpu_percent + cpu_slope * future_seconds;

        Some(ResourcePrediction {
            prediction_time: future_duration,
            predicted_memory_mb: predicted_memory.max(0.0),
            predicted_cpu_percent: predicted_cpu.max(0.0).min(100.0),
            confidence: self.calculate_prediction_confidence(),
        })
    }

    /// 清理旧的资源记录
    pub fn cleanup_old_records(&mut self, max_age: Duration) {
        let cutoff_time = Instant::now() - max_age;
        
        while let Some(front) = self.resource_history.front() {
            if front.timestamp < cutoff_time {
                self.resource_history.pop_front();
            } else {
                break;
            }
        }

        info!("清理旧资源记录完成，剩余记录数: {}", self.resource_history.len());
    }

    /// 检查资源使用警告
    fn check_resource_warnings(&self, trend: &ResourceTrend) {
        // 检查内存警告
        if let Some(max_memory) = self.limits.max_memory_mb {
            let usage_percent = trend.memory_mb / max_memory;
            if usage_percent > self.warning_threshold {
                warn!("进程 {} 内存使用接近限制: {:.2} MB ({:.1}%)", 
                      self.pid, trend.memory_mb, usage_percent * 100.0);
            }
        }

        // 检查CPU警告
        if let Some(max_cpu) = self.limits.max_cpu_percent {
            let usage_percent = trend.cpu_percent / max_cpu;
            if usage_percent > self.warning_threshold {
                warn!("进程 {} CPU使用接近限制: {:.2}% ({:.1}%)", 
                      self.pid, trend.cpu_percent, usage_percent * 100.0);
            }
        }
    }

    /// 计算斜率 (用于趋势预测)
    fn calculate_slope<F>(&self, trends: &[&ResourceTrend], value_fn: F) -> f64 
    where 
        F: Fn(&ResourceTrend) -> f64,
    {
        if trends.len() < 2 {
            return 0.0;
        }

        let n = trends.len() as f64;
        let mut sum_x = 0.0;
        let mut sum_y = 0.0;
        let mut sum_xy = 0.0;
        let mut sum_x2 = 0.0;

        for (i, trend) in trends.iter().enumerate() {
            let x = i as f64;
            let y = value_fn(trend);
            
            sum_x += x;
            sum_y += y;
            sum_xy += x * y;
            sum_x2 += x * x;
        }

        let denominator = n * sum_x2 - sum_x * sum_x;
        if denominator.abs() < f64::EPSILON {
            return 0.0;
        }

        (n * sum_xy - sum_x * sum_y) / denominator
    }

    /// 计算预测置信度
    fn calculate_prediction_confidence(&self) -> f64 {
        // 简单的置信度计算，基于数据点数量和变化稳定性
        let data_points = self.resource_history.len();
        let base_confidence = (data_points as f64 / 100.0).min(1.0);
        
        // 可以根据数据的稳定性进一步调整置信度
        base_confidence * 0.8 // 保守估计
    }
}

/// 资源统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceStatistics {
    /// 总记录数
    pub total_records: usize,
    /// 平均内存使用 (MB)
    pub avg_memory_mb: f64,
    /// 平均CPU使用率 (%)
    pub avg_cpu_percent: f64,
    /// 平均线程数
    pub avg_thread_count: f64,
    /// 最大内存使用 (MB)
    pub max_memory_mb: f64,
    /// 最大CPU使用率 (%)
    pub max_cpu_percent: f64,
    /// 最大线程数
    pub max_thread_count: u32,
    /// 最小内存使用 (MB)
    pub min_memory_mb: f64,
    /// 最小CPU使用率 (%)
    pub min_cpu_percent: f64,
    /// 当前内存使用 (MB)
    pub current_memory_mb: Option<f64>,
    /// 当前CPU使用率 (%)
    pub current_cpu_percent: Option<f64>,
    /// 当前线程数
    pub current_thread_count: Option<u32>,
}

impl Default for ResourceStatistics {
    fn default() -> Self {
        Self {
            total_records: 0,
            avg_memory_mb: 0.0,
            avg_cpu_percent: 0.0,
            avg_thread_count: 0.0,
            max_memory_mb: 0.0,
            max_cpu_percent: 0.0,
            max_thread_count: 0,
            min_memory_mb: 0.0,
            min_cpu_percent: 0.0,
            current_memory_mb: None,
            current_cpu_percent: None,
            current_thread_count: None,
        }
    }
}

/// 资源违规
#[derive(Debug, Clone)]
pub struct ResourceViolation {
    /// 资源类型
    pub resource_type: ResourceType,
    /// 当前值
    pub current_value: f64,
    /// 限制值
    pub limit_value: f64,
    /// 严重程度
    pub severity: ViolationSeverity,
}

/// 资源类型
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum ResourceType {
    Memory,
    Cpu,
    Threads,
    FileDescriptors,
}

/// 违规严重程度
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum ViolationSeverity {
    Warning,
    Critical,
}

/// 资源预测
#[derive(Debug, Clone)]
pub struct ResourcePrediction {
    /// 预测时间
    pub prediction_time: Duration,
    /// 预测内存使用 (MB)
    pub predicted_memory_mb: f64,
    /// 预测CPU使用率 (%)
    pub predicted_cpu_percent: f64,
    /// 预测置信度 (0.0-1.0)
    pub confidence: f64,
}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_limits() -> ResourceLimits {
        ResourceLimits {
            max_memory_mb: Some(100.0),
            max_cpu_percent: Some(80.0),
            max_threads: Some(10),
            max_file_descriptors: Some(100),
        }
    }

    fn create_test_process_info(memory_mb: f64, cpu_percent: f64, threads: u32) -> ProcessInfo {
        ProcessInfo {
            pid: 1234,
            name: "test_process".to_string(),
            start_time: chrono::Utc::now(),
            uptime: Duration::from_secs(3600),
            parent_pid: Some(1),
            status: super::super::ProcessStatus::Running,
            memory_mb,
            cpu_percent,
            thread_count: threads,
            fd_count: Some(20),
        }
    }

    #[test]
    fn test_resource_tracker_creation() {
        let limits = create_test_limits();
        let tracker = ResourceTracker::new(1234, limits);

        assert_eq!(tracker.pid, 1234);
        assert_eq!(tracker.resource_history.len(), 0);
    }

    #[test]
    fn test_track_resource_usage() {
        let limits = create_test_limits();
        let mut tracker = ResourceTracker::new(1234, limits);

        let process_info = create_test_process_info(50.0, 30.0, 5);
        tracker.track_resource_usage(&process_info);

        assert_eq!(tracker.resource_history.len(), 1);
        
        let stats = tracker.get_resource_statistics();
        assert_eq!(stats.total_records, 1);
        assert_eq!(stats.avg_memory_mb, 50.0);
        assert_eq!(stats.avg_cpu_percent, 30.0);
    }

    #[test]
    fn test_resource_violations() {
        let limits = create_test_limits();
        let mut tracker = ResourceTracker::new(1234, limits);

        // 添加超出限制的资源使用
        let process_info = create_test_process_info(150.0, 90.0, 15);
        tracker.track_resource_usage(&process_info);

        let violations = tracker.check_resource_limits();
        assert!(!violations.is_empty());
        
        // 应该有内存、CPU和线程的违规
        assert!(violations.iter().any(|v| v.resource_type == ResourceType::Memory));
        assert!(violations.iter().any(|v| v.resource_type == ResourceType::Cpu));
        assert!(violations.iter().any(|v| v.resource_type == ResourceType::Threads));
    }

    #[test]
    fn test_resource_trend() {
        let limits = create_test_limits();
        let mut tracker = ResourceTracker::new(1234, limits);

        // 添加多个数据点
        for i in 1..=5 {
            let process_info = create_test_process_info(i as f64 * 10.0, i as f64 * 5.0, i);
            tracker.track_resource_usage(&process_info);
        }

        let trend = tracker.get_resource_trend(Duration::from_secs(3600));
        assert_eq!(trend.len(), 5);
    }

    #[test]
    fn test_cleanup_old_records() {
        let limits = create_test_limits();
        let mut tracker = ResourceTracker::new(1234, limits);

        // 添加一些记录
        for i in 1..=10 {
            let process_info = create_test_process_info(i as f64 * 10.0, i as f64 * 5.0, i);
            tracker.track_resource_usage(&process_info);
        }

        assert_eq!(tracker.resource_history.len(), 10);

        // 清理旧记录 (这里由于时间戳都是最近的，不会清理任何记录)
        tracker.cleanup_old_records(Duration::from_secs(1));
        
        // 在实际测试中，可能需要手动设置时间戳来测试清理功能
    }
} 