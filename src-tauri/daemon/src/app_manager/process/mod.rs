//! 进程管理模块
//! 
//! 负责进程监控、资源跟踪和信号处理

pub mod process_monitor;
pub mod resource_tracker;
pub mod signal_handler;

// 重新导出主要结构
pub use process_monitor::ProcessMonitor;
pub use resource_tracker::ResourceTracker;
pub use signal_handler::SignalHandler;

use std::time::Duration;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

/// 进程信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessInfo {
    /// 进程 ID
    pub pid: u32,
    /// 进程名称
    pub name: String,
    /// 启动时间
    pub start_time: DateTime<Utc>,
    /// 运行时长
    pub uptime: Duration,
    /// 父进程 ID
    pub parent_pid: Option<u32>,
    /// 进程状态
    pub status: ProcessStatus,
    /// 内存使用 (MB)
    pub memory_mb: f64,
    /// CPU 使用率 (%)
    pub cpu_percent: f64,
    /// 线程数
    pub thread_count: u32,
    /// 文件描述符数量
    pub fd_count: Option<u32>,
}

/// 进程状态
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ProcessStatus {
    /// 运行中
    Running,
    /// 睡眠
    Sleeping,
    /// 等待
    Waiting,
    /// 僵尸进程
    Zombie,
    /// 已停止
    Stopped,
    /// 未知状态
    Unknown,
}

/// 资源限制
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceLimits {
    /// 最大内存 (MB)
    pub max_memory_mb: Option<f64>,
    /// 最大CPU使用率 (%)
    pub max_cpu_percent: Option<f64>,
    /// 最大线程数
    pub max_threads: Option<u32>,
    /// 最大文件描述符数
    pub max_file_descriptors: Option<u32>,
}

impl Default for ResourceLimits {
    fn default() -> Self {
        Self {
            max_memory_mb: Some(500.0),
            max_cpu_percent: Some(80.0),
            max_threads: Some(100),
            max_file_descriptors: Some(1024),
        }
    }
}

/// 进程管理器错误
#[derive(Debug, thiserror::Error)]
pub enum ProcessManagerError {
    #[error("进程不存在: {0}")]
    ProcessNotFound(u32),
    
    #[error("权限不足: {0}")]
    PermissionDenied(String),
    
    #[error("资源限制超出: {0}")]
    ResourceLimitExceeded(String),
    
    #[error("信号发送失败: {0}")]
    SignalSendFailed(String),
    
    #[error("系统错误: {0}")]
    SystemError(String),
    
    #[error("IO 错误: {0}")]
    IoError(#[from] std::io::Error),
}

/// 进程管理器结果类型
pub type ProcessManagerResult<T> = Result<T, ProcessManagerError>; 