//! 进程信号处理器
//! 
//! 负责处理进程信号和进程间通信

use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{info, warn, error, debug};

use super::{ProcessManagerError, ProcessManagerResult};

/// 信号类型
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum SignalType {
    /// 终止信号
    Terminate,
    /// 强制终止信号
    Kill,
    /// 中断信号
    Interrupt,
    /// 挂起信号
    Stop,
    /// 继续信号
    Continue,
    /// 用户定义信号1
    User1,
    /// 用户定义信号2
    User2,
}

/// 信号处理器
pub struct SignalHandler {
    /// 目标进程 ID
    target_pid: u32,
    /// 信号发送历史
    signal_history: Arc<RwLock<Vec<SignalRecord>>>,
}

/// 信号记录
#[derive(Debug, Clone)]
pub struct SignalRecord {
    /// 信号类型
    pub signal_type: SignalType,
    /// 发送时间
    pub sent_at: chrono::DateTime<chrono::Utc>,
    /// 是否成功
    pub success: bool,
    /// 错误信息
    pub error_message: Option<String>,
}

impl SignalHandler {
    /// 创建新的信号处理器
    pub fn new(target_pid: u32) -> Self {
        Self {
            target_pid,
            signal_history: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// 发送信号
    pub async fn send_signal(&self, signal_type: SignalType) -> ProcessManagerResult<()> {
        info!("向进程 {} 发送信号: {:?}", self.target_pid, signal_type);

        let result = self.send_signal_impl(&signal_type).await;
        
        // 记录信号发送历史
        let record = SignalRecord {
            signal_type: signal_type.clone(),
            sent_at: chrono::Utc::now(),
            success: result.is_ok(),
            error_message: result.as_ref().err().map(|e| e.to_string()),
        };

        {
            let mut history = self.signal_history.write().await;
            history.push(record);
            
            // 保持历史记录在合理范围内 (最多保留100条)
            if history.len() > 100 {
                history.remove(0);
            }
        }

        result
    }

    /// 优雅终止进程
    pub async fn graceful_terminate(&self) -> ProcessManagerResult<()> {
        info!("优雅终止进程: {}", self.target_pid);
        
        // 首先尝试发送 SIGTERM
        match self.send_signal(SignalType::Terminate).await {
            Ok(()) => {
                info!("SIGTERM 信号发送成功");
                Ok(())
            }
            Err(e) => {
                warn!("SIGTERM 信号发送失败: {}, 尝试其他方法", e);
                // 如果 SIGTERM 失败，可以尝试其他信号
                self.send_signal(SignalType::Interrupt).await
            }
        }
    }

    /// 强制终止进程
    pub async fn force_terminate(&self) -> ProcessManagerResult<()> {
        warn!("强制终止进程: {}", self.target_pid);
        
        self.send_signal(SignalType::Kill).await
    }

    /// 暂停进程
    pub async fn pause_process(&self) -> ProcessManagerResult<()> {
        info!("暂停进程: {}", self.target_pid);
        
        self.send_signal(SignalType::Stop).await
    }

    /// 恢复进程
    pub async fn resume_process(&self) -> ProcessManagerResult<()> {
        info!("恢复进程: {}", self.target_pid);
        
        self.send_signal(SignalType::Continue).await
    }

    /// 检查进程是否存在
    pub async fn is_process_alive(&self) -> bool {
        // 发送信号0来检查进程是否存在 (不会实际发送信号)
        self.check_process_existence().await.unwrap_or(false)
    }

    /// 获取信号发送历史
    pub async fn get_signal_history(&self) -> Vec<SignalRecord> {
        let history = self.signal_history.read().await;
        history.clone()
    }

    /// 获取信号统计信息
    pub async fn get_signal_statistics(&self) -> SignalStatistics {
        let history = self.signal_history.read().await;
        
        let total_signals = history.len();
        let successful_signals = history.iter().filter(|r| r.success).count();
        let failed_signals = total_signals - successful_signals;
        
        let success_rate = if total_signals > 0 {
            (successful_signals as f64 / total_signals as f64) * 100.0
        } else {
            0.0
        };

        let last_signal_time = history.last().map(|r| r.sent_at);

        SignalStatistics {
            total_signals,
            successful_signals,
            failed_signals,
            success_rate,
            last_signal_time,
        }
    }

    /// 实际发送信号的实现
    async fn send_signal_impl(&self, signal_type: &SignalType) -> ProcessManagerResult<()> {
        #[cfg(unix)]
        {
            self.send_unix_signal(signal_type).await
        }

        #[cfg(windows)]
        {
            self.send_windows_signal(signal_type).await
        }
    }

    /// Unix系统信号发送
    #[cfg(unix)]
    async fn send_unix_signal(&self, signal_type: &SignalType) -> ProcessManagerResult<()> {
        use std::process::Command;

        let signal_num = match signal_type {
            SignalType::Terminate => "TERM",
            SignalType::Kill => "KILL",
            SignalType::Interrupt => "INT",
            SignalType::Stop => "STOP",
            SignalType::Continue => "CONT",
            SignalType::User1 => "USR1",
            SignalType::User2 => "USR2",
        };

        let output = Command::new("kill")
            .args(&["-s", signal_num, &self.target_pid.to_string()])
            .output()
            .map_err(|e| ProcessManagerError::SignalSendFailed(format!("执行kill命令失败: {}", e)))?;

        if output.status.success() {
            debug!("Unix信号 {} 发送成功到进程 {}", signal_num, self.target_pid);
            Ok(())
        } else {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            Err(ProcessManagerError::SignalSendFailed(
                format!("信号发送失败: {}", error_msg)
            ))
        }
    }

    /// Windows系统信号发送
    #[cfg(windows)]
    async fn send_windows_signal(&self, signal_type: &SignalType) -> ProcessManagerResult<()> {
        use std::process::Command;

        match signal_type {
            SignalType::Terminate | SignalType::Kill => {
                // Windows使用taskkill命令
                let force_flag = if signal_type == &SignalType::Kill { "/F" } else { "" };
                
                let mut cmd = Command::new("taskkill");
                cmd.args(&["/PID", &self.target_pid.to_string()]);
                
                if !force_flag.is_empty() {
                    cmd.arg(force_flag);
                }

                let output = cmd.output()
                    .map_err(|e| ProcessManagerError::SignalSendFailed(format!("执行taskkill命令失败: {}", e)))?;

                if output.status.success() {
                    debug!("Windows终止信号发送成功到进程 {}", self.target_pid);
                    Ok(())
                } else {
                    let error_msg = String::from_utf8_lossy(&output.stderr);
                    Err(ProcessManagerError::SignalSendFailed(
                        format!("进程终止失败: {}", error_msg)
                    ))
                }
            }
            _ => {
                // Windows不支持其他类型的信号
                Err(ProcessManagerError::SignalSendFailed(
                    format!("Windows不支持信号类型: {:?}", signal_type)
                ))
            }
        }
    }

    /// 检查进程是否存在
    async fn check_process_existence(&self) -> ProcessManagerResult<bool> {
        #[cfg(unix)]
        {
            use std::process::Command;
            
            let output = Command::new("kill")
                .args(&["-0", &self.target_pid.to_string()])
                .output()
                .map_err(|e| ProcessManagerError::SystemError(format!("检查进程存在性失败: {}", e)))?;

            Ok(output.status.success())
        }

        #[cfg(windows)]
        {
            use std::process::Command;
            
            let output = Command::new("tasklist")
                .args(&["/FI", &format!("PID eq {}", self.target_pid)])
                .output()
                .map_err(|e| ProcessManagerError::SystemError(format!("检查进程存在性失败: {}", e)))?;

            if output.status.success() {
                let output_str = String::from_utf8_lossy(&output.stdout);
                Ok(output_str.contains(&self.target_pid.to_string()))
            } else {
                Ok(false)
            }
        }
    }
}

/// 信号统计信息
#[derive(Debug, Clone)]
pub struct SignalStatistics {
    /// 总信号数
    pub total_signals: usize,
    /// 成功信号数
    pub successful_signals: usize,
    /// 失败信号数
    pub failed_signals: usize,
    /// 成功率 (%)
    pub success_rate: f64,
    /// 最后信号时间
    pub last_signal_time: Option<chrono::DateTime<chrono::Utc>>,
}

impl Drop for SignalHandler {
    fn drop(&mut self) {
        info!("信号处理器正在销毁，目标进程: {}", self.target_pid);
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_signal_handler_creation() {
        let handler = SignalHandler::new(1234);
        assert_eq!(handler.target_pid, 1234);
        
        let history = handler.get_signal_history().await;
        assert!(history.is_empty());
    }

    #[tokio::test]
    async fn test_signal_statistics() {
        let handler = SignalHandler::new(1234);
        
        let stats = handler.get_signal_statistics().await;
        assert_eq!(stats.total_signals, 0);
        assert_eq!(stats.successful_signals, 0);
        assert_eq!(stats.failed_signals, 0);
        assert_eq!(stats.success_rate, 0.0);
        assert!(stats.last_signal_time.is_none());
    }

    #[tokio::test]
    async fn test_process_existence_check() {
        let handler = SignalHandler::new(1); // PID 1通常是init进程
        
        // 注意：这个测试可能在某些系统上失败，取决于权限和系统配置
        let _exists = handler.is_process_alive().await;
        // 我们不断言结果，因为这取决于系统状态
    }

    #[test]
    fn test_signal_type_equality() {
        assert_eq!(SignalType::Terminate, SignalType::Terminate);
        assert_ne!(SignalType::Terminate, SignalType::Kill);
    }
} 