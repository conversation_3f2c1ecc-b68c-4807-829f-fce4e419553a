//! 应用版本管理器
//! 
//! 负责应用版本检测、更新检查和版本管理

use std::path::PathBuf;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::RwLock;
use tracing::{info, warn, error, debug};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

use super::{AppManagerError, AppManagerResult, AppConfig};

/// 版本信息
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct VersionInfo {
    /// 版本号
    pub version: String,
    /// 构建时间
    pub build_time: Option<DateTime<Utc>>,
    /// Git 提交哈希
    pub commit_hash: Option<String>,
    /// 构建类型 (release, debug)
    pub build_type: String,
    /// 平台信息
    pub platform: String,
    /// 架构信息
    pub architecture: String,
}

/// 更新信息
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UpdateInfo {
    /// 可用版本
    pub available_version: String,
    /// 当前版本
    pub current_version: String,
    /// 是否有更新
    pub has_update: bool,
    /// 更新类型
    pub update_type: UpdateType,
    /// 更新说明
    pub release_notes: Option<String>,
    /// 下载链接
    pub download_url: Option<String>,
    /// 文件大小 (字节)
    pub file_size: Option<u64>,
    /// 检查时间
    pub checked_at: DateTime<Utc>,
}

/// 更新类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum UpdateType {
    /// 主版本更新
    Major,
    /// 次版本更新
    Minor,
    /// 补丁更新
    Patch,
    /// 预发布版本
    PreRelease,
    /// 开发版本
    Development,
}

/// 版本历史记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VersionHistory {
    /// 版本信息
    pub version: String,
    /// 安装时间
    pub installed_at: DateTime<Utc>,
    /// 安装路径
    pub install_path: PathBuf,
    /// 是否为当前版本
    pub is_current: bool,
    /// 备份路径
    pub backup_path: Option<PathBuf>,
}

/// 更新统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateStatistics {
    /// 总更新次数
    pub total_updates: usize,
    /// 成功更新次数
    pub successful_updates: usize,
    /// 失败更新次数
    pub failed_updates: usize,
    /// 成功率 (%)
    pub success_rate: f64,
    /// 最后更新时间
    pub last_update_time: Option<DateTime<Utc>>,
    /// 平均更新时间
    pub average_update_time: Duration,
}

/// 版本管理器
pub struct VersionManager {
    /// 应用配置
    config: AppConfig,
    /// 当前版本信息
    current_version: Arc<RwLock<Option<VersionInfo>>>,
    /// 版本历史
    version_history: Arc<RwLock<Vec<VersionHistory>>>,
    /// 更新统计
    update_statistics: Arc<RwLock<UpdateStatistics>>,
    /// 更新服务器URL
    update_server_url: String,
}

impl VersionManager {
    /// 创建新的版本管理器
    pub fn new(config: AppConfig) -> Self {
        Self {
            config,
            current_version: Arc::new(RwLock::new(None)),
            version_history: Arc::new(RwLock::new(Vec::new())),
            update_statistics: Arc::new(RwLock::new(UpdateStatistics {
                total_updates: 0,
                successful_updates: 0,
                failed_updates: 0,
                success_rate: 0.0,
                last_update_time: None,
                average_update_time: Duration::ZERO,
            })),
            update_server_url: "https://api.github.com/repos/example/secure-password/releases".to_string(),
        }
    }

    /// 检测当前版本
    pub async fn detect_current_version(&self) -> AppManagerResult<VersionInfo> {
        info!("检测当前应用版本");

        // 尝试从多个来源获取版本信息
        let version_info = if let Ok(info) = self.get_version_from_executable().await {
            info
        } else if let Ok(info) = self.get_version_from_manifest().await {
            info
        } else if let Ok(info) = self.get_version_from_env().await {
            info
        } else {
            self.get_default_version_info()
        };

        // 更新当前版本缓存
        {
            let mut current = self.current_version.write().await;
            *current = Some(version_info.clone());
        }

        info!("当前版本: {}", version_info.version);
        Ok(version_info)
    }

    /// 检查更新
    pub async fn check_for_updates(&self) -> AppManagerResult<UpdateInfo> {
        info!("检查应用更新");

        let current_version = self.get_current_version().await?;
        let latest_version = self.fetch_latest_version().await?;

        let has_update = self.compare_versions(&current_version.version, &latest_version.version)?;
        let update_type = if has_update {
            self.determine_update_type(&current_version.version, &latest_version.version)?
        } else {
            UpdateType::Patch
        };

        let update_info = UpdateInfo {
            available_version: latest_version.version.clone(),
            current_version: current_version.version.clone(),
            has_update,
            update_type,
            release_notes: latest_version.release_notes,
            download_url: latest_version.download_url,
            file_size: latest_version.file_size,
            checked_at: chrono::Utc::now(),
        };

        if has_update {
            info!("发现新版本: {} -> {}", current_version.version, latest_version.version);
        } else {
            info!("当前版本已是最新版本: {}", current_version.version);
        }

        Ok(update_info)
    }

    /// 执行更新
    pub async fn perform_update(&self, update_info: &UpdateInfo) -> AppManagerResult<()> {
        info!("开始执行更新: {} -> {}", update_info.current_version, update_info.available_version);

        let start_time = chrono::Utc::now();
        let mut success = false;

        // 执行更新流程
        let result = async {
            // 1. 下载更新文件
            let download_path = self.download_update_file(update_info).await?;
            
            // 2. 验证下载文件
            self.verify_update_file(&download_path).await?;
            
            // 3. 备份当前版本
            let backup_path = self.backup_current_version().await?;
            
            // 4. 应用更新
            self.apply_update(&download_path).await?;
            
            // 5. 验证更新
            self.verify_update_success(&update_info.available_version).await?;
            
            // 6. 清理临时文件
            self.cleanup_update_files(&download_path).await?;
            
            // 7. 更新版本历史
            self.update_version_history(&update_info.available_version, backup_path).await?;
            
            Ok(())
        }.await;

        success = result.is_ok();
        let duration = chrono::Utc::now() - start_time;

        // 更新统计信息
        self.update_statistics(success, duration).await;

        match result {
            Ok(()) => {
                info!("更新成功完成: {}", update_info.available_version);
                Ok(())
            }
            Err(e) => {
                error!("更新失败: {}", e);
                // 尝试回滚
                if let Err(rollback_error) = self.rollback_update().await {
                    error!("回滚失败: {}", rollback_error);
                }
                Err(e)
            }
        }
    }

    /// 回滚到上一版本
    pub async fn rollback_to_previous_version(&self) -> AppManagerResult<()> {
        info!("回滚到上一版本");

        let previous_version = {
            let history = self.version_history.read().await;
            history.iter()
                .filter(|v| !v.is_current)
                .max_by_key(|v| v.installed_at)
                .cloned()
                .ok_or_else(|| AppManagerError::VersionManagementFailed("没有可回滚的版本".to_string()))?
        };

        // 执行回滚
        self.restore_version(&previous_version).await?;

        info!("回滚成功: {}", previous_version.version);
        Ok(())
    }

    /// 获取版本历史
    pub async fn get_version_history(&self) -> Vec<VersionHistory> {
        let history = self.version_history.read().await;
        history.clone()
    }

    /// 获取更新统计信息
    pub async fn get_update_statistics(&self) -> UpdateStatistics {
        let stats = self.update_statistics.read().await;
        stats.clone()
    }

    /// 清理旧版本
    pub async fn cleanup_old_versions(&self, keep_count: usize) -> AppManagerResult<()> {
        info!("清理旧版本，保留最新 {} 个版本", keep_count);

        let mut history = self.version_history.write().await;
        
        // 按安装时间排序
        history.sort_by(|a, b| b.installed_at.cmp(&a.installed_at));
        
        // 保留当前版本和最新的几个版本
        let mut to_remove = Vec::new();
        for (index, version) in history.iter().enumerate() {
            if !version.is_current && index >= keep_count {
                to_remove.push(version.clone());
            }
        }

        // 删除旧版本文件
        for version in &to_remove {
            if let Some(backup_path) = &version.backup_path {
                if backup_path.exists() {
                    if let Err(e) = tokio::fs::remove_dir_all(backup_path).await {
                        warn!("删除版本备份失败: {} - {}", version.version, e);
                    } else {
                        info!("已删除版本备份: {}", version.version);
                    }
                }
            }
        }

        // 从历史记录中移除
        history.retain(|v| !to_remove.iter().any(|r| r.version == v.version));

        info!("清理完成，删除了 {} 个旧版本", to_remove.len());
        Ok(())
    }

    /// 获取当前版本
    async fn get_current_version(&self) -> AppManagerResult<VersionInfo> {
        let current = self.current_version.read().await;
        current.clone().ok_or_else(|| 
            AppManagerError::VersionManagementFailed("未检测到当前版本".to_string())
        )
    }

    /// 从可执行文件获取版本信息
    async fn get_version_from_executable(&self) -> AppManagerResult<VersionInfo> {
        // 尝试执行应用程序获取版本信息
        let output = tokio::process::Command::new(&self.config.app_path)
            .arg("--version")
            .output()
            .await
            .map_err(|e| AppManagerError::VersionManagementFailed(format!("执行版本命令失败: {}", e)))?;

        if output.status.success() {
            let version_output = String::from_utf8_lossy(&output.stdout);
            let version = self.parse_version_from_output(&version_output)?;
            
            Ok(VersionInfo {
                version,
                build_time: None,
                commit_hash: None,
                build_type: "unknown".to_string(),
                platform: std::env::consts::OS.to_string(),
                architecture: std::env::consts::ARCH.to_string(),
            })
        } else {
            Err(AppManagerError::VersionManagementFailed("版本命令执行失败".to_string()))
        }
    }

    /// 从清单文件获取版本信息
    async fn get_version_from_manifest(&self) -> AppManagerResult<VersionInfo> {
        let manifest_path = self.config.app_path.with_file_name("manifest.json");
        
        if !manifest_path.exists() {
            return Err(AppManagerError::VersionManagementFailed("清单文件不存在".to_string()));
        }

        let manifest_content = tokio::fs::read_to_string(&manifest_path).await
            .map_err(|e| AppManagerError::VersionManagementFailed(format!("读取清单文件失败: {}", e)))?;

        let manifest: serde_json::Value = serde_json::from_str(&manifest_content)
            .map_err(|e| AppManagerError::VersionManagementFailed(format!("解析清单文件失败: {}", e)))?;

        let version = manifest["version"].as_str()
            .ok_or_else(|| AppManagerError::VersionManagementFailed("清单文件中缺少版本信息".to_string()))?
            .to_string();

        Ok(VersionInfo {
            version,
            build_time: manifest["build_time"].as_str().and_then(|s| chrono::DateTime::parse_from_rfc3339(s).ok().map(|dt| dt.with_timezone(&chrono::Utc))),
            commit_hash: manifest["commit_hash"].as_str().map(|s| s.to_string()),
            build_type: manifest["build_type"].as_str().unwrap_or("unknown").to_string(),
            platform: std::env::consts::OS.to_string(),
            architecture: std::env::consts::ARCH.to_string(),
        })
    }

    /// 从环境变量获取版本信息
    async fn get_version_from_env(&self) -> AppManagerResult<VersionInfo> {
        let version = std::env::var("APP_VERSION")
            .map_err(|_| AppManagerError::VersionManagementFailed("环境变量中未找到版本信息".to_string()))?;

        Ok(VersionInfo {
            version,
            build_time: None,
            commit_hash: std::env::var("APP_COMMIT_HASH").ok(),
            build_type: std::env::var("APP_BUILD_TYPE").unwrap_or_else(|_| "unknown".to_string()),
            platform: std::env::consts::OS.to_string(),
            architecture: std::env::consts::ARCH.to_string(),
        })
    }

    /// 获取默认版本信息
    fn get_default_version_info(&self) -> VersionInfo {
        VersionInfo {
            version: "0.1.0".to_string(),
            build_time: None,
            commit_hash: None,
            build_type: "unknown".to_string(),
            platform: std::env::consts::OS.to_string(),
            architecture: std::env::consts::ARCH.to_string(),
        }
    }

    /// 从输出中解析版本号
    fn parse_version_from_output(&self, output: &str) -> AppManagerResult<String> {
        // 尝试匹配常见的版本格式
        let patterns = [
            r"(\d+\.\d+\.\d+)",          // 1.2.3
            r"v(\d+\.\d+\.\d+)",         // v1.2.3
            r"version (\d+\.\d+\.\d+)",  // version 1.2.3
        ];

        for pattern in &patterns {
            if let Ok(re) = regex::Regex::new(pattern) {
                if let Some(captures) = re.captures(output) {
                    if let Some(version) = captures.get(1) {
                        return Ok(version.as_str().to_string());
                    }
                }
            }
        }

        Err(AppManagerError::VersionManagementFailed("无法解析版本信息".to_string()))
    }

    /// 获取最新版本信息
    async fn fetch_latest_version(&self) -> AppManagerResult<LatestVersionInfo> {
        // 这里应该实现从更新服务器获取最新版本信息的逻辑
        // 目前返回模拟数据
        Ok(LatestVersionInfo {
            version: "1.0.1".to_string(),
            release_notes: Some("修复了一些bug".to_string()),
            download_url: Some("https://example.com/download".to_string()),
            file_size: Some(1024 * 1024 * 10), // 10MB
        })
    }

    /// 比较版本号
    fn compare_versions(&self, current: &str, latest: &str) -> AppManagerResult<bool> {
        // 简单的版本比较实现
        let current_parts: Vec<u32> = current.split('.').map(|s| s.parse().unwrap_or(0)).collect();
        let latest_parts: Vec<u32> = latest.split('.').map(|s| s.parse().unwrap_or(0)).collect();

        for i in 0..std::cmp::max(current_parts.len(), latest_parts.len()) {
            let current_part = current_parts.get(i).unwrap_or(&0);
            let latest_part = latest_parts.get(i).unwrap_or(&0);

            if latest_part > current_part {
                return Ok(true);
            } else if latest_part < current_part {
                return Ok(false);
            }
        }

        Ok(false)
    }

    /// 确定更新类型
    fn determine_update_type(&self, current: &str, latest: &str) -> AppManagerResult<UpdateType> {
        let current_parts: Vec<u32> = current.split('.').map(|s| s.parse().unwrap_or(0)).collect();
        let latest_parts: Vec<u32> = latest.split('.').map(|s| s.parse().unwrap_or(0)).collect();

        if current_parts.len() >= 3 && latest_parts.len() >= 3 {
            if latest_parts[0] > current_parts[0] {
                Ok(UpdateType::Major)
            } else if latest_parts[1] > current_parts[1] {
                Ok(UpdateType::Minor)
            } else {
                Ok(UpdateType::Patch)
            }
        } else {
            Ok(UpdateType::Patch)
        }
    }

    /// 下载更新文件
    async fn download_update_file(&self, _update_info: &UpdateInfo) -> AppManagerResult<PathBuf> {
        // 模拟下载过程
        let download_path = std::env::temp_dir().join("secure_password_update.tmp");
        
        // 模拟下载延迟
        tokio::time::sleep(Duration::from_millis(100)).await;
        
        // 创建模拟文件
        tokio::fs::write(&download_path, b"mock update file").await
            .map_err(|e| AppManagerError::VersionManagementFailed(format!("创建更新文件失败: {}", e)))?;

        Ok(download_path)
    }

    /// 验证更新文件
    async fn verify_update_file(&self, _file_path: &PathBuf) -> AppManagerResult<()> {
        // 模拟验证过程
        tokio::time::sleep(Duration::from_millis(50)).await;
        Ok(())
    }

    /// 备份当前版本
    async fn backup_current_version(&self) -> AppManagerResult<PathBuf> {
        let backup_dir = std::env::temp_dir().join("secure_password_backup");
        tokio::fs::create_dir_all(&backup_dir).await
            .map_err(|e| AppManagerError::VersionManagementFailed(format!("创建备份目录失败: {}", e)))?;

        // 模拟备份过程
        tokio::time::sleep(Duration::from_millis(100)).await;

        Ok(backup_dir)
    }

    /// 应用更新
    async fn apply_update(&self, _update_file: &PathBuf) -> AppManagerResult<()> {
        // 模拟应用更新过程
        tokio::time::sleep(Duration::from_millis(200)).await;
        Ok(())
    }

    /// 验证更新成功
    async fn verify_update_success(&self, _expected_version: &str) -> AppManagerResult<()> {
        // 模拟验证过程
        tokio::time::sleep(Duration::from_millis(50)).await;
        Ok(())
    }

    /// 清理更新文件
    async fn cleanup_update_files(&self, update_file: &PathBuf) -> AppManagerResult<()> {
        if update_file.exists() {
            tokio::fs::remove_file(update_file).await
                .map_err(|e| AppManagerError::VersionManagementFailed(format!("清理更新文件失败: {}", e)))?;
        }
        Ok(())
    }

    /// 更新版本历史
    async fn update_version_history(&self, version: &str, backup_path: PathBuf) -> AppManagerResult<()> {
        let mut history = self.version_history.write().await;
        
        // 将之前的版本标记为非当前版本
        for v in history.iter_mut() {
            v.is_current = false;
        }

        // 添加新版本记录
        history.push(VersionHistory {
            version: version.to_string(),
            installed_at: chrono::Utc::now(),
            install_path: self.config.app_path.clone(),
            is_current: true,
            backup_path: Some(backup_path),
        });

        Ok(())
    }

    /// 回滚更新
    async fn rollback_update(&self) -> AppManagerResult<()> {
        // 模拟回滚过程
        tokio::time::sleep(Duration::from_millis(100)).await;
        Ok(())
    }

    /// 恢复版本
    async fn restore_version(&self, _version: &VersionHistory) -> AppManagerResult<()> {
        // 模拟恢复过程
        tokio::time::sleep(Duration::from_millis(150)).await;
        Ok(())
    }

    /// 更新统计信息
    async fn update_statistics(&self, success: bool, duration: chrono::Duration) {
        let mut stats = self.update_statistics.write().await;
        
        stats.total_updates += 1;
        if success {
            stats.successful_updates += 1;
        } else {
            stats.failed_updates += 1;
        }
        
        stats.success_rate = if stats.total_updates > 0 {
            (stats.successful_updates as f64 / stats.total_updates as f64) * 100.0
        } else {
            0.0
        };
        
        stats.last_update_time = Some(chrono::Utc::now());
        
        // 更新平均时间
        let duration_std = duration.to_std().unwrap_or(Duration::ZERO);
        stats.average_update_time = if stats.total_updates > 1 {
            (stats.average_update_time * (stats.total_updates - 1) as u32 + duration_std) / stats.total_updates as u32
        } else {
            duration_std
        };
    }
}

/// 最新版本信息
#[derive(Debug, Clone)]
struct LatestVersionInfo {
    version: String,
    release_notes: Option<String>,
    download_url: Option<String>,
    file_size: Option<u64>,
}

impl Drop for VersionManager {
    fn drop(&mut self) {
        info!("版本管理器正在销毁");
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    fn create_test_config() -> AppConfig {
        AppConfig {
            app_path: PathBuf::from("test_app"),
            working_directory: None,
            startup_args: vec![],
            startup_timeout: 30,
            health_check_interval: 10,
            max_restart_attempts: 3,
            restart_delay: 5,
            ipc_port: 8080,
            silent_mode: true,
            memory_limit_mb: Some(100),
            cpu_limit_percent: Some(50.0),
        }
    }

    #[tokio::test]
    async fn test_version_manager_creation() {
        let config = create_test_config();
        let manager = VersionManager::new(config);
        
        let history = manager.get_version_history().await;
        assert!(history.is_empty());
        
        let stats = manager.get_update_statistics().await;
        assert_eq!(stats.total_updates, 0);
    }

    #[tokio::test]
    async fn test_version_comparison() {
        let config = create_test_config();
        let manager = VersionManager::new(config);
        
        // 测试版本比较
        assert!(manager.compare_versions("1.0.0", "1.0.1").unwrap());
        assert!(manager.compare_versions("1.0.0", "1.1.0").unwrap());
        assert!(manager.compare_versions("1.0.0", "2.0.0").unwrap());
        assert!(!manager.compare_versions("1.0.1", "1.0.0").unwrap());
        assert!(!manager.compare_versions("1.0.0", "1.0.0").unwrap());
    }

    #[tokio::test]
    async fn test_update_type_determination() {
        let config = create_test_config();
        let manager = VersionManager::new(config);
        
        assert_eq!(manager.determine_update_type("1.0.0", "2.0.0").unwrap(), UpdateType::Major);
        assert_eq!(manager.determine_update_type("1.0.0", "1.1.0").unwrap(), UpdateType::Minor);
        assert_eq!(manager.determine_update_type("1.0.0", "1.0.1").unwrap(), UpdateType::Patch);
    }

    #[tokio::test]
    async fn test_version_info_creation() {
        let version_info = VersionInfo {
            version: "1.0.0".to_string(),
            build_time: None,
            commit_hash: None,
            build_type: "release".to_string(),
            platform: "linux".to_string(),
            architecture: "x86_64".to_string(),
        };
        
        assert_eq!(version_info.version, "1.0.0");
        assert_eq!(version_info.build_type, "release");
        assert_eq!(version_info.platform, "linux");
    }

    #[tokio::test]
    async fn test_update_info_creation() {
        let update_info = UpdateInfo {
            available_version: "1.0.1".to_string(),
            current_version: "1.0.0".to_string(),
            has_update: true,
            update_type: UpdateType::Patch,
            release_notes: Some("Bug fixes".to_string()),
            download_url: Some("https://example.com/download".to_string()),
            file_size: Some(1024),
            checked_at: chrono::Utc::now(),
        };
        
        assert!(update_info.has_update);
        assert_eq!(update_info.update_type, UpdateType::Patch);
        assert_eq!(update_info.current_version, "1.0.0");
        assert_eq!(update_info.available_version, "1.0.1");
    }

    #[test]
    fn test_version_parsing() {
        let config = create_test_config();
        let manager = VersionManager::new(config);
        
        // 测试版本解析
        assert_eq!(manager.parse_version_from_output("1.2.3").unwrap(), "1.2.3");
        assert_eq!(manager.parse_version_from_output("v1.2.3").unwrap(), "1.2.3");
        assert_eq!(manager.parse_version_from_output("version 1.2.3").unwrap(), "1.2.3");
        assert_eq!(manager.parse_version_from_output("MyApp version 1.2.3").unwrap(), "1.2.3");
    }
} 