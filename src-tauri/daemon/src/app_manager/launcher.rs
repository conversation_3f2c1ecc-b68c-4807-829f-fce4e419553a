//! 应用启动器
//! 
//! 负责 Tauri 应用的启动逻辑，支持静默模式和参数配置

use std::path::Path;
use std::process::Stdio;
use std::time::{Duration, Instant};
use tokio::process::{Child, Command};
use tokio::time::timeout;
use tracing::{info, warn, error, debug};
use chrono::Utc;

use super::{AppConfig, AppStatus, AppInfo, AppManagerError, AppManagerResult};

/// 应用启动器
pub struct AppLauncher {
    /// 应用配置
    config: AppConfig,
    /// 当前进程
    current_process: Option<Child>,
    /// 启动时间
    start_time: Option<Instant>,
    /// 重启计数
    restart_count: u32,
}

impl AppLauncher {
    /// 创建新的应用启动器
    pub fn new(config: AppConfig) -> Self {
        Self {
            config,
            current_process: None,
            start_time: None,
            restart_count: 0,
        }
    }

    /// 启动 Tauri 应用 (静默模式)
    pub async fn launch_app_silent(&mut self) -> AppManagerResult<u32> {
        info!("启动 Tauri 应用 (静默模式)");

        // 1. 检查是否已有实例运行
        if self.is_app_running().await? {
            info!("应用已在运行，跳过启动");
            return Ok(self.current_process.as_ref().unwrap().id().unwrap_or(0));
        }

        // 2. 准备启动参数 (静默模式)
        let launch_args = self.prepare_silent_launch_args().await?;

        // 3. 启动应用进程
        let child = self.spawn_app_process(&launch_args).await?;
        let process_id = child.id().unwrap_or(0);

        self.current_process = Some(child);
        self.start_time = Some(Instant::now());

        // 4. 等待应用启动完成
        self.wait_for_app_ready().await?;

        info!("应用已成功启动 (静默模式)，PID: {}", process_id);
        Ok(process_id)
    }

    /// 启动 Tauri 应用 (交互模式)
    pub async fn launch_app_interactive(&mut self) -> AppManagerResult<u32> {
        info!("启动 Tauri 应用 (交互模式)");

        // 1. 检查是否已有实例运行
        if self.is_app_running().await? {
            warn!("应用已在运行");
            return Err(AppManagerError::StartupFailed("应用已在运行".to_string()));
        }

        // 2. 准备启动参数 (交互模式)
        let launch_args = self.prepare_interactive_launch_args().await?;

        // 3. 启动应用进程
        let child = self.spawn_app_process(&launch_args).await?;
        let process_id = child.id().unwrap_or(0);

        self.current_process = Some(child);
        self.start_time = Some(Instant::now());

        // 4. 等待应用启动完成
        self.wait_for_app_ready().await?;

        info!("应用已成功启动 (交互模式)，PID: {}", process_id);
        Ok(process_id)
    }

    /// 检查应用是否运行中
    pub async fn is_app_running(&mut self) -> AppManagerResult<bool> {
        if let Some(process) = &mut self.current_process {
            match process.try_wait() {
                Ok(Some(status)) => {
                    info!("应用进程已退出，状态: {}", status);
                    self.current_process = None;
                    self.start_time = None;
                    Ok(false)
                }
                Ok(None) => Ok(true), // 进程仍在运行
                Err(e) => {
                    error!("检查进程状态失败: {}", e);
                    Ok(false)
                }
            }
        } else {
            Ok(false)
        }
    }

    /// 优雅关闭应用
    pub async fn shutdown_app_graceful(&mut self) -> AppManagerResult<()> {
        if let Some(mut process) = self.current_process.take() {
            info!("开始优雅关闭应用");

            // 1. 发送优雅关闭信号
            self.send_shutdown_signal(&mut process).await?;

            // 2. 等待进程退出 (最多等待30秒)
            let shutdown_timeout = Duration::from_secs(30);
            if !self.wait_for_exit(&mut process, shutdown_timeout).await? {
                // 3. 超时则强制终止
                warn!("应用未在规定时间内退出，强制终止");
                if let Err(e) = process.kill().await {
                    error!("强制终止进程失败: {}", e);
                }
            }

            self.start_time = None;
            info!("应用已成功关闭");
        }
        Ok(())
    }

    /// 强制终止应用
    pub async fn force_kill_app(&mut self) -> AppManagerResult<()> {
        if let Some(mut process) = self.current_process.take() {
            info!("强制终止应用");

            if let Err(e) = process.kill().await {
                error!("强制终止进程失败: {}", e);
                return Err(AppManagerError::ShutdownFailed(format!("强制终止失败: {}", e)));
            }

            // 等待进程退出确认
            let _ = timeout(Duration::from_secs(5), process.wait()).await;

            self.start_time = None;
            info!("应用已被强制终止");
        }
        Ok(())
    }

    /// 获取应用信息
    pub async fn get_app_info(&mut self) -> AppManagerResult<AppInfo> {
        let status = if self.is_app_running().await? {
            AppStatus::Running
        } else {
            AppStatus::Stopped
        };

        let (process_id, uptime) = if let Some(process) = &self.current_process {
            let pid = process.id();
            let uptime = self.start_time.map(|start| start.elapsed());
            (pid, uptime)
        } else {
            (None, None)
        };

        Ok(AppInfo {
            status,
            process_id,
            start_time: self.start_time.map(|_| Utc::now()),
            uptime,
            version: None, // TODO: 从应用获取版本信息
            memory_usage_mb: None, // TODO: 从进程监控获取
            cpu_usage_percent: None, // TODO: 从进程监控获取
            restart_count: self.restart_count,
            last_error: None,
        })
    }

    /// 增加重启计数
    pub fn increment_restart_count(&mut self) {
        self.restart_count += 1;
    }

    /// 重置重启计数
    pub fn reset_restart_count(&mut self) {
        self.restart_count = 0;
    }

    /// 获取重启计数
    pub fn get_restart_count(&self) -> u32 {
        self.restart_count
    }

    /// 准备静默启动参数
    async fn prepare_silent_launch_args(&self) -> AppManagerResult<Vec<String>> {
        let mut args = self.config.startup_args.clone();

        // 添加静默模式参数
        args.extend_from_slice(&[
            "--headless".to_string(),        // 无界面模式
            "--no-window".to_string(),       // 不显示窗口
            "--daemon-mode".to_string(),     // 守护进程模式
            "--ipc-port".to_string(),        // IPC 端口
            self.config.ipc_port.to_string(),
        ]);

        // 添加平台特定参数
        #[cfg(target_os = "macos")]
        args.push("--background".to_string());

        #[cfg(target_os = "linux")]
        args.push("--no-sandbox".to_string());

        #[cfg(windows)]
        args.push("--disable-gpu".to_string());

        debug!("静默启动参数: {:?}", args);
        Ok(args)
    }

    /// 准备交互启动参数
    async fn prepare_interactive_launch_args(&self) -> AppManagerResult<Vec<String>> {
        let mut args = self.config.startup_args.clone();

        // 添加交互模式参数
        args.extend_from_slice(&[
            "--interactive".to_string(),     // 交互模式
            "--show-window".to_string(),     // 显示窗口
            "--ipc-port".to_string(),        // IPC 端口
            self.config.ipc_port.to_string(),
        ]);

        debug!("交互启动参数: {:?}", args);
        Ok(args)
    }

    /// 启动应用进程
    async fn spawn_app_process(&self, args: &[String]) -> AppManagerResult<Child> {
        let mut cmd = Command::new(&self.config.app_path);
        cmd.args(args);

        // 设置工作目录
        if let Some(working_dir) = &self.config.working_directory {
            cmd.current_dir(working_dir);
        }

        // 静默模式下重定向输出
        if self.config.silent_mode {
            cmd.stdout(Stdio::null())
               .stderr(Stdio::piped())
               .stdin(Stdio::null());
        } else {
            cmd.stdout(Stdio::piped())
               .stderr(Stdio::piped())
               .stdin(Stdio::null());
        }

        // 设置环境变量
        cmd.env("RUST_LOG", "info");
        cmd.env("DAEMON_MANAGED", "true");

        let child = cmd.spawn()
            .map_err(|e| AppManagerError::StartupFailed(format!("进程启动失败: {}", e)))?;

        debug!("应用进程已启动，PID: {:?}", child.id());
        Ok(child)
    }

    /// 等待应用准备就绪
    async fn wait_for_app_ready(&self) -> AppManagerResult<()> {
        let max_wait_time = Duration::from_secs(self.config.startup_timeout);
        let check_interval = Duration::from_millis(500);
        let start_time = Instant::now();

        info!("等待应用准备就绪，最大等待时间: {} 秒", self.config.startup_timeout);

        while start_time.elapsed() < max_wait_time {
            // 通过 IPC 检查应用是否就绪
            if self.check_app_ready_via_ipc().await? {
                info!("应用已准备就绪");
                return Ok(());
            }

            tokio::time::sleep(check_interval).await;
        }

        error!("应用启动超时");
        Err(AppManagerError::StartupTimeout)
    }

    /// 通过 IPC 检查应用是否就绪
    async fn check_app_ready_via_ipc(&self) -> AppManagerResult<bool> {
        // TODO: 实现 IPC 连接检查
        // 这里需要与 IPC 模块集成
        
        // 临时实现：检查端口是否监听
        match tokio::net::TcpStream::connect(format!("127.0.0.1:{}", self.config.ipc_port)).await {
            Ok(_) => {
                debug!("IPC 端口 {} 已就绪", self.config.ipc_port);
                Ok(true)
            }
            Err(_) => {
                debug!("IPC 端口 {} 尚未就绪", self.config.ipc_port);
                Ok(false)
            }
        }
    }

    /// 发送优雅关闭信号
    async fn send_shutdown_signal(&self, process: &mut Child) -> AppManagerResult<()> {
        // TODO: 通过 IPC 发送优雅关闭信号
        // 这里需要与 IPC 模块集成

        #[cfg(unix)]
        {
            // Unix 系统发送 SIGTERM 信号
            if let Some(pid) = process.id() {
                unsafe {
                    libc::kill(pid as i32, libc::SIGTERM);
                }
                debug!("已发送 SIGTERM 信号到进程 {}", pid);
            }
        }

        #[cfg(windows)]
        {
            // Windows 系统尝试优雅关闭
            // 这里可以通过 IPC 发送关闭消息
            debug!("Windows 系统暂时使用强制终止");
        }

        Ok(())
    }

    /// 等待进程退出
    async fn wait_for_exit(&self, process: &mut Child, timeout_duration: Duration) -> AppManagerResult<bool> {
        match timeout(timeout_duration, process.wait()).await {
            Ok(Ok(status)) => {
                info!("进程已退出，状态: {}", status);
                Ok(true)
            }
            Ok(Err(e)) => {
                error!("等待进程退出失败: {}", e);
                Err(AppManagerError::ShutdownFailed(format!("等待进程退出失败: {}", e)))
            }
            Err(_) => {
                warn!("等待进程退出超时");
                Ok(false)
            }
        }
    }

    /// 验证应用路径
    pub fn validate_app_path(&self) -> AppManagerResult<()> {
        if !self.config.app_path.exists() {
            return Err(AppManagerError::ConfigError(
                format!("应用路径不存在: {:?}", self.config.app_path)
            ));
        }

        if !self.config.app_path.is_file() {
            return Err(AppManagerError::ConfigError(
                format!("应用路径不是文件: {:?}", self.config.app_path)
            ));
        }

        // 检查可执行权限
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;
            let metadata = std::fs::metadata(&self.config.app_path)?;
            let permissions = metadata.permissions();
            if permissions.mode() & 0o111 == 0 {
                return Err(AppManagerError::ConfigError(
                    format!("应用文件没有执行权限: {:?}", self.config.app_path)
                ));
            }
        }

        Ok(())
    }
}

impl Drop for AppLauncher {
    fn drop(&mut self) {
        if self.current_process.is_some() {
            warn!("AppLauncher 被销毁时仍有运行中的进程，尝试清理");
            // 注意：这里不能使用 async，所以只能尝试同步终止
            if let Some(mut process) = self.current_process.take() {
                let _ = process.start_kill();
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;
    use tempfile::tempdir;

    fn create_test_config() -> AppConfig {
        AppConfig {
            app_path: PathBuf::from("echo"),  // 使用 echo 命令作为测试
            working_directory: None,
            startup_args: vec!["test".to_string()],
            startup_timeout: 5,
            health_check_interval: 10,
            max_restart_attempts: 2,
            restart_delay: 1,
            ipc_port: 18080,
            silent_mode: true,
            memory_limit_mb: Some(100),
            cpu_limit_percent: Some(50.0),
        }
    }

    #[tokio::test]
    async fn test_app_launcher_creation() {
        let config = create_test_config();
        let launcher = AppLauncher::new(config.clone());
        
        assert_eq!(launcher.config.app_path, config.app_path);
        assert_eq!(launcher.restart_count, 0);
        assert!(launcher.current_process.is_none());
    }

    #[tokio::test]
    async fn test_validate_app_path() {
        let mut config = create_test_config();
        config.app_path = PathBuf::from("/bin/echo");  // Unix 系统的 echo
        
        #[cfg(windows)]
        {
            config.app_path = PathBuf::from("C:\\Windows\\System32\\cmd.exe");
        }
        
        let launcher = AppLauncher::new(config);
        assert!(launcher.validate_app_path().is_ok());
    }

    #[tokio::test]
    async fn test_validate_invalid_app_path() {
        let mut config = create_test_config();
        config.app_path = PathBuf::from("/nonexistent/path");
        
        let launcher = AppLauncher::new(config);
        assert!(launcher.validate_app_path().is_err());
    }

    #[tokio::test]
    async fn test_prepare_silent_launch_args() {
        let config = create_test_config();
        let launcher = AppLauncher::new(config);
        
        let args = launcher.prepare_silent_launch_args().await.unwrap();
        
        assert!(args.contains(&"--headless".to_string()));
        assert!(args.contains(&"--no-window".to_string()));
        assert!(args.contains(&"--daemon-mode".to_string()));
        assert!(args.contains(&"--ipc-port".to_string()));
        assert!(args.contains(&"18080".to_string()));
    }

    #[tokio::test]
    async fn test_restart_count_management() {
        let config = create_test_config();
        let mut launcher = AppLauncher::new(config);
        
        assert_eq!(launcher.get_restart_count(), 0);
        
        launcher.increment_restart_count();
        assert_eq!(launcher.get_restart_count(), 1);
        
        launcher.increment_restart_count();
        assert_eq!(launcher.get_restart_count(), 2);
        
        launcher.reset_restart_count();
        assert_eq!(launcher.get_restart_count(), 0);
    }
} 