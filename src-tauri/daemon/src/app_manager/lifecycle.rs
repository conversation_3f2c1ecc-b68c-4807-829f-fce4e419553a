//! 应用生命周期管理模块
//! 
//! 整合启动器和健康监控，提供完整的应用生命周期管理

use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{RwLock, Notify};
use tracing::{info, warn, error, debug};
use chrono::Utc;

use super::{
    AppConfig, AppStatus, AppInfo, HealthStatus, HealthCheckResult,
    AppManagerError, AppManagerResult, RecoveryStrategy,
    AppLauncher, HealthChecker, RecoveryManager
};

/// 应用生命周期管理器
pub struct AppLifecycleManager {
    /// 应用配置
    config: AppConfig,
    /// 应用启动器
    launcher: Arc<RwLock<AppLauncher>>,
    /// 健康检查器
    health_checker: Arc<RwLock<HealthChecker>>,
    /// 恢复管理器
    recovery_manager: Arc<RwLock<RecoveryManager>>,
    /// 当前状态
    current_status: Arc<RwLock<AppStatus>>,
    /// 关闭信号
    shutdown_signal: Arc<Notify>,
    /// 是否正在管理
    is_managing: Arc<RwLock<bool>>,
}

impl AppLifecycleManager {
    /// 创建新的应用生命周期管理器
    pub fn new(config: AppConfig) -> Self {
        let launcher = Arc::new(RwLock::new(AppLauncher::new(config.clone())));
        let health_checker = Arc::new(RwLock::new(HealthChecker::new(config.clone())));
        let recovery_manager = Arc::new(RwLock::new(RecoveryManager::new(config.clone())));

        Self {
            config,
            launcher,
            health_checker,
            recovery_manager,
            current_status: Arc::new(RwLock::new(AppStatus::NotStarted)),
            shutdown_signal: Arc::new(Notify::new()),
            is_managing: Arc::new(RwLock::new(false)),
        }
    }

    /// 启动应用生命周期管理
    pub async fn start_lifecycle_management(&self) -> AppManagerResult<()> {
        {
            let mut is_managing = self.is_managing.write().await;
            if *is_managing {
                return Err(AppManagerError::StartupFailed("生命周期管理已在运行".to_string()));
            }
            *is_managing = true;
        }

        info!("启动应用生命周期管理");

        // 1. 验证配置
        self.validate_configuration().await?;

        // 2. 启动应用 (静默模式)
        self.launch_app_silent().await?;

        // 3. 启动健康监控
        self.start_health_monitoring().await?;

        // 4. 启动生命周期监控循环
        self.start_lifecycle_loop().await?;

        info!("应用生命周期管理已启动");
        Ok(())
    }

    /// 停止应用生命周期管理
    pub async fn stop_lifecycle_management(&self) -> AppManagerResult<()> {
        info!("停止应用生命周期管理");

        // 1. 发送关闭信号
        self.shutdown_signal.notify_waiters();

        // 2. 停止健康监控
        {
            let health_checker = self.health_checker.read().await;
            health_checker.stop_monitoring().await?;
        }

        // 3. 优雅关闭应用
        self.shutdown_app_graceful().await?;

        // 4. 更新状态
        {
            let mut status = self.current_status.write().await;
            *status = AppStatus::Stopped;
        }

        {
            let mut is_managing = self.is_managing.write().await;
            *is_managing = false;
        }

        info!("应用生命周期管理已停止");
        Ok(())
    }

    /// 启动 Tauri 应用 (静默模式)
    pub async fn launch_app_silent(&self) -> AppManagerResult<u32> {
        info!("启动 Tauri 应用 (静默模式)");

        {
            let mut status = self.current_status.write().await;
            *status = AppStatus::Starting;
        }

        let process_id = {
            let mut launcher = self.launcher.write().await;
            launcher.launch_app_silent().await?
        };

        // 设置健康检查器的进程 ID
        {
            let mut health_checker = self.health_checker.write().await;
            health_checker.set_process_id(process_id);
        }

        {
            let mut status = self.current_status.write().await;
            *status = AppStatus::Running;
        }

        info!("应用已成功启动，PID: {}", process_id);
        Ok(process_id)
    }

    /// 重启应用
    pub async fn restart_app(&self) -> AppManagerResult<u32> {
        info!("重启应用");

        {
            let mut status = self.current_status.write().await;
            *status = AppStatus::Restarting;
        }

        // 1. 增加重启计数
        {
            let mut launcher = self.launcher.write().await;
            launcher.increment_restart_count();
        }

        // 2. 优雅关闭当前应用
        self.shutdown_app_graceful().await?;

        // 3. 等待一段时间后重启
        tokio::time::sleep(Duration::from_secs(self.config.restart_delay)).await;

        // 4. 重新启动应用
        let process_id = self.launch_app_silent().await?;

        info!("应用重启完成，PID: {}", process_id);
        Ok(process_id)
    }

    /// 优雅关闭应用
    pub async fn shutdown_app_graceful(&self) -> AppManagerResult<()> {
        info!("优雅关闭应用");

        {
            let mut status = self.current_status.write().await;
            *status = AppStatus::Stopping;
        }

        let result = {
            let mut launcher = self.launcher.write().await;
            launcher.shutdown_app_graceful().await
        };

        {
            let mut status = self.current_status.write().await;
            *status = AppStatus::Stopped;
        }

        result
    }

    /// 强制终止应用
    pub async fn force_kill_app(&self) -> AppManagerResult<()> {
        warn!("强制终止应用");

        {
            let mut status = self.current_status.write().await;
            *status = AppStatus::Stopping;
        }

        let result = {
            let mut launcher = self.launcher.write().await;
            launcher.force_kill_app().await
        };

        {
            let mut status = self.current_status.write().await;
            *status = AppStatus::Stopped;
        }

        result
    }

    /// 获取应用信息
    pub async fn get_app_info(&self) -> AppManagerResult<AppInfo> {
        let mut launcher = self.launcher.write().await;
        let mut app_info = launcher.get_app_info().await?;

        // 从健康检查器获取资源使用情况
        let health_checker = self.health_checker.read().await;
        if let Ok(health_result) = health_checker.check_health().await {
            if let Some(memory_usage) = health_result.memory_usage {
                app_info.memory_usage_mb = Some(memory_usage.current as u64);
            }
            if let Some(cpu_usage) = health_result.cpu_usage {
                app_info.cpu_usage_percent = Some(cpu_usage.current);
            }
        }

        // 更新状态
        {
            let current_status = self.current_status.read().await;
            app_info.status = current_status.clone();
        }

        Ok(app_info)
    }

    /// 获取当前状态
    pub async fn get_current_status(&self) -> AppStatus {
        let status = self.current_status.read().await;
        status.clone()
    }

    /// 检查是否正在管理
    pub async fn is_managing(&self) -> bool {
        let is_managing = self.is_managing.read().await;
        *is_managing
    }

    /// 获取健康检查历史
    pub async fn get_health_history(&self) -> Vec<HealthCheckResult> {
        let health_checker = self.health_checker.read().await;
        health_checker.get_check_history().await
    }

    /// 验证配置
    async fn validate_configuration(&self) -> AppManagerResult<()> {
        info!("验证应用配置");

        // 验证应用路径
        {
            let launcher = self.launcher.read().await;
            launcher.validate_app_path()?;
        }

        // 验证端口可用性
        self.validate_port_availability().await?;

        info!("应用配置验证通过");
        Ok(())
    }

    /// 验证端口可用性
    async fn validate_port_availability(&self) -> AppManagerResult<()> {
        let port = self.config.ipc_port;
        
        // 简单的端口可用性检查
        match tokio::net::TcpListener::bind(format!("127.0.0.1:{}", port)).await {
            Ok(_) => {
                debug!("端口 {} 可用", port);
                Ok(())
            }
            Err(e) => {
                error!("端口 {} 不可用: {}", port, e);
                Err(AppManagerError::ConfigError(format!("端口 {} 不可用: {}", port, e)))
            }
        }
    }

    /// 启动健康监控
    async fn start_health_monitoring(&self) -> AppManagerResult<()> {
        info!("启动健康监控");
        
        let health_checker = self.health_checker.read().await;
        health_checker.start_monitoring().await?;
        
        Ok(())
    }

    /// 启动生命周期监控循环
    async fn start_lifecycle_loop(&self) -> AppManagerResult<()> {
        info!("启动生命周期监控循环");

        let health_checker = Arc::clone(&self.health_checker);
        let recovery_manager = Arc::clone(&self.recovery_manager);
        let current_status = Arc::clone(&self.current_status);
        let shutdown_signal = Arc::clone(&self.shutdown_signal);
        let config = self.config.clone();

        tokio::spawn(async move {
            let mut check_interval = tokio::time::interval(Duration::from_secs(config.health_check_interval));
            
            loop {
                tokio::select! {
                    _ = check_interval.tick() => {
                        // 检查健康状态
                        let health_status = {
                            let health_checker = health_checker.read().await;
                            health_checker.get_latest_health_status().await
                        };

                        if let Some(status) = health_status {
                            match status {
                                HealthStatus::Critical | HealthStatus::Dead => {
                                    error!("应用健康状态异常: {:?}，启动恢复流程", status);
                                    
                                    // 更新状态为错误
                                    {
                                        let mut current_status = current_status.write().await;
                                        *current_status = AppStatus::Error(format!("健康检查失败: {:?}", status));
                                    }

                                    // 执行恢复策略
                                    let recovery_manager = recovery_manager.read().await;
                                    if let Err(e) = recovery_manager.auto_recover().await {
                                        error!("自动恢复失败: {}", e);
                                    }
                                }
                                HealthStatus::Warning => {
                                    warn!("应用健康状态警告: {:?}", status);
                                }
                                HealthStatus::Healthy => {
                                    debug!("应用健康状态正常");
                                }
                            }
                        }
                    }
                    _ = shutdown_signal.notified() => {
                        info!("收到关闭信号，退出生命周期监控循环");
                        break;
                    }
                }
            }
        });

        Ok(())
    }

    /// 执行恢复策略
    pub async fn execute_recovery_strategy(&self, strategy: RecoveryStrategy) -> AppManagerResult<()> {
        info!("执行恢复策略: {:?}", strategy);

        match strategy {
            RecoveryStrategy::RestartApp => {
                self.restart_app().await?;
            }
            RecoveryStrategy::ForceKillAndRestart => {
                self.force_kill_app().await?;
                tokio::time::sleep(Duration::from_secs(2)).await;
                self.launch_app_silent().await?;
            }
            RecoveryStrategy::ClearCache => {
                // TODO: 实现缓存清理逻辑
                warn!("缓存清理功能尚未实现");
            }
            RecoveryStrategy::ResetConfig => {
                // TODO: 实现配置重置逻辑
                warn!("配置重置功能尚未实现");
            }
            RecoveryStrategy::RollbackVersion => {
                // TODO: 实现版本回滚逻辑
                warn!("版本回滚功能尚未实现");
            }
        }

        info!("恢复策略执行完成");
        Ok(())
    }

    /// 暂停应用 (为未来扩展预留)
    pub async fn pause_app(&self) -> AppManagerResult<()> {
        info!("暂停应用");

        {
            let mut status = self.current_status.write().await;
            *status = AppStatus::Paused;
        }

        // TODO: 实现应用暂停逻辑
        warn!("应用暂停功能尚未实现");
        Ok(())
    }

    /// 恢复应用 (为未来扩展预留)
    pub async fn resume_app(&self) -> AppManagerResult<()> {
        info!("恢复应用");

        {
            let mut status = self.current_status.write().await;
            *status = AppStatus::Running;
        }

        // TODO: 实现应用恢复逻辑
        warn!("应用恢复功能尚未实现");
        Ok(())
    }

    /// 获取重启计数
    pub async fn get_restart_count(&self) -> u32 {
        let launcher = self.launcher.read().await;
        launcher.get_restart_count()
    }

    /// 重置重启计数
    pub async fn reset_restart_count(&self) -> AppManagerResult<()> {
        let mut launcher = self.launcher.write().await;
        launcher.reset_restart_count();
        Ok(())
    }
}

impl Drop for AppLifecycleManager {
    fn drop(&mut self) {
        info!("应用生命周期管理器正在销毁");
        // 发送关闭信号
        self.shutdown_signal.notify_waiters();
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_config() -> AppConfig {
        AppConfig {
            app_path: std::env::current_exe().unwrap(), // 使用当前可执行文件进行测试
            working_directory: None,
            startup_args: vec![],
            startup_timeout: 30,
            health_check_interval: 5,
            max_restart_attempts: 2,
            restart_delay: 1,
            ipc_port: 8081,
            silent_mode: true,
            memory_limit_mb: Some(100),
            cpu_limit_percent: Some(50.0),
        }
    }

    #[tokio::test]
    async fn test_lifecycle_manager_creation() {
        let config = create_test_config();
        let manager = AppLifecycleManager::new(config);

        assert!(!manager.is_managing().await);
        assert_eq!(manager.get_current_status().await, AppStatus::NotStarted);
    }

    #[tokio::test]
    async fn test_validate_configuration() {
        let config = create_test_config();
        let manager = AppLifecycleManager::new(config);

        // 验证配置应该成功，因为我们使用的是有效的可执行文件路径
        let result = manager.validate_configuration().await;
        
        // 端口可能被占用，所以我们只检查是否有明确的错误类型
        match result {
            Ok(_) => {
                // 配置验证成功
            }
            Err(AppManagerError::ConfigError(_)) => {
                // 端口被占用是可以接受的错误
            }
            Err(e) => {
                panic!("意外的错误类型: {}", e);
            }
        }
    }

    #[tokio::test]
    async fn test_status_transitions() {
        let config = create_test_config();
        let manager = AppLifecycleManager::new(config);

        // 初始状态
        assert_eq!(manager.get_current_status().await, AppStatus::NotStarted);

        // 测试状态更新
        {
            let mut status = manager.current_status.write().await;
            *status = AppStatus::Starting;
        }
        assert_eq!(manager.get_current_status().await, AppStatus::Starting);

        {
            let mut status = manager.current_status.write().await;
            *status = AppStatus::Running;
        }
        assert_eq!(manager.get_current_status().await, AppStatus::Running);
    }

    #[tokio::test]
    async fn test_restart_count_management() {
        let config = create_test_config();
        let manager = AppLifecycleManager::new(config);

        // 初始重启计数应该为0
        assert_eq!(manager.get_restart_count().await, 0);

        // 模拟重启
        {
            let mut launcher = manager.launcher.write().await;
            launcher.increment_restart_count();
        }
        assert_eq!(manager.get_restart_count().await, 1);

        // 重置重启计数
        manager.reset_restart_count().await.unwrap();
        assert_eq!(manager.get_restart_count().await, 0);
    }

    #[tokio::test]
    async fn test_health_history() {
        let config = create_test_config();
        let manager = AppLifecycleManager::new(config);

        // 初始健康历史应该为空
        let history = manager.get_health_history().await;
        assert!(history.is_empty());
    }
} 