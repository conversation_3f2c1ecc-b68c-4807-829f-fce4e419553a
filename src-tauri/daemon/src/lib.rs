//! Secure Password Daemon 库
//! 
//! 企业级独立守护进程库，提供核心功能模块

pub mod daemon_core;
pub mod config;
pub mod platform;
pub mod ipc;
pub mod native_messaging;
pub mod app_manager;
pub mod security;
pub mod monitoring;
pub mod error;
pub mod utils;

// 重新导出主要类型
pub use daemon_core::{SecurePasswordDaemon, RuntimeMode, DaemonStatus};
pub use config::DaemonConfig;
pub use error::{<PERSON><PERSON><PERSON><PERSON>, DaemonResult};
