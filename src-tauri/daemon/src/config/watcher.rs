//! 配置文件监控模块
//! 
//! 监控配置文件变化并触发热重载

use std::path::{Path, PathBuf};
use std::sync::Arc;
use tokio::sync::mpsc;
use tokio::time::{Duration, interval};
use tracing::{info, warn, error};

use crate::config::DaemonConfig;
use crate::error::DaemonError;

/// 配置变化事件
#[derive(Debug, Clone)]
pub enum ConfigChangeEvent {
    /// 配置文件已修改
    Modified(PathBuf),
    /// 配置文件已删除
    Deleted(PathBuf),
    /// 配置文件已创建
    Created(PathBuf),
}

/// 配置文件监控器
pub struct ConfigWatcher {
    /// 配置文件路径
    config_path: PathBuf,
    /// 事件发送器
    event_sender: mpsc::UnboundedSender<ConfigChangeEvent>,
    /// 事件接收器
    event_receiver: Option<mpsc::UnboundedReceiver<ConfigChangeEvent>>,
    /// 最后修改时间
    last_modified: Option<std::time::SystemTime>,
}

impl ConfigWatcher {
    /// 创建新的配置监控器
    pub fn new<P: AsRef<Path>>(config_path: P) -> Self {
        let (event_sender, event_receiver) = mpsc::unbounded_channel();
        
        Self {
            config_path: config_path.as_ref().to_path_buf(),
            event_sender,
            event_receiver: Some(event_receiver),
            last_modified: None,
        }
    }
    
    /// 启动监控
    pub async fn start(&mut self) -> Result<mpsc::UnboundedReceiver<ConfigChangeEvent>, DaemonError> {
        info!("启动配置文件监控: {:?}", self.config_path);
        
        // 获取初始修改时间
        if let Ok(metadata) = tokio::fs::metadata(&self.config_path).await {
            if let Ok(modified) = metadata.modified() {
                self.last_modified = Some(modified);
            }
        }
        
        // 启动监控任务
        let config_path = self.config_path.clone();
        let event_sender = self.event_sender.clone();
        let mut last_modified = self.last_modified;
        
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(1));
            
            loop {
                interval.tick().await;
                
                match tokio::fs::metadata(&config_path).await {
                    Ok(metadata) => {
                        if let Ok(modified) = metadata.modified() {
                            match last_modified {
                                Some(last) if modified > last => {
                                    // 文件已修改
                                    info!("检测到配置文件变化: {:?}", config_path);
                                    if let Err(e) = event_sender.send(ConfigChangeEvent::Modified(config_path.clone())) {
                                        error!("发送配置变化事件失败: {}", e);
                                        break;
                                    }
                                    last_modified = Some(modified);
                                }
                                None => {
                                    // 首次检测到文件
                                    last_modified = Some(modified);
                                }
                                _ => {
                                    // 文件未变化
                                }
                            }
                        }
                    }
                    Err(_) => {
                        // 文件不存在或无法访问
                        if last_modified.is_some() {
                            warn!("配置文件不存在或无法访问: {:?}", config_path);
                            if let Err(e) = event_sender.send(ConfigChangeEvent::Deleted(config_path.clone())) {
                                error!("发送配置删除事件失败: {}", e);
                                break;
                            }
                            last_modified = None;
                        }
                    }
                }
            }
        });
        
        // 返回事件接收器
        self.event_receiver.take()
            .ok_or_else(|| DaemonError::InternalError("事件接收器已被取走".to_string()))
    }
    
    /// 停止监控
    pub fn stop(&self) {
        info!("停止配置文件监控");
        // 监控任务会在事件发送器被丢弃时自动停止
    }
}

/// 配置热重载管理器
pub struct ConfigReloadManager {
    /// 当前配置
    current_config: Arc<tokio::sync::RwLock<DaemonConfig>>,
    /// 配置文件路径
    config_path: PathBuf,
}

impl ConfigReloadManager {
    /// 创建新的配置重载管理器
    pub fn new<P: AsRef<Path>>(config: DaemonConfig, config_path: P) -> Self {
        Self {
            current_config: Arc::new(tokio::sync::RwLock::new(config)),
            config_path: config_path.as_ref().to_path_buf(),
        }
    }
    
    /// 获取当前配置
    pub async fn get_config(&self) -> DaemonConfig {
        self.current_config.read().await.clone()
    }
    
    /// 启动配置热重载
    pub async fn start_hot_reload(&self) -> Result<(), DaemonError> {
        info!("启动配置热重载管理器");
        
        let mut watcher = ConfigWatcher::new(&self.config_path);
        let mut event_receiver = watcher.start().await?;
        
        let current_config = self.current_config.clone();
        let config_path = self.config_path.clone();
        
        tokio::spawn(async move {
            while let Some(event) = event_receiver.recv().await {
                match event {
                    ConfigChangeEvent::Modified(path) => {
                        info!("配置文件已修改，开始热重载: {:?}", path);
                        
                        match DaemonConfig::load_from_file(&config_path).await {
                            Ok(new_config) => {
                                match new_config.validate() {
                                    Ok(()) => {
                                        let mut config_guard = current_config.write().await;
                                        let old_config = config_guard.clone();
                                        
                                        // 显示配置差异
                                        let differences = old_config.diff(&new_config);
                                        if !differences.is_empty() {
                                            info!("配置变化:");
                                            for diff in differences {
                                                info!("  {}", diff);
                                            }
                                        }
                                        
                                        *config_guard = new_config;
                                        info!("配置热重载成功");
                                    }
                                    Err(e) => {
                                        error!("新配置验证失败，跳过热重载: {}", e);
                                    }
                                }
                            }
                            Err(e) => {
                                error!("加载新配置失败: {}", e);
                            }
                        }
                    }
                    ConfigChangeEvent::Deleted(path) => {
                        warn!("配置文件已删除: {:?}", path);
                        // 可以选择使用默认配置或停止服务
                    }
                    ConfigChangeEvent::Created(path) => {
                        info!("配置文件已创建: {:?}", path);
                        // 可以选择重新加载配置
                    }
                }
            }
        });
        
        Ok(())
    }
}
