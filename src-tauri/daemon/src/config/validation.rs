//! 配置验证模块

use std::path::Path;
use crate::config::{DaemonConfig, IpcTransportType, LogFormat};
use crate::error::DaemonError;

/// 配置验证器
pub struct ConfigValidator;

impl ConfigValidator {
    /// 验证守护进程配置
    pub fn validate(config: &DaemonConfig) -> Result<(), DaemonError> {
        // 1. 验证服务配置
        Self::validate_service_config(config)?;

        // 2. 验证 IPC 配置
        Self::validate_ipc_config(config)?;

        // 3. 验证 Native Messaging 配置
        Self::validate_native_messaging_config(config)?;

        // 4. 验证应用管理配置
        Self::validate_app_manager_config(config)?;

        // 5. 验证安全配置
        Self::validate_security_config(config)?;

        // 6. 验证监控配置
        Self::validate_monitoring_config(config)?;

        // 7. 验证日志配置
        Self::validate_logging_config(config)?;

        Ok(())
    }

    /// 验证服务配置
    fn validate_service_config(config: &DaemonConfig) -> Result<(), DaemonError> {
        let service = &config.service;

        // 验证服务名称
        if service.name.is_empty() {
            return Err(DaemonError::ConfigValidationError("服务名称不能为空".to_string()));
        }

        if service.name.len() > 64 {
            return Err(DaemonError::ConfigValidationError("服务名称长度不能超过64个字符".to_string()));
        }

        // 验证显示名称
        if service.display_name.is_empty() {
            return Err(DaemonError::ConfigValidationError("服务显示名称不能为空".to_string()));
        }

        // 验证工作目录
        if let Some(ref working_dir) = service.working_directory {
            if !working_dir.is_empty() && !Path::new(working_dir).exists() {
                return Err(DaemonError::ConfigValidationError(
                    format!("工作目录不存在: {}", working_dir)
                ));
            }
        }

        Ok(())
    }

    /// 验证 IPC 配置
    fn validate_ipc_config(config: &DaemonConfig) -> Result<(), DaemonError> {
        let ipc = &config.ipc;

        // 验证绑定地址
        if ipc.bind_address.is_empty() {
            return Err(DaemonError::ConfigValidationError("IPC 绑定地址不能为空".to_string()));
        }

        // 验证端口
        match ipc.transport {
            IpcTransportType::Tcp | IpcTransportType::Auto => {
                if let Some(port) = ipc.port {
                    if port == 0 || port > 65535 {
                        return Err(DaemonError::ConfigValidationError(
                            format!("无效的端口号: {}", port)
                        ));
                    }
                }
            }
            _ => {}
        }

        // 验证连接数限制
        if ipc.max_connections == 0 {
            return Err(DaemonError::ConfigValidationError("最大连接数必须大于0".to_string()));
        }

        if ipc.max_connections > 10000 {
            return Err(DaemonError::ConfigValidationError("最大连接数不能超过10000".to_string()));
        }

        // 验证超时时间
        if ipc.connection_timeout == 0 {
            return Err(DaemonError::ConfigValidationError("连接超时时间必须大于0".to_string()));
        }

        Ok(())
    }

    /// 验证 Native Messaging 配置
    fn validate_native_messaging_config(config: &DaemonConfig) -> Result<(), DaemonError> {
        let nm = &config.native_messaging;

        if nm.enabled {
            // 验证 Host 名称
            if nm.host_name.is_empty() {
                return Err(DaemonError::ConfigValidationError("Native Messaging Host 名称不能为空".to_string()));
            }

            // 验证支持的浏览器
            if nm.supported_browsers.is_empty() {
                return Err(DaemonError::ConfigValidationError("至少需要支持一个浏览器".to_string()));
            }

            // 验证浏览器名称
            for browser in &nm.supported_browsers {
                if !["chrome", "firefox", "edge", "safari"].contains(&browser.as_str()) {
                    return Err(DaemonError::ConfigValidationError(
                        format!("不支持的浏览器: {}", browser)
                    ));
                }
            }
        }

        Ok(())
    }

    /// 验证应用管理配置
    fn validate_app_manager_config(config: &DaemonConfig) -> Result<(), DaemonError> {
        let app = &config.app_manager;

        // 验证应用路径
        if app.app_path.is_empty() {
            return Err(DaemonError::ConfigValidationError("应用路径不能为空".to_string()));
        }

        // 验证超时时间
        if app.startup_timeout == 0 {
            return Err(DaemonError::ConfigValidationError("启动超时时间必须大于0".to_string()));
        }

        if app.health_check_interval == 0 {
            return Err(DaemonError::ConfigValidationError("健康检查间隔必须大于0".to_string()));
        }

        // 验证重启次数
        if app.max_restart_attempts > 10 {
            return Err(DaemonError::ConfigValidationError("最大重启次数不能超过10".to_string()));
        }

        Ok(())
    }

    /// 验证安全配置
    fn validate_security_config(config: &DaemonConfig) -> Result<(), DaemonError> {
        let security = &config.security;

        if security.enabled {
            // 验证加密算法
            if !["AES-256-GCM", "AES-128-GCM", "ChaCha20-Poly1305"].contains(&security.encryption_algorithm.as_str()) {
                return Err(DaemonError::ConfigValidationError(
                    format!("不支持的加密算法: {}", security.encryption_algorithm)
                ));
            }

            // 验证密钥长度
            if ![128, 256].contains(&security.key_length) {
                return Err(DaemonError::ConfigValidationError(
                    format!("不支持的密钥长度: {}", security.key_length)
                ));
            }
        }

        Ok(())
    }

    /// 验证监控配置
    fn validate_monitoring_config(config: &DaemonConfig) -> Result<(), DaemonError> {
        let monitoring = &config.monitoring;

        if monitoring.enabled {
            // 验证指标收集间隔
            if monitoring.metrics_interval == 0 {
                return Err(DaemonError::ConfigValidationError("指标收集间隔必须大于0".to_string()));
            }

            // 验证监控端口
            if let Some(port) = monitoring.monitoring_port {
                if port == 0 || port > 65535 {
                    return Err(DaemonError::ConfigValidationError(
                        format!("无效的监控端口: {}", port)
                    ));
                }
            }
        }

        Ok(())
    }

    /// 验证日志配置
    fn validate_logging_config(config: &DaemonConfig) -> Result<(), DaemonError> {
        let logging = &config.logging;

        // 验证日志级别
        if !["trace", "debug", "info", "warn", "error"].contains(&logging.level.as_str()) {
            return Err(DaemonError::ConfigValidationError(
                format!("无效的日志级别: {}", logging.level)
            ));
        }

        // 验证日志文件路径
        if let Some(ref file_path) = logging.file_path {
            if !file_path.is_empty() {
                let path = Path::new(file_path);
                if let Some(parent) = path.parent() {
                    // 只有当父目录不是空字符串且不存在时才报错
                    if !parent.as_os_str().is_empty() && !parent.exists() {
                        return Err(DaemonError::ConfigValidationError(
                            format!("日志文件目录不存在: {}", parent.display())
                        ));
                    }
                }
            }
        }

        // 验证日志格式
        match logging.format {
            LogFormat::Text | LogFormat::Json => {} // 都是有效的
        }

        Ok(())
    }
}
