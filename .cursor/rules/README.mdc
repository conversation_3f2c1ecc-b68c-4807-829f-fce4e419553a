---
description: 
globs: 
alwaysApply: true
---
对话必须全程保持为中文

# Cursor Rules 索引

这是一个基于 Tauri + React + Ant Design 的跨平台密码管理器项目的 Cursor Rules 集合。这些规则帮助 AI 助手更好地理解项目结构、开发规范和最佳实践，特别强调 **测试驱动开发(TDD)** 和 **模块化设计**。

## 📋 规则文件概览

### 📁 [project-architecture.mdc](mdc:.cursor/rules/project-architecture.mdc)
- **作用**: 定义项目的模块化架构和设计原则
- **包含**: 分层架构、依赖注入、测试架构、安全架构、性能优化
- **适用**: 架构设计、模块划分、设计模式应用
- **更新**: ✅ 已整合测试驱动架构和模块化设计

### 💻 [coding-standards.mdc](mdc:.cursor/rules/coding-standards.mdc)
- **作用**: 定义严格的编码规范和测试驱动开发标准
- **包含**: 函数式编程、TDD流程、代码质量要求、测试覆盖率95%+
- **适用**: 代码编写、代码审查、质量控制
- **更新**: ✅ 已强化TDD要求和函数式编程标准

### 🧪 [testing-strategy.mdc](mdc:.cursor/rules/testing-strategy.mdc)
- **作用**: 专门的测试策略和实践指导
- **包含**: 测试金字塔、单元测试、集成测试、E2E测试、性能测试
- **适用**: 测试设计、测试实现、测试自动化
- **特色**: ✨ **新增** - 完整的测试体系和最佳实践

### 📅 [milestone-management.mdc](mdc:.cursor/rules/milestone-management.mdc)
- **作用**: 里程碑管理和增量开发流程
- **包含**: 三级里程碑体系、10周开发路线图、风险管理、进度跟踪
- **适用**: 项目管理、进度控制、质量保证
- **特色**: ✨ **新增** - 详细的里程碑规划和验收标准

### 🔄 [development-workflow.mdc](mdc:.cursor/rules/development-workflow.mdc)
- **作用**: 增量开发工作流和模块化开发流程
- **包含**: TDD循环、模块开发生命周期、CI/CD、团队协作
- **适用**: 开发流程、构建部署、版本管理
- **更新**: ✅ 已整合增量开发和模块化流程

### 🔧 [tauri-integration.mdc](mdc:.cursor/rules/tauri-integration.mdc)
- **作用**: Tauri 相关的开发规范和跨平台支持
- **包含**: 前后端通信、移动端支持、构建部署、原生功能集成
- **适用**: Tauri 应用开发、多平台适配、原生功能集成

### 🔐 [security-encryption.mdc](mdc:.cursor/rules/security-encryption.mdc)
- **作用**: 安全和加密相关的开发规范
- **包含**: 零知识架构、安全存储、认证授权、威胁模型
- **适用**: 安全功能开发、加密实现、敏感数据处理

### 🌐 [browser-extension.mdc](mdc:.cursor/rules/browser-extension.mdc)
- **作用**: 浏览器扩展开发规范和集成指导
- **包含**: 扩展架构、原生消息传递、表单检测、多浏览器兼容
- **适用**: 浏览器扩展开发、网页集成、跨浏览器兼容性

## 🎯 核心开发理念

### 测试驱动开发 (TDD)
```
红 → 绿 → 重构
├─ 先写测试 (失败)
├─ 实现功能 (通过)
└─ 重构优化 (保持通过)
```

**强制要求**:
- 📊 **测试覆盖率**: >95%
- 🧪 **测试先行**: 新功能必须先写测试
- ⚡ **快速反馈**: 每次提交触发测试
- 🔄 **持续重构**: 保持代码质量

### 模块化增量开发
```
L1 主要里程碑 (2-4周)
├─ L2 模块里程碑 (3-7天)
│  ├─ L3 功能里程碑 (1-3天)
│  └─ L3 功能里程碑 (1-3天)
└─ L2 模块里程碑 (3-7天)
```

**核心特点**:
- 🏗️ **模块独立**: 单一职责，松耦合
- 📈 **增量交付**: 分阶段完成，风险可控
- ✅ **验收驱动**: 明确的完成标准
- 🔄 **持续集成**: 频繁集成，快速反馈

## 🚀 使用指南

### 对于新功能开发
1. **架构设计**: 参考 [project-architecture.mdc](mdc:.cursor/rules/project-architecture.mdc)
2. **编码规范**: 遵循 [coding-standards.mdc](mdc:.cursor/rules/coding-standards.mdc)
3. **测试策略**: 应用 [testing-strategy.mdc](mdc:.cursor/rules/testing-strategy.mdc)
4. **里程碑规划**: 使用 [milestone-management.mdc](mdc:.cursor/rules/milestone-management.mdc)

### 对于代码审查
1. **质量检查**: [coding-standards.mdc](mdc:.cursor/rules/coding-standards.mdc) 的检查清单
2. **测试验证**: [testing-strategy.mdc](mdc:.cursor/rules/testing-strategy.mdc) 的覆盖要求
3. **安全审计**: [security-encryption.mdc](mdc:.cursor/rules/security-encryption.mdc) 的安全标准

### 对于跨平台开发
1. **Tauri 集成**: [tauri-integration.mdc](mdc:.cursor/rules/tauri-integration.mdc)
2. **浏览器扩展**: [browser-extension.mdc](mdc:.cursor/rules/browser-extension.mdc)
3. **开发流程**: [development-workflow.mdc](mdc:.cursor/rules/development-workflow.mdc)

## 📊 质量指标体系

### 代码质量指标
- **测试覆盖率**: >95% (强制)
- **代码复杂度**: 函数不超过50行
- **依赖关系**: 无循环依赖
- **文档完整性**: 所有公共API有文档

### 性能基准指标
- **应用启动**: <2s
- **密码生成**: <1ms
- **数据加密**: <5ms/MB
- **数据库查询**: <10ms
- **内存使用**: <100MB (空闲)

### 安全合规指标
- **零知识架构**: 100% 客户端加密
- **敏感数据**: 内存使用后立即清零
- **依赖安全**: 所有依赖通过安全审计
- **威胁模型**: 覆盖已知攻击向量

## 🔄 持续改进

### 规则更新机制
1. **定期回顾**: 每月评估规则有效性
2. **实践反馈**: 根据开发经验优化规则
3. **技术演进**: 跟进新技术和最佳实践
4. **团队共识**: 确保所有开发者理解和遵循

### 项目发展路线图
```
Phase 1: 核心基础架构 (4周)
├─ 认证与加密基础
└─ 数据存储与管理

Phase 2: 用户界面与体验 (3周)
├─ 核心 UI 组件
└─ 高级功能

Phase 3: 跨平台适配 (3周)
├─ 移动端适配
└─ 浏览器扩展
```

## 💡 最佳实践总结

### 开发流程
1. **规划**: 制定模块计划和接口设计
2. **测试**: 编写测试用例，确保失败
3. **实现**: 编写最简实现，通过测试
4. **重构**: 优化代码质量，保持测试通过
5. **集成**: 模块集成测试和功能验证
6. **验收**: 里程碑验收和质量检查

### 代码质量
- ✅ **函数式优先**: 纯函数、不可变数据
- ✅ **测试驱动**: 先写测试，再写实现
- ✅ **模块化设计**: 单一职责、依赖注入
- ✅ **安全第一**: 零知识架构、数据加密
- ✅ **性能优化**: 异步操作、缓存策略

### 团队协作
- 🤝 **代码审查**: 所有代码必须经过审查
- 📝 **文档同步**: 代码变更同步更新文档
- 🔄 **持续集成**: 自动化测试和部署
- 📊 **质量监控**: 实时监控质量指标
- 🎯 **里程碑管理**: 定期检查项目进度

这套完整的规则体系确保项目按照高质量标准交付，支持测试驱动开发和模块化设计的最佳实践。

# 不要简化实现或使用模拟数据以避免测试中的错误，因为这会引入漏洞并降低代码质量。相反：

- 实现完整功能：编写完整的、可投入生产的代码，即使它更复杂或更容易出现初始错误。
- 使用真实数据和API：针对实际数据源、数据库和外部服务进行测试，而不是模拟响应。
- 妥善处理错误：当测试中出现错误时，调试并修复根本原因，而不是绕过它们。
- 维护测试完整性：确保测试验证实际行为，并捕捉在生产环境中可能出现的真实问题。
- 迭代以达到正确性：接受初始实现可能未通过测试，然后努力修复问题，直到测试通过并具有真实功能。

这种方法虽然需要更多的前期努力来解决真正的实现问题，但会导致更强大、可靠的代码。
