# 代码示例和模板

## 文档概述

本文档提供了 Native Messaging 企业级独立守护进程系统的完整代码示例和模板，包括关键模块的实现模板、可运行的示例代码、错误处理、日志记录和测试用例。

**相关文档**:
- [API设计规范](./API_DESIGN_SPECIFICATION.md)
- [技术栈选型指南](./TECHNOLOGY_STACK_GUIDE.md)
- [开发环境设置指南](./DEVELOPMENT_ENVIRONMENT_SETUP.md)
- [增量开发路线图](./NATIVE_MESSAGING_INCREMENTAL_ROADMAP.md)

## 🏗️ 项目结构模板

### 完整项目结构
```
secure-password/
├── 📦 Cargo.toml                          # 工作空间配置
├── 📄 README.md
├── 📄 LICENSE
├── 📁 .github/
│   └── 📁 workflows/
│       ├── ci.yml                          # CI/CD 流水线
│       ├── security-audit.yml              # 安全审计
│       └── release.yml                     # 发布流程
├── 📁 src-tauri/
│   ├── 📁 daemon/                          # 🔧 独立守护进程
│   │   ├── 📦 Cargo.toml
│   │   ├── 📁 src/
│   │   │   ├── 📄 main.rs                  # 守护进程入口
│   │   │   ├── 📄 daemon_core.rs           # 核心逻辑
│   │   │   ├── 📁 platform/                # 平台特定实现
│   │   │   ├── 📁 ipc/                     # IPC 通信引擎
│   │   │   ├── 📁 native_messaging/        # Native Messaging 代理
│   │   │   ├── 📁 app_manager/             # 应用管理器
│   │   │   ├── 📁 security/                # 安全代理
│   │   │   └── 📁 monitoring/              # 性能监控
│   │   ├── 📁 resources/                   # 系统服务配置
│   │   └── 📁 tests/                       # 测试文件
│   ├── 📁 src/                             # 🎨 Tauri 主应用
│   │   ├── 📄 main.rs
│   │   ├── 📄 lib.rs
│   │   ├── 📁 ipc_client/                  # IPC 客户端
│   │   ├── 📁 business/                    # 业务逻辑
│   │   ├── 📁 storage/                     # 数据存储
│   │   └── 📁 config/                      # 配置管理
│   ├── 📦 Cargo.toml
│   ├── 📄 tauri.conf.json
│   └── 📄 build.rs
├── 📁 src/                                 # 🌐 前端源码
│   ├── 📁 components/
│   ├── 📁 pages/
│   ├── 📁 stores/
│   ├── 📁 utils/
│   ├── 📄 main.ts
│   └── 📄 App.vue
├── 📁 extensions/                          # 🌐 浏览器扩展
│   ├── 📁 chrome/
│   ├── 📁 firefox/
│   ├── 📁 edge/
│   └── 📁 shared/
├── 📁 crates/                              # 📚 共享库
│   ├── 📁 native-messaging/
│   ├── 📁 security/
│   ├── 📁 ipc/
│   └── 📁 types/
├── 📁 docs/                                # 📖 文档
├── 📁 scripts/                             # 🔧 构建脚本
├── 📁 tests/                               # 🧪 集成测试
├── 📄 package.json                         # 前端依赖
├── 📄 tsconfig.json                        # TypeScript 配置
├── 📄 vite.config.ts                       # Vite 配置
└── 📄 .env.example                         # 环境变量示例
```

## 🔧 守护进程核心模板

### 1. 守护进程主入口 (src-tauri/daemon/src/main.rs)

```rust
//! 企业级独立守护进程主入口
//! 
//! 提供7x24小时稳定运行的系统服务，负责：
//! - Native Messaging 代理
//! - IPC 通信服务
//! - Tauri 应用管理
//! - 企业级安全防护

use std::sync::Arc;
use tokio::signal;
use tracing::{info, error, warn};
use anyhow::Result;

mod daemon_core;
mod config;
mod platform;
mod ipc;
mod native_messaging;
mod app_manager;
mod security;
mod monitoring;
mod error;
mod utils;

use daemon_core::SecurePasswordDaemon;
use config::DaemonConfig;
use error::DaemonError;
use utils::logging::init_logging;

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志系统
    init_logging()?;
    
    info!("🚀 启动 Secure Password 守护进程 v{}", env!("CARGO_PKG_VERSION"));
    
    // 解析命令行参数
    let args = parse_command_line_args();
    
    // 加载配置文件
    let config = DaemonConfig::load_from_file(&args.config_path)
        .await
        .map_err(|e| {
            error!("配置文件加载失败: {}", e);
            e
        })?;
    
    // 验证配置
    config.validate()
        .map_err(|e| {
            error!("配置验证失败: {}", e);
            e
        })?;
    
    // 创建守护进程实例
    let mut daemon = SecurePasswordDaemon::new(config.clone())
        .await
        .map_err(|e| {
            error!("守护进程创建失败: {}", e);
            e
        })?;
    
    // 设置信号处理
    let shutdown_signal = setup_signal_handlers();
    
    // 启动守护进程
    match daemon.start().await {
        Ok(()) => {
            info!("✅ 守护进程启动成功");
            
            // 等待关闭信号
            shutdown_signal.await;
            
            info!("📥 收到关闭信号，开始优雅关闭...");
            
            // 优雅关闭
            if let Err(e) = daemon.shutdown().await {
                error!("守护进程关闭失败: {}", e);
                std::process::exit(1);
            }
            
            info!("✅ 守护进程已安全关闭");
        }
        Err(e) => {
            error!("❌ 守护进程启动失败: {}", e);
            std::process::exit(1);
        }
    }
    
    Ok(())
}

/// 命令行参数结构
#[derive(Debug)]
struct CommandLineArgs {
    config_path: String,
    log_level: String,
    daemon_mode: bool,
}

/// 解析命令行参数
fn parse_command_line_args() -> CommandLineArgs {
    use clap::{Arg, Command};
    
    let matches = Command::new("secure-password-daemon")
        .version(env!("CARGO_PKG_VERSION"))
        .about("企业级密码管理守护进程")
        .arg(
            Arg::new("config")
                .short('c')
                .long("config")
                .value_name("FILE")
                .help("配置文件路径")
                .default_value("daemon.toml")
        )
        .arg(
            Arg::new("log-level")
                .short('l')
                .long("log-level")
                .value_name("LEVEL")
                .help("日志级别")
                .default_value("info")
                .value_parser(["trace", "debug", "info", "warn", "error"])
        )
        .arg(
            Arg::new("daemon")
                .short('d')
                .long("daemon")
                .help("以守护进程模式运行")
                .action(clap::ArgAction::SetTrue)
        )
        .get_matches();
    
    CommandLineArgs {
        config_path: matches.get_one::<String>("config").unwrap().clone(),
        log_level: matches.get_one::<String>("log-level").unwrap().clone(),
        daemon_mode: matches.get_flag("daemon"),
    }
}

/// 设置信号处理器
async fn setup_signal_handlers() {
    use tokio::signal::unix::{signal, SignalKind};
    
    let mut sigterm = signal(SignalKind::terminate())
        .expect("Failed to register SIGTERM handler");
    let mut sigint = signal(SignalKind::interrupt())
        .expect("Failed to register SIGINT handler");
    
    tokio::select! {
        _ = sigterm.recv() => {
            info!("收到 SIGTERM 信号");
        }
        _ = sigint.recv() => {
            info!("收到 SIGINT 信号");
        }
        _ = signal::ctrl_c() => {
            info!("收到 Ctrl+C 信号");
        }
    }
}

#[cfg(windows)]
async fn setup_signal_handlers() {
    use tokio::signal::windows;
    
    let mut ctrl_c = windows::ctrl_c()
        .expect("Failed to register Ctrl+C handler");
    let mut ctrl_break = windows::ctrl_break()
        .expect("Failed to register Ctrl+Break handler");
    let mut ctrl_close = windows::ctrl_close()
        .expect("Failed to register Ctrl+Close handler");
    
    tokio::select! {
        _ = ctrl_c.recv() => {
            info!("收到 Ctrl+C 信号");
        }
        _ = ctrl_break.recv() => {
            info!("收到 Ctrl+Break 信号");
        }
        _ = ctrl_close.recv() => {
            info!("收到 Ctrl+Close 信号");
        }
    }
}
```

### 2. 守护进程核心逻辑 (src-tauri/daemon/src/daemon_core.rs)

```rust
//! 守护进程核心逻辑实现
//! 
//! 负责协调各个子模块的启动、运行和关闭

use std::sync::Arc;
use tokio::sync::{Mutex, Notify};
use tracing::{info, error, warn, debug};
use anyhow::Result;

use crate::config::DaemonConfig;
use crate::platform::SystemServiceManager;
use crate::ipc::IpcServer;
use crate::native_messaging::NativeMessagingHost;
use crate::app_manager::AppLifecycleManager;
use crate::security::DaemonSecurityProxy;
use crate::monitoring::PerformanceMonitor;
use crate::error::DaemonError;

/// 守护进程主结构
pub struct SecurePasswordDaemon {
    config: DaemonConfig,
    state: Arc<Mutex<DaemonState>>,
    shutdown_notify: Arc<Notify>,
    
    // 子服务组件
    service_manager: Option<Box<dyn SystemServiceManager>>,
    ipc_server: Option<IpcServer>,
    native_messaging_host: Option<NativeMessagingHost>,
    app_manager: Option<AppLifecycleManager>,
    security_proxy: Option<DaemonSecurityProxy>,
    performance_monitor: Option<PerformanceMonitor>,
}

/// 守护进程状态
#[derive(Debug, Clone, PartialEq)]
pub enum DaemonState {
    Initializing,
    Starting,
    Running,
    Stopping,
    Stopped,
    Error(String),
}

impl SecurePasswordDaemon {
    /// 创建新的守护进程实例
    pub async fn new(config: DaemonConfig) -> Result<Self, DaemonError> {
        info!("🔧 初始化守护进程实例");
        
        Ok(Self {
            config,
            state: Arc::new(Mutex::new(DaemonState::Initializing)),
            shutdown_notify: Arc::new(Notify::new()),
            service_manager: None,
            ipc_server: None,
            native_messaging_host: None,
            app_manager: None,
            security_proxy: None,
            performance_monitor: None,
        })
    }
    
    /// 启动守护进程
    pub async fn start(&mut self) -> Result<(), DaemonError> {
        self.set_state(DaemonState::Starting).await;
        
        info!("🚀 启动守护进程服务...");
        
        // 1. 初始化安全代理
        self.init_security_proxy().await?;
        
        // 2. 启动性能监控
        self.start_performance_monitor().await?;
        
        // 3. 启动 IPC 服务器
        self.start_ipc_server().await?;
        
        // 4. 启动 Native Messaging Host
        self.start_native_messaging_host().await?;
        
        // 5. 启动应用管理器
        self.start_app_manager().await?;
        
        // 6. 注册系统服务 (如果需要)
        if self.config.service_config.auto_register {
            self.register_system_service().await?;
        }
        
        self.set_state(DaemonState::Running).await;
        info!("✅ 守护进程启动完成");
        
        Ok(())
    }
    
    /// 优雅关闭守护进程
    pub async fn shutdown(&mut self) -> Result<(), DaemonError> {
        self.set_state(DaemonState::Stopping).await;
        
        info!("🛑 开始关闭守护进程...");
        
        // 通知所有等待的任务
        self.shutdown_notify.notify_waiters();
        
        // 按相反顺序关闭服务
        
        // 1. 停止应用管理器
        if let Some(mut app_manager) = self.app_manager.take() {
            info!("关闭应用管理器...");
            if let Err(e) = app_manager.shutdown().await {
                warn!("应用管理器关闭失败: {}", e);
            }
        }
        
        // 2. 停止 Native Messaging Host
        if let Some(mut nm_host) = self.native_messaging_host.take() {
            info!("关闭 Native Messaging Host...");
            if let Err(e) = nm_host.stop().await {
                warn!("Native Messaging Host 关闭失败: {}", e);
            }
        }
        
        // 3. 停止 IPC 服务器
        if let Some(mut ipc_server) = self.ipc_server.take() {
            info!("关闭 IPC 服务器...");
            if let Err(e) = ipc_server.stop().await {
                warn!("IPC 服务器关闭失败: {}", e);
            }
        }
        
        // 4. 停止性能监控
        if let Some(mut monitor) = self.performance_monitor.take() {
            info!("关闭性能监控...");
            if let Err(e) = monitor.stop().await {
                warn!("性能监控关闭失败: {}", e);
            }
        }
        
        // 5. 关闭安全代理
        if let Some(mut security_proxy) = self.security_proxy.take() {
            info!("关闭安全代理...");
            if let Err(e) = security_proxy.shutdown().await {
                warn!("安全代理关闭失败: {}", e);
            }
        }
        
        self.set_state(DaemonState::Stopped).await;
        info!("✅ 守护进程已安全关闭");
        
        Ok(())
    }
    
    /// 获取守护进程状态
    pub async fn get_state(&self) -> DaemonState {
        self.state.lock().await.clone()
    }
    
    /// 设置守护进程状态
    async fn set_state(&self, new_state: DaemonState) {
        let mut state = self.state.lock().await;
        debug!("守护进程状态变更: {:?} -> {:?}", *state, new_state);
        *state = new_state;
    }
    
    /// 初始化安全代理
    async fn init_security_proxy(&mut self) -> Result<(), DaemonError> {
        info!("🔐 初始化安全代理...");
        
        let mut security_proxy = DaemonSecurityProxy::new(
            self.config.security_config.clone()
        ).await?;
        
        security_proxy.initialize().await?;
        
        self.security_proxy = Some(security_proxy);
        
        info!("✅ 安全代理初始化完成");
        Ok(())
    }
    
    /// 启动性能监控
    async fn start_performance_monitor(&mut self) -> Result<(), DaemonError> {
        info!("📊 启动性能监控...");
        
        let mut monitor = PerformanceMonitor::new(
            self.config.monitoring_config.clone()
        ).await?;
        
        monitor.start().await?;
        
        self.performance_monitor = Some(monitor);
        
        info!("✅ 性能监控启动完成");
        Ok(())
    }
    
    /// 启动 IPC 服务器
    async fn start_ipc_server(&mut self) -> Result<(), DaemonError> {
        info!("🔗 启动 IPC 服务器...");
        
        let mut ipc_server = IpcServer::new(
            self.config.ipc_config.clone()
        ).await?;
        
        ipc_server.start().await?;
        
        self.ipc_server = Some(ipc_server);
        
        info!("✅ IPC 服务器启动完成");
        Ok(())
    }
    
    /// 启动 Native Messaging Host
    async fn start_native_messaging_host(&mut self) -> Result<(), DaemonError> {
        info!("📡 启动 Native Messaging Host...");
        
        let mut nm_host = NativeMessagingHost::new(
            self.config.native_messaging_config.clone()
        ).await?;
        
        nm_host.start().await?;
        
        // 注册浏览器 Host 配置
        nm_host.register_browser_hosts().await?;
        
        self.native_messaging_host = Some(nm_host);
        
        info!("✅ Native Messaging Host 启动完成");
        Ok(())
    }
    
    /// 启动应用管理器
    async fn start_app_manager(&mut self) -> Result<(), DaemonError> {
        info!("🎨 启动应用管理器...");
        
        let mut app_manager = AppLifecycleManager::new(
            self.config.app_management_config.clone()
        ).await?;
        
        // 启动 Tauri 应用 (静默模式)
        app_manager.launch_app_silent().await?;
        
        // 启动健康监控
        app_manager.start_health_monitoring().await?;
        
        self.app_manager = Some(app_manager);
        
        info!("✅ 应用管理器启动完成");
        Ok(())
    }
    
    /// 注册系统服务
    async fn register_system_service(&mut self) -> Result<(), DaemonError> {
        info!("⚙️ 注册系统服务...");
        
        // 根据平台创建相应的服务管理器
        let service_manager = crate::platform::create_service_manager()?;
        
        // 检查服务是否已注册
        match service_manager.get_service_status().await {
            Ok(status) => {
                info!("系统服务状态: {:?}", status);
            }
            Err(_) => {
                // 服务未注册，进行注册
                let install_config = self.config.service_config.to_install_config();
                service_manager.install_service(&install_config).await?;
                info!("✅ 系统服务注册完成");
            }
        }
        
        self.service_manager = Some(service_manager);
        
        Ok(())
    }
}

/// 实现 Drop trait 确保资源清理
impl Drop for SecurePasswordDaemon {
    fn drop(&mut self) {
        debug!("守护进程实例被销毁");
    }
}
```

### 3. IPC 服务器实现模板 (src-tauri/daemon/src/ipc/server.rs)

```rust
//! IPC 服务器实现
//!
//! 提供跨平台的进程间通信服务

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{RwLock, mpsc};
use tokio::net::{TcpListener, UnixListener};
use tracing::{info, error, warn, debug};
use anyhow::Result;
use uuid::Uuid;

use crate::config::IpcConfig;
use crate::error::IpcError;
use super::protocol::{IpcMessage, IpcResponse, IpcMessageType};
use super::transport::{IpcTransport, IpcConnection};
use super::handlers::IpcMessageHandler;

/// IPC 服务器
pub struct IpcServer {
    config: IpcConfig,
    state: Arc<RwLock<ServerState>>,
    connections: Arc<RwLock<HashMap<String, Arc<dyn IpcConnection>>>>,
    message_handlers: Arc<RwLock<HashMap<IpcMessageType, Box<dyn IpcMessageHandler>>>>,
    shutdown_tx: Option<mpsc::Sender<()>>,
}

/// 服务器状态
#[derive(Debug, Clone, PartialEq)]
pub enum ServerState {
    Stopped,
    Starting,
    Running,
    Stopping,
    Error(String),
}

impl IpcServer {
    /// 创建新的 IPC 服务器
    pub async fn new(config: IpcConfig) -> Result<Self, IpcError> {
        Ok(Self {
            config,
            state: Arc::new(RwLock::new(ServerState::Stopped)),
            connections: Arc::new(RwLock::new(HashMap::new())),
            message_handlers: Arc::new(RwLock::new(HashMap::new())),
            shutdown_tx: None,
        })
    }

    /// 启动 IPC 服务器
    pub async fn start(&mut self) -> Result<(), IpcError> {
        self.set_state(ServerState::Starting).await;

        info!("🔗 启动 IPC 服务器，地址: {}", self.config.bind_address);

        // 创建关闭通道
        let (shutdown_tx, mut shutdown_rx) = mpsc::channel::<()>(1);
        self.shutdown_tx = Some(shutdown_tx);

        // 根据配置选择传输方式
        let transport = self.create_transport().await?;

        // 克隆必要的数据用于异步任务
        let connections = Arc::clone(&self.connections);
        let handlers = Arc::clone(&self.message_handlers);
        let state = Arc::clone(&self.state);

        // 启动服务器监听循环
        tokio::spawn(async move {
            let mut listener = match transport.bind().await {
                Ok(listener) => listener,
                Err(e) => {
                    error!("IPC 服务器绑定失败: {}", e);
                    let mut state_guard = state.write().await;
                    *state_guard = ServerState::Error(e.to_string());
                    return;
                }
            };

            // 设置为运行状态
            {
                let mut state_guard = state.write().await;
                *state_guard = ServerState::Running;
            }

            info!("✅ IPC 服务器启动成功");

            loop {
                tokio::select! {
                    // 接受新连接
                    connection_result = listener.accept() => {
                        match connection_result {
                            Ok(connection) => {
                                let connection_id = Uuid::new_v4().to_string();
                                info!("新的 IPC 连接: {}", connection_id);

                                // 存储连接
                                {
                                    let mut connections_guard = connections.write().await;
                                    connections_guard.insert(connection_id.clone(), connection.clone());
                                }

                                // 为每个连接启动处理任务
                                let connection_clone = connection.clone();
                                let handlers_clone = Arc::clone(&handlers);
                                let connections_clone = Arc::clone(&connections);

                                tokio::spawn(async move {
                                    if let Err(e) = Self::handle_connection(
                                        connection_id.clone(),
                                        connection_clone,
                                        handlers_clone,
                                        connections_clone,
                                    ).await {
                                        error!("连接处理失败 {}: {}", connection_id, e);
                                    }
                                });
                            }
                            Err(e) => {
                                error!("接受连接失败: {}", e);
                            }
                        }
                    }

                    // 接收关闭信号
                    _ = shutdown_rx.recv() => {
                        info!("收到 IPC 服务器关闭信号");
                        break;
                    }
                }
            }

            // 关闭所有连接
            {
                let connections_guard = connections.read().await;
                for (id, connection) in connections_guard.iter() {
                    info!("关闭连接: {}", id);
                    if let Err(e) = connection.close().await {
                        warn!("关闭连接失败 {}: {}", id, e);
                    }
                }
            }

            // 设置为停止状态
            {
                let mut state_guard = state.write().await;
                *state_guard = ServerState::Stopped;
            }

            info!("✅ IPC 服务器已停止");
        });

        // 等待服务器启动完成
        while self.get_state().await != ServerState::Running {
            tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
        }

        Ok(())
    }

    /// 停止 IPC 服务器
    pub async fn stop(&mut self) -> Result<(), IpcError> {
        self.set_state(ServerState::Stopping).await;

        if let Some(shutdown_tx) = self.shutdown_tx.take() {
            let _ = shutdown_tx.send(()).await;
        }

        // 等待服务器完全停止
        while self.get_state().await != ServerState::Stopped {
            tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
        }

        Ok(())
    }

    /// 发送消息到指定客户端
    pub async fn send_to_client(&self, client_id: &str, message: IpcMessage) -> Result<(), IpcError> {
        let connections = self.connections.read().await;

        if let Some(connection) = connections.get(client_id) {
            let serialized = serde_json::to_vec(&message)
                .map_err(|e| IpcError::SerializationFailed(e.to_string()))?;

            connection.send(&serialized).await
                .map_err(|e| IpcError::SendFailed(e.to_string()))?;

            debug!("消息已发送到客户端 {}: {:?}", client_id, message.message_type);
            Ok(())
        } else {
            Err(IpcError::ClientNotFound(client_id.to_string()))
        }
    }

    /// 广播消息到所有客户端
    pub async fn broadcast(&self, message: IpcMessage) -> Result<(), IpcError> {
        let connections = self.connections.read().await;

        let serialized = serde_json::to_vec(&message)
            .map_err(|e| IpcError::SerializationFailed(e.to_string()))?;

        for (client_id, connection) in connections.iter() {
            if let Err(e) = connection.send(&serialized).await {
                warn!("广播消息到客户端 {} 失败: {}", client_id, e);
            }
        }

        debug!("消息已广播到 {} 个客户端: {:?}", connections.len(), message.message_type);
        Ok(())
    }

    /// 注册消息处理器
    pub async fn register_handler(
        &mut self,
        message_type: IpcMessageType,
        handler: Box<dyn IpcMessageHandler>,
    ) -> Result<(), IpcError> {
        let mut handlers = self.message_handlers.write().await;
        handlers.insert(message_type.clone(), handler);

        info!("注册消息处理器: {:?}", message_type);
        Ok(())
    }

    /// 获取服务器状态
    async fn get_state(&self) -> ServerState {
        self.state.read().await.clone()
    }

    /// 设置服务器状态
    async fn set_state(&self, new_state: ServerState) {
        let mut state = self.state.write().await;
        *state = new_state;
    }

    /// 创建传输层
    async fn create_transport(&self) -> Result<Box<dyn IpcTransport>, IpcError> {
        match &self.config.transport {
            crate::config::IpcTransportType::Auto => {
                // 根据平台自动选择最佳传输方式
                #[cfg(windows)]
                return Ok(Box::new(super::transport::NamedPipeTransport::new()));

                #[cfg(unix)]
                return Ok(Box::new(super::transport::UnixSocketTransport::new()));
            }
            crate::config::IpcTransportType::Tcp { port } => {
                Ok(Box::new(super::transport::TcpTransport::new(*port)))
            }
            crate::config::IpcTransportType::UnixSocket { path } => {
                Ok(Box::new(super::transport::UnixSocketTransport::with_path(path.clone())))
            }
            crate::config::IpcTransportType::NamedPipe { name } => {
                Ok(Box::new(super::transport::NamedPipeTransport::with_name(name.clone())))
            }
        }
    }

    /// 处理单个连接
    async fn handle_connection(
        connection_id: String,
        connection: Arc<dyn IpcConnection>,
        handlers: Arc<RwLock<HashMap<IpcMessageType, Box<dyn IpcMessageHandler>>>>,
        connections: Arc<RwLock<HashMap<String, Arc<dyn IpcConnection>>>>,
    ) -> Result<(), IpcError> {
        loop {
            // 接收消息
            let data = match connection.recv().await {
                Ok(data) => data,
                Err(e) => {
                    debug!("连接 {} 接收数据失败: {}", connection_id, e);
                    break;
                }
            };

            // 反序列化消息
            let message: IpcMessage = match serde_json::from_slice(&data) {
                Ok(msg) => msg,
                Err(e) => {
                    error!("消息反序列化失败: {}", e);
                    continue;
                }
            };

            debug!("收到消息 {}: {:?}", connection_id, message.message_type);

            // 查找并调用处理器
            let response = {
                let handlers_guard = handlers.read().await;
                if let Some(handler) = handlers_guard.get(&message.message_type) {
                    handler.handle(message.clone()).await
                } else {
                    warn!("未找到消息处理器: {:?}", message.message_type);
                    Err(IpcError::HandlerNotFound(format!("{:?}", message.message_type)))
                }
            };

            // 如果需要响应，发送响应消息
            if message.response_required {
                let response_message = match response {
                    Ok(data) => IpcResponse {
                        request_id: message.message_id,
                        status: crate::ipc::protocol::ResponseStatus::Success,
                        data,
                        error: None,
                        timestamp: chrono::Utc::now().timestamp_millis() as u64,
                        processing_time: 0, // TODO: 计算实际处理时间
                    },
                    Err(e) => IpcResponse {
                        request_id: message.message_id,
                        status: crate::ipc::protocol::ResponseStatus::Error,
                        data: serde_json::Value::Null,
                        error: Some(e.to_string()),
                        timestamp: chrono::Utc::now().timestamp_millis() as u64,
                        processing_time: 0,
                    },
                };

                let response_data = serde_json::to_vec(&response_message)
                    .map_err(|e| IpcError::SerializationFailed(e.to_string()))?;

                if let Err(e) = connection.send(&response_data).await {
                    error!("发送响应失败 {}: {}", connection_id, e);
                    break;
                }
            }
        }

        // 移除连接
        {
            let mut connections_guard = connections.write().await;
            connections_guard.remove(&connection_id);
        }

        info!("连接已断开: {}", connection_id);
        Ok(())
    }
}
```

### 4. Native Messaging Host 实现模板

```rust
//! Native Messaging Host 实现
//!
//! 处理浏览器扩展的 Native Messaging 请求

use std::io::{self, Read, Write};
use std::sync::Arc;
use tokio::sync::{mpsc, RwLock};
use tracing::{info, error, warn, debug};
use anyhow::Result;
use serde_json::Value;

use crate::config::NativeMessagingConfig;
use crate::error::NativeMessagingError;
use super::protocol::{BrowserRequest, BrowserResponse};
use super::browser::BrowserRegistry;
use super::proxy::RequestProxy;

/// Native Messaging Host
pub struct NativeMessagingHost {
    config: NativeMessagingConfig,
    browser_registry: BrowserRegistry,
    request_proxy: RequestProxy,
    running: Arc<RwLock<bool>>,
}

impl NativeMessagingHost {
    /// 创建新的 Native Messaging Host
    pub async fn new(config: NativeMessagingConfig) -> Result<Self, NativeMessagingError> {
        let browser_registry = BrowserRegistry::new().await?;
        let request_proxy = RequestProxy::new(config.clone()).await?;

        Ok(Self {
            config,
            browser_registry,
            request_proxy,
            running: Arc::new(RwLock::new(false)),
        })
    }

    /// 启动 Native Messaging Host
    pub async fn start(&mut self) -> Result<(), NativeMessagingError> {
        info!("📡 启动 Native Messaging Host");

        {
            let mut running = self.running.write().await;
            *running = true;
        }

        // 启动标准输入/输出监听
        self.start_stdio_listener().await?;

        info!("✅ Native Messaging Host 启动完成");
        Ok(())
    }

    /// 停止 Native Messaging Host
    pub async fn stop(&mut self) -> Result<(), NativeMessagingError> {
        info!("🛑 停止 Native Messaging Host");

        {
            let mut running = self.running.write().await;
            *running = false;
        }

        Ok(())
    }

    /// 注册浏览器 Host 配置
    pub async fn register_browser_hosts(&self) -> Result<Vec<BrowserRegistration>, NativeMessagingError> {
        info!("🌐 注册浏览器 Host 配置");

        let mut registrations = Vec::new();

        // Chrome
        match self.browser_registry.register_chrome_host().await {
            Ok(()) => {
                info!("✅ Chrome Host 注册成功");
                registrations.push(BrowserRegistration {
                    browser: BrowserType::Chrome,
                    success: true,
                    error: None,
                });
            }
            Err(e) => {
                warn!("❌ Chrome Host 注册失败: {}", e);
                registrations.push(BrowserRegistration {
                    browser: BrowserType::Chrome,
                    success: false,
                    error: Some(e.to_string()),
                });
            }
        }

        // Firefox
        match self.browser_registry.register_firefox_host().await {
            Ok(()) => {
                info!("✅ Firefox Host 注册成功");
                registrations.push(BrowserRegistration {
                    browser: BrowserType::Firefox,
                    success: true,
                    error: None,
                });
            }
            Err(e) => {
                warn!("❌ Firefox Host 注册失败: {}", e);
                registrations.push(BrowserRegistration {
                    browser: BrowserType::Firefox,
                    success: false,
                    error: Some(e.to_string()),
                });
            }
        }

        // Edge
        match self.browser_registry.register_edge_host().await {
            Ok(()) => {
                info!("✅ Edge Host 注册成功");
                registrations.push(BrowserRegistration {
                    browser: BrowserType::Edge,
                    success: true,
                    error: None,
                });
            }
            Err(e) => {
                warn!("❌ Edge Host 注册失败: {}", e);
                registrations.push(BrowserRegistration {
                    browser: BrowserType::Edge,
                    success: false,
                    error: Some(e.to_string()),
                });
            }
        }

        // Safari (仅 macOS)
        #[cfg(target_os = "macos")]
        {
            match self.browser_registry.register_safari_host().await {
                Ok(()) => {
                    info!("✅ Safari Host 注册成功");
                    registrations.push(BrowserRegistration {
                        browser: BrowserType::Safari,
                        success: true,
                        error: None,
                    });
                }
                Err(e) => {
                    warn!("❌ Safari Host 注册失败: {}", e);
                    registrations.push(BrowserRegistration {
                        browser: BrowserType::Safari,
                        success: false,
                        error: Some(e.to_string()),
                    });
                }
            }
        }

        Ok(registrations)
    }

    /// 启动标准输入/输出监听
    async fn start_stdio_listener(&self) -> Result<(), NativeMessagingError> {
        let running = Arc::clone(&self.running);
        let request_proxy = self.request_proxy.clone();

        tokio::spawn(async move {
            let mut stdin = io::stdin();
            let mut stdout = io::stdout();

            while *running.read().await {
                // 读取消息长度 (4字节，小端序)
                let mut length_bytes = [0u8; 4];
                if let Err(e) = stdin.read_exact(&mut length_bytes) {
                    if e.kind() == io::ErrorKind::UnexpectedEof {
                        debug!("标准输入已关闭");
                        break;
                    }
                    error!("读取消息长度失败: {}", e);
                    continue;
                }

                let message_length = u32::from_le_bytes(length_bytes) as usize;

                // 验证消息长度
                if message_length > 1024 * 1024 {  // 1MB 限制
                    error!("消息长度过大: {} bytes", message_length);
                    continue;
                }

                // 读取消息内容
                let mut message_bytes = vec![0u8; message_length];
                if let Err(e) = stdin.read_exact(&mut message_bytes) {
                    error!("读取消息内容失败: {}", e);
                    continue;
                }

                // 解析 JSON 消息
                let message_value: Value = match serde_json::from_slice(&message_bytes) {
                    Ok(value) => value,
                    Err(e) => {
                        error!("JSON 解析失败: {}", e);
                        continue;
                    }
                };

                // 转换为浏览器请求
                let browser_request = match BrowserRequest::from_json(message_value) {
                    Ok(request) => request,
                    Err(e) => {
                        error!("浏览器请求转换失败: {}", e);
                        continue;
                    }
                };

                debug!("收到浏览器请求: {:?}", browser_request.message_type);

                // 处理请求
                let response = match request_proxy.handle_request(browser_request).await {
                    Ok(response) => response,
                    Err(e) => {
                        error!("请求处理失败: {}", e);
                        BrowserResponse::error(
                            "unknown".to_string(),
                            format!("Internal error: {}", e),
                        )
                    }
                };

                // 序列化响应
                let response_json = match serde_json::to_vec(&response) {
                    Ok(json) => json,
                    Err(e) => {
                        error!("响应序列化失败: {}", e);
                        continue;
                    }
                };

                // 发送响应长度
                let response_length = response_json.len() as u32;
                let length_bytes = response_length.to_le_bytes();

                if let Err(e) = stdout.write_all(&length_bytes) {
                    error!("发送响应长度失败: {}", e);
                    break;
                }

                // 发送响应内容
                if let Err(e) = stdout.write_all(&response_json) {
                    error!("发送响应内容失败: {}", e);
                    break;
                }

                if let Err(e) = stdout.flush() {
                    error!("刷新输出缓冲区失败: {}", e);
                    break;
                }

                debug!("响应已发送: {:?}", response.status);
            }

            info!("标准输入/输出监听已停止");
        });

        Ok(())
    }
}

/// 浏览器注册结果
#[derive(Debug, Clone)]
pub struct BrowserRegistration {
    pub browser: BrowserType,
    pub success: bool,
    pub error: Option<String>,
}

/// 浏览器类型
#[derive(Debug, Clone, PartialEq)]
pub enum BrowserType {
    Chrome,
    Firefox,
    Edge,
    Safari,
}
```

## 🎨 Tauri 主应用模板

### 1. Tauri 主应用入口 (src-tauri/src/main.rs)

```rust
//! Tauri 主应用入口
//!
//! 密码管理应用的主要用户界面和业务逻辑

#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use std::sync::Arc;
use tauri::{Manager, State, Window};
use tracing::{info, error};
use anyhow::Result;

mod ipc_client;
mod business;
mod storage;
mod config;
mod error;
mod commands;

use ipc_client::DaemonClient;
use business::CredentialManager;
use storage::DatabaseManager;
use config::AppConfig;

/// 应用状态
#[derive(Debug)]
pub struct AppState {
    pub daemon_client: Arc<DaemonClient>,
    pub credential_manager: Arc<CredentialManager>,
    pub database_manager: Arc<DatabaseManager>,
    pub config: Arc<AppConfig>,
}

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志
    init_logging()?;

    info!("🎨 启动 Secure Password 主应用 v{}", env!("CARGO_PKG_VERSION"));

    // 加载应用配置
    let config = Arc::new(AppConfig::load().await?);

    // 初始化数据库
    let database_manager = Arc::new(DatabaseManager::new(config.database.clone()).await?);
    database_manager.initialize().await?;

    // 初始化业务逻辑
    let credential_manager = Arc::new(
        CredentialManager::new(Arc::clone(&database_manager)).await?
    );

    // 连接到守护进程
    let daemon_client = Arc::new(
        DaemonClient::connect(config.daemon_connection.clone()).await?
    );

    // 注册浏览器消息处理器
    register_browser_handlers(&daemon_client, &credential_manager).await?;

    // 创建应用状态
    let app_state = AppState {
        daemon_client,
        credential_manager,
        database_manager,
        config,
    };

    // 启动 Tauri 应用
    tauri::Builder::default()
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![
            commands::get_credentials,
            commands::save_credential,
            commands::update_credential,
            commands::delete_credential,
            commands::search_credentials,
            commands::generate_password,
            commands::get_app_status,
            commands::sync_data,
        ])
        .setup(|app| {
            let window = app.get_window("main").unwrap();

            // 设置窗口属性
            #[cfg(debug_assertions)]
            {
                window.open_devtools();
            }

            // 启动后台任务
            let app_handle = app.handle();
            tokio::spawn(async move {
                start_background_tasks(app_handle).await;
            });

            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("启动 Tauri 应用失败");

    Ok(())
}

/// 初始化日志系统
fn init_logging() -> Result<()> {
    use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "info".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    Ok(())
}

/// 注册浏览器消息处理器
async fn register_browser_handlers(
    daemon_client: &DaemonClient,
    credential_manager: &CredentialManager,
) -> Result<()> {
    use crate::ipc_client::handlers::*;

    // 凭证获取处理器
    daemon_client.register_handler(
        "get_credentials".to_string(),
        Box::new(GetCredentialsHandler::new(Arc::clone(credential_manager))),
    ).await?;

    // 凭证保存处理器
    daemon_client.register_handler(
        "save_credential".to_string(),
        Box::new(SaveCredentialHandler::new(Arc::clone(credential_manager))),
    ).await?;

    // 密码生成处理器
    daemon_client.register_handler(
        "generate_password".to_string(),
        Box::new(GeneratePasswordHandler::new(Arc::clone(credential_manager))),
    ).await?;

    info!("✅ 浏览器消息处理器注册完成");
    Ok(())
}

/// 启动后台任务
async fn start_background_tasks(app_handle: tauri::AppHandle) {
    info!("🔄 启动后台任务");

    // 定期同步任务
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(300)); // 5分钟

        loop {
            interval.tick().await;

            // 执行数据同步
            if let Err(e) = perform_sync(&app_handle).await {
                error!("数据同步失败: {}", e);
            }
        }
    });

    // 健康检查任务
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(60)); // 1分钟

        loop {
            interval.tick().await;

            // 检查守护进程连接状态
            if let Err(e) = check_daemon_health(&app_handle).await {
                error!("守护进程健康检查失败: {}", e);
            }
        }
    });
}

/// 执行数据同步
async fn perform_sync(app_handle: &tauri::AppHandle) -> Result<()> {
    let state: State<AppState> = app_handle.state();

    // 这里实现数据同步逻辑
    info!("执行数据同步");

    Ok(())
}

/// 检查守护进程健康状态
async fn check_daemon_health(app_handle: &tauri::AppHandle) -> Result<()> {
    let state: State<AppState> = app_handle.state();

    match state.daemon_client.ping().await {
        Ok(_) => {
            debug!("守护进程连接正常");
        }
        Err(e) => {
            warn!("守护进程连接异常: {}", e);

            // 尝试重新连接
            if let Err(reconnect_error) = state.daemon_client.reconnect().await {
                error!("重新连接守护进程失败: {}", reconnect_error);
            } else {
                info!("守护进程重新连接成功");
            }
        }
    }

    Ok(())
}
```

### 2. Tauri 命令实现 (src-tauri/src/commands.rs)

```rust
//! Tauri 命令实现
//!
//! 提供前端调用的 API 命令

use tauri::{State, Window};
use serde::{Deserialize, Serialize};
use anyhow::Result;

use crate::AppState;
use crate::business::{Credential, NewCredential, CredentialUpdate, SearchOptions};
use crate::error::AppError;

/// 获取凭证命令
#[tauri::command]
pub async fn get_credentials(
    domain: String,
    state: State<'_, AppState>,
) -> Result<Vec<Credential>, String> {
    state
        .credential_manager
        .get_credentials_for_domain(&domain, Default::default())
        .await
        .map_err(|e| e.to_string())
}

/// 保存凭证命令
#[tauri::command]
pub async fn save_credential(
    credential: NewCredential,
    state: State<'_, AppState>,
) -> Result<String, String> {
    state
        .credential_manager
        .save_credential(credential)
        .await
        .map_err(|e| e.to_string())
}

/// 更新凭证命令
#[tauri::command]
pub async fn update_credential(
    id: String,
    updates: CredentialUpdate,
    state: State<'_, AppState>,
) -> Result<(), String> {
    state
        .credential_manager
        .update_credential(&id, updates)
        .await
        .map_err(|e| e.to_string())
}

/// 删除凭证命令
#[tauri::command]
pub async fn delete_credential(
    id: String,
    state: State<'_, AppState>,
) -> Result<(), String> {
    state
        .credential_manager
        .delete_credential(&id)
        .await
        .map_err(|e| e.to_string())
}

/// 搜索凭证命令
#[tauri::command]
pub async fn search_credentials(
    query: String,
    options: SearchOptions,
    state: State<'_, AppState>,
) -> Result<Vec<Credential>, String> {
    state
        .credential_manager
        .search_credentials(&query, options)
        .await
        .map_err(|e| e.to_string())
}

/// 生成密码命令
#[tauri::command]
pub async fn generate_password(
    length: usize,
    include_symbols: bool,
    state: State<'_, AppState>,
) -> Result<String, String> {
    use crate::business::PasswordPolicy;

    let policy = PasswordPolicy {
        length,
        include_uppercase: true,
        include_lowercase: true,
        include_numbers: true,
        include_symbols,
        exclude_ambiguous: true,
    };

    state
        .credential_manager
        .generate_password(policy)
        .await
        .map_err(|e| e.to_string())
}

/// 获取应用状态命令
#[tauri::command]
pub async fn get_app_status(
    state: State<'_, AppState>,
) -> Result<AppStatusResponse, String> {
    let daemon_status = state
        .daemon_client
        .get_status()
        .await
        .map_err(|e| e.to_string())?;

    let credential_count = state
        .credential_manager
        .get_credential_count()
        .await
        .map_err(|e| e.to_string())?;

    Ok(AppStatusResponse {
        daemon_connected: daemon_status.connected,
        credential_count,
        last_sync: daemon_status.last_sync,
        version: env!("CARGO_PKG_VERSION").to_string(),
    })
}

/// 同步数据命令
#[tauri::command]
pub async fn sync_data(
    state: State<'_, AppState>,
    window: Window,
) -> Result<(), String> {
    // 发送同步开始事件
    window.emit("sync_started", ()).map_err(|e| e.to_string())?;

    // 执行同步逻辑
    match perform_data_sync(&state).await {
        Ok(()) => {
            window.emit("sync_completed", ()).map_err(|e| e.to_string())?;
            Ok(())
        }
        Err(e) => {
            window.emit("sync_failed", e.to_string()).map_err(|e| e.to_string())?;
            Err(e.to_string())
        }
    }
}

/// 应用状态响应
#[derive(Debug, Serialize, Deserialize)]
pub struct AppStatusResponse {
    pub daemon_connected: bool,
    pub credential_count: usize,
    pub last_sync: Option<chrono::DateTime<chrono::Utc>>,
    pub version: String,
}

/// 执行数据同步
async fn perform_data_sync(state: &AppState) -> Result<(), AppError> {
    // 这里实现具体的同步逻辑
    // 1. 检查远程更新
    // 2. 上传本地更改
    // 3. 解决冲突
    // 4. 更新本地数据

    Ok(())
}
```

## 🌐 浏览器扩展模板

### 1. Chrome 扩展 Manifest (extensions/chrome/manifest.json)

```json
{
  "manifest_version": 3,
  "name": "Secure Password Manager",
  "version": "1.0.0",
  "description": "企业级密码管理器浏览器扩展",

  "permissions": [
    "activeTab",
    "storage",
    "nativeMessaging"
  ],

  "host_permissions": [
    "http://*/*",
    "https://*/*"
  ],

  "background": {
    "service_worker": "background.js",
    "type": "module"
  },

  "content_scripts": [
    {
      "matches": ["http://*/*", "https://*/*"],
      "js": ["content.js"],
      "run_at": "document_end",
      "all_frames": true
    }
  ],

  "action": {
    "default_popup": "popup.html",
    "default_title": "Secure Password Manager",
    "default_icon": {
      "16": "icons/icon16.png",
      "32": "icons/icon32.png",
      "48": "icons/icon48.png",
      "128": "icons/icon128.png"
    }
  },

  "icons": {
    "16": "icons/icon16.png",
    "32": "icons/icon32.png",
    "48": "icons/icon48.png",
    "128": "icons/icon128.png"
  },

  "web_accessible_resources": [
    {
      "resources": ["injected.js"],
      "matches": ["http://*/*", "https://*/*"]
    }
  ]
}
```

### 2. 扩展后台脚本 (extensions/shared/background.ts)

```typescript
/**
 * 浏览器扩展后台脚本
 *
 * 负责与 Native Messaging Host 通信
 */

import { NativeMessagingClient } from './native-messaging-client';
import { MessageType, ExtensionMessage, ExtensionResponse } from './types';

class BackgroundService {
    private nativeClient: NativeMessagingClient;
    private isConnected = false;

    constructor() {
        this.nativeClient = new NativeMessagingClient({
            hostName: 'com.securepassword.daemon',
            timeout: 10000,
            autoReconnect: true,
        });

        this.initialize();
    }

    /**
     * 初始化后台服务
     */
    private async initialize(): Promise<void> {
        try {
            // 连接到 Native Messaging Host
            await this.nativeClient.connect();
            this.isConnected = true;

            console.log('✅ Native Messaging 连接成功');

            // 监听来自应用的消息
            this.nativeClient.onMessage((message) => {
                this.handleNativeMessage(message);
            });

            // 监听连接状态变化
            this.nativeClient.onConnectionStatusChange((status) => {
                this.isConnected = status === 'connected';
                this.notifyConnectionStatus(status);
            });

        } catch (error) {
            console.error('❌ Native Messaging 连接失败:', error);
            this.isConnected = false;
        }

        // 设置扩展消息监听器
        this.setupMessageListeners();
    }

    /**
     * 设置消息监听器
     */
    private setupMessageListeners(): void {
        // 监听来自 content script 的消息
        chrome.runtime.onMessage.addListener(
            (message: ExtensionMessage, sender, sendResponse) => {
                this.handleExtensionMessage(message, sender)
                    .then(sendResponse)
                    .catch((error) => {
                        console.error('消息处理失败:', error);
                        sendResponse({
                            success: false,
                            error: error.message,
                        });
                    });

                // 返回 true 表示异步响应
                return true;
            }
        );

        // 监听标签页更新
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (changeInfo.status === 'complete' && tab.url) {
                this.handleTabUpdate(tabId, tab.url);
            }
        });
    }

    /**
     * 处理扩展内部消息
     */
    private async handleExtensionMessage(
        message: ExtensionMessage,
        sender: chrome.runtime.MessageSender
    ): Promise<ExtensionResponse> {
        if (!this.isConnected) {
            throw new Error('Native Messaging 未连接');
        }

        switch (message.type) {
            case MessageType.GET_CREDENTIALS:
                return this.getCredentials(message.data.domain);

            case MessageType.SAVE_CREDENTIAL:
                return this.saveCredential(message.data);

            case MessageType.GENERATE_PASSWORD:
                return this.generatePassword(message.data.options);

            case MessageType.CHECK_CONNECTION:
                return { success: true, data: { connected: this.isConnected } };

            default:
                throw new Error(`未知消息类型: ${message.type}`);
        }
    }

    /**
     * 获取凭证
     */
    private async getCredentials(domain: string): Promise<ExtensionResponse> {
        try {
            const response = await this.nativeClient.sendMessage({
                type: 'get_credentials',
                requestId: this.generateRequestId(),
                payload: { domain },
                timestamp: Date.now(),
                source: 'browser-extension',
            });

            if (response.status === 'success') {
                return {
                    success: true,
                    data: response.data,
                };
            } else {
                throw new Error(response.error || '获取凭证失败');
            }
        } catch (error) {
            console.error('获取凭证失败:', error);
            return {
                success: false,
                error: error.message,
            };
        }
    }

    /**
     * 保存凭证
     */
    private async saveCredential(credentialData: any): Promise<ExtensionResponse> {
        try {
            const response = await this.nativeClient.sendMessage({
                type: 'save_credential',
                requestId: this.generateRequestId(),
                payload: { credential: credentialData },
                timestamp: Date.now(),
                source: 'browser-extension',
            });

            if (response.status === 'success') {
                return {
                    success: true,
                    data: response.data,
                };
            } else {
                throw new Error(response.error || '保存凭证失败');
            }
        } catch (error) {
            console.error('保存凭证失败:', error);
            return {
                success: false,
                error: error.message,
            };
        }
    }

    /**
     * 生成密码
     */
    private async generatePassword(options: any): Promise<ExtensionResponse> {
        try {
            const response = await this.nativeClient.sendMessage({
                type: 'generate_password',
                requestId: this.generateRequestId(),
                payload: { options },
                timestamp: Date.now(),
                source: 'browser-extension',
            });

            if (response.status === 'success') {
                return {
                    success: true,
                    data: response.data,
                };
            } else {
                throw new Error(response.error || '生成密码失败');
            }
        } catch (error) {
            console.error('生成密码失败:', error);
            return {
                success: false,
                error: error.message,
            };
        }
    }

    /**
     * 处理来自应用的消息
     */
    private handleNativeMessage(message: any): void {
        console.log('收到应用消息:', message);

        // 根据消息类型处理
        switch (message.type) {
            case 'credentials_updated':
                this.notifyCredentialsUpdated(message.payload);
                break;

            case 'sync_status':
                this.notifySyncStatus(message.payload);
                break;

            default:
                console.log('未处理的应用消息:', message.type);
        }
    }

    /**
     * 处理标签页更新
     */
    private async handleTabUpdate(tabId: number, url: string): Promise<void> {
        try {
            const domain = new URL(url).hostname;

            // 检查是否有该域名的凭证
            const credentials = await this.getCredentials(domain);

            if (credentials.success && credentials.data?.credentials?.length > 0) {
                // 显示页面动作图标
                chrome.action.setBadgeText({
                    tabId,
                    text: credentials.data.credentials.length.toString(),
                });
                chrome.action.setBadgeBackgroundColor({
                    tabId,
                    color: '#4CAF50',
                });
            } else {
                // 清除徽章
                chrome.action.setBadgeText({ tabId, text: '' });
            }
        } catch (error) {
            console.error('处理标签页更新失败:', error);
        }
    }

    /**
     * 通知连接状态
     */
    private notifyConnectionStatus(status: string): void {
        chrome.runtime.sendMessage({
            type: 'connection_status_changed',
            data: { status },
        }).catch(() => {
            // 忽略没有接收者的错误
        });
    }

    /**
     * 通知凭证更新
     */
    private notifyCredentialsUpdated(payload: any): void {
        chrome.runtime.sendMessage({
            type: 'credentials_updated',
            data: payload,
        }).catch(() => {
            // 忽略没有接收者的错误
        });
    }

    /**
     * 通知同步状态
     */
    private notifySyncStatus(payload: any): void {
        chrome.runtime.sendMessage({
            type: 'sync_status',
            data: payload,
        }).catch(() => {
            // 忽略没有接收者的错误
        });
    }

    /**
     * 生成请求 ID
     */
    private generateRequestId(): string {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}

// 启动后台服务
new BackgroundService();
```

## 🧪 测试用例模板

### 1. 守护进程单元测试 (src-tauri/daemon/tests/daemon_core_test.rs)

```rust
//! 守护进程核心功能测试

use std::time::Duration;
use tokio::time::timeout;
use tempfile::TempDir;
use serial_test::serial;

use secure_password_daemon::{
    daemon_core::SecurePasswordDaemon,
    config::{DaemonConfig, IpcConfig, SecurityConfig},
    error::DaemonError,
};

/// 测试辅助函数
mod test_helpers {
    use super::*;

    pub fn create_test_config(temp_dir: &TempDir) -> DaemonConfig {
        DaemonConfig {
            ipc_config: IpcConfig {
                bind_address: "127.0.0.1:0".to_string(), // 随机端口
                transport: crate::config::IpcTransportType::Auto,
                max_connections: 10,
                timeout: Duration::from_secs(30),
            },
            security_config: SecurityConfig {
                encryption_enabled: true,
                key_file: temp_dir.path().join("test.key"),
                audit_enabled: false,
            },
            // ... 其他配置字段
        }
    }

    pub async fn wait_for_state(
        daemon: &SecurePasswordDaemon,
        expected_state: crate::daemon_core::DaemonState,
        timeout_duration: Duration,
    ) -> Result<(), DaemonError> {
        let start = std::time::Instant::now();

        while start.elapsed() < timeout_duration {
            if daemon.get_state().await == expected_state {
                return Ok(());
            }
            tokio::time::sleep(Duration::from_millis(10)).await;
        }

        Err(DaemonError::Timeout(format!(
            "等待状态 {:?} 超时",
            expected_state
        )))
    }
}

#[tokio::test]
#[serial]
async fn test_daemon_lifecycle() -> Result<(), Box<dyn std::error::Error>> {
    // 创建临时目录
    let temp_dir = tempfile::tempdir()?;
    let config = test_helpers::create_test_config(&temp_dir);

    // 创建守护进程实例
    let mut daemon = SecurePasswordDaemon::new(config).await?;

    // 测试初始状态
    assert_eq!(
        daemon.get_state().await,
        crate::daemon_core::DaemonState::Initializing
    );

    // 启动守护进程
    daemon.start().await?;

    // 等待启动完成
    test_helpers::wait_for_state(
        &daemon,
        crate::daemon_core::DaemonState::Running,
        Duration::from_secs(10),
    ).await?;

    // 验证运行状态
    assert_eq!(
        daemon.get_state().await,
        crate::daemon_core::DaemonState::Running
    );

    // 关闭守护进程
    daemon.shutdown().await?;

    // 等待关闭完成
    test_helpers::wait_for_state(
        &daemon,
        crate::daemon_core::DaemonState::Stopped,
        Duration::from_secs(10),
    ).await?;

    // 验证停止状态
    assert_eq!(
        daemon.get_state().await,
        crate::daemon_core::DaemonState::Stopped
    );

    Ok(())
}

#[tokio::test]
#[serial]
async fn test_daemon_startup_failure() -> Result<(), Box<dyn std::error::Error>> {
    let temp_dir = tempfile::tempdir()?;
    let mut config = test_helpers::create_test_config(&temp_dir);

    // 设置无效的绑定地址
    config.ipc_config.bind_address = "invalid_address".to_string();

    let mut daemon = SecurePasswordDaemon::new(config).await?;

    // 启动应该失败
    let result = daemon.start().await;
    assert!(result.is_err());

    // 验证错误状态
    match daemon.get_state().await {
        crate::daemon_core::DaemonState::Error(_) => {
            // 预期的错误状态
        }
        state => {
            panic!("期望错误状态，但得到: {:?}", state);
        }
    }

    Ok(())
}

#[tokio::test]
#[serial]
async fn test_daemon_graceful_shutdown() -> Result<(), Box<dyn std::error::Error>> {
    let temp_dir = tempfile::tempdir()?;
    let config = test_helpers::create_test_config(&temp_dir);

    let mut daemon = SecurePasswordDaemon::new(config).await?;
    daemon.start().await?;

    // 等待启动完成
    test_helpers::wait_for_state(
        &daemon,
        crate::daemon_core::DaemonState::Running,
        Duration::from_secs(10),
    ).await?;

    // 测试优雅关闭在超时内完成
    let shutdown_result = timeout(Duration::from_secs(5), daemon.shutdown()).await;

    assert!(shutdown_result.is_ok(), "守护进程关闭超时");
    assert!(shutdown_result.unwrap().is_ok(), "守护进程关闭失败");

    Ok(())
}
```

### 2. IPC 通信集成测试 (src-tauri/daemon/tests/ipc_integration_test.rs)

```rust
//! IPC 通信集成测试

use std::sync::Arc;
use std::time::Duration;
use tokio::sync::mpsc;
use serde_json::json;

use secure_password_daemon::{
    ipc::{IpcServer, IpcClient},
    config::IpcConfig,
    ipc::protocol::{IpcMessage, IpcMessageType, IpcResponse},
};

#[tokio::test]
async fn test_ipc_message_exchange() -> Result<(), Box<dyn std::error::Error>> {
    // 创建 IPC 配置
    let config = IpcConfig {
        bind_address: "127.0.0.1:0".to_string(),
        transport: crate::config::IpcTransportType::Auto,
        max_connections: 10,
        timeout: Duration::from_secs(30),
    };

    // 启动 IPC 服务器
    let mut server = IpcServer::new(config.clone()).await?;

    // 注册测试消息处理器
    server.register_handler(
        IpcMessageType::Custom("test_message".to_string()),
        Box::new(TestMessageHandler::new()),
    ).await?;

    server.start().await?;

    // 获取实际绑定的地址
    let server_address = server.get_bind_address().await?;

    // 创建客户端连接
    let mut client = IpcClient::connect(&server_address).await?;

    // 发送测试消息
    let test_message = IpcMessage {
        message_id: "test_001".to_string(),
        message_type: IpcMessageType::Custom("test_message".to_string()),
        payload: json!({
            "test_data": "hello world",
            "timestamp": chrono::Utc::now().timestamp()
        }),
        response_required: true,
        timestamp: chrono::Utc::now().timestamp_millis() as u64,
        source: "test_client".to_string(),
        priority: None,
        timeout: None,
        metadata: None,
    };

    // 发送消息并等待响应
    let response = client.send_message(test_message).await?;

    // 验证响应
    assert_eq!(response.status, crate::ipc::protocol::ResponseStatus::Success);
    assert_eq!(response.request_id, "test_001");

    // 验证响应数据
    let response_data = response.data.as_object().unwrap();
    assert_eq!(response_data["echo"], "hello world");
    assert!(response_data.contains_key("processed_at"));

    // 清理资源
    client.disconnect().await?;
    server.stop().await?;

    Ok(())
}

#[tokio::test]
async fn test_ipc_concurrent_connections() -> Result<(), Box<dyn std::error::Error>> {
    let config = IpcConfig {
        bind_address: "127.0.0.1:0".to_string(),
        transport: crate::config::IpcTransportType::Auto,
        max_connections: 5,
        timeout: Duration::from_secs(30),
    };

    let mut server = IpcServer::new(config.clone()).await?;

    // 注册处理器
    server.register_handler(
        IpcMessageType::Custom("concurrent_test".to_string()),
        Box::new(ConcurrentTestHandler::new()),
    ).await?;

    server.start().await?;
    let server_address = server.get_bind_address().await?;

    // 创建多个并发客户端
    let client_count = 3;
    let mut handles = Vec::new();

    for i in 0..client_count {
        let address = server_address.clone();
        let handle = tokio::spawn(async move {
            let mut client = IpcClient::connect(&address).await?;

            // 发送多条消息
            for j in 0..5 {
                let message = IpcMessage {
                    message_id: format!("client_{}_msg_{}", i, j),
                    message_type: IpcMessageType::Custom("concurrent_test".to_string()),
                    payload: json!({
                        "client_id": i,
                        "message_number": j
                    }),
                    response_required: true,
                    timestamp: chrono::Utc::now().timestamp_millis() as u64,
                    source: format!("test_client_{}", i),
                    priority: None,
                    timeout: None,
                    metadata: None,
                };

                let response = client.send_message(message).await?;
                assert_eq!(response.status, crate::ipc::protocol::ResponseStatus::Success);
            }

            client.disconnect().await?;
            Ok::<(), Box<dyn std::error::Error + Send + Sync>>(())
        });

        handles.push(handle);
    }

    // 等待所有客户端完成
    for handle in handles {
        handle.await??;
    }

    server.stop().await?;

    Ok(())
}

/// 测试消息处理器
struct TestMessageHandler;

impl TestMessageHandler {
    fn new() -> Self {
        Self
    }
}

#[async_trait::async_trait]
impl crate::ipc::handlers::IpcMessageHandler for TestMessageHandler {
    async fn handle(&self, message: IpcMessage) -> Result<serde_json::Value, crate::error::IpcError> {
        // 简单的回显处理器
        let test_data = message.payload
            .get("test_data")
            .and_then(|v| v.as_str())
            .unwrap_or("unknown");

        Ok(json!({
            "echo": test_data,
            "processed_at": chrono::Utc::now().timestamp(),
            "original_message_id": message.message_id
        }))
    }

    fn message_type(&self) -> IpcMessageType {
        IpcMessageType::Custom("test_message".to_string())
    }
}

/// 并发测试处理器
struct ConcurrentTestHandler;

impl ConcurrentTestHandler {
    fn new() -> Self {
        Self
    }
}

#[async_trait::async_trait]
impl crate::ipc::handlers::IpcMessageHandler for ConcurrentTestHandler {
    async fn handle(&self, message: IpcMessage) -> Result<serde_json::Value, crate::error::IpcError> {
        // 模拟一些处理时间
        tokio::time::sleep(Duration::from_millis(10)).await;

        let client_id = message.payload
            .get("client_id")
            .and_then(|v| v.as_u64())
            .unwrap_or(0);

        let message_number = message.payload
            .get("message_number")
            .and_then(|v| v.as_u64())
            .unwrap_or(0);

        Ok(json!({
            "client_id": client_id,
            "message_number": message_number,
            "processed_at": chrono::Utc::now().timestamp(),
            "thread_id": format!("{:?}", std::thread::current().id())
        }))
    }

    fn message_type(&self) -> IpcMessageType {
        IpcMessageType::Custom("concurrent_test".to_string())
    }
}
```

### 3. 性能基准测试 (benches/ipc_performance.rs)

```rust
//! IPC 性能基准测试

use criterion::{criterion_group, criterion_main, Criterion, BenchmarkId, Throughput};
use std::time::Duration;
use tokio::runtime::Runtime;
use serde_json::json;

use secure_password_daemon::{
    ipc::{IpcServer, IpcClient},
    config::IpcConfig,
    ipc::protocol::{IpcMessage, IpcMessageType},
};

/// 消息吞吐量基准测试
fn bench_message_throughput(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    // 设置测试服务器
    let (server_address, _server_handle) = rt.block_on(async {
        setup_test_server().await.unwrap()
    });

    let mut group = c.benchmark_group("ipc_throughput");

    for message_count in [100, 1000, 5000].iter() {
        group.throughput(Throughput::Elements(*message_count as u64));

        group.bench_with_input(
            BenchmarkId::new("sequential_messages", message_count),
            message_count,
            |b, &count| {
                b.to_async(&rt).iter(|| async {
                    let mut client = IpcClient::connect(&server_address).await.unwrap();

                    for i in 0..count {
                        let message = create_test_message(i);
                        let _response = client.send_message(message).await.unwrap();
                    }

                    client.disconnect().await.unwrap();
                });
            },
        );

        group.bench_with_input(
            BenchmarkId::new("concurrent_messages", message_count),
            message_count,
            |b, &count| {
                b.to_async(&rt).iter(|| async {
                    let mut handles = Vec::new();

                    for i in 0..count {
                        let address = server_address.clone();
                        let handle = tokio::spawn(async move {
                            let mut client = IpcClient::connect(&address).await.unwrap();
                            let message = create_test_message(i);
                            let _response = client.send_message(message).await.unwrap();
                            client.disconnect().await.unwrap();
                        });
                        handles.push(handle);
                    }

                    for handle in handles {
                        handle.await.unwrap();
                    }
                });
            },
        );
    }

    group.finish();
}

/// 消息延迟基准测试
fn bench_message_latency(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let (server_address, _server_handle) = rt.block_on(async {
        setup_test_server().await.unwrap()
    });

    let mut group = c.benchmark_group("ipc_latency");

    // 测试不同大小的消息
    for payload_size in [1024, 10240, 102400].iter() {  // 1KB, 10KB, 100KB
        group.bench_with_input(
            BenchmarkId::new("message_latency", payload_size),
            payload_size,
            |b, &size| {
                b.to_async(&rt).iter(|| async {
                    let mut client = IpcClient::connect(&server_address).await.unwrap();

                    let message = create_large_test_message(size);
                    let start = std::time::Instant::now();
                    let _response = client.send_message(message).await.unwrap();
                    let _duration = start.elapsed();

                    client.disconnect().await.unwrap();
                });
            },
        );
    }

    group.finish();
}

/// 连接建立性能测试
fn bench_connection_establishment(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let (server_address, _server_handle) = rt.block_on(async {
        setup_test_server().await.unwrap()
    });

    c.bench_function("connection_establishment", |b| {
        b.to_async(&rt).iter(|| async {
            let mut client = IpcClient::connect(&server_address).await.unwrap();
            client.disconnect().await.unwrap();
        });
    });
}

/// 设置测试服务器
async fn setup_test_server() -> Result<(String, tokio::task::JoinHandle<()>), Box<dyn std::error::Error>> {
    let config = IpcConfig {
        bind_address: "127.0.0.1:0".to_string(),
        transport: crate::config::IpcTransportType::Auto,
        max_connections: 100,
        timeout: Duration::from_secs(30),
    };

    let mut server = IpcServer::new(config).await?;

    // 注册基准测试处理器
    server.register_handler(
        IpcMessageType::Custom("benchmark_test".to_string()),
        Box::new(BenchmarkHandler::new()),
    ).await?;

    server.start().await?;
    let server_address = server.get_bind_address().await?;

    // 在后台运行服务器
    let server_handle = tokio::spawn(async move {
        // 服务器会一直运行直到被停止
        tokio::time::sleep(Duration::from_secs(3600)).await;
        let _ = server.stop().await;
    });

    Ok((server_address, server_handle))
}

/// 创建测试消息
fn create_test_message(id: usize) -> IpcMessage {
    IpcMessage {
        message_id: format!("bench_{}", id),
        message_type: IpcMessageType::Custom("benchmark_test".to_string()),
        payload: json!({
            "id": id,
            "data": "test_data",
            "timestamp": chrono::Utc::now().timestamp()
        }),
        response_required: true,
        timestamp: chrono::Utc::now().timestamp_millis() as u64,
        source: "benchmark_client".to_string(),
        priority: None,
        timeout: None,
        metadata: None,
    }
}

/// 创建大消息
fn create_large_test_message(size: usize) -> IpcMessage {
    let large_data = "x".repeat(size);

    IpcMessage {
        message_id: format!("large_bench_{}", size),
        message_type: IpcMessageType::Custom("benchmark_test".to_string()),
        payload: json!({
            "large_data": large_data,
            "size": size,
            "timestamp": chrono::Utc::now().timestamp()
        }),
        response_required: true,
        timestamp: chrono::Utc::now().timestamp_millis() as u64,
        source: "benchmark_client".to_string(),
        priority: None,
        timeout: None,
        metadata: None,
    }
}

/// 基准测试处理器
struct BenchmarkHandler;

impl BenchmarkHandler {
    fn new() -> Self {
        Self
    }
}

#[async_trait::async_trait]
impl crate::ipc::handlers::IpcMessageHandler for BenchmarkHandler {
    async fn handle(&self, message: IpcMessage) -> Result<serde_json::Value, crate::error::IpcError> {
        // 最小化处理时间
        Ok(json!({
            "echo": message.message_id,
            "processed_at": chrono::Utc::now().timestamp_millis()
        }))
    }

    fn message_type(&self) -> IpcMessageType {
        IpcMessageType::Custom("benchmark_test".to_string())
    }
}

criterion_group!(
    benches,
    bench_message_throughput,
    bench_message_latency,
    bench_connection_establishment
);
criterion_main!(benches);
```

## 🚨 错误处理模板

### 1. 统一错误类型定义 (src-tauri/daemon/src/error.rs)

```rust
//! 统一错误处理定义
//!
//! 提供整个系统的错误类型和处理机制

use std::fmt;
use thiserror::Error;
use serde::{Serialize, Deserialize};

/// 守护进程主要错误类型
#[derive(Error, Debug, Clone, Serialize, Deserialize)]
pub enum DaemonError {
    /// 配置错误
    #[error("配置错误: {message}")]
    ConfigError { message: String },

    /// IPC 通信错误
    #[error("IPC 通信错误: {0}")]
    IpcError(#[from] IpcError),

    /// Native Messaging 错误
    #[error("Native Messaging 错误: {0}")]
    NativeMessagingError(#[from] NativeMessagingError),

    /// 安全相关错误
    #[error("安全错误: {0}")]
    SecurityError(#[from] SecurityError),

    /// 数据库错误
    #[error("数据库错误: {0}")]
    DatabaseError(#[from] DatabaseError),

    /// 系统服务错误
    #[error("系统服务错误: {message}")]
    ServiceError { message: String },

    /// 网络错误
    #[error("网络错误: {message}")]
    NetworkError { message: String },

    /// 超时错误
    #[error("操作超时: {message}")]
    Timeout { message: String },

    /// 权限错误
    #[error("权限不足: {message}")]
    PermissionDenied { message: String },

    /// 资源不足错误
    #[error("资源不足: {message}")]
    ResourceExhausted { message: String },

    /// 内部错误
    #[error("内部错误: {message}")]
    Internal { message: String },

    /// 未知错误
    #[error("未知错误: {message}")]
    Unknown { message: String },
}

/// IPC 通信错误
#[derive(Error, Debug, Clone, Serialize, Deserialize)]
pub enum IpcError {
    #[error("连接失败: {message}")]
    ConnectionFailed { message: String },

    #[error("连接已断开")]
    ConnectionLost,

    #[error("消息序列化失败: {message}")]
    SerializationFailed { message: String },

    #[error("消息反序列化失败: {message}")]
    DeserializationFailed { message: String },

    #[error("发送消息失败: {message}")]
    SendFailed { message: String },

    #[error("接收消息失败: {message}")]
    ReceiveFailed { message: String },

    #[error("消息处理器未找到: {handler_type}")]
    HandlerNotFound { handler_type: String },

    #[error("客户端未找到: {client_id}")]
    ClientNotFound { client_id: String },

    #[error("协议版本不兼容: 期望 {expected}, 实际 {actual}")]
    ProtocolMismatch { expected: String, actual: String },

    #[error("消息格式无效: {message}")]
    InvalidMessageFormat { message: String },

    #[error("操作超时")]
    Timeout,
}

/// Native Messaging 错误
#[derive(Error, Debug, Clone, Serialize, Deserialize)]
pub enum NativeMessagingError {
    #[error("浏览器连接失败: {browser}")]
    BrowserConnectionFailed { browser: String },

    #[error("Host 注册失败: {browser}, 原因: {reason}")]
    HostRegistrationFailed { browser: String, reason: String },

    #[error("消息格式无效: {message}")]
    InvalidMessageFormat { message: String },

    #[error("不支持的消息类型: {message_type}")]
    UnsupportedMessageType { message_type: String },

    #[error("扩展权限不足: {extension_id}")]
    InsufficientPermissions { extension_id: String },

    #[error("标准输入/输出错误: {message}")]
    StdioError { message: String },
}

/// 安全错误
#[derive(Error, Debug, Clone, Serialize, Deserialize)]
pub enum SecurityError {
    #[error("加密失败: {message}")]
    EncryptionFailed { message: String },

    #[error("解密失败: {message}")]
    DecryptionFailed { message: String },

    #[error("密钥生成失败: {message}")]
    KeyGenerationFailed { message: String },

    #[error("密钥派生失败: {message}")]
    KeyDerivationFailed { message: String },

    #[error("签名验证失败")]
    SignatureVerificationFailed,

    #[error("证书无效: {message}")]
    InvalidCertificate { message: String },

    #[error("认证失败: {message}")]
    AuthenticationFailed { message: String },

    #[error("授权失败: {message}")]
    AuthorizationFailed { message: String },

    #[error("密码强度不足")]
    WeakPassword,

    #[error("密码已泄露")]
    PasswordBreached,
}

/// 数据库错误
#[derive(Error, Debug, Clone, Serialize, Deserialize)]
pub enum DatabaseError {
    #[error("连接失败: {message}")]
    ConnectionFailed { message: String },

    #[error("查询执行失败: {query}, 错误: {error}")]
    QueryFailed { query: String, error: String },

    #[error("事务失败: {message}")]
    TransactionFailed { message: String },

    #[error("数据迁移失败: {version}, 错误: {error}")]
    MigrationFailed { version: String, error: String },

    #[error("数据完整性错误: {message}")]
    IntegrityError { message: String },

    #[error("记录未找到: {table}, ID: {id}")]
    RecordNotFound { table: String, id: String },

    #[error("重复记录: {table}, 字段: {field}")]
    DuplicateRecord { table: String, field: String },

    #[error("数据库锁定: {message}")]
    DatabaseLocked { message: String },

    #[error("备份失败: {message}")]
    BackupFailed { message: String },

    #[error("恢复失败: {message}")]
    RestoreFailed { message: String },
}

/// 错误严重级别
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ErrorSeverity {
    /// 信息级别 - 不影响功能
    Info,
    /// 警告级别 - 可能影响性能
    Warning,
    /// 错误级别 - 影响部分功能
    Error,
    /// 严重级别 - 影响核心功能
    Critical,
    /// 致命级别 - 系统无法继续运行
    Fatal,
}

/// 错误上下文信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorContext {
    /// 错误发生的模块
    pub module: String,
    /// 错误发生的函数
    pub function: String,
    /// 错误发生的文件
    pub file: String,
    /// 错误发生的行号
    pub line: u32,
    /// 错误严重级别
    pub severity: ErrorSeverity,
    /// 错误发生时间
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// 相关的请求 ID (如果有)
    pub request_id: Option<String>,
    /// 用户 ID (如果有)
    pub user_id: Option<String>,
    /// 额外的上下文数据
    pub metadata: std::collections::HashMap<String, String>,
}

impl ErrorContext {
    /// 创建新的错误上下文
    pub fn new(module: &str, function: &str, severity: ErrorSeverity) -> Self {
        Self {
            module: module.to_string(),
            function: function.to_string(),
            file: file!().to_string(),
            line: line!(),
            severity,
            timestamp: chrono::Utc::now(),
            request_id: None,
            user_id: None,
            metadata: std::collections::HashMap::new(),
        }
    }

    /// 添加请求 ID
    pub fn with_request_id(mut self, request_id: String) -> Self {
        self.request_id = Some(request_id);
        self
    }

    /// 添加用户 ID
    pub fn with_user_id(mut self, user_id: String) -> Self {
        self.user_id = Some(user_id);
        self
    }

    /// 添加元数据
    pub fn with_metadata(mut self, key: String, value: String) -> Self {
        self.metadata.insert(key, value);
        self
    }
}

/// 结果类型别名
pub type DaemonResult<T> = Result<T, DaemonError>;
pub type IpcResult<T> = Result<T, IpcError>;
pub type SecurityResult<T> = Result<T, SecurityError>;
pub type DatabaseResult<T> = Result<T, DatabaseError>;

/// 错误处理宏
#[macro_export]
macro_rules! daemon_error {
    ($variant:ident, $message:expr) => {
        DaemonError::$variant {
            message: $message.to_string(),
        }
    };
    ($variant:ident, $message:expr, $($arg:expr),*) => {
        DaemonError::$variant {
            message: format!($message, $($arg),*),
        }
    };
}

/// 错误记录宏
#[macro_export]
macro_rules! log_error {
    ($error:expr, $context:expr) => {
        tracing::error!(
            error = %$error,
            module = $context.module,
            function = $context.function,
            severity = ?$context.severity,
            request_id = ?$context.request_id,
            "错误发生"
        );
    };
}

/// 错误转换实现
impl From<std::io::Error> for DaemonError {
    fn from(error: std::io::Error) -> Self {
        DaemonError::Internal {
            message: format!("IO 错误: {}", error),
        }
    }
}

impl From<serde_json::Error> for DaemonError {
    fn from(error: serde_json::Error) -> Self {
        DaemonError::Internal {
            message: format!("JSON 序列化错误: {}", error),
        }
    }
}

impl From<tokio::time::error::Elapsed> for DaemonError {
    fn from(_: tokio::time::error::Elapsed) -> Self {
        DaemonError::Timeout {
            message: "操作超时".to_string(),
        }
    }
}

/// 错误恢复策略
#[derive(Debug, Clone)]
pub enum RecoveryStrategy {
    /// 重试操作
    Retry {
        max_attempts: u32,
        delay: std::time::Duration,
    },
    /// 降级服务
    Fallback {
        fallback_action: String,
    },
    /// 忽略错误
    Ignore,
    /// 终止操作
    Abort,
    /// 重启服务
    Restart,
}

/// 错误处理器 trait
#[async_trait::async_trait]
pub trait ErrorHandler: Send + Sync {
    /// 处理错误
    async fn handle_error(
        &self,
        error: &DaemonError,
        context: &ErrorContext,
    ) -> RecoveryStrategy;

    /// 记录错误
    async fn log_error(&self, error: &DaemonError, context: &ErrorContext);

    /// 通知错误
    async fn notify_error(&self, error: &DaemonError, context: &ErrorContext);
}
```

### 2. 日志记录系统 (src-tauri/daemon/src/utils/logging.rs)

```rust
//! 结构化日志记录系统
//!
//! 提供统一的日志记录和监控功能

use std::path::PathBuf;
use tracing::{Level, Subscriber};
use tracing_subscriber::{
    layer::SubscriberExt,
    util::SubscriberInitExt,
    fmt,
    filter::{EnvFilter, LevelFilter},
    Registry,
};
use tracing_appender::{rolling, non_blocking};
use anyhow::Result;
use serde::{Serialize, Deserialize};

/// 日志配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    /// 日志级别
    pub level: LogLevel,
    /// 日志输出目标
    pub targets: Vec<LogTarget>,
    /// 日志格式
    pub format: LogFormat,
    /// 日志文件配置
    pub file_config: Option<FileLogConfig>,
    /// 结构化日志配置
    pub structured_config: Option<StructuredLogConfig>,
    /// 性能追踪配置
    pub tracing_config: Option<TracingConfig>,
}

/// 日志级别
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LogLevel {
    Trace,
    Debug,
    Info,
    Warn,
    Error,
}

/// 日志输出目标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LogTarget {
    /// 控制台输出
    Console,
    /// 文件输出
    File,
    /// 系统日志
    Syslog,
    /// 远程日志服务
    Remote { endpoint: String },
}

/// 日志格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LogFormat {
    /// 人类可读格式
    Pretty,
    /// JSON 格式
    Json,
    /// 紧凑格式
    Compact,
    /// 自定义格式
    Custom { pattern: String },
}

/// 文件日志配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileLogConfig {
    /// 日志目录
    pub directory: PathBuf,
    /// 文件名前缀
    pub filename_prefix: String,
    /// 轮转策略
    pub rotation: RotationStrategy,
    /// 最大文件大小 (MB)
    pub max_file_size: u64,
    /// 保留文件数量
    pub max_files: u32,
    /// 是否压缩旧文件
    pub compress_old_files: bool,
}

/// 轮转策略
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RotationStrategy {
    /// 按时间轮转
    Hourly,
    Daily,
    Weekly,
    /// 按大小轮转
    Size { max_size_mb: u64 },
    /// 不轮转
    Never,
}

/// 结构化日志配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StructuredLogConfig {
    /// 是否包含调用栈
    pub include_caller: bool,
    /// 是否包含线程信息
    pub include_thread: bool,
    /// 是否包含模块路径
    pub include_module_path: bool,
    /// 自定义字段
    pub custom_fields: std::collections::HashMap<String, String>,
}

/// 性能追踪配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TracingConfig {
    /// 是否启用性能追踪
    pub enabled: bool,
    /// 采样率 (0.0 - 1.0)
    pub sampling_rate: f64,
    /// 追踪输出目标
    pub output: TracingOutput,
}

/// 追踪输出目标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TracingOutput {
    /// 控制台输出
    Console,
    /// 文件输出
    File { path: PathBuf },
    /// Jaeger 追踪
    Jaeger { endpoint: String },
    /// OpenTelemetry
    OpenTelemetry { endpoint: String },
}

/// 初始化日志系统
pub fn init_logging() -> Result<()> {
    let config = load_logging_config()?;
    init_logging_with_config(config)
}

/// 使用指定配置初始化日志系统
pub fn init_logging_with_config(config: LoggingConfig) -> Result<()> {
    let mut layers = Vec::new();

    // 设置过滤器
    let filter = create_filter(&config)?;

    // 添加控制台输出层
    if config.targets.contains(&LogTarget::Console) {
        let console_layer = create_console_layer(&config)?;
        layers.push(console_layer.boxed());
    }

    // 添加文件输出层
    if config.targets.contains(&LogTarget::File) {
        if let Some(file_config) = &config.file_config {
            let file_layer = create_file_layer(file_config, &config)?;
            layers.push(file_layer.boxed());
        }
    }

    // 添加系统日志层
    if config.targets.contains(&LogTarget::Syslog) {
        let syslog_layer = create_syslog_layer(&config)?;
        layers.push(syslog_layer.boxed());
    }

    // 添加性能追踪层
    if let Some(tracing_config) = &config.tracing_config {
        if tracing_config.enabled {
            let tracing_layer = create_tracing_layer(tracing_config)?;
            layers.push(tracing_layer.boxed());
        }
    }

    // 初始化订阅器
    Registry::default()
        .with(filter)
        .with(layers)
        .init();

    tracing::info!("日志系统初始化完成");
    Ok(())
}

/// 创建过滤器
fn create_filter(config: &LoggingConfig) -> Result<EnvFilter> {
    let level = match config.level {
        LogLevel::Trace => LevelFilter::TRACE,
        LogLevel::Debug => LevelFilter::DEBUG,
        LogLevel::Info => LevelFilter::INFO,
        LogLevel::Warn => LevelFilter::WARN,
        LogLevel::Error => LevelFilter::ERROR,
    };

    let filter = EnvFilter::from_default_env()
        .add_directive(level.into())
        // 设置第三方库的日志级别
        .add_directive("hyper=warn".parse()?)
        .add_directive("tokio=info".parse()?)
        .add_directive("sqlx=warn".parse()?);

    Ok(filter)
}

/// 创建控制台输出层
fn create_console_layer(config: &LoggingConfig) -> Result<impl tracing_subscriber::Layer<Registry>> {
    let layer = match config.format {
        LogFormat::Pretty => {
            fmt::layer()
                .pretty()
                .with_target(true)
                .with_thread_ids(true)
                .with_thread_names(true)
        }
        LogFormat::Json => {
            fmt::layer()
                .json()
                .with_current_span(false)
                .with_span_list(true)
        }
        LogFormat::Compact => {
            fmt::layer()
                .compact()
                .with_target(false)
        }
        LogFormat::Custom { pattern: _ } => {
            // 实现自定义格式
            fmt::layer()
                .pretty()
                .with_target(true)
        }
    };

    Ok(layer)
}

/// 创建文件输出层
fn create_file_layer(
    file_config: &FileLogConfig,
    config: &LoggingConfig,
) -> Result<impl tracing_subscriber::Layer<Registry>> {
    // 创建日志目录
    std::fs::create_dir_all(&file_config.directory)?;

    // 创建文件追加器
    let file_appender = match file_config.rotation {
        RotationStrategy::Hourly => {
            rolling::hourly(&file_config.directory, &file_config.filename_prefix)
        }
        RotationStrategy::Daily => {
            rolling::daily(&file_config.directory, &file_config.filename_prefix)
        }
        RotationStrategy::Weekly => {
            rolling::daily(&file_config.directory, &file_config.filename_prefix) // 简化为每日
        }
        RotationStrategy::Size { max_size_mb: _ } => {
            // 实现基于大小的轮转
            rolling::daily(&file_config.directory, &file_config.filename_prefix)
        }
        RotationStrategy::Never => {
            rolling::never(&file_config.directory, &file_config.filename_prefix)
        }
    };

    let (non_blocking, _guard) = non_blocking(file_appender);

    let layer = match config.format {
        LogFormat::Json => {
            fmt::layer()
                .json()
                .with_writer(non_blocking)
                .with_ansi(false)
        }
        _ => {
            fmt::layer()
                .with_writer(non_blocking)
                .with_ansi(false)
        }
    };

    Ok(layer)
}

/// 创建系统日志层
fn create_syslog_layer(config: &LoggingConfig) -> Result<impl tracing_subscriber::Layer<Registry>> {
    // 这里实现系统日志集成
    // 为简化示例，返回一个空层
    Ok(fmt::layer().with_writer(std::io::sink()))
}

/// 创建性能追踪层
fn create_tracing_layer(
    tracing_config: &TracingConfig,
) -> Result<impl tracing_subscriber::Layer<Registry>> {
    // 这里实现性能追踪集成
    // 为简化示例，返回一个空层
    Ok(fmt::layer().with_writer(std::io::sink()))
}

/// 加载日志配置
fn load_logging_config() -> Result<LoggingConfig> {
    // 默认配置
    Ok(LoggingConfig {
        level: LogLevel::Info,
        targets: vec![LogTarget::Console, LogTarget::File],
        format: LogFormat::Pretty,
        file_config: Some(FileLogConfig {
            directory: PathBuf::from("logs"),
            filename_prefix: "daemon".to_string(),
            rotation: RotationStrategy::Daily,
            max_file_size: 100, // 100MB
            max_files: 30,
            compress_old_files: true,
        }),
        structured_config: Some(StructuredLogConfig {
            include_caller: true,
            include_thread: true,
            include_module_path: true,
            custom_fields: std::collections::HashMap::new(),
        }),
        tracing_config: Some(TracingConfig {
            enabled: true,
            sampling_rate: 0.1,
            output: TracingOutput::Console,
        }),
    })
}

/// 日志记录宏
#[macro_export]
macro_rules! log_with_context {
    ($level:ident, $context:expr, $message:expr) => {
        tracing::$level!(
            module = $context.module,
            function = $context.function,
            request_id = ?$context.request_id,
            user_id = ?$context.user_id,
            $message
        );
    };
    ($level:ident, $context:expr, $message:expr, $($arg:expr),*) => {
        tracing::$level!(
            module = $context.module,
            function = $context.function,
            request_id = ?$context.request_id,
            user_id = ?$context.user_id,
            $message,
            $($arg),*
        );
    };
}

/// 性能监控宏
#[macro_export]
macro_rules! measure_time {
    ($name:expr, $block:block) => {{
        let start = std::time::Instant::now();
        let result = $block;
        let duration = start.elapsed();
        tracing::info!(
            operation = $name,
            duration_ms = duration.as_millis(),
            "操作完成"
        );
        result
    }};
}
```

## ⚙️ 配置管理模板

### 1. 配置文件结构 (daemon.toml)

```toml
# Secure Password 守护进程配置文件
# 版本: 1.0.0

[daemon]
# 守护进程基本配置
name = "secure-password-daemon"
version = "1.0.0"
description = "企业级密码管理守护进程"

# 运行模式: development, production, testing
environment = "production"

# 进程配置
[daemon.process]
# 工作目录
working_directory = "/var/lib/secure-password"
# PID 文件路径
pid_file = "/var/run/secure-password-daemon.pid"
# 用户和组
user = "secure-password"
group = "secure-password"
# 文件权限掩码
umask = "0027"

# IPC 通信配置
[ipc]
# 绑定地址
bind_address = "127.0.0.1:9090"
# 传输类型: auto, tcp, unix_socket, named_pipe
transport = "auto"
# 最大连接数
max_connections = 100
# 连接超时时间 (秒)
timeout = 30
# 缓冲区大小 (字节)
buffer_size = 8192

# Unix Socket 配置 (Linux/macOS)
[ipc.unix_socket]
path = "/tmp/secure-password-daemon.sock"
permissions = "0600"

# Named Pipe 配置 (Windows)
[ipc.named_pipe]
name = "secure-password-daemon"
buffer_size = 8192

# Native Messaging 配置
[native_messaging]
# 是否启用
enabled = true
# Host 名称
host_name = "com.securepassword.daemon"
# 支持的浏览器
supported_browsers = ["chrome", "firefox", "edge", "safari"]
# 消息大小限制 (字节)
max_message_size = 1048576  # 1MB

# 浏览器特定配置
[native_messaging.chrome]
manifest_path = "/etc/opt/chrome/native-messaging-hosts/com.securepassword.daemon.json"
extension_ids = ["abcdefghijklmnopqrstuvwxyz123456"]

[native_messaging.firefox]
manifest_path = "/usr/lib/mozilla/native-messaging-hosts/com.securepassword.daemon.json"
extension_ids = ["<EMAIL>"]

[native_messaging.edge]
manifest_path = "/etc/opt/edge/native-messaging-hosts/com.securepassword.daemon.json"
extension_ids = ["abcdefghijklmnopqrstuvwxyz123456"]

# 应用管理配置
[app_management]
# 是否启用应用管理
enabled = true
# Tauri 应用路径
app_executable = "/usr/bin/secure-password"
# 启动模式: auto, manual, on_demand
startup_mode = "auto"
# 健康检查间隔 (秒)
health_check_interval = 60
# 重启策略
restart_policy = "on_failure"
# 最大重启次数
max_restart_attempts = 3

# 安全配置
[security]
# 是否启用加密
encryption_enabled = true
# 加密算法: aes256gcm, chacha20poly1305, xchacha20poly1305
encryption_algorithm = "aes256gcm"
# 密钥派生函数: pbkdf2, argon2id, scrypt
key_derivation_function = "argon2id"
# 密钥派生迭代次数
kdf_iterations = 100000
# 主密钥文件路径
master_key_file = "/etc/secure-password/master.key"
# 是否启用审计日志
audit_enabled = true
# 审计日志路径
audit_log_path = "/var/log/secure-password/audit.log"

# 数据库配置
[database]
# 数据库类型: sqlite, postgresql, mysql
type = "sqlite"
# 数据库文件路径 (SQLite)
path = "/var/lib/secure-password/database.db"
# 是否启用加密 (SQLCipher)
encryption_enabled = true
# 连接池大小
pool_size = 10
# 连接超时 (秒)
connection_timeout = 30
# 是否启用 WAL 模式
wal_mode = true

# PostgreSQL 配置 (可选)
[database.postgresql]
host = "localhost"
port = 5432
database = "secure_password"
username = "secure_password"
password_file = "/etc/secure-password/db_password"
ssl_mode = "require"

# 存储配置
[storage]
# 数据目录
data_directory = "/var/lib/secure-password/data"
# 备份目录
backup_directory = "/var/lib/secure-password/backups"
# 自动备份间隔 (小时)
auto_backup_interval = 24
# 备份保留天数
backup_retention_days = 30
# 是否压缩备份
compress_backups = true

# 日志配置
[logging]
# 日志级别: trace, debug, info, warn, error
level = "info"
# 输出目标: console, file, syslog
targets = ["file", "syslog"]
# 日志格式: pretty, json, compact
format = "json"

# 文件日志配置
[logging.file]
directory = "/var/log/secure-password"
filename_prefix = "daemon"
# 轮转策略: hourly, daily, weekly, size
rotation = "daily"
max_file_size_mb = 100
max_files = 30
compress_old_files = true

# 系统日志配置
[logging.syslog]
facility = "daemon"
tag = "secure-password-daemon"

# 监控配置
[monitoring]
# 是否启用监控
enabled = true
# 指标收集间隔 (秒)
metrics_interval = 60
# 健康检查端口
health_check_port = 9091
# Prometheus 指标端点
prometheus_endpoint = "/metrics"

# 性能配置
[performance]
# 工作线程数 (0 = CPU 核心数)
worker_threads = 0
# 线程栈大小 (字节)
thread_stack_size = 2097152  # 2MB
# 是否启用性能追踪
tracing_enabled = false
# 追踪采样率 (0.0 - 1.0)
tracing_sampling_rate = 0.1

# 系统服务配置
[service]
# 是否自动注册为系统服务
auto_register = true
# 服务显示名称
display_name = "Secure Password Daemon"
# 服务描述
description = "企业级密码管理守护进程服务"
# 启动类型: auto, manual, disabled
start_type = "auto"
# 依赖服务
dependencies = []

# Windows 服务配置
[service.windows]
service_type = "own_process"
error_control = "normal"
# 服务账户: local_system, local_service, network_service
account = "local_system"

# Linux systemd 配置
[service.linux]
# 服务类型: simple, forking, oneshot, notify
type = "simple"
# 重启策略: no, on-success, on-failure, on-abnormal, on-abort, always
restart = "on-failure"
# 重启延迟 (秒)
restart_sec = 5
# 用户
user = "secure-password"
# 组
group = "secure-password"

# macOS launchd 配置
[service.macos]
# 标签
label = "com.securepassword.daemon"
# 是否保持运行
keep_alive = true
# 运行时间间隔 (秒)
start_interval = 0

# 网络配置
[network]
# 连接超时 (秒)
connect_timeout = 10
# 读取超时 (秒)
read_timeout = 30
# 写入超时 (秒)
write_timeout = 30
# 是否启用 Keep-Alive
keep_alive = true
# Keep-Alive 间隔 (秒)
keep_alive_interval = 60

# TLS 配置
[network.tls]
# 是否启用 TLS
enabled = false
# 证书文件路径
cert_file = "/etc/secure-password/cert.pem"
# 私钥文件路径
key_file = "/etc/secure-password/key.pem"
# CA 证书文件路径
ca_file = "/etc/secure-password/ca.pem"
# TLS 版本: 1.2, 1.3
min_version = "1.2"

# 开发配置 (仅在 development 环境生效)
[development]
# 是否启用热重载
hot_reload = true
# 是否启用调试模式
debug_mode = true
# 是否启用详细日志
verbose_logging = true
# 测试数据目录
test_data_directory = "./test_data"
```

### 2. 配置加载器实现 (src-tauri/daemon/src/config/mod.rs)

```rust
//! 配置管理模块
//!
//! 提供配置文件加载、验证和管理功能

use std::path::{Path, PathBuf};
use std::time::Duration;
use serde::{Deserialize, Serialize};
use anyhow::{Result, Context};
use tracing::{info, warn, debug};

mod validation;
mod environment;
mod defaults;

pub use validation::ConfigValidator;
pub use environment::EnvironmentOverrides;

/// 主配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DaemonConfig {
    /// 守护进程配置
    pub daemon: DaemonSection,
    /// IPC 配置
    pub ipc: IpcConfig,
    /// Native Messaging 配置
    pub native_messaging: NativeMessagingConfig,
    /// 应用管理配置
    pub app_management: AppManagementConfig,
    /// 安全配置
    pub security: SecurityConfig,
    /// 数据库配置
    pub database: DatabaseConfig,
    /// 存储配置
    pub storage: StorageConfig,
    /// 日志配置
    pub logging: LoggingConfig,
    /// 监控配置
    pub monitoring: MonitoringConfig,
    /// 性能配置
    pub performance: PerformanceConfig,
    /// 系统服务配置
    pub service: ServiceConfig,
    /// 网络配置
    pub network: NetworkConfig,
    /// 开发配置 (可选)
    pub development: Option<DevelopmentConfig>,
}

/// 守护进程基本配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DaemonSection {
    pub name: String,
    pub version: String,
    pub description: String,
    pub environment: Environment,
    pub process: ProcessConfig,
}

/// 运行环境
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum Environment {
    Development,
    Production,
    Testing,
}

/// 进程配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessConfig {
    pub working_directory: PathBuf,
    pub pid_file: PathBuf,
    pub user: Option<String>,
    pub group: Option<String>,
    pub umask: Option<String>,
}

/// IPC 配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IpcConfig {
    pub bind_address: String,
    pub transport: IpcTransportType,
    pub max_connections: u32,
    pub timeout: Duration,
    pub buffer_size: usize,
    pub unix_socket: Option<UnixSocketConfig>,
    pub named_pipe: Option<NamedPipeConfig>,
}

/// IPC 传输类型
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum IpcTransportType {
    Auto,
    Tcp { port: u16 },
    UnixSocket { path: PathBuf },
    NamedPipe { name: String },
}

/// Unix Socket 配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UnixSocketConfig {
    pub path: PathBuf,
    pub permissions: String,
}

/// Named Pipe 配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NamedPipeConfig {
    pub name: String,
    pub buffer_size: usize,
}

impl DaemonConfig {
    /// 从文件加载配置
    pub async fn load_from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let path = path.as_ref();
        info!("加载配置文件: {}", path.display());

        // 读取配置文件
        let content = tokio::fs::read_to_string(path)
            .await
            .with_context(|| format!("无法读取配置文件: {}", path.display()))?;

        // 解析 TOML
        let mut config: DaemonConfig = toml::from_str(&content)
            .with_context(|| format!("配置文件格式错误: {}", path.display()))?;

        // 应用环境变量覆盖
        EnvironmentOverrides::apply(&mut config)?;

        // 应用默认值
        defaults::apply_defaults(&mut config)?;

        debug!("配置加载完成: {:?}", config);
        Ok(config)
    }

    /// 从默认位置加载配置
    pub async fn load_default() -> Result<Self> {
        let config_paths = [
            "./daemon.toml",
            "/etc/secure-password/daemon.toml",
            "/usr/local/etc/secure-password/daemon.toml",
            "~/.config/secure-password/daemon.toml",
        ];

        for path in &config_paths {
            let expanded_path = shellexpand::tilde(path);
            let path = Path::new(expanded_path.as_ref());

            if path.exists() {
                return Self::load_from_file(path).await;
            }
        }

        warn!("未找到配置文件，使用默认配置");
        Ok(Self::default())
    }

    /// 验证配置
    pub fn validate(&self) -> Result<()> {
        let validator = ConfigValidator::new();
        validator.validate(self)
    }

    /// 保存配置到文件
    pub async fn save_to_file<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        let path = path.as_ref();

        // 序列化为 TOML
        let content = toml::to_string_pretty(self)
            .context("配置序列化失败")?;

        // 创建目录
        if let Some(parent) = path.parent() {
            tokio::fs::create_dir_all(parent)
                .await
                .with_context(|| format!("无法创建目录: {}", parent.display()))?;
        }

        // 写入文件
        tokio::fs::write(path, content)
            .await
            .with_context(|| format!("无法写入配置文件: {}", path.display()))?;

        info!("配置已保存到: {}", path.display());
        Ok(())
    }

    /// 获取运行环境
    pub fn environment(&self) -> &Environment {
        &self.daemon.environment
    }

    /// 是否为开发环境
    pub fn is_development(&self) -> bool {
        self.daemon.environment == Environment::Development
    }

    /// 是否为生产环境
    pub fn is_production(&self) -> bool {
        self.daemon.environment == Environment::Production
    }

    /// 获取数据目录
    pub fn data_directory(&self) -> &PathBuf {
        &self.storage.data_directory
    }

    /// 获取日志目录
    pub fn log_directory(&self) -> Option<&PathBuf> {
        self.logging.file.as_ref().map(|f| &f.directory)
    }

    /// 合并配置
    pub fn merge(&mut self, other: DaemonConfig) -> Result<()> {
        // 实现配置合并逻辑
        // 这里简化为直接替换
        *self = other;
        Ok(())
    }

    /// 克隆配置用于修改
    pub fn clone_for_modification(&self) -> DaemonConfig {
        self.clone()
    }
}

/// 实现默认配置
impl Default for DaemonConfig {
    fn default() -> Self {
        Self {
            daemon: DaemonSection {
                name: "secure-password-daemon".to_string(),
                version: env!("CARGO_PKG_VERSION").to_string(),
                description: "企业级密码管理守护进程".to_string(),
                environment: Environment::Development,
                process: ProcessConfig {
                    working_directory: PathBuf::from("."),
                    pid_file: PathBuf::from("daemon.pid"),
                    user: None,
                    group: None,
                    umask: Some("0027".to_string()),
                },
            },
            ipc: IpcConfig {
                bind_address: "127.0.0.1:9090".to_string(),
                transport: IpcTransportType::Auto,
                max_connections: 100,
                timeout: Duration::from_secs(30),
                buffer_size: 8192,
                unix_socket: Some(UnixSocketConfig {
                    path: PathBuf::from("/tmp/secure-password-daemon.sock"),
                    permissions: "0600".to_string(),
                }),
                named_pipe: Some(NamedPipeConfig {
                    name: "secure-password-daemon".to_string(),
                    buffer_size: 8192,
                }),
            },
            // ... 其他字段的默认值
            native_messaging: defaults::default_native_messaging_config(),
            app_management: defaults::default_app_management_config(),
            security: defaults::default_security_config(),
            database: defaults::default_database_config(),
            storage: defaults::default_storage_config(),
            logging: defaults::default_logging_config(),
            monitoring: defaults::default_monitoring_config(),
            performance: defaults::default_performance_config(),
            service: defaults::default_service_config(),
            network: defaults::default_network_config(),
            development: None,
        }
    }
}

/// 配置热重载
pub struct ConfigWatcher {
    config_path: PathBuf,
    current_config: DaemonConfig,
    reload_callback: Box<dyn Fn(DaemonConfig) -> Result<()> + Send + Sync>,
}

impl ConfigWatcher {
    /// 创建配置监视器
    pub fn new<P: AsRef<Path>, F>(
        config_path: P,
        initial_config: DaemonConfig,
        reload_callback: F,
    ) -> Self
    where
        F: Fn(DaemonConfig) -> Result<()> + Send + Sync + 'static,
    {
        Self {
            config_path: config_path.as_ref().to_path_buf(),
            current_config: initial_config,
            reload_callback: Box::new(reload_callback),
        }
    }

    /// 启动配置监视
    pub async fn start_watching(&mut self) -> Result<()> {
        use tokio::time::{interval, Duration};

        let mut interval = interval(Duration::from_secs(5));

        loop {
            interval.tick().await;

            if let Err(e) = self.check_and_reload().await {
                warn!("配置重载检查失败: {}", e);
            }
        }
    }

    /// 检查并重载配置
    async fn check_and_reload(&mut self) -> Result<()> {
        // 检查文件修改时间
        let metadata = tokio::fs::metadata(&self.config_path).await?;
        let modified = metadata.modified()?;

        // 这里简化实现，实际应该记录上次修改时间
        // 如果文件被修改，重新加载配置
        match DaemonConfig::load_from_file(&self.config_path).await {
            Ok(new_config) => {
                if !self.configs_equal(&self.current_config, &new_config) {
                    info!("检测到配置变更，重新加载配置");

                    // 验证新配置
                    if let Err(e) = new_config.validate() {
                        warn!("新配置验证失败: {}", e);
                        return Ok(());
                    }

                    // 调用重载回调
                    (self.reload_callback)(new_config.clone())?;

                    self.current_config = new_config;
                    info!("配置重载完成");
                }
            }
            Err(e) => {
                warn!("配置文件加载失败: {}", e);
            }
        }

        Ok(())
    }

    /// 比较配置是否相等
    fn configs_equal(&self, config1: &DaemonConfig, config2: &DaemonConfig) -> bool {
        // 简化实现，实际应该比较关键字段
        format!("{:?}", config1) == format!("{:?}", config2)
    }
}
```

## 🚀 部署脚本模板

### 1. 自动化部署脚本 (scripts/deploy.sh)

```bash
#!/bin/bash
# Secure Password 守护进程部署脚本
# 支持 Linux、macOS 和 Windows (WSL)

set -euo pipefail

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BUILD_DIR="$PROJECT_ROOT/target/release"
INSTALL_PREFIX="/usr/local"
SERVICE_USER="secure-password"
SERVICE_GROUP="secure-password"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查运行权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要 root 权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 检测操作系统
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        if command -v systemctl >/dev/null 2>&1; then
            INIT_SYSTEM="systemd"
        elif command -v service >/dev/null 2>&1; then
            INIT_SYSTEM="sysv"
        else
            log_error "不支持的 init 系统"
            exit 1
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        INIT_SYSTEM="launchd"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
        OS="windows"
        INIT_SYSTEM="windows_service"
    else
        log_error "不支持的操作系统: $OSTYPE"
        exit 1
    fi

    log_info "检测到操作系统: $OS ($INIT_SYSTEM)"
}

# 创建系统用户
create_system_user() {
    log_info "创建系统用户和组..."

    if [[ "$OS" == "linux" ]]; then
        # 创建组
        if ! getent group "$SERVICE_GROUP" >/dev/null 2>&1; then
            groupadd --system "$SERVICE_GROUP"
            log_success "创建组: $SERVICE_GROUP"
        fi

        # 创建用户
        if ! getent passwd "$SERVICE_USER" >/dev/null 2>&1; then
            useradd --system --gid "$SERVICE_GROUP" \
                    --home-dir "/var/lib/$SERVICE_USER" \
                    --shell /bin/false \
                    --comment "Secure Password Daemon" \
                    "$SERVICE_USER"
            log_success "创建用户: $SERVICE_USER"
        fi
    elif [[ "$OS" == "macos" ]]; then
        # macOS 用户创建逻辑
        if ! dscl . -read "/Users/<USER>" >/dev/null 2>&1; then
            # 查找可用的 UID
            local uid=$(dscl . -list /Users UniqueID | awk '{print $2}' | sort -n | tail -1)
            uid=$((uid + 1))

            # 创建用户
            dscl . -create "/Users/<USER>"
            dscl . -create "/Users/<USER>" UserShell /usr/bin/false
            dscl . -create "/Users/<USER>" RealName "Secure Password Daemon"
            dscl . -create "/Users/<USER>" UniqueID "$uid"
            dscl . -create "/Users/<USER>" PrimaryGroupID 20
            dscl . -create "/Users/<USER>" NFSHomeDirectory "/var/lib/$SERVICE_USER"

            log_success "创建用户: $SERVICE_USER"
        fi
    fi
}

# 创建目录结构
create_directories() {
    log_info "创建目录结构..."

    local directories=(
        "/etc/secure-password"
        "/var/lib/secure-password"
        "/var/log/secure-password"
        "/var/run/secure-password"
        "$INSTALL_PREFIX/bin"
        "$INSTALL_PREFIX/share/secure-password"
    )

    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
        log_success "创建目录: $dir"
    done

    # 设置目录权限
    chown -R "$SERVICE_USER:$SERVICE_GROUP" "/var/lib/secure-password"
    chown -R "$SERVICE_USER:$SERVICE_GROUP" "/var/log/secure-password"
    chown -R "$SERVICE_USER:$SERVICE_GROUP" "/var/run/secure-password"

    chmod 750 "/var/lib/secure-password"
    chmod 750 "/var/log/secure-password"
    chmod 755 "/var/run/secure-password"
    chmod 755 "/etc/secure-password"
}

# 安装二进制文件
install_binaries() {
    log_info "安装二进制文件..."

    # 检查构建产物
    if [[ ! -f "$BUILD_DIR/secure-password-daemon" ]]; then
        log_error "未找到守护进程二进制文件: $BUILD_DIR/secure-password-daemon"
        log_info "请先运行: cargo build --release"
        exit 1
    fi

    if [[ ! -f "$BUILD_DIR/secure-password" ]]; then
        log_error "未找到主应用二进制文件: $BUILD_DIR/secure-password"
        log_info "请先运行: cargo tauri build"
        exit 1
    fi

    # 复制二进制文件
    cp "$BUILD_DIR/secure-password-daemon" "$INSTALL_PREFIX/bin/"
    cp "$BUILD_DIR/secure-password" "$INSTALL_PREFIX/bin/"

    # 设置权限
    chmod 755 "$INSTALL_PREFIX/bin/secure-password-daemon"
    chmod 755 "$INSTALL_PREFIX/bin/secure-password"

    log_success "安装二进制文件完成"
}

# 安装配置文件
install_config() {
    log_info "安装配置文件..."

    # 复制默认配置
    if [[ -f "$PROJECT_ROOT/daemon.toml.example" ]]; then
        cp "$PROJECT_ROOT/daemon.toml.example" "/etc/secure-password/daemon.toml"
        log_success "安装配置文件: /etc/secure-password/daemon.toml"
    else
        log_warning "未找到示例配置文件，将创建默认配置"
        create_default_config
    fi

    # 设置配置文件权限
    chown root:root "/etc/secure-password/daemon.toml"
    chmod 644 "/etc/secure-password/daemon.toml"
}

# 创建默认配置
create_default_config() {
    cat > "/etc/secure-password/daemon.toml" << 'EOF'
[daemon]
name = "secure-password-daemon"
version = "1.0.0"
description = "企业级密码管理守护进程"
environment = "production"

[daemon.process]
working_directory = "/var/lib/secure-password"
pid_file = "/var/run/secure-password/daemon.pid"
user = "secure-password"
group = "secure-password"
umask = "0027"

[ipc]
bind_address = "127.0.0.1:9090"
transport = "auto"
max_connections = 100
timeout = 30
buffer_size = 8192

[logging]
level = "info"
targets = ["file", "syslog"]
format = "json"

[logging.file]
directory = "/var/log/secure-password"
filename_prefix = "daemon"
rotation = "daily"
max_file_size_mb = 100
max_files = 30
compress_old_files = true

[service]
auto_register = true
display_name = "Secure Password Daemon"
description = "企业级密码管理守护进程服务"
start_type = "auto"
EOF

    log_success "创建默认配置文件"
}

# 安装系统服务
install_service() {
    log_info "安装系统服务..."

    case "$INIT_SYSTEM" in
        "systemd")
            install_systemd_service
            ;;
        "launchd")
            install_launchd_service
            ;;
        "sysv")
            install_sysv_service
            ;;
        *)
            log_error "不支持的 init 系统: $INIT_SYSTEM"
            exit 1
            ;;
    esac
}

# 安装 systemd 服务
install_systemd_service() {
    cat > "/etc/systemd/system/secure-password-daemon.service" << EOF
[Unit]
Description=Secure Password Daemon
Documentation=https://github.com/your-org/secure-password
After=network.target
Wants=network.target

[Service]
Type=simple
User=$SERVICE_USER
Group=$SERVICE_GROUP
ExecStart=$INSTALL_PREFIX/bin/secure-password-daemon --config /etc/secure-password/daemon.toml
ExecReload=/bin/kill -HUP \$MAINPID
Restart=on-failure
RestartSec=5
TimeoutStopSec=30
KillMode=process
PIDFile=/var/run/secure-password/daemon.pid

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/lib/secure-password /var/log/secure-password /var/run/secure-password
CapabilityBoundingSet=CAP_NET_BIND_SERVICE
AmbientCapabilities=CAP_NET_BIND_SERVICE

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
EOF

    # 重新加载 systemd
    systemctl daemon-reload
    systemctl enable secure-password-daemon.service

    log_success "安装 systemd 服务完成"
}

# 安装 launchd 服务 (macOS)
install_launchd_service() {
    cat > "/Library/LaunchDaemons/com.securepassword.daemon.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.securepassword.daemon</string>
    <key>ProgramArguments</key>
    <array>
        <string>$INSTALL_PREFIX/bin/secure-password-daemon</string>
        <string>--config</string>
        <string>/etc/secure-password/daemon.toml</string>
    </array>
    <key>UserName</key>
    <string>$SERVICE_USER</string>
    <key>GroupName</key>
    <string>$SERVICE_GROUP</string>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
    <key>StandardOutPath</key>
    <string>/var/log/secure-password/daemon.log</string>
    <key>StandardErrorPath</key>
    <string>/var/log/secure-password/daemon.error.log</string>
    <key>WorkingDirectory</key>
    <string>/var/lib/secure-password</string>
</dict>
</plist>
EOF

    # 设置权限
    chown root:wheel "/Library/LaunchDaemons/com.securepassword.daemon.plist"
    chmod 644 "/Library/LaunchDaemons/com.securepassword.daemon.plist"

    # 加载服务
    launchctl load "/Library/LaunchDaemons/com.securepassword.daemon.plist"

    log_success "安装 launchd 服务完成"
}

# 安装 Native Messaging Host 配置
install_native_messaging() {
    log_info "安装 Native Messaging Host 配置..."

    # Chrome
    install_chrome_native_messaging

    # Firefox
    install_firefox_native_messaging

    # Edge (Linux)
    if [[ "$OS" == "linux" ]]; then
        install_edge_native_messaging
    fi

    log_success "Native Messaging Host 配置安装完成"
}

# 安装 Chrome Native Messaging
install_chrome_native_messaging() {
    local chrome_dir

    case "$OS" in
        "linux")
            chrome_dir="/etc/opt/chrome/native-messaging-hosts"
            ;;
        "macos")
            chrome_dir="/Library/Google/Chrome/NativeMessagingHosts"
            ;;
        *)
            log_warning "不支持的操作系统: $OS"
            return
            ;;
    esac

    mkdir -p "$chrome_dir"

    cat > "$chrome_dir/com.securepassword.daemon.json" << EOF
{
    "name": "com.securepassword.daemon",
    "description": "Secure Password Daemon Native Messaging Host",
    "path": "$INSTALL_PREFIX/bin/secure-password-daemon",
    "type": "stdio",
    "allowed_origins": [
        "chrome-extension://abcdefghijklmnopqrstuvwxyz123456/"
    ]
}
EOF

    log_success "安装 Chrome Native Messaging Host"
}

# 安装 Firefox Native Messaging
install_firefox_native_messaging() {
    local firefox_dir

    case "$OS" in
        "linux")
            firefox_dir="/usr/lib/mozilla/native-messaging-hosts"
            ;;
        "macos")
            firefox_dir="/Library/Application Support/Mozilla/NativeMessagingHosts"
            ;;
        *)
            log_warning "不支持的操作系统: $OS"
            return
            ;;
    esac

    mkdir -p "$firefox_dir"

    cat > "$firefox_dir/com.securepassword.daemon.json" << EOF
{
    "name": "com.securepassword.daemon",
    "description": "Secure Password Daemon Native Messaging Host",
    "path": "$INSTALL_PREFIX/bin/secure-password-daemon",
    "type": "stdio",
    "allowed_extensions": [
        "<EMAIL>"
    ]
}
EOF

    log_success "安装 Firefox Native Messaging Host"
}

# 启动服务
start_service() {
    log_info "启动服务..."

    case "$INIT_SYSTEM" in
        "systemd")
            systemctl start secure-password-daemon.service
            systemctl status secure-password-daemon.service --no-pager
            ;;
        "launchd")
            launchctl start com.securepassword.daemon
            ;;
        "sysv")
            service secure-password-daemon start
            ;;
    esac

    log_success "服务启动完成"
}

# 验证安装
verify_installation() {
    log_info "验证安装..."

    # 检查二进制文件
    if [[ -x "$INSTALL_PREFIX/bin/secure-password-daemon" ]]; then
        log_success "守护进程二进制文件: ✓"
    else
        log_error "守护进程二进制文件: ✗"
    fi

    # 检查配置文件
    if [[ -f "/etc/secure-password/daemon.toml" ]]; then
        log_success "配置文件: ✓"
    else
        log_error "配置文件: ✗"
    fi

    # 检查服务状态
    case "$INIT_SYSTEM" in
        "systemd")
            if systemctl is-active --quiet secure-password-daemon.service; then
                log_success "服务状态: ✓ (运行中)"
            else
                log_warning "服务状态: ✗ (未运行)"
            fi
            ;;
        "launchd")
            if launchctl list | grep -q com.securepassword.daemon; then
                log_success "服务状态: ✓ (已加载)"
            else
                log_warning "服务状态: ✗ (未加载)"
            fi
            ;;
    esac

    # 检查端口监听
    if netstat -ln | grep -q ":9090"; then
        log_success "IPC 端口监听: ✓"
    else
        log_warning "IPC 端口监听: ✗"
    fi
}

# 主函数
main() {
    log_info "开始部署 Secure Password 守护进程..."

    # 检查权限
    check_permissions

    # 检测操作系统
    detect_os

    # 创建系统用户
    create_system_user

    # 创建目录结构
    create_directories

    # 安装二进制文件
    install_binaries

    # 安装配置文件
    install_config

    # 安装系统服务
    install_service

    # 安装 Native Messaging Host
    install_native_messaging

    # 启动服务
    start_service

    # 验证安装
    verify_installation

    log_success "部署完成！"
    log_info "服务管理命令:"

    case "$INIT_SYSTEM" in
        "systemd")
            echo "  启动: sudo systemctl start secure-password-daemon"
            echo "  停止: sudo systemctl stop secure-password-daemon"
            echo "  重启: sudo systemctl restart secure-password-daemon"
            echo "  状态: sudo systemctl status secure-password-daemon"
            echo "  日志: sudo journalctl -u secure-password-daemon -f"
            ;;
        "launchd")
            echo "  启动: sudo launchctl start com.securepassword.daemon"
            echo "  停止: sudo launchctl stop com.securepassword.daemon"
            echo "  状态: sudo launchctl list | grep securepassword"
            echo "  日志: tail -f /var/log/secure-password/daemon.log"
            ;;
    esac
}

# 运行主函数
main "$@"
```

## 📋 总结

本文档提供了 Native Messaging 企业级独立守护进程系统的完整代码示例和模板，涵盖了：

### 🏗️ 核心组件模板
- **守护进程核心**: 完整的生命周期管理和服务协调
- **IPC 通信引擎**: 高性能的进程间通信实现
- **Native Messaging Host**: 标准化的浏览器扩展通信
- **Tauri 主应用**: 现代化的桌面应用界面

### 🧪 测试和质量保证
- **单元测试**: 核心功能的全面测试覆盖
- **集成测试**: 跨组件的端到端测试
- **性能基准**: 系统性能的量化评估
- **错误处理**: 统一的错误管理和恢复机制

### 📊 监控和日志
- **结构化日志**: 企业级的日志记录系统
- **性能监控**: 实时的系统状态监控
- **错误追踪**: 完整的错误上下文和恢复策略

### ⚙️ 配置和部署
- **配置管理**: 灵活的配置文件系统
- **自动化部署**: 跨平台的部署脚本
- **系统服务**: 原生的系统服务集成

### 🔗 文档关联
这些代码模板与以下文档紧密配合：
- [API设计规范](./API_DESIGN_SPECIFICATION.md) - 提供接口定义
- [技术栈选型指南](./TECHNOLOGY_STACK_GUIDE.md) - 确定技术选择
- [开发环境设置指南](./DEVELOPMENT_ENVIRONMENT_SETUP.md) - 配置开发环境
- [增量开发路线图](./NATIVE_MESSAGING_INCREMENTAL_ROADMAP.md) - 指导开发进度

### 🚀 使用建议
1. **从模板开始**: 使用提供的模板作为起点，根据具体需求进行调整
2. **渐进式开发**: 按照路线图逐步实现各个模块
3. **测试驱动**: 先编写测试用例，再实现功能
4. **持续集成**: 建立自动化的构建和测试流程
5. **监控优先**: 从一开始就集成日志和监控功能

这些模板提供了生产就绪的代码基础，可以大大加速开发进程并确保代码质量。
