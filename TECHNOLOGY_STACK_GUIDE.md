# 技术栈选型指南

## 文档概述

本文档详细说明了 Native Messaging 企业级独立守护进程系统的技术栈选择，包括具体版本号、选择理由、兼容性矩阵和替代方案。

**相关文档**:
- [API设计规范](./API_DESIGN_SPECIFICATION.md)
- [增量开发路线图](./NATIVE_MESSAGING_INCREMENTAL_ROADMAP.md)
- [跨平台实施计划](./NATIVE_MESSAGING_CROSS_PLATFORM_IMPLEMENTATION_PLAN.md)
- [开发环境设置指南](./DEVELOPMENT_ENVIRONMENT_SETUP.md)

## 🏗️ 技术架构概览

```
┌─────────────────────────────────────────────────────────────────┐
│                        技术栈分层架构                            │
├─────────────────────────────────────────────────────────────────┤
│  🌐 前端层 (Browser Extensions)                                │
│  ├─ TypeScript 5.3+                                            │
│  ├─ WebExtension APIs                                          │
│  ├─ React 18.2+ / Vue 3.4+ (可选)                             │
│  └─ Webpack 5.89+ / Vite 5.0+                                 │
├─────────────────────────────────────────────────────────────────┤
│  🔧 守护进程层 (Daemon Service)                                │
│  ├─ Rust 1.75+ (2024 Edition)                                 │
│  ├─ Tokio 1.35+ (异步运行时)                                   │
│  ├─ Serde 1.0+ (序列化)                                        │
│  └─ 平台特定库 (Windows/macOS/Linux)                           │
├─────────────────────────────────────────────────────────────────┤
│  🎨 应用层 (Tauri Application)                                 │
│  ├─ Tauri 2.0+ (应用框架)                                      │
│  ├─ Rust 1.75+ (后端)                                          │
│  ├─ React 18.2+ / Vue 3.4+ (前端)                             │
│  └─ TypeScript 5.3+ (类型安全)                                 │
├─────────────────────────────────────────────────────────────────┤
│  💾 数据层 (Storage & Database)                                │
│  ├─ SQLite 3.44+ (本地数据库)                                  │
│  ├─ SeaORM 0.12+ (ORM框架)                                     │
│  ├─ SQLCipher 4.5+ (数据库加密)                                │
│  └─ Tauri Stronghold 2.0+ (密钥管理)                          │
├─────────────────────────────────────────────────────────────────┤
│  🔐 安全层 (Security & Encryption)                             │
│  ├─ Ring 0.17+ (加密库)                                        │
│  ├─ Argon2 0.5+ (密码哈希)                                     │
│  ├─ X25519-Dalek 2.0+ (密钥交换)                              │
│  └─ AES-GCM 0.10+ (对称加密)                                   │
└─────────────────────────────────────────────────────────────────┘
```

## 🦀 Rust 生态系统

### 核心运行时

#### Rust 编译器
- **版本**: `1.75.0+` (2024 Edition)
- **选择理由**:
  - 内存安全和线程安全保证
  - 零成本抽象和高性能
  - 优秀的跨平台支持
  - 活跃的生态系统
- **兼容性**: 支持 Windows 10+, macOS 10.15+, Linux (glibc 2.17+)
- **替代方案**: Go 1.21+, C++ 20

#### Tokio 异步运行时
- **版本**: `tokio = "1.35"`
- **功能特性**: `["full", "tracing", "macros"]`
- **选择理由**:
  - 成熟的异步运行时
  - 优秀的性能和可扩展性
  - 丰富的生态系统
  - 内置的追踪和调试支持
- **配置示例**:
```toml
[dependencies]
tokio = { version = "1.35", features = ["full", "tracing", "macros"] }
tokio-util = { version = "0.7", features = ["codec", "net"] }
tokio-stream = "0.1"
```

### 序列化和数据处理

#### Serde 序列化框架
- **版本**: `serde = "1.0"`
- **相关库**:
  - `serde_json = "1.0"` - JSON 支持
  - `toml = "0.8"` - TOML 配置文件
  - `bincode = "1.3"` - 二进制序列化
  - `rmp-serde = "1.1"` - MessagePack 支持
- **选择理由**:
  - 零成本序列化
  - 广泛的格式支持
  - 类型安全
  - 优秀的性能

#### Protocol Buffers
- **版本**: `prost = "0.12"`
- **构建工具**: `prost-build = "0.12"`
- **选择理由**:
  - 高效的二进制协议
  - 跨语言支持
  - 向后兼容性
  - 适合 IPC 通信

### 网络和通信

#### HTTP 客户端/服务器
- **版本**: `hyper = "1.0"`
- **功能特性**: `["server", "client", "http1", "http2"]`
- **TLS 支持**: `rustls = "0.22"`
- **选择理由**:
  - 高性能 HTTP 实现
  - 异步支持
  - 内存安全
  - 模块化设计

#### WebSocket 支持
- **版本**: `tokio-tungstenite = "0.21"`
- **功能特性**: `["native-tls"]`
- **用途**: 实时通信和状态同步

### 系统服务集成

#### Windows 平台
```toml
[target.'cfg(windows)'.dependencies]
windows-service = "0.6"
winapi = { version = "0.3", features = [
    "winsvc", "winreg", "processthreadsapi", 
    "handleapi", "synchapi", "winbase"
] }
windows = { version = "0.52", features = [
    "Win32_System_Services",
    "Win32_System_Registry", 
    "Win32_Security"
] }
```

#### macOS 平台
```toml
[target.'cfg(target_os = "macos")'.dependencies]
core-foundation = "0.9"
core-foundation-sys = "0.8"
libc = "0.2"
mach2 = "0.4"
```

#### Linux 平台
```toml
[target.'cfg(target_os = "linux")'.dependencies]
systemd = "0.10"
nix = { version = "0.27", features = ["process", "signal", "user"] }
libc = "0.2"
```

## 🎨 Tauri 应用框架

### Tauri 核心
- **版本**: `tauri = "2.0"`
- **功能特性**: `["api-all", "system-tray", "updater", "isolation"]`
- **选择理由**:
  - 轻量级桌面应用框架
  - 安全的前后端通信
  - 跨平台支持
  - 小体积分发包
  - 原生性能

### 构建工具
- **版本**: `tauri-build = "2.0"`
- **功能特性**: `["isolation", "config-json5"]`

### 配置示例
```toml
[dependencies]
tauri = { version = "2.0", features = [
    "api-all",
    "system-tray", 
    "updater",
    "isolation",
    "window-all",
    "shell-all"
] }
tauri-plugin-stronghold = "2.0"
tauri-plugin-store = "2.0"
tauri-plugin-window-state = "2.0"
```

## 💾 数据存储技术栈

### 数据库系统

#### SQLite
- **版本**: `3.44.0+`
- **Rust 绑定**: `rusqlite = "0.30"`
- **功能特性**: `["bundled", "backup", "blob", "chrono", "serde_json"]`
- **选择理由**:
  - 零配置嵌入式数据库
  - ACID 事务支持
  - 跨平台兼容性
  - 优秀的性能
  - 成熟稳定

#### SQLCipher (加密扩展)
- **版本**: `4.5.5+`
- **集成方式**: 通过 `libsqlite3-sys` 编译时链接
- **加密算法**: AES-256-CBC
- **选择理由**:
  - 透明数据库加密
  - 与 SQLite 完全兼容
  - 经过安全审计
  - 企业级安全标准

### ORM 框架

#### SeaORM
- **版本**: `sea-orm = "0.12"`
- **功能特性**: `["sqlx-sqlite", "runtime-tokio-rustls", "macros", "with-chrono", "with-uuid"]`
- **选择理由**:
  - 异步 ORM 框架
  - 类型安全的查询构建
  - 自动迁移支持
  - 优秀的性能
  - 活跃的社区

#### SQLx (底层数据库驱动)
- **版本**: `sqlx = "0.7"`
- **功能特性**: `["runtime-tokio-rustls", "sqlite", "chrono", "uuid", "migrate"]`

### 配置示例
```toml
[dependencies]
sea-orm = { version = "0.12", features = [
    "sqlx-sqlite", 
    "runtime-tokio-rustls", 
    "macros",
    "with-chrono",
    "with-uuid",
    "with-json"
] }
sqlx = { version = "0.7", features = [
    "runtime-tokio-rustls",
    "sqlite",
    "chrono", 
    "uuid",
    "migrate",
    "json"
] }
```

## 🔐 安全和加密技术栈

### 密码学库

#### Ring 加密库
- **版本**: `ring = "0.17"`
- **选择理由**:
  - 经过安全审计的加密实现
  - 高性能优化
  - 内存安全
  - 广泛的算法支持
- **支持算法**:
  - AEAD: AES-GCM, ChaCha20-Poly1305
  - 哈希: SHA-256, SHA-384, SHA-512
  - HMAC: HMAC-SHA256, HMAC-SHA384
  - 密钥交换: ECDH (P-256, P-384)

#### 专用加密库
```toml
[dependencies]
# 对称加密
aes-gcm = "0.10"
chacha20poly1305 = "0.10"

# 密钥派生
argon2 = "0.5"
pbkdf2 = "0.12"
scrypt = "0.11"

# 非对称加密
x25519-dalek = "2.0"
ed25519-dalek = "2.0"

# 随机数生成
rand = "0.8"
rand_chacha = "0.3"

# 编码
base64 = "0.21"
hex = "0.4"
```

### 密钥管理

#### Tauri Stronghold
- **版本**: `tauri-plugin-stronghold = "2.0"`
- **选择理由**:
  - 专为 Tauri 设计的安全存储
  - 硬件安全模块支持
  - 零知识架构
  - 内存保护
- **功能特性**:
  - 安全密钥存储
  - 加密数据保险库
  - 生物识别集成
  - 审计日志

## 🌐 前端技术栈

### 浏览器扩展开发

#### TypeScript
- **版本**: `typescript = "^5.3.0"`
- **选择理由**:
  - 类型安全
  - 优秀的 IDE 支持
  - 现代 JavaScript 特性
  - 大型项目维护性

#### WebExtension APIs
- **Polyfill**: `webextension-polyfill = "^0.10.0"`
- **类型定义**: `@types/webextension-polyfill = "^0.10.0"`
- **浏览器支持**:
  - Chrome/Chromium: Manifest V3
  - Firefox: Manifest V2/V3
  - Edge: Manifest V3
  - Safari: Safari Web Extensions

#### 构建工具
```json
{
  "devDependencies": {
    "webpack": "^5.89.0",
    "webpack-cli": "^5.1.4",
    "ts-loader": "^9.5.1",
    "copy-webpack-plugin": "^11.0.0",
    "terser-webpack-plugin": "^5.3.9"
  }
}
```

### Tauri 前端 (可选)

#### React 技术栈
```json
{
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "@types/react": "^18.2.45",
    "@types/react-dom": "^18.2.18"
  },
  "devDependencies": {
    "vite": "^5.0.0",
    "@vitejs/plugin-react": "^4.2.0"
  }
}
```

#### Vue 技术栈 (替代方案)
```json
{
  "dependencies": {
    "vue": "^3.4.0",
    "@vue/runtime-core": "^3.4.0"
  },
  "devDependencies": {
    "vite": "^5.0.0",
    "@vitejs/plugin-vue": "^5.0.0"
  }
}
```

## 🔧 开发工具和测试

### 测试框架
```toml
[dev-dependencies]
# 单元测试
tokio-test = "0.4"
mockall = "0.11"
serial_test = "3.0"

# 集成测试
testcontainers = "0.15"
tempfile = "3.8"

# 性能测试
criterion = { version = "0.5", features = ["html_reports"] }

# 属性测试
proptest = "1.4"
quickcheck = "1.0"
```

### 代码质量工具
```toml
# Cargo.toml
[workspace.lints.rust]
unsafe_code = "forbid"
missing_docs = "warn"

[workspace.lints.clippy]
all = "warn"
pedantic = "warn"
nursery = "warn"
```

### 日志和监控
```toml
[dependencies]
# 结构化日志
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
tracing-appender = "0.2"

# 性能监控
prometheus = "0.13"
sysinfo = "0.30"

# 错误处理
thiserror = "1.0"
anyhow = "1.0"
```

## 📊 兼容性矩阵

### 操作系统支持

| 平台 | 最低版本 | 推荐版本 | 架构支持 | 状态 |
|------|----------|----------|----------|------|
| **Windows** | Windows 10 (1903) | Windows 11 22H2+ | x64, ARM64 | ✅ 完全支持 |
| **macOS** | macOS 10.15 (Catalina) | macOS 14+ (Sonoma) | x64, ARM64 (Apple Silicon) | ✅ 完全支持 |
| **Linux** | Ubuntu 18.04+ / CentOS 8+ | Ubuntu 22.04+ / RHEL 9+ | x64, ARM64 | ✅ 完全支持 |

### 浏览器支持

| 浏览器 | 最低版本 | 推荐版本 | Manifest | Native Messaging | 状态 |
|--------|----------|----------|----------|------------------|------|
| **Chrome** | 88+ | 120+ | V3 | ✅ | ✅ 完全支持 |
| **Firefox** | 78+ | 121+ | V2/V3 | ✅ | ✅ 完全支持 |
| **Edge** | 88+ | 120+ | V3 | ✅ | ✅ 完全支持 |
| **Safari** | 14+ | 17+ | Safari Extensions | ✅ | ✅ 完全支持 |
| **Brave** | 1.20+ | 1.60+ | V3 | ✅ | ✅ 完全支持 |
| **Opera** | 74+ | 105+ | V3 | ✅ | ✅ 完全支持 |

### Rust 工具链兼容性

| 组件 | 最低版本 | 推荐版本 | 说明 |
|------|----------|----------|------|
| **rustc** | 1.75.0 | 1.76+ | 2024 Edition 支持 |
| **cargo** | 1.75.0 | 1.76+ | 工作空间和特性解析 |
| **rustfmt** | 1.7.0 | 最新 | 代码格式化 |
| **clippy** | 0.1.75 | 最新 | 静态分析 |

## 🔄 依赖版本管理策略

### 版本固定策略

#### 核心依赖 (严格版本控制)
```toml
# 关键安全和稳定性依赖使用精确版本
[dependencies]
ring = "=0.17.7"                    # 加密库，安全关键
tokio = "=1.35.1"                   # 运行时，稳定性关键
serde = "=1.0.193"                  # 序列化，兼容性关键
tauri = "=2.0.0-beta.13"           # 应用框架，功能关键
```

#### 工具依赖 (兼容版本控制)
```toml
# 开发工具和非关键依赖使用兼容版本
[dev-dependencies]
tokio-test = "0.4"                  # 测试工具
criterion = "0.5"                   # 性能测试
mockall = "0.11"                    # Mock 框架
```

### 依赖更新策略

#### 自动化更新流程
1. **每周依赖检查**: 使用 `cargo audit` 检查安全漏洞
2. **每月小版本更新**: 更新补丁版本和小版本
3. **每季度大版本评估**: 评估主要版本升级
4. **安全更新**: 立即应用安全补丁

#### 更新验证流程
```bash
# 1. 检查过时依赖
cargo outdated

# 2. 安全审计
cargo audit

# 3. 更新依赖
cargo update

# 4. 运行完整测试套件
cargo test --all-features
cargo test --release

# 5. 性能回归测试
cargo bench
```

## 🏗️ 构建和部署配置

### Cargo 工作空间配置

```toml
# Cargo.toml (工作空间根目录)
[workspace]
members = [
    "src-tauri/daemon",           # 守护进程
    "src-tauri",                  # 主应用
    "crates/native-messaging",    # 共享库
    "crates/security",            # 安全模块
    "crates/ipc",                 # IPC 通信
]

[workspace.dependencies]
# 共享依赖版本管理
tokio = { version = "1.35", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
tracing = "0.1"
anyhow = "1.0"
thiserror = "1.0"

[workspace.lints.rust]
unsafe_code = "forbid"
missing_docs = "warn"
dead_code = "warn"

[workspace.lints.clippy]
all = "warn"
pedantic = "warn"
nursery = "warn"
cargo = "warn"
```

### 平台特定构建配置

#### Windows 构建
```toml
# .cargo/config.toml
[target.x86_64-pc-windows-msvc]
rustflags = [
    "-C", "target-feature=+crt-static",  # 静态链接 CRT
    "-C", "link-arg=/SUBSYSTEM:WINDOWS", # Windows 子系统
]

[target.aarch64-pc-windows-msvc]
rustflags = [
    "-C", "target-feature=+crt-static",
    "-C", "link-arg=/SUBSYSTEM:WINDOWS",
]
```

#### macOS 构建
```toml
[target.x86_64-apple-darwin]
rustflags = [
    "-C", "link-arg=-Wl,-rpath,@loader_path",
]

[target.aarch64-apple-darwin]
rustflags = [
    "-C", "link-arg=-Wl,-rpath,@loader_path",
]
```

#### Linux 构建
```toml
[target.x86_64-unknown-linux-gnu]
rustflags = [
    "-C", "link-arg=-Wl,-rpath,$ORIGIN",
]

[target.aarch64-unknown-linux-gnu]
rustflags = [
    "-C", "link-arg=-Wl,-rpath,$ORIGIN",
]
```

### 发布配置优化

```toml
# Cargo.toml
[profile.release]
opt-level = 3                    # 最大优化
lto = true                       # 链接时优化
codegen-units = 1               # 单个代码生成单元
panic = "abort"                 # 崩溃时直接终止
strip = true                    # 移除调试符号

[profile.release-with-debug]
inherits = "release"
debug = true                    # 保留调试信息
strip = false                   # 保留符号表
```

## 🔧 替代技术方案

### 编程语言替代方案

#### Go 语言方案
**优势**:
- 简单的并发模型
- 快速编译
- 优秀的跨平台支持
- 内置垃圾回收

**劣势**:
- 运行时开销
- 内存使用较高
- 缺乏零成本抽象

**适用场景**: 快速原型开发，对性能要求不极致的场景

#### C++ 方案
**优势**:
- 极致性能
- 成熟的生态系统
- 广泛的平台支持

**劣势**:
- 内存安全问题
- 复杂的构建系统
- 开发效率较低

**适用场景**: 对性能有极致要求的场景

### 数据库替代方案

#### PostgreSQL 嵌入式
- **库**: `libpq` + `embedded-postgres`
- **优势**: 功能丰富，SQL 标准兼容
- **劣势**: 体积大，配置复杂

#### RocksDB
- **库**: `rocksdb`
- **优势**: 高性能 KV 存储，LSM 树结构
- **劣势**: 无 SQL 支持，学习成本高

#### LMDB
- **库**: `lmdb-rs`
- **优势**: 极高性能，内存映射
- **劣势**: 功能有限，事务模型简单

### 加密库替代方案

#### OpenSSL
- **库**: `openssl`
- **优势**: 功能全面，广泛使用
- **劣势**: 安全历史问题，体积大

#### libsodium
- **库**: `sodiumoxide`
- **优势**: 易用性好，安全性高
- **劣势**: 算法选择有限

#### AWS-LC
- **库**: `aws-lc-rs`
- **优势**: AWS 维护，FIPS 认证
- **劣势**: 相对较新，生态系统小

## 📈 性能基准和优化

### 性能目标

| 指标 | 目标值 | 测量方法 |
|------|--------|----------|
| **启动时间** | < 2秒 | 守护进程完全启动时间 |
| **内存使用** | < 50MB | 空闲状态下内存占用 |
| **响应延迟** | < 10ms | IPC 消息往返时间 |
| **吞吐量** | > 10,000 msg/s | 并发消息处理能力 |
| **CPU 使用** | < 5% | 空闲状态下 CPU 占用 |

### 性能优化策略

#### 编译时优化
```toml
[profile.release]
# 启用所有优化
opt-level = 3
lto = "fat"
codegen-units = 1
panic = "abort"

# 目标 CPU 优化
[build]
rustflags = ["-C", "target-cpu=native"]
```

#### 运行时优化
```rust
// 内存池配置
tokio::runtime::Builder::new_multi_thread()
    .worker_threads(num_cpus::get())
    .thread_stack_size(2 * 1024 * 1024)  // 2MB 栈大小
    .build()
```

#### 数据库优化
```sql
-- SQLite 性能调优
PRAGMA journal_mode = WAL;
PRAGMA synchronous = NORMAL;
PRAGMA cache_size = 10000;
PRAGMA temp_store = MEMORY;
PRAGMA mmap_size = 268435456; -- 256MB
```

## 🔍 监控和可观测性

### 指标收集
```toml
[dependencies]
# Prometheus 指标
prometheus = "0.13"
# 系统信息
sysinfo = "0.30"
# 进程监控
procfs = "0.16"
```

### 日志配置
```rust
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

tracing_subscriber::registry()
    .with(tracing_subscriber::EnvFilter::new("info"))
    .with(tracing_subscriber::fmt::layer().json())
    .with(tracing_appender::rolling::daily("logs", "daemon.log"))
    .init();
```

### 健康检查端点
```rust
// HTTP 健康检查服务
async fn health_check() -> impl warp::Reply {
    warp::reply::json(&serde_json::json!({
        "status": "healthy",
        "timestamp": chrono::Utc::now(),
        "version": env!("CARGO_PKG_VERSION")
    }))
}
```
